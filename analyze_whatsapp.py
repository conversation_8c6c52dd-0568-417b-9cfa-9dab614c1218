import csv
from collections import Counter, defaultdict
import re

# Read the CSV file
messages = []
with open('/tmp/whatsapp_messages.csv', 'r', encoding='utf-8') as f:
    reader = csv.reader(f)
    for row in reader:
        if len(row) >= 2:
            messages.append(row[1])

# Analyze patterns
greeting_patterns = []
price_patterns = []
promotion_patterns = []
appointment_patterns = []
followup_patterns = []
other_patterns = []

# Common emojis
emoji_counter = Counter()

for msg in messages:
    # Count emojis
    emojis = re.findall(r'[👋🎯🎁✅📋🔬🩻🧪📄📍🕐📆💰🧑‍🧑‍🧒💲🙇🏻‍♀️⸻❤️😊🙏👍💪🎉✨🌟🔥]', msg)
    emoji_counter.update(emojis)
    
    # Categorize messages
    if '你好' in msg or '歡迎' in msg:
        greeting_patterns.append(msg)
    elif '價錢' in msg or '$' in msg or '原價' in msg:
        price_patterns.append(msg)
    elif '優惠' in msg or '折' in msg or '免費' in msg:
        promotion_patterns.append(msg)
    elif '預約' in msg or '日期' in msg or 'book' in msg:
        appointment_patterns.append(msg)
    elif '跟進' in msg or '提醒' in msg or '確認' in msg:
        followup_patterns.append(msg)
    else:
        other_patterns.append(msg)

# Print analysis
print("=== 消息分類統計 ===")
print(f"總消息數: {len(messages)}")
print(f"開場白類: {len(greeting_patterns)}")
print(f"價格類: {len(price_patterns)}")
print(f"優惠類: {len(promotion_patterns)}")
print(f"預約類: {len(appointment_patterns)}")
print(f"跟進類: {len(followup_patterns)}")
print(f"其他類: {len(other_patterns)}")

print("\n=== 最常用表情符號 TOP 15 ===")
for emoji, count in emoji_counter.most_common(15):
    print(f"{emoji}: {count}次")

print("\n=== 開場白示例 (前3個不同模板) ===")
unique_greetings = list(set(greeting_patterns))[:3]
for i, msg in enumerate(unique_greetings, 1):
    print(f"\n模板{i}:\n{msg[:500]}")

print("\n=== 價格說明示例 (前3個不同模板) ===")
unique_prices = list(set(price_patterns))[:3]
for i, msg in enumerate(unique_prices, 1):
    print(f"\n模板{i}:\n{msg[:300]}")

print("\n=== 優惠介紹示例 (前3個不同模板) ===")
unique_promos = list(set(promotion_patterns))[:3]
for i, msg in enumerate(unique_promos, 1):
    print(f"\n模板{i}:\n{msg[:300]}")

print("\n=== 預約確認示例 (前3個不同模板) ===")
unique_appts = list(set(appointment_patterns))[:3]
for i, msg in enumerate(unique_appts, 1):
    print(f"\n模板{i}:\n{msg[:300]}")

print("\n=== 跟進消息示例 (前3個不同模板) ===")
unique_followups = list(set(followup_patterns))[:3]
for i, msg in enumerate(unique_followups, 1):
    print(f"\n模板{i}:\n{msg[:300]}")