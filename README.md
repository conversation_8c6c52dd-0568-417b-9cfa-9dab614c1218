# Claude Code 安裝與使用指南

這個項目包含了 Anthropic Claude Code 的安裝和使用指南。Claude Code 是一個 agentic 編程工具，可以直接在您的終端中運行，理解您的代碼庫，並通過自然語言命令幫助您更快地編程。

## 🎉 安裝狀態

✅ **Node.js v24.3.0** - 已安裝  
✅ **npm v11.4.2** - 已安裝  
✅ **Claude Code v1.0.35** - 已安裝  

## 🚀 快速開始

### 1. 設置 API Key

選擇以下任一方法設置 API key：

**方法 A - 使用 Claude Code 內建認證 (推薦)**
```bash
claude auth
```

**方法 B - 環境變數設置**
```bash
export ANTHROPIC_API_KEY='your-api-key-here'
```

**方法 C - 永久設置**
```bash
echo 'export ANTHROPIC_API_KEY="your-api-key-here"' >> ~/.zshrc
source ~/.zshrc
```

### 2. 啟動 Claude Code

```bash
claude
```

### 3. 開始使用

在 Claude Code 互動模式中，您可以使用自然語言與 AI 對話：

```
你好！請幫我分析這個 Python 文件的功能
```

```
創建一個 React 組件來顯示用戶列表
```

```
修復這個函數中的 bug
```

## 📁 項目文件

```
/Users/<USER>/Claude/
├── README.md                    # 本文件
├── setup_claude_code.sh         # 設置和檢查腳本
└── claude_code_examples.md      # 使用示例和最佳實踐
```

## 🔧 主要功能

### 💻 代碼開發
- **文件編輯**: 直接編輯和修改代碼文件
- **Bug 修復**: 自動檢測和修復代碼問題
- **代碼生成**: 根據需求生成新代碼
- **重構**: 改進代碼結構和性能

### 🧪 測試和調試
- **測試生成**: 自動生成單元測試
- **測試執行**: 運行測試並分析結果
- **調試協助**: 幫助定位和解決問題

### 📚 文檔和分析
- **代碼分析**: 理解代碼架構和邏輯
- **文檔生成**: 自動生成 API 文檔
- **代碼審查**: 提供代碼質量建議

### 🔄 版本控制
- **Git 操作**: 查看歷史、解決衝突
- **提交管理**: 創建描述性提交消息
- **分支管理**: 協助分支操作

### 🌐 網絡搜索
- **文檔查找**: 搜索相關技術文檔
- **問題解決**: 查找解決方案和最佳實踐

## 🛠️ 常用命令

```bash
claude                    # 啟動互動模式
claude auth               # 設置認證
claude --help             # 查看幫助
claude --version          # 查看版本
./setup_claude_code.sh    # 運行設置腳本
```

## 🔒 安全和隱私

Claude Code 的架構確保您的代碼安全：

- **直接 API 連接**: 查詢直接發送到 Anthropic API，無中間服務器
- **本地運行**: 直接在您的終端中操作
- **上下文感知**: 理解整個項目結構
- **實際操作**: 執行真實的文件編輯和提交操作

## 🏢 企業集成

Claude Code 可以與企業 AI 平台無縫集成：
- Amazon Bedrock
- Google Vertex AI
- 企業代理服務器
- LLM 網關

## 📚 學習資源

- [官方文檔](https://docs.anthropic.com/en/docs/claude-code/overview)
- [快速開始指南](https://docs.anthropic.com/en/docs/claude-code/quickstart)
- [常見工作流程](https://docs.anthropic.com/en/docs/claude-code/common-workflows)
- [故障排除](https://docs.anthropic.com/en/docs/claude-code/troubleshooting)

## ❓ 常見問題

### Q: 如何獲取 API Key？
A: 前往 [Anthropic Console](https://console.anthropic.com/) 註冊並創建 API key

### Q: Claude Code 支援哪些程式語言？
A: 支援所有主流程式語言，包括 Python、JavaScript、TypeScript、Java、C++、Go 等

### Q: 可以在企業環境中使用嗎？
A: 是的，支援企業級部署和安全要求

### Q: 如何更新 Claude Code？
A: 運行 `npm update -g @anthropic-ai/claude-code`

## 🎯 下一步

1. 運行 `claude auth` 設置認證
2. 啟動 `claude` 開始使用
3. 查看 `claude_code_examples.md` 了解更多使用示例
4. 探索官方文檔學習進階功能

---

🚀 **Claude Code 已準備就緒！開始您的 AI 輔助編程之旅吧！**
