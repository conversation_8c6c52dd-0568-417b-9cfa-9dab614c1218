#!/bin/bash

# 測試腳本顏色
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

echo "開始測試 AI Marketing Website..."
echo "================================="

# 定義要測試的頁面
declare -a pages=(
    "/"
    "/services"
    "/case-studies"
    "/about"
    "/blog"
    "/contact"
)

declare -a page_names=(
    "首頁"
    "服務頁面"
    "案例研究頁面"
    "關於我們頁面"
    "部落格頁面"
    "聯絡我們頁面"
)

# 測試每個頁面
for i in "${!pages[@]}"; do
    url="http://localhost:3001${pages[$i]}"
    name="${page_names[$i]}"
    
    echo -e "\n測試 ${YELLOW}$name${NC} ($url)..."
    
    # 獲取狀態碼
    status_code=$(curl -s -o /dev/null -w "%{http_code}" "$url")
    
    if [ "$status_code" = "200" ]; then
        echo -e "  ✓ 狀態碼: ${GREEN}$status_code${NC}"
        
        # 檢查頁面大小
        size=$(curl -s "$url" | wc -c)
        echo -e "  ✓ 頁面大小: $(echo $size | numfmt --to=iec-i --suffix=B)"
        
        # 檢查是否包含錯誤訊息
        errors=$(curl -s "$url" | grep -i -E "(error|exception|failed|crash)" | wc -l)
        if [ "$errors" -eq 0 ]; then
            echo -e "  ✓ ${GREEN}沒有發現錯誤訊息${NC}"
        else
            echo -e "  ✗ ${RED}發現 $errors 個可能的錯誤${NC}"
        fi
        
        # 檢查響應時間
        response_time=$(curl -s -o /dev/null -w "%{time_total}" "$url")
        echo -e "  ✓ 響應時間: ${response_time}s"
        
    else
        echo -e "  ✗ 狀態碼: ${RED}$status_code${NC}"
    fi
done

echo -e "\n================================="
echo "測試完成！"

# 測試 API 端點（如果有的話）
echo -e "\n測試 API 端點..."
api_status=$(curl -s -o /dev/null -w "%{http_code}" "http://localhost:3001/api/health")
if [ "$api_status" = "200" ] || [ "$api_status" = "404" ]; then
    echo -e "  ✓ API 端點回應正常"
else
    echo -e "  ✗ API 端點可能有問題: $api_status"
fi