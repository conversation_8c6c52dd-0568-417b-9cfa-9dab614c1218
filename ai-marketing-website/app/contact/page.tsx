'use client'

import { motion } from 'framer-motion'
import { useState } from 'react'
import { 
  Mail, 
  Phone, 
  MapPin, 
  Clock,
  Send,
  Sparkles,
  MessageSquare,
  CheckCircle,
  ArrowRight
} from 'lucide-react'

export default function ContactPage() {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    company: '',
    phone: '',
    service: '',
    message: '',
  })

  const [isSubmitting, setIsSubmitting] = useState(false)
  const [isSubmitted, setIsSubmitted] = useState(false)

  const services = [
    'AI 營銷策略諮詢',
    '精準營銷自動化',
    '數據分析與洞察',
    '增長駭客服務',
    '其他',
  ]

  const contactInfo = [
    {
      icon: MapPin,
      title: '辦公地址',
      content: '香港中環德輔道中88號',
      subContent: '中環廣場 18 樓',
    },
    {
      icon: Phone,
      title: '聯絡電話',
      content: '+852 1234 5678',
      subContent: '週一至週五 9:00-18:00',
    },
    {
      icon: Mail,
      title: '電子郵件',
      content: '<EMAIL>',
      subContent: '24小時內回覆',
    },
    {
      icon: Clock,
      title: '營業時間',
      content: '週一至週五：9:00 - 18:00',
      subContent: '週末及公眾假期休息',
    },
  ]

  const benefits = [
    '免費營銷策略評估',
    '專業 AI 顧問一對一諮詢',
    '定制化解決方案建議',
    '無隱藏費用',
  ]

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value,
    })
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsSubmitting(true)
    
    // 模擬提交延遲
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    setIsSubmitting(false)
    setIsSubmitted(true)
  }

  return (
    <div className="min-h-screen pt-20">
      {/* Hero Section */}
      <section className="section-spacing relative overflow-hidden">
        <div className="absolute inset-0 -z-10">
          <div className="absolute inset-0 bg-gradient-radial"></div>
        </div>

        <div className="container-custom">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="text-center max-w-4xl mx-auto"
          >
            <motion.div
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              className="inline-flex items-center space-x-2 badge badge-primary mb-6"
            >
              <Sparkles className="w-4 h-4" />
              <span>聯絡我們</span>
            </motion.div>

            <h1 className="heading-xl mb-6">
              開啟您的 <span className="gradient-text">AI 營銷之旅</span>
            </h1>
            
            <p className="body-lg text-gray-600 mb-8 max-w-3xl mx-auto">
              我們的專家團隊隨時準備為您提供專業諮詢，
              讓我們一起探討如何用 AI 技術推動您的業務增長
            </p>
          </motion.div>
        </div>
      </section>

      {/* Contact Form & Info Section */}
      <section className="section-spacing bg-gradient-to-b from-gray-50 to-white">
        <div className="container-custom">
          <div className="grid lg:grid-cols-2 gap-12">
            {/* Contact Form */}
            <motion.div
              initial={{ opacity: 0, x: -50 }}
              whileInView={{ opacity: 1, x: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.8 }}
            >
              <div className="card-modern p-8">
                <h2 className="text-2xl font-bold mb-6">立即預約免費諮詢</h2>
                
                {!isSubmitted ? (
                  <form onSubmit={handleSubmit} className="space-y-6">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          您的姓名 *
                        </label>
                        <input
                          type="text"
                          name="name"
                          required
                          value={formData.name}
                          onChange={handleChange}
                          className="w-full px-4 py-3 rounded-lg border border-gray-300 focus:border-indigo-500 focus:ring-2 focus:ring-indigo-200 transition-all"
                          placeholder="請輸入您的姓名"
                        />
                      </div>
                      
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          電子郵件 *
                        </label>
                        <input
                          type="email"
                          name="email"
                          required
                          value={formData.email}
                          onChange={handleChange}
                          className="w-full px-4 py-3 rounded-lg border border-gray-300 focus:border-indigo-500 focus:ring-2 focus:ring-indigo-200 transition-all"
                          placeholder="<EMAIL>"
                        />
                      </div>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          公司名稱
                        </label>
                        <input
                          type="text"
                          name="company"
                          value={formData.company}
                          onChange={handleChange}
                          className="w-full px-4 py-3 rounded-lg border border-gray-300 focus:border-indigo-500 focus:ring-2 focus:ring-indigo-200 transition-all"
                          placeholder="您的公司名稱"
                        />
                      </div>
                      
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          聯絡電話
                        </label>
                        <input
                          type="tel"
                          name="phone"
                          value={formData.phone}
                          onChange={handleChange}
                          className="w-full px-4 py-3 rounded-lg border border-gray-300 focus:border-indigo-500 focus:ring-2 focus:ring-indigo-200 transition-all"
                          placeholder="+852 xxxx xxxx"
                        />
                      </div>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        感興趣的服務 *
                      </label>
                      <select
                        name="service"
                        required
                        value={formData.service}
                        onChange={handleChange}
                        className="w-full px-4 py-3 rounded-lg border border-gray-300 focus:border-indigo-500 focus:ring-2 focus:ring-indigo-200 transition-all"
                      >
                        <option value="">請選擇服務類型</option>
                        {services.map((service) => (
                          <option key={service} value={service}>
                            {service}
                          </option>
                        ))}
                      </select>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        項目詳情
                      </label>
                      <textarea
                        name="message"
                        rows={4}
                        value={formData.message}
                        onChange={handleChange}
                        className="w-full px-4 py-3 rounded-lg border border-gray-300 focus:border-indigo-500 focus:ring-2 focus:ring-indigo-200 transition-all resize-none"
                        placeholder="請簡述您的需求或挑戰..."
                      />
                    </div>

                    <button
                      type="submit"
                      disabled={isSubmitting}
                      className="w-full btn-primary disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      {isSubmitting ? (
                        <span className="flex items-center justify-center">
                          <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                            <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                          </svg>
                          提交中...
                        </span>
                      ) : (
                        <span className="flex items-center justify-center">
                          發送諮詢請求
                          <Send className="w-5 h-5 ml-2" />
                        </span>
                      )}
                    </button>
                  </form>
                ) : (
                  <motion.div
                    initial={{ opacity: 0, scale: 0.9 }}
                    animate={{ opacity: 1, scale: 1 }}
                    className="text-center py-12"
                  >
                    <div className="w-20 h-20 mx-auto mb-6 bg-green-100 rounded-full flex items-center justify-center">
                      <CheckCircle className="w-10 h-10 text-green-600" />
                    </div>
                    <h3 className="text-2xl font-bold mb-4">感謝您的諮詢！</h3>
                    <p className="text-gray-600 mb-6">
                      我們已收到您的訊息，專業顧問將在 24 小時內與您聯繫。
                    </p>
                    <button
                      onClick={() => {
                        setIsSubmitted(false)
                        setFormData({
                          name: '',
                          email: '',
                          company: '',
                          phone: '',
                          service: '',
                          message: '',
                        })
                      }}
                      className="text-indigo-600 font-semibold hover:text-indigo-700"
                    >
                      發送新的諮詢
                    </button>
                  </motion.div>
                )}
              </div>
            </motion.div>

            {/* Contact Info */}
            <motion.div
              initial={{ opacity: 0, x: 50 }}
              whileInView={{ opacity: 1, x: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.8 }}
              className="space-y-8"
            >
              {/* Benefits Card */}
              <div className="card-modern p-8 bg-gradient-to-r from-indigo-50 to-purple-50 border-0">
                <h3 className="text-xl font-bold mb-6">您將獲得：</h3>
                <div className="space-y-3">
                  {benefits.map((benefit, index) => (
                    <motion.div
                      key={index}
                      initial={{ opacity: 0, x: -20 }}
                      whileInView={{ opacity: 1, x: 0 }}
                      viewport={{ once: true }}
                      transition={{ duration: 0.5, delay: index * 0.1 }}
                      className="flex items-center space-x-3"
                    >
                      <CheckCircle className="w-5 h-5 text-green-500 flex-shrink-0" />
                      <span className="text-gray-700">{benefit}</span>
                    </motion.div>
                  ))}
                </div>
              </div>

              {/* Contact Information */}
              <div className="space-y-4">
                {contactInfo.map((info, index) => (
                  <motion.div
                    key={index}
                    initial={{ opacity: 0, y: 20 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    viewport={{ once: true }}
                    transition={{ duration: 0.6, delay: index * 0.1 }}
                    className="card-modern p-6"
                  >
                    <div className="flex items-start space-x-4">
                      <div className="w-12 h-12 bg-gradient-to-r from-indigo-600 to-purple-600 rounded-xl flex items-center justify-center flex-shrink-0">
                        <info.icon className="w-6 h-6 text-white" />
                      </div>
                      <div>
                        <h4 className="font-semibold mb-1">{info.title}</h4>
                        <p className="text-gray-900">{info.content}</p>
                        <p className="text-sm text-gray-600">{info.subContent}</p>
                      </div>
                    </div>
                  </motion.div>
                ))}
              </div>

              {/* Quick Chat */}
              <div className="card-modern p-6 text-center">
                <MessageSquare className="w-12 h-12 mx-auto mb-4 text-indigo-600" />
                <h4 className="font-semibold mb-2">需要即時對話？</h4>
                <p className="text-gray-600 mb-4">
                  我們的線上客服隨時為您解答疑問
                </p>
                <button className="btn-secondary w-full">
                  開始線上對話
                </button>
              </div>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Map Section */}
      <section className="section-spacing">
        <div className="container-custom">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            className="text-center mb-12"
          >
            <h2 className="heading-lg mb-6">
              歡迎蒞臨我們的 <span className="gradient-text">辦公室</span>
            </h2>
            <p className="body-lg text-gray-600 max-w-3xl mx-auto">
              位於香港中環核心商業區，交通便利，歡迎預約拜訪
            </p>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, scale: 0.95 }}
            whileInView={{ opacity: 1, scale: 1 }}
            viewport={{ once: true }}
            transition={{ duration: 0.8 }}
            className="card-modern p-2 overflow-hidden"
          >
            <div className="aspect-video bg-gray-200 rounded-lg flex items-center justify-center">
              <div className="text-center">
                <MapPin className="w-16 h-16 text-gray-400 mx-auto mb-4" />
                <p className="text-gray-600">互動地圖加載中...</p>
              </div>
            </div>
          </motion.div>
        </div>
      </section>

      {/* FAQ Section */}
      <section className="section-spacing bg-gradient-to-b from-gray-50 to-white">
        <div className="container-custom">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            className="text-center mb-12"
          >
            <h2 className="heading-lg mb-6">常見問題</h2>
          </motion.div>

          <div className="max-w-3xl mx-auto space-y-4">
            {[
              {
                question: '諮詢服務是否收費？',
                answer: '首次諮詢完全免費，我們會為您提供初步的營銷策略建議和解決方案。',
              },
              {
                question: '項目通常需要多長時間？',
                answer: '根據項目複雜度不同，一般需要 2-6 個月。我們會在初次評估後提供詳細時間表。',
              },
              {
                question: '你們服務哪些地區？',
                answer: '我們的服務覆蓋全球，主要服務亞太地區，包括香港、中國大陸、東南亞等市場。',
              },
              {
                question: '如何確保數據安全？',
                answer: '我們採用企業級安全標準，包括端到端加密、定期安全審計，並完全符合 GDPR 等數據保護法規。',
              },
            ].map((faq, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                className="card-modern p-6"
              >
                <h3 className="font-semibold mb-2">{faq.question}</h3>
                <p className="text-gray-600">{faq.answer}</p>
              </motion.div>
            ))}
          </div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            className="text-center mt-12"
          >
            <p className="text-gray-600 mb-4">還有其他問題？</p>
            <a href="#" className="text-indigo-600 font-semibold hover:text-indigo-700">
              查看完整 FAQ
              <ArrowRight className="inline-block w-4 h-4 ml-1" />
            </a>
          </motion.div>
        </div>
      </section>
    </div>
  )
}