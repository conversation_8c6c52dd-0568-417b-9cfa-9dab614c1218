'use client'

export default function TestPage() {
  return (
    <div style={{
      background: 'linear-gradient(135deg, #6366f1 0%, #a855f7 50%, #ec4899 100%)',
      minHeight: '100vh',
      padding: '2rem',
      color: 'white',
      fontFamily: 'system-ui, sans-serif'
    }}>
      <div style={{
        maxWidth: '800px',
        margin: '0 auto',
        textAlign: 'center'
      }}>
        <h1 style={{
          fontSize: '3rem',
          fontWeight: 'bold',
          marginBottom: '2rem',
          background: 'linear-gradient(45deg, #ffffff, #fbbf24, #f59e0b)',
          WebkitBackgroundClip: 'text',
          WebkitTextFillColor: 'transparent',
          backgroundClip: 'text'
        }}>
          🎉 新設計測試頁面
        </h1>
        
        <div style={{
          background: 'rgba(255, 255, 255, 0.1)',
          backdropFilter: 'blur(10px)',
          borderRadius: '1rem',
          padding: '2rem',
          marginBottom: '2rem',
          border: '1px solid rgba(255, 255, 255, 0.2)'
        }}>
          <h2 style={{ fontSize: '1.5rem', marginBottom: '1rem' }}>
            AI Marketing Pro - 智能營銷解決方案
          </h2>
          <p style={{ fontSize: '1.1rem', lineHeight: '1.6' }}>
            如果您能看到這個頁面的彩色漸變背景和玻璃態卡片效果，
            說明新設計已經成功加載！
          </p>
        </div>

        <div style={{
          display: 'grid',
          gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',
          gap: '1rem',
          marginBottom: '2rem'
        }}>
          <div style={{
            background: 'rgba(99, 102, 241, 0.2)',
            padding: '1.5rem',
            borderRadius: '0.5rem',
            border: '1px solid rgba(99, 102, 241, 0.3)'
          }}>
            <h3 style={{ marginBottom: '0.5rem' }}>🧠 AI 智能分析</h3>
            <p style={{ fontSize: '0.9rem' }}>機器學習驅動的營銷洞察</p>
          </div>
          
          <div style={{
            background: 'rgba(168, 85, 247, 0.2)',
            padding: '1.5rem',
            borderRadius: '0.5rem',
            border: '1px solid rgba(168, 85, 247, 0.3)'
          }}>
            <h3 style={{ marginBottom: '0.5rem' }}>🎯 精準定位</h3>
            <p style={{ fontSize: '0.9rem' }}>AI 驅動的客戶細分</p>
          </div>
          
          <div style={{
            background: 'rgba(236, 72, 153, 0.2)',
            padding: '1.5rem',
            borderRadius: '0.5rem',
            border: '1px solid rgba(236, 72, 153, 0.3)'
          }}>
            <h3 style={{ marginBottom: '0.5rem' }}>⚡ 自動化</h3>
            <p style={{ fontSize: '0.9rem' }}>智能營銷流程優化</p>
          </div>
        </div>

        <div style={{
          background: 'rgba(0, 0, 0, 0.2)',
          padding: '1rem',
          borderRadius: '0.5rem',
          fontSize: '0.9rem'
        }}>
          <p><strong>測試說明：</strong></p>
          <p>如果您看到這個頁面是彩色的（不是白色背景），說明新設計可以正常工作。</p>
          <p>請訪問：<code style={{ background: 'rgba(255,255,255,0.2)', padding: '0.2rem 0.5rem', borderRadius: '0.25rem' }}>http://localhost:3001/test</code></p>
        </div>
      </div>
    </div>
  )
}
