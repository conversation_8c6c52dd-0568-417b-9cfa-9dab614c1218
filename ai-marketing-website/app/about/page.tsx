'use client'

import { motion } from 'framer-motion'
import Image from 'next/image'
import Link from 'next/link'
import { 
  Users, 
  Target, 
  Award, 
  Sparkles,
  Heart,
  Globe,
  Zap,
  Shield,
  ArrowRight,
  CheckCircle
} from 'lucide-react'

export default function AboutPage() {
  const team = [
    {
      name: '張明華',
      role: '創始人兼CEO',
      image: '/images/placeholder.svg',
      bio: '擁有15年AI和營銷技術經驗，曾任職於Google和Microsoft',
      gradient: 'from-indigo-600 to-purple-600',
    },
    {
      name: '李美玲',
      role: '首席技術官',
      image: '/images/placeholder.svg',
      bio: 'AI專家，博士學位，發表過30+篇頂級學術論文',
      gradient: 'from-purple-600 to-pink-600',
    },
    {
      name: '王建國',
      role: '營銷總監',
      image: '/images/placeholder.svg',
      bio: '數字營銷專家，幫助100+企業實現營銷轉型',
      gradient: 'from-cyan-600 to-blue-600',
    },
    {
      name: '陳雅婷',
      role: '產品總監',
      image: '/images/placeholder.svg',
      bio: '產品設計專家，專注於用戶體驗和產品創新',
      gradient: 'from-green-600 to-emerald-600',
    },
  ]

  const values = [
    {
      icon: Heart,
      title: '客戶至上',
      description: '始終將客戶的成功放在首位，提供超越期望的服務',
    },
    {
      icon: Zap,
      title: '創新驅動',
      description: '持續探索最新技術，為客戶帶來創新解決方案',
    },
    {
      icon: Shield,
      title: '誠信可靠',
      description: '建立透明、誠實的合作關係，贏得長期信任',
    },
    {
      icon: Users,
      title: '團隊協作',
      description: '匯聚頂尖人才，通過協作創造卓越成果',
    },
  ]

  const milestones = [
    { year: '2019', title: '公司成立', description: '在香港創立，專注AI營銷解決方案' },
    { year: '2020', title: '首個產品發布', description: '推出AI營銷自動化平台' },
    { year: '2021', title: '國際擴張', description: '業務拓展至東南亞市場' },
    { year: '2022', title: '獲得融資', description: '完成A輪融資，加速產品研發' },
    { year: '2023', title: '行業認可', description: '榮獲"最佳AI營銷解決方案"獎' },
    { year: '2024', title: '全球佈局', description: '服務覆蓋15+國家和地區' },
  ]

  return (
    <div className="min-h-screen pt-20">
      {/* Hero Section */}
      <section className="section-spacing relative overflow-hidden">
        <div className="absolute inset-0 -z-10">
          <div className="absolute inset-0 bg-gradient-radial"></div>
        </div>

        <div className="container-custom">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="text-center max-w-4xl mx-auto"
          >
            <motion.div
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              className="inline-flex items-center space-x-2 badge badge-primary mb-6"
            >
              <Sparkles className="w-4 h-4" />
              <span>關於我們</span>
            </motion.div>

            <h1 className="heading-xl mb-6">
              用 AI 重新定義 <span className="gradient-text">營銷的未來</span>
            </h1>
            
            <p className="body-lg text-gray-600 mb-8 max-w-3xl mx-auto">
              我們是一家專注於人工智能營銷解決方案的創新公司，
              致力於幫助企業在數字時代實現突破性增長
            </p>

            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link href="/contact" className="btn-primary">
                與我們合作
                <ArrowRight className="w-5 h-5 ml-2" />
              </Link>
              <Link href="/case-studies" className="btn-secondary">
                查看成功案例
              </Link>
            </div>
          </motion.div>
        </div>
      </section>

      {/* Mission & Vision */}
      <section className="section-spacing bg-gradient-to-b from-gray-50 to-white">
        <div className="container-custom">
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            <motion.div
              initial={{ opacity: 0, x: -50 }}
              whileInView={{ opacity: 1, x: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.8 }}
            >
              <h2 className="heading-md mb-6">我們的使命</h2>
              <p className="body-lg text-gray-600 mb-6">
                透過尖端的人工智能技術，賦能企業實現營銷轉型，
                創造可持續的業務增長，讓每個企業都能享受 AI 帶來的競爭優勢。
              </p>
              <div className="space-y-4">
                {['democratize AI 營銷技術', '推動行業數字化轉型', '創造長期客戶價值'].map((item, index) => (
                  <div key={index} className="flex items-center space-x-3">
                    <CheckCircle className="w-5 h-5 text-green-500 flex-shrink-0" />
                    <span className="text-gray-700">{item}</span>
                  </div>
                ))}
              </div>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, x: 50 }}
              whileInView={{ opacity: 1, x: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.8 }}
            >
              <div className="relative">
                <div className="absolute inset-0 bg-gradient-to-r from-indigo-600 to-purple-600 rounded-3xl blur-3xl opacity-20"></div>
                <div className="relative card-modern p-8">
                  <div className="w-16 h-16 bg-gradient-to-r from-indigo-600 to-purple-600 rounded-2xl flex items-center justify-center mb-6">
                    <Target className="w-8 h-8 text-white" />
                  </div>
                  <h3 className="text-2xl font-bold mb-4">我們的願景</h3>
                  <p className="text-gray-600 mb-6">
                    成為全球領先的 AI 營銷解決方案提供商，
                    引領營銷行業的智能化革命。
                  </p>
                  <div className="grid grid-cols-2 gap-4 text-center">
                    <div>
                      <div className="text-3xl font-bold gradient-text">2030</div>
                      <div className="text-sm text-gray-600">目標年份</div>
                    </div>
                    <div>
                      <div className="text-3xl font-bold gradient-text">No.1</div>
                      <div className="text-sm text-gray-600">行業地位</div>
                    </div>
                  </div>
                </div>
              </div>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Core Values */}
      <section className="section-spacing" id="culture">
        <div className="container-custom">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            className="text-center mb-16"
          >
            <h2 className="heading-lg mb-6">
              我們的 <span className="gradient-text">核心價值觀</span>
            </h2>
            <p className="body-lg text-gray-600 max-w-3xl mx-auto">
              這些價值觀指導著我們的每一個決策和行動
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {values.map((value, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                className="text-center"
              >
                <div className="card-modern p-6 h-full">
                  <div className="w-16 h-16 mx-auto mb-4 bg-gradient-to-r from-indigo-600 to-purple-600 rounded-2xl flex items-center justify-center">
                    <value.icon className="w-8 h-8 text-white" />
                  </div>
                  <h3 className="text-xl font-bold mb-3">{value.title}</h3>
                  <p className="text-gray-600">{value.description}</p>
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Team Section */}
      <section className="section-spacing bg-gradient-to-b from-gray-50 to-white" id="team">
        <div className="container-custom">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            className="text-center mb-16"
          >
            <h2 className="heading-lg mb-6">
              認識我們的 <span className="gradient-text">領導團隊</span>
            </h2>
            <p className="body-lg text-gray-600 max-w-3xl mx-auto">
              匯聚業界頂尖人才，共同推動 AI 營銷創新
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {team.map((member, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, scale: 0.9 }}
                whileInView={{ opacity: 1, scale: 1 }}
                viewport={{ once: true }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                className="group"
              >
                <div className="card-modern overflow-hidden hover:shadow-2xl transition-all duration-300">
                  <div className="relative h-64 overflow-hidden">
                    <Image
                      src={member.image}
                      alt={member.name}
                      fill
                      className="object-cover group-hover:scale-105 transition-transform duration-300"
                    />
                    <div className={`absolute inset-0 bg-gradient-to-t ${member.gradient} opacity-0 group-hover:opacity-20 transition-opacity`}></div>
                  </div>
                  <div className="p-6">
                    <h3 className="text-xl font-bold mb-1">{member.name}</h3>
                    <p className="text-indigo-600 mb-3">{member.role}</p>
                    <p className="text-gray-600 text-sm">{member.bio}</p>
                  </div>
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Timeline */}
      <section className="section-spacing">
        <div className="container-custom">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            className="text-center mb-16"
          >
            <h2 className="heading-lg mb-6">
              我們的 <span className="gradient-text">發展歷程</span>
            </h2>
          </motion.div>

          <div className="relative">
            {/* Timeline Line */}
            <div className="absolute left-1/2 transform -translate-x-1/2 w-0.5 h-full bg-gray-200"></div>

            <div className="space-y-12">
              {milestones.map((milestone, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, x: index % 2 === 0 ? -50 : 50 }}
                  whileInView={{ opacity: 1, x: 0 }}
                  viewport={{ once: true }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                  className={`flex items-center ${index % 2 === 0 ? 'justify-start' : 'justify-end'}`}
                >
                  <div className={`w-5/12 ${index % 2 === 0 ? 'text-right pr-8' : 'text-left pl-8'}`}>
                    <div className="card-modern p-6">
                      <div className="text-3xl font-bold gradient-text mb-2">{milestone.year}</div>
                      <h3 className="text-xl font-bold mb-2">{milestone.title}</h3>
                      <p className="text-gray-600">{milestone.description}</p>
                    </div>
                  </div>
                  <div className="absolute left-1/2 transform -translate-x-1/2 w-4 h-4 bg-indigo-600 rounded-full"></div>
                </motion.div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Global Presence */}
      <section className="section-spacing bg-gradient-to-b from-gray-50 to-white">
        <div className="container-custom">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            className="text-center"
          >
            <div className="card-modern p-12 bg-gradient-to-r from-indigo-50 to-purple-50 border-0">
              <Globe className="w-16 h-16 mx-auto mb-6 text-indigo-600" />
              <h2 className="heading-md mb-6">全球業務佈局</h2>
              <p className="body-lg text-gray-600 mb-8 max-w-3xl mx-auto">
                我們的服務已覆蓋全球15+個國家和地區，為超過300家企業提供AI營銷解決方案
              </p>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
                {[
                  { value: '15+', label: '服務國家' },
                  { value: '300+', label: '企業客戶' },
                  { value: '50+', label: '專業團隊' },
                  { value: '24/7', label: '全天候支持' },
                ].map((stat, index) => (
                  <div key={index}>
                    <div className="text-3xl font-bold gradient-text mb-2">{stat.value}</div>
                    <div className="text-gray-600">{stat.label}</div>
                  </div>
                ))}
              </div>
            </div>
          </motion.div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="section-spacing">
        <div className="container-custom">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            className="text-center"
          >
            <h2 className="heading-md mb-4">
              準備加入我們的成功故事？
            </h2>
            <p className="body-lg text-gray-600 mb-8 max-w-2xl mx-auto">
              讓我們一起探索 AI 營銷的無限可能
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link href="/contact" className="btn-primary">
                立即聯繫我們
                <ArrowRight className="w-5 h-5 ml-2" />
              </Link>
              <Link href="/careers" className="btn-secondary">
                加入我們團隊
              </Link>
            </div>
          </motion.div>
        </div>
      </section>
    </div>
  )
}