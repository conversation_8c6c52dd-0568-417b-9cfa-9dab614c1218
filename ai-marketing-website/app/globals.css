@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    /* Modern Color Palette */
    --primary: 99, 102, 241; /* Indigo */
    --secondary: 168, 85, 247; /* Purple */
    --accent: 34, 211, 238; /* <PERSON>an */
    --success: 34, 197, 94; /* Green */
    --warning: 251, 191, 36; /* Amber */
    --danger: 239, 68, 68; /* Red */
    
    /* Neutral Colors */
    --gray-50: 249, 250, 251;
    --gray-100: 243, 244, 246;
    --gray-200: 229, 231, 235;
    --gray-300: 209, 213, 219;
    --gray-400: 156, 163, 175;
    --gray-500: 107, 114, 128;
    --gray-600: 75, 85, 99;
    --gray-700: 55, 65, 81;
    --gray-800: 31, 41, 55;
    --gray-900: 17, 24, 39;
    
    /* Background Gradients */
    --bg-gradient-start: 249, 250, 251;
    --bg-gradient-end: 243, 244, 246;
  }

  * {
    @apply border-border;
  }
  
  body {
    @apply text-gray-800 antialiased;
    background: linear-gradient(135deg, 
      rgb(var(--bg-gradient-start)) 0%, 
      rgb(var(--bg-gradient-end)) 100%);
    min-height: 100vh;
  }
}

@layer components {
  /* Modern Gradient Text */
  .gradient-text {
    @apply bg-gradient-to-r from-indigo-600 via-purple-600 to-pink-600 bg-clip-text text-transparent font-bold;
  }
  
  .gradient-text-subtle {
    @apply bg-gradient-to-r from-gray-700 via-gray-800 to-gray-900 bg-clip-text text-transparent;
  }

  /* Modern Cards */
  .card-modern {
    @apply bg-white rounded-2xl shadow-xl shadow-gray-200/50 backdrop-blur-lg border border-gray-100 transition-all duration-300;
  }
  
  .card-hover {
    @apply hover:shadow-2xl hover:shadow-indigo-200/50 hover:-translate-y-1 hover:border-indigo-200;
  }

  /* Glass Morphism Effects */
  .glass-light {
    @apply bg-white/80 backdrop-blur-xl border border-white/20 shadow-lg;
  }
  
  .glass-dark {
    @apply bg-gray-900/80 backdrop-blur-xl border border-gray-700/50 shadow-2xl;
  }

  /* Modern Buttons */
  .btn-primary {
    @apply bg-gradient-to-r from-indigo-600 to-purple-600 text-white px-8 py-4 rounded-xl font-semibold shadow-lg shadow-indigo-200/50 hover:shadow-xl hover:shadow-indigo-300/50 transform hover:-translate-y-0.5 transition-all duration-300;
  }
  
  .btn-secondary {
    @apply bg-white text-gray-800 px-8 py-4 rounded-xl font-semibold border-2 border-gray-200 hover:border-indigo-300 hover:bg-gray-50 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5 transition-all duration-300;
  }
  
  .btn-ghost {
    @apply text-gray-700 px-6 py-3 rounded-lg font-medium hover:bg-gray-100 transition-all duration-200;
  }

  /* Section Styles */
  .section-padding {
    @apply px-6 md:px-12 lg:px-20 xl:px-32;
  }
  
  .section-spacing {
    @apply py-20 md:py-28 lg:py-32;
  }

  /* Gradient Backgrounds */
  .bg-gradient-radial {
    background: radial-gradient(ellipse at center, 
      rgba(99, 102, 241, 0.15) 0%, 
      rgba(168, 85, 247, 0.1) 35%, 
      rgba(255, 255, 255, 0) 70%);
  }
  
  .bg-gradient-mesh {
    background-image: 
      radial-gradient(at 20% 80%, rgba(99, 102, 241, 0.3) 0px, transparent 50%),
      radial-gradient(at 80% 20%, rgba(168, 85, 247, 0.3) 0px, transparent 50%),
      radial-gradient(at 40% 40%, rgba(34, 211, 238, 0.2) 0px, transparent 50%);
  }

  /* Modern Glow Effects */
  .glow-primary {
    @apply shadow-2xl shadow-indigo-500/25;
  }
  
  .glow-secondary {
    @apply shadow-2xl shadow-purple-500/25;
  }
  
  .glow-accent {
    @apply shadow-2xl shadow-cyan-500/25;
  }

  /* Text Styles */
  .heading-xl {
    @apply text-5xl md:text-6xl lg:text-7xl font-bold leading-tight;
  }
  
  .heading-lg {
    @apply text-4xl md:text-5xl lg:text-6xl font-bold leading-tight;
  }
  
  .heading-md {
    @apply text-3xl md:text-4xl font-bold leading-snug;
  }
  
  .heading-sm {
    @apply text-2xl md:text-3xl font-semibold leading-snug;
  }
  
  .body-lg {
    @apply text-lg md:text-xl text-gray-600 leading-relaxed;
  }

  /* Badge Styles */
  .badge {
    @apply inline-flex items-center px-4 py-2 rounded-full text-sm font-medium;
  }
  
  .badge-primary {
    @apply bg-indigo-100 text-indigo-800 border border-indigo-200;
  }
  
  .badge-secondary {
    @apply bg-purple-100 text-purple-800 border border-purple-200;
  }

  /* Floating Elements */
  .floating {
    animation: floating 6s ease-in-out infinite;
  }
  
  @keyframes floating {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-20px); }
  }

  /* Pulse Animation */
  .pulse-slow {
    animation: pulse 4s cubic-bezier(0.4, 0, 0.6, 1) infinite;
  }
  
  @keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: .5; }
  }

  /* Gradient Border */
  .gradient-border {
    @apply relative;
    background: linear-gradient(white, white) padding-box,
                linear-gradient(to right, rgb(99, 102, 241), rgb(168, 85, 247)) border-box;
    border: 2px solid transparent;
  }

  /* Responsive Container */
  .container-custom {
    @apply max-w-7xl mx-auto px-6 md:px-12 lg:px-20;
  }
}

/* Smooth Scrolling */
html {
  scroll-behavior: smooth;
}

/* Selection Color */
::selection {
  background-color: rgb(99, 102, 241, 0.3);
  color: rgb(55, 65, 81);
}

/* Custom Scrollbar */
::-webkit-scrollbar {
  width: 12px;
}

::-webkit-scrollbar-track {
  background: rgb(var(--gray-100));
}

::-webkit-scrollbar-thumb {
  background: rgb(var(--gray-400));
  border-radius: 6px;
}

::-webkit-scrollbar-thumb:hover {
  background: rgb(var(--gray-500));
}

/* Loading Animation */
@keyframes shimmer {
  0% {
    background-position: -1000px 0;
  }
  100% {
    background-position: 1000px 0;
  }
}

.loading-shimmer {
  animation: shimmer 2s infinite;
  background: linear-gradient(to right, transparent 0%, rgba(255, 255, 255, 0.3) 50%, transparent 100%);
  background-size: 1000px 100%;
}