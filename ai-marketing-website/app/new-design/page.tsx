'use client'

export default function NewDesignTest() {
  return (
    <div 
      style={{
        background: 'linear-gradient(135deg, #f9fafb 0%, #f3f4f6 100%)',
        minHeight: '100vh',
        padding: '2rem',
        fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif'
      }}
    >
      {/* 狀態指示器 */}
      <div 
        style={{
          display: 'inline-flex',
          alignItems: 'center',
          gap: '0.5rem',
          background: 'rgba(34, 197, 94, 0.1)',
          color: '#059669',
          padding: '0.5rem 1rem',
          borderRadius: '2rem',
          fontSize: '0.875rem',
          fontWeight: '500',
          marginBottom: '2rem'
        }}
      >
        <div 
          style={{
            width: '8px',
            height: '8px',
            background: '#059669',
            borderRadius: '50%',
            animation: 'pulse 2s infinite'
          }}
        />
        Next.js 新設計測試頁面
      </div>

      {/* 主標題 */}
      <div style={{ textAlign: 'center', marginBottom: '3rem' }}>
        <div 
          style={{
            display: 'inline-flex',
            alignItems: 'center',
            gap: '0.75rem',
            marginBottom: '2rem'
          }}
        >
          <div 
            style={{
              width: '48px',
              height: '48px',
              background: 'linear-gradient(135deg, #6366f1 0%, #a855f7 100%)',
              borderRadius: '12px',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              color: 'white',
              fontSize: '24px'
            }}
          >
            ✨
          </div>
          <div 
            style={{
              fontSize: '1.5rem',
              fontWeight: 'bold',
              background: 'linear-gradient(135deg, #6366f1 0%, #a855f7 50%, #ec4899 100%)',
              WebkitBackgroundClip: 'text',
              WebkitTextFillColor: 'transparent',
              backgroundClip: 'text'
            }}
          >
            AI Marketing Pro
          </div>
        </div>

        <h1 
          style={{
            fontSize: '4rem',
            fontWeight: 'bold',
            marginBottom: '2rem',
            lineHeight: '1.1',
            color: '#1f2937'
          }}
        >
          用{' '}
          <span 
            style={{
              background: 'linear-gradient(135deg, #6366f1 0%, #a855f7 50%, #ec4899 100%)',
              WebkitBackgroundClip: 'text',
              WebkitTextFillColor: 'transparent',
              backgroundClip: 'text',
              display: 'inline-block'
            }}
          >
            人工智能
          </span>
          <br />
          重新定義營銷
        </h1>

        <p 
          style={{
            fontSize: '1.25rem',
            color: '#6b7280',
            marginBottom: '3rem',
            maxWidth: '600px',
            marginLeft: 'auto',
            marginRight: 'auto',
            lineHeight: '1.6'
          }}
        >
          透過尖端的 AI 技術和數據分析，我們幫助企業實現營銷自動化、
          精準定位目標客戶，並將營銷投資回報率提升至新高度。
        </p>

        <button
          style={{
            display: 'inline-flex',
            alignItems: 'center',
            gap: '0.5rem',
            padding: '1rem 2rem',
            borderRadius: '0.75rem',
            fontWeight: '600',
            textDecoration: 'none',
            background: 'linear-gradient(135deg, #6366f1 0%, #a855f7 100%)',
            color: 'white',
            border: 'none',
            cursor: 'pointer',
            fontSize: '1rem',
            transition: 'all 0.3s ease'
          }}
          onMouseEnter={(e) => {
            e.currentTarget.style.transform = 'translateY(-2px)'
            e.currentTarget.style.boxShadow = '0 10px 25px rgba(99, 102, 241, 0.3)'
          }}
          onMouseLeave={(e) => {
            e.currentTarget.style.transform = 'translateY(0)'
            e.currentTarget.style.boxShadow = 'none'
          }}
        >
          開始免費諮詢 →
        </button>
      </div>

      {/* 成功卡片 */}
      <div 
        style={{
          background: 'rgba(255, 255, 255, 0.8)',
          backdropFilter: 'blur(10px)',
          borderRadius: '1rem',
          padding: '2rem',
          margin: '2rem auto',
          border: '1px solid rgba(255, 255, 255, 0.2)',
          boxShadow: '0 10px 25px rgba(0, 0, 0, 0.1)',
          maxWidth: '800px',
          textAlign: 'center'
        }}
      >
        <h2 
          style={{
            marginBottom: '1rem',
            color: '#1f2937',
            fontSize: '1.5rem',
            fontWeight: 'bold'
          }}
        >
          🎉 恭喜！Next.js 新設計成功顯示
        </h2>
        <p style={{ color: '#6b7280', lineHeight: '1.6' }}>
          如果您能看到這個頁面的漸變背景、彩色文字和現代化設計，
          說明 Next.js 中的新視覺設計已經完全正常工作了！
        </p>
      </div>

      {/* 功能卡片 */}
      <div 
        style={{
          display: 'grid',
          gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))',
          gap: '2rem',
          margin: '3rem 0',
          maxWidth: '1200px',
          marginLeft: 'auto',
          marginRight: 'auto'
        }}
      >
        {[
          { icon: '🧠', title: 'AI 智能分析', desc: '機器學習驅動的營銷洞察' },
          { icon: '🎯', title: '精準定位', desc: 'AI 驅動的客戶細分' },
          { icon: '⚡', title: '自動化', desc: '智能營銷流程優化' }
        ].map((feature, index) => (
          <div
            key={index}
            style={{
              background: 'rgba(255, 255, 255, 0.9)',
              borderRadius: '1rem',
              padding: '2rem',
              textAlign: 'center',
              border: '1px solid rgba(99, 102, 241, 0.1)',
              transition: 'transform 0.3s ease'
            }}
            onMouseEnter={(e) => {
              e.currentTarget.style.transform = 'translateY(-5px)'
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.transform = 'translateY(0)'
            }}
          >
            <div 
              style={{
                width: '64px',
                height: '64px',
                background: 'linear-gradient(135deg, #6366f1 0%, #a855f7 100%)',
                borderRadius: '1rem',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                margin: '0 auto 1rem',
                fontSize: '24px'
              }}
            >
              {feature.icon}
            </div>
            <h3 
              style={{
                marginBottom: '1rem',
                color: '#1f2937',
                fontSize: '1.25rem',
                fontWeight: 'bold'
              }}
            >
              {feature.title}
            </h3>
            <p style={{ color: '#6b7280' }}>
              {feature.desc}
            </p>
          </div>
        ))}
      </div>

      <style jsx>{`
        @keyframes pulse {
          0%, 100% { opacity: 1; }
          50% { opacity: 0.5; }
        }
      `}</style>
    </div>
  )
}
