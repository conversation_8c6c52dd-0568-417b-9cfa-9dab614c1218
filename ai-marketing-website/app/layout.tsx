import type { Metadata } from 'next'
import { Inter, Poppins } from 'next/font/google'
import './globals.css'
import Header from '@/components/layout/Header'
import Footer from '@/components/layout/Footer'

const inter = Inter({ 
  subsets: ['latin'],
  variable: '--font-inter',
})

const poppins = Poppins({ 
  weight: ['400', '500', '600', '700', '800'],
  subsets: ['latin'],
  variable: '--font-poppins',
})

export const metadata: Metadata = {
  title: 'AI Marketing Pro - 智能營銷解決方案',
  description: '領先的AI營銷諮詢公司，為企業提供創新的人工智能營銷策略和解決方案',
  keywords: 'AI營銷, 人工智能, 營銷策略, 數字化轉型, 營銷自動化',
  openGraph: {
    title: 'AI Marketing Pro - 智能營銷解決方案',
    description: '領先的AI營銷諮詢公司',
    images: ['/og-image.png'],
  },
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="zh-HK">
      <body className={`${inter.variable} ${poppins.variable} font-sans antialiased min-h-screen flex flex-col relative overflow-x-hidden`}>
        {/* Gradient Background Effects */}
        <div className="fixed inset-0 -z-10">
          <div className="absolute inset-0 bg-gradient-radial"></div>
          <div className="absolute inset-0 bg-gradient-mesh"></div>
        </div>
        
        <Header />
        <main className="flex-grow relative z-10">
          {children}
        </main>
        <Footer />
      </body>
    </html>
  )
}