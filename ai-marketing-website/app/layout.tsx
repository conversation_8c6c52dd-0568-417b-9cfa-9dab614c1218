import type { Metadata } from 'next'
import { Inter, Poppins } from 'next/font/google'
import './globals.css'
import Header from '@/components/layout/Header'
import Footer from '@/components/layout/Footer'

const inter = Inter({ 
  subsets: ['latin'],
  variable: '--font-inter',
})

const poppins = Poppins({ 
  weight: ['400', '500', '600', '700', '800'],
  subsets: ['latin'],
  variable: '--font-poppins',
})

export const metadata: Metadata = {
  title: 'AI Marketing Pro - 智能營銷解決方案',
  description: '領先的AI營銷諮詢公司，為企業提供創新的人工智能營銷策略和解決方案',
  keywords: 'AI營銷, 人工智能, 營銷策略, 數字化轉型, 營銷自動化',
  openGraph: {
    title: 'AI Marketing Pro - 智能營銷解決方案',
    description: '領先的AI營銷諮詢公司',
    images: ['/og-image.png'],
  },
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="zh-HK">
      <body
        className={`${inter.variable} ${poppins.variable} font-sans antialiased min-h-screen flex flex-col relative overflow-x-hidden`}
        style={{
          background: 'linear-gradient(135deg, rgb(249, 250, 251) 0%, rgb(243, 244, 246) 100%) !important',
          minHeight: '100vh !important'
        }}
      >
        {/* Force styles to override any caching issues */}
        <style dangerouslySetInnerHTML={{
          __html: `
            body {
              background: linear-gradient(135deg, rgb(249, 250, 251) 0%, rgb(243, 244, 246) 100%) !important;
              min-height: 100vh !important;
            }
            .gradient-text {
              background: linear-gradient(135deg, #6366f1 0%, #a855f7 50%, #ec4899 100%) !important;
              -webkit-background-clip: text !important;
              -webkit-text-fill-color: transparent !important;
              background-clip: text !important;
              display: inline-block !important;
            }
          `
        }} />
        {/* Gradient Background Effects */}
        <div className="fixed inset-0 -z-10" style={{
          background: 'radial-gradient(ellipse at center, rgba(99, 102, 241, 0.1) 0%, rgba(168, 85, 247, 0.05) 50%, transparent 100%)'
        }}>
          <div className="absolute inset-0" style={{
            background: 'linear-gradient(135deg, rgba(99, 102, 241, 0.1) 0%, rgba(168, 85, 247, 0.1) 25%, rgba(34, 211, 238, 0.1) 50%, rgba(168, 85, 247, 0.1) 75%, rgba(99, 102, 241, 0.1) 100%)'
          }}></div>
        </div>
        
        <Header />
        <main className="flex-grow relative z-10">
          {children}
        </main>
        <Footer />
      </body>
    </html>
  )
}