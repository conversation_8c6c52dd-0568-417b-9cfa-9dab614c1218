'use client'

import { motion } from 'framer-motion'
import Image from 'next/image'
import Link from 'next/link'
import { 
  Calendar,
  Clock,
  User,
  ArrowRight,
  Sparkles,
  TrendingUp,
  Filter,
  Search
} from 'lucide-react'
import { useState } from 'react'

export default function BlogPage() {
  const [selectedCategory, setSelectedCategory] = useState('all')
  const [searchQuery, setSearchQuery] = useState('')

  const categories = [
    { id: 'all', name: '全部文章' },
    { id: 'ai-marketing', name: 'AI 營銷' },
    { id: 'case-study', name: '案例分析' },
    { id: 'industry-insights', name: '行業洞察' },
    { id: 'tutorials', name: '教程指南' },
    { id: 'news', name: '新聞動態' },
  ]

  const blogPosts = [
    {
      id: 1,
      title: '2024年AI營銷十大趨勢：企業必須知道的創新策略',
      excerpt: '探索2024年最具影響力的AI營銷趨勢，了解如何利用這些創新技術提升您的營銷效果...',
      author: '張明華',
      date: '2024-01-15',
      readTime: '8 分鐘',
      category: 'ai-marketing',
      categoryName: 'AI 營銷',
      image: '/images/placeholder.svg',
      featured: true,
    },
    {
      id: 2,
      title: '成功案例：電商平台如何通過AI實現300%營收增長',
      excerpt: '深入分析一家領先電商平台如何運用AI技術改造營銷策略，實現驚人的業績增長...',
      author: '李美玲',
      date: '2024-01-12',
      readTime: '6 分鐘',
      category: 'case-study',
      categoryName: '案例分析',
      image: '/images/placeholder.svg',
      featured: true,
    },
    {
      id: 3,
      title: 'ChatGPT在營銷中的應用：完整指南',
      excerpt: '了解如何將ChatGPT整合到您的營銷工作流程中，提升內容創作和客戶互動效率...',
      author: '王建國',
      date: '2024-01-10',
      readTime: '10 分鐘',
      category: 'tutorials',
      categoryName: '教程指南',
      image: '/images/placeholder.svg',
      featured: false,
    },
    {
      id: 4,
      title: '數據驅動營銷：如何利用AI進行精準客戶定位',
      excerpt: '掌握使用AI分析客戶數據的技巧，實現更精準的市場定位和個性化營銷...',
      author: '陳雅婷',
      date: '2024-01-08',
      readTime: '7 分鐘',
      category: 'ai-marketing',
      categoryName: 'AI 營銷',
      image: '/images/placeholder.svg',
      featured: false,
    },
    {
      id: 5,
      title: '金融科技行業的AI營銷革命',
      excerpt: '探討金融科技公司如何利用AI技術改變傳統營銷模式，提升獲客效率...',
      author: '林志偉',
      date: '2024-01-05',
      readTime: '9 分鐘',
      category: 'industry-insights',
      categoryName: '行業洞察',
      image: '/images/placeholder.svg',
      featured: false,
    },
    {
      id: 6,
      title: '我們榮獲2024年最佳AI營銷解決方案獎',
      excerpt: 'AI Marketing Pro在年度行業評選中脫穎而出，獲得專家評審團一致認可...',
      author: '市場部',
      date: '2024-01-03',
      readTime: '3 分鐘',
      category: 'news',
      categoryName: '新聞動態',
      image: '/images/placeholder.svg',
      featured: false,
    },
  ]

  const filteredPosts = blogPosts.filter(post => {
    const matchesCategory = selectedCategory === 'all' || post.category === selectedCategory
    const matchesSearch = post.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         post.excerpt.toLowerCase().includes(searchQuery.toLowerCase())
    return matchesCategory && matchesSearch
  })

  const featuredPosts = blogPosts.filter(post => post.featured)

  return (
    <div className="min-h-screen pt-20">
      {/* Hero Section */}
      <section className="section-spacing relative overflow-hidden">
        <div className="absolute inset-0 -z-10">
          <div className="absolute inset-0 bg-gradient-radial"></div>
        </div>

        <div className="container-custom">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="text-center max-w-4xl mx-auto"
          >
            <motion.div
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              className="inline-flex items-center space-x-2 badge badge-secondary mb-6"
            >
              <Sparkles className="w-4 h-4" />
              <span>部落格</span>
            </motion.div>

            <h1 className="heading-xl mb-6">
              探索 AI 營銷的 <span className="gradient-text">最新洞察</span>
            </h1>
            
            <p className="body-lg text-gray-600 mb-8 max-w-3xl mx-auto">
              獲取最新的行業趨勢、實用技巧和成功案例，
              讓您始終走在 AI 營銷的最前沿
            </p>

            {/* Search Bar */}
            <div className="max-w-2xl mx-auto">
              <div className="relative">
                <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
                <input
                  type="text"
                  placeholder="搜索文章..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="w-full pl-12 pr-4 py-4 rounded-xl border border-gray-300 focus:border-indigo-500 focus:ring-2 focus:ring-indigo-200 transition-all"
                />
              </div>
            </div>
          </motion.div>
        </div>
      </section>

      {/* Featured Posts */}
      {!searchQuery && selectedCategory === 'all' && (
        <section className="section-spacing bg-gradient-to-b from-gray-50 to-white">
          <div className="container-custom">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              className="mb-12"
            >
              <h2 className="heading-md mb-4">精選文章</h2>
              <p className="text-gray-600">深度解析 AI 營銷的最新趨勢和最佳實踐</p>
            </motion.div>

            <div className="grid lg:grid-cols-2 gap-8">
              {featuredPosts.map((post, index) => (
                <motion.article
                  key={post.id}
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  viewport={{ once: true }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                  className="group"
                >
                  <Link href={`/blog/${post.id}`}>
                    <div className="card-modern overflow-hidden h-full hover:shadow-2xl transition-all duration-300">
                      <div className="relative h-64 overflow-hidden">
                        <Image
                          src={post.image}
                          alt={post.title}
                          fill
                          className="object-cover group-hover:scale-105 transition-transform duration-300"
                        />
                        <div className="absolute top-4 left-4">
                          <span className="badge badge-primary">
                            <TrendingUp className="w-3 h-3 mr-1" />
                            精選
                          </span>
                        </div>
                      </div>
                      <div className="p-6">
                        <div className="flex items-center space-x-4 text-sm text-gray-600 mb-3">
                          <span className="text-indigo-600 font-medium">{post.categoryName}</span>
                          <span className="flex items-center">
                            <Calendar className="w-4 h-4 mr-1" />
                            {post.date}
                          </span>
                          <span className="flex items-center">
                            <Clock className="w-4 h-4 mr-1" />
                            {post.readTime}
                          </span>
                        </div>
                        <h3 className="text-xl font-bold mb-3 group-hover:text-indigo-600 transition-colors">
                          {post.title}
                        </h3>
                        <p className="text-gray-600 mb-4 line-clamp-3">
                          {post.excerpt}
                        </p>
                        <div className="flex items-center justify-between">
                          <div className="flex items-center space-x-2 text-sm text-gray-600">
                            <User className="w-4 h-4" />
                            <span>{post.author}</span>
                          </div>
                          <span className="text-indigo-600 font-semibold flex items-center group-hover:gap-2 transition-all">
                            閱讀更多
                            <ArrowRight className="w-4 h-4" />
                          </span>
                        </div>
                      </div>
                    </div>
                  </Link>
                </motion.article>
              ))}
            </div>
          </div>
        </section>
      )}

      {/* Category Filter */}
      <section className="py-8 bg-white sticky top-20 z-40 shadow-sm">
        <div className="container-custom">
          <div className="flex items-center justify-between flex-wrap gap-4">
            <div className="flex items-center space-x-2">
              <Filter className="w-5 h-5 text-gray-600" />
              <span className="text-gray-700 font-medium">分類篩選：</span>
            </div>
            <div className="flex flex-wrap gap-2">
              {categories.map((category) => (
                <button
                  key={category.id}
                  onClick={() => setSelectedCategory(category.id)}
                  className={`px-4 py-2 rounded-full text-sm font-medium transition-all ${
                    selectedCategory === category.id
                      ? 'bg-indigo-600 text-white'
                      : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                  }`}
                >
                  {category.name}
                </button>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Blog Grid */}
      <section className="section-spacing">
        <div className="container-custom">
          {filteredPosts.length > 0 ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {filteredPosts.map((post, index) => (
                <motion.article
                  key={post.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                  className="group"
                >
                  <Link href={`/blog/${post.id}`}>
                    <div className="card-modern overflow-hidden h-full hover:shadow-2xl transition-all duration-300">
                      <div className="relative h-48 overflow-hidden">
                        <Image
                          src={post.image}
                          alt={post.title}
                          fill
                          className="object-cover group-hover:scale-105 transition-transform duration-300"
                        />
                        <div className="absolute top-4 left-4">
                          <span className="badge badge-secondary text-xs">
                            {post.categoryName}
                          </span>
                        </div>
                      </div>
                      <div className="p-6">
                        <div className="flex items-center space-x-3 text-sm text-gray-600 mb-3">
                          <span className="flex items-center">
                            <Calendar className="w-4 h-4 mr-1" />
                            {post.date}
                          </span>
                          <span className="flex items-center">
                            <Clock className="w-4 h-4 mr-1" />
                            {post.readTime}
                          </span>
                        </div>
                        <h3 className="text-lg font-bold mb-3 group-hover:text-indigo-600 transition-colors line-clamp-2">
                          {post.title}
                        </h3>
                        <p className="text-gray-600 mb-4 text-sm line-clamp-3">
                          {post.excerpt}
                        </p>
                        <div className="flex items-center justify-between">
                          <div className="flex items-center space-x-2 text-sm text-gray-600">
                            <User className="w-4 h-4" />
                            <span>{post.author}</span>
                          </div>
                          <ArrowRight className="w-4 h-4 text-indigo-600 group-hover:translate-x-1 transition-transform" />
                        </div>
                      </div>
                    </div>
                  </Link>
                </motion.article>
              ))}
            </div>
          ) : (
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              className="text-center py-20"
            >
              <p className="text-gray-600 text-lg">沒有找到相關文章</p>
              <button
                onClick={() => {
                  setSelectedCategory('all')
                  setSearchQuery('')
                }}
                className="mt-4 text-indigo-600 font-semibold hover:text-indigo-700"
              >
                清除篩選條件
              </button>
            </motion.div>
          )}
        </div>
      </section>

      {/* Newsletter Section */}
      <section className="section-spacing bg-gradient-to-b from-gray-50 to-white">
        <div className="container-custom">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            className="card-modern p-12 text-center bg-gradient-to-r from-indigo-50 to-purple-50 border-0"
          >
            <h2 className="heading-md mb-4">
              訂閱我們的 AI 營銷週報
            </h2>
            <p className="body-lg text-gray-600 mb-8 max-w-2xl mx-auto">
              每週獲取最新的 AI 營銷趨勢、實用技巧和獨家洞察
            </p>
            <form className="max-w-md mx-auto flex flex-col sm:flex-row gap-4">
              <input
                type="email"
                placeholder="您的電子郵件"
                className="flex-1 px-6 py-4 rounded-xl border border-gray-300 focus:border-indigo-500 focus:ring-2 focus:ring-indigo-200 transition-all"
              />
              <button type="submit" className="btn-primary whitespace-nowrap">
                立即訂閱
                <ArrowRight className="w-5 h-5 ml-2" />
              </button>
            </form>
            <p className="text-sm text-gray-500 mt-4">
              我們尊重您的隱私，絕不會發送垃圾郵件
            </p>
          </motion.div>
        </div>
      </section>
    </div>
  )
}