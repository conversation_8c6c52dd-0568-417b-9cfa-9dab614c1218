'use client'

import Hero from '@/components/Hero'
import Features from '@/components/Features'
import Services from '@/components/Services'
import Stats from '@/components/Stats'
import CaseStudies from '@/components/CaseStudies'
import Testimonials from '@/components/Testimonials'
import CTASection from '@/components/ui/CTASection'

export default function Home() {
  return (
    <>
      {/* Force styles to override any caching issues */}
      <style dangerouslySetInnerHTML={{
        __html: `
          body {
            background: linear-gradient(135deg, rgb(249, 250, 251) 0%, rgb(243, 244, 246) 100%) !important;
            min-height: 100vh !important;
          }
          .gradient-text {
            background: linear-gradient(135deg, #6366f1 0%, #a855f7 50%, #ec4899 100%) !important;
            -webkit-background-clip: text !important;
            -webkit-text-fill-color: transparent !important;
            background-clip: text !important;
            display: inline-block !important;
          }
        `
      }} />

      <div className="overflow-hidden" style={{
        background: 'linear-gradient(135deg, rgb(249, 250, 251) 0%, rgb(243, 244, 246) 100%) !important',
        minHeight: '100vh !important'
      }}>
        {/* Force CSS classes to be included */}
        <div className="hidden bg-gradient-to-r from-indigo-600 to-purple-600 gradient-text card-modern bg-gradient-radial bg-gradient-mesh"></div>
      <Hero />
      <Features />
      <Services />
      <Stats />
      <CaseStudies />
      <Testimonials />
      <CTASection />
    </div>
  )
}