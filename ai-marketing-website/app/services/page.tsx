'use client'

import { motion } from 'framer-motion'
import Link from 'next/link'
import { 
  Brain, 
  Target, 
  BarChart3, 
  TrendingUp,
  CheckCircle,
  ArrowRight,
  Sparkles,
  MessageSquare,
  Shield,
  Clock,
  Users,
  Award
} from 'lucide-react'

export default function ServicesPage() {
  const services = [
    {
      id: 'strategy',
      icon: Brain,
      title: 'AI 營銷策略諮詢',
      description: '為您的企業量身定制 AI 營銷戰略，從整體規劃到執行落地',
      gradient: 'from-indigo-600 to-purple-600',
      features: [
        {
          title: '市場分析與洞察',
          description: '深度分析市場趨勢、競爭格局和客戶需求',
          icon: BarChart3,
        },
        {
          title: 'AI 技術評估',
          description: '評估並推薦最適合您業務的 AI 技術方案',
          icon: Shield,
        },
        {
          title: '戰略路線圖',
          description: '制定清晰的 AI 營銷實施路線圖和里程碑',
          icon: Target,
        },
      ],
      benefits: [
        '降低決策風險',
        '加速數字化轉型',
        '提升競爭優勢',
        '優化資源配置',
      ],
    },
    {
      id: 'automation',
      icon: Target,
      title: '精準營銷自動化',
      description: '利用 AI 技術實現營銷全流程自動化，提升效率和精準度',
      gradient: 'from-purple-600 to-pink-600',
      features: [
        {
          title: '智能客戶分群',
          description: '基於行為數據的動態客戶細分',
          icon: Users,
        },
        {
          title: '個性化內容引擎',
          description: 'AI 驅動的內容生成和推薦系統',
          icon: MessageSquare,
        },
        {
          title: '多渠道協同',
          description: '跨平台營銷活動的統一管理和優化',
          icon: TrendingUp,
        },
      ],
      benefits: [
        '提升轉化率 300%',
        '節省人力成本 70%',
        '客戶滿意度提升',
        '營銷 ROI 最大化',
      ],
    },
    {
      id: 'analytics',
      icon: BarChart3,
      title: '數據分析與洞察',
      description: '將海量數據轉化為有價值的業務洞察，驅動智能決策',
      gradient: 'from-cyan-600 to-blue-600',
      features: [
        {
          title: '實時數據監控',
          description: '全渠道數據的實時收集和監控',
          icon: Clock,
        },
        {
          title: '預測性分析',
          description: '基於機器學習的趨勢預測和風險預警',
          icon: Brain,
        },
        {
          title: '可視化報告',
          description: '直觀易懂的數據儀表板和洞察報告',
          icon: BarChart3,
        },
      ],
      benefits: [
        '決策速度提升 5 倍',
        '預測準確率 95%',
        '發現隱藏商機',
        '降低營銷風險',
      ],
    },
    {
      id: 'growth',
      icon: TrendingUp,
      title: '增長駭客服務',
      description: '結合 AI 技術和增長駭客方法論，實現業務快速增長',
      gradient: 'from-green-600 to-emerald-600',
      features: [
        {
          title: '增長實驗平台',
          description: '快速測試和驗證增長假設',
          icon: Award,
        },
        {
          title: '病毒式傳播',
          description: '設計和優化病毒式增長機制',
          icon: Users,
        },
        {
          title: '留存優化',
          description: '提升用戶生命週期價值',
          icon: Target,
        },
      ],
      benefits: [
        '用戶增長 10 倍',
        '獲客成本降低 80%',
        '留存率提升 200%',
        '病毒係數優化',
      ],
    },
  ]

  const process = [
    {
      step: '01',
      title: '需求評估',
      description: '深入了解您的業務目標、挑戰和期望',
      icon: MessageSquare,
    },
    {
      step: '02',
      title: '方案設計',
      description: '基於評估結果設計定制化解決方案',
      icon: Brain,
    },
    {
      step: '03',
      title: '試點實施',
      description: '小規模試點驗證方案效果',
      icon: Target,
    },
    {
      step: '04',
      title: '全面部署',
      description: '成功驗證後進行全面實施',
      icon: TrendingUp,
    },
    {
      step: '05',
      title: '持續優化',
      description: '監控效果並持續優化提升',
      icon: BarChart3,
    },
  ]

  return (
    <div className="min-h-screen pt-20">
      {/* Hero Section */}
      <section className="section-spacing relative overflow-hidden">
        <div className="absolute inset-0 -z-10">
          <div className="absolute inset-0 bg-gradient-radial"></div>
        </div>

        <div className="container-custom">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="text-center max-w-4xl mx-auto"
          >
            <motion.div
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              className="inline-flex items-center space-x-2 badge badge-primary mb-6"
            >
              <Sparkles className="w-4 h-4" />
              <span>專業服務</span>
            </motion.div>

            <h1 className="heading-xl mb-6">
              全方位 <span className="gradient-text">AI 營銷服務</span>
            </h1>
            
            <p className="body-lg text-gray-600 mb-8 max-w-3xl mx-auto">
              從策略規劃到技術實施，我們提供端到端的 AI 營銷解決方案，
              幫助您在數字時代實現業務騰飛
            </p>

            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link href="/contact" className="btn-primary">
                立即諮詢
                <ArrowRight className="w-5 h-5 ml-2" />
              </Link>
              <Link href="/case-studies" className="btn-secondary">
                查看成功案例
              </Link>
            </div>
          </motion.div>
        </div>
      </section>

      {/* Services Detail Section */}
      <section className="section-spacing bg-gradient-to-b from-gray-50 to-white">
        <div className="container-custom">
          <div className="space-y-32">
            {services.map((service, index) => (
              <motion.div
                key={service.id}
                id={service.id}
                initial={{ opacity: 0, y: 50 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.8, delay: index * 0.1 }}
                className={`grid lg:grid-cols-2 gap-12 items-center ${
                  index % 2 === 1 ? 'lg:flex-row-reverse' : ''
                }`}
              >
                {/* Content */}
                <div className={`${index % 2 === 1 ? 'lg:order-2' : ''}`}>
                  <div className="mb-8">
                    <div className={`inline-flex p-4 rounded-2xl bg-gradient-to-r ${service.gradient} mb-6`}>
                      <service.icon className="w-8 h-8 text-white" />
                    </div>
                    <h2 className="heading-md mb-4">{service.title}</h2>
                    <p className="body-lg text-gray-600">{service.description}</p>
                  </div>

                  {/* Features */}
                  <div className="space-y-4 mb-8">
                    {service.features.map((feature, idx) => (
                      <motion.div
                        key={idx}
                        initial={{ opacity: 0, x: -20 }}
                        whileInView={{ opacity: 1, x: 0 }}
                        viewport={{ once: true }}
                        transition={{ duration: 0.5, delay: 0.2 + idx * 0.1 }}
                        className="flex items-start space-x-4 p-4 rounded-xl hover:bg-gray-50 transition-colors"
                      >
                        <div className="w-12 h-12 rounded-lg bg-gray-100 flex items-center justify-center flex-shrink-0">
                          <feature.icon className="w-6 h-6 text-gray-700" />
                        </div>
                        <div>
                          <h4 className="font-semibold mb-1">{feature.title}</h4>
                          <p className="text-gray-600 text-sm">{feature.description}</p>
                        </div>
                      </motion.div>
                    ))}
                  </div>

                  <Link href="/contact" className="inline-flex items-center text-indigo-600 font-semibold hover:text-indigo-700 transition-colors group">
                    了解更多詳情
                    <ArrowRight className="w-5 h-5 ml-2 group-hover:translate-x-1 transition-transform" />
                  </Link>
                </div>

                {/* Visual */}
                <div className={`${index % 2 === 1 ? 'lg:order-1' : ''}`}>
                  <div className="relative">
                    <div className={`absolute inset-0 bg-gradient-to-r ${service.gradient} rounded-3xl blur-3xl opacity-20`}></div>
                    <div className="relative card-modern p-8">
                      <h3 className="text-xl font-semibold mb-6">核心優勢</h3>
                      <div className="grid grid-cols-2 gap-4">
                        {service.benefits.map((benefit, idx) => (
                          <div key={idx} className="flex items-center space-x-2">
                            <CheckCircle className="w-5 h-5 text-green-500 flex-shrink-0" />
                            <span className="text-sm text-gray-700">{benefit}</span>
                          </div>
                        ))}
                      </div>
                      
                      <div className="mt-8 pt-8 border-t border-gray-200">
                        <div className="grid grid-cols-3 gap-4 text-center">
                          <div>
                            <div className="text-2xl font-bold gradient-text">98%</div>
                            <div className="text-xs text-gray-600">客戶滿意度</div>
                          </div>
                          <div>
                            <div className="text-2xl font-bold gradient-text">300+</div>
                            <div className="text-xs text-gray-600">成功案例</div>
                          </div>
                          <div>
                            <div className="text-2xl font-bold gradient-text">24/7</div>
                            <div className="text-xs text-gray-600">技術支持</div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Process Section */}
      <section className="section-spacing">
        <div className="container-custom">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            className="text-center mb-16"
          >
            <h2 className="heading-lg mb-6">
              我們的 <span className="gradient-text">服務流程</span>
            </h2>
            <p className="body-lg text-gray-600 max-w-3xl mx-auto">
              透過標準化的服務流程，確保每個項目都能達到最佳效果
            </p>
          </motion.div>

          <div className="relative">
            {/* Connection Line */}
            <div className="absolute top-1/2 left-0 right-0 h-0.5 bg-gray-200 hidden lg:block"></div>
            
            <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-5 gap-8 relative">
              {process.map((item, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  viewport={{ once: true }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                  className="text-center relative"
                >
                  <div className="card-modern p-6 relative z-10 bg-white">
                    <div className={`w-16 h-16 mx-auto mb-4 rounded-2xl bg-gradient-to-r from-indigo-600 to-purple-600 flex items-center justify-center`}>
                      <item.icon className="w-8 h-8 text-white" />
                    </div>
                    <div className="text-3xl font-bold gradient-text mb-2">{item.step}</div>
                    <h3 className="font-semibold mb-2">{item.title}</h3>
                    <p className="text-sm text-gray-600">{item.description}</p>
                  </div>
                </motion.div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="section-spacing bg-gradient-to-b from-gray-50 to-white">
        <div className="container-custom">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            className="card-modern p-12 text-center bg-gradient-to-r from-indigo-50 to-purple-50 border-0"
          >
            <h2 className="heading-md mb-4">
              準備開始您的 AI 營銷之旅？
            </h2>
            <p className="body-lg text-gray-600 mb-8 max-w-2xl mx-auto">
              我們的專家團隊隨時準備為您提供專業諮詢和定制化解決方案
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link href="/contact" className="btn-primary">
                預約免費諮詢
                <ArrowRight className="w-5 h-5 ml-2" />
              </Link>
              <Link href="/case-studies" className="btn-secondary">
                查看客戶案例
              </Link>
            </div>
          </motion.div>
        </div>
      </section>
    </div>
  )
}