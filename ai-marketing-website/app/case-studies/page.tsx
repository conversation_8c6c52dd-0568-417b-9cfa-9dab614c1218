'use client'

import { motion } from 'framer-motion'
import Image from 'next/image'
import Link from 'next/link'
import { 
  TrendingUp, 
  Users, 
  DollarSign, 
  BarChart3,
  ArrowRight,
  Calendar,
  Building2,
  Target,
  Sparkles,
  Filter
} from 'lucide-react'
import { useState } from 'react'

export default function CaseStudiesPage() {
  const [selectedIndustry, setSelectedIndustry] = useState('all')

  const industries = [
    { id: 'all', name: '全部行業' },
    { id: 'ecommerce', name: '電子商務' },
    { id: 'fintech', name: '金融科技' },
    { id: 'retail', name: '零售業' },
    { id: 'healthcare', name: '醫療健康' },
    { id: 'education', name: '教育科技' },
  ]

  const caseStudies = [
    {
      id: 1,
      title: '電商巨頭營收增長 350%',
      client: 'TechMart Asia',
      industry: 'ecommerce',
      industryName: '電子商務',
      duration: '6 個月',
      image: '/images/placeholder.svg',
      description: '透過 AI 個性化推薦系統和智能營銷自動化，幫助亞洲最大電商平台實現營收翻倍增長',
      challenge: '面對激烈的市場競爭和客戶流失率上升的挑戰',
      solution: '實施 AI 驅動的個性化推薦引擎和自動化營銷系統',
      results: [
        { icon: TrendingUp, label: '營收增長', value: '+350%', color: 'text-green-600' },
        { icon: Users, label: '客戶留存', value: '+85%', color: 'text-blue-600' },
        { icon: DollarSign, label: 'ROI', value: '12:1', color: 'text-indigo-600' },
        { icon: BarChart3, label: '轉化率', value: '+120%', color: 'text-purple-600' },
      ],
      tags: ['AI 推薦系統', '營銷自動化', '客戶分析'],
    },
    {
      id: 2,
      title: '金融科技獲客成本降低 60%',
      client: 'FinanceFlow',
      industry: 'fintech',
      industryName: '金融科技',
      duration: '4 個月',
      image: '/images/placeholder.svg',
      description: '運用 AI 精準定位和預測模型，大幅降低獲客成本，提升轉化率',
      challenge: '高昂的獲客成本和低轉化率限制了業務增長',
      solution: '建立 AI 預測模型和智能客戶定位系統',
      results: [
        { icon: DollarSign, label: '獲客成本', value: '-60%', color: 'text-red-600' },
        { icon: BarChart3, label: '轉化率', value: '+120%', color: 'text-green-600' },
        { icon: Users, label: '新用戶', value: '+200K', color: 'text-blue-600' },
        { icon: TrendingUp, label: '收入增長', value: '+180%', color: 'text-purple-600' },
      ],
      tags: ['預測分析', '客戶定位', '成本優化'],
    },
    {
      id: 3,
      title: '時尚品牌社交媒體互動率提升 500%',
      client: 'StyleVogue',
      industry: 'retail',
      industryName: '時尚零售',
      duration: '3 個月',
      image: '/images/placeholder.svg',
      description: '透過 AI 內容生成和社交媒體自動化，顯著提升品牌影響力和客戶互動',
      challenge: '社交媒體參與度低，品牌曝光不足',
      solution: 'AI 內容創作工具和智能社交媒體管理平台',
      results: [
        { icon: Users, label: '互動率', value: '+500%', color: 'text-pink-600' },
        { icon: TrendingUp, label: '品牌曝光', value: '+280%', color: 'text-purple-600' },
        { icon: DollarSign, label: '銷售額', value: '+180%', color: 'text-green-600' },
        { icon: BarChart3, label: '粉絲增長', value: '+350%', color: 'text-blue-600' },
      ],
      tags: ['內容生成', '社交媒體', '品牌營銷'],
    },
    {
      id: 4,
      title: '醫療平台用戶體驗優化',
      client: 'HealthConnect',
      industry: 'healthcare',
      industryName: '醫療健康',
      duration: '5 個月',
      image: '/images/placeholder.svg',
      description: '利用 AI 改善用戶體驗，提高患者滿意度和平台使用率',
      challenge: '複雜的預約流程導致用戶流失',
      solution: 'AI 聊天機器人和智能預約系統',
      results: [
        { icon: Users, label: '用戶滿意度', value: '+92%', color: 'text-green-600' },
        { icon: BarChart3, label: '預約完成率', value: '+75%', color: 'text-blue-600' },
        { icon: TrendingUp, label: '平台活躍度', value: '+140%', color: 'text-purple-600' },
        { icon: DollarSign, label: '營收增長', value: '+95%', color: 'text-indigo-600' },
      ],
      tags: ['用戶體驗', 'AI 客服', '流程優化'],
    },
    {
      id: 5,
      title: '教育平台個性化學習提升成績',
      client: 'EduTech Pro',
      industry: 'education',
      industryName: '教育科技',
      duration: '8 個月',
      image: '/images/placeholder.svg',
      description: '透過 AI 個性化學習路徑，顯著提升學生學習成效',
      challenge: '學生學習進度差異大，統一教學效果不佳',
      solution: 'AI 個性化學習系統和智能輔導助手',
      results: [
        { icon: TrendingUp, label: '學習成績', value: '+45%', color: 'text-green-600' },
        { icon: Users, label: '學生參與度', value: '+180%', color: 'text-blue-600' },
        { icon: BarChart3, label: '完課率', value: '+95%', color: 'text-purple-600' },
        { icon: DollarSign, label: '續費率', value: '+120%', color: 'text-indigo-600' },
      ],
      tags: ['個性化學習', 'AI 教育', '學習分析'],
    },
    {
      id: 6,
      title: '零售連鎖店庫存優化',
      client: 'RetailChain Plus',
      industry: 'retail',
      industryName: '零售業',
      duration: '4 個月',
      image: '/images/placeholder.svg',
      description: '使用 AI 預測需求，優化庫存管理，減少浪費',
      challenge: '庫存積壓和缺貨問題嚴重影響營運',
      solution: 'AI 需求預測和智能庫存管理系統',
      results: [
        { icon: DollarSign, label: '庫存成本', value: '-40%', color: 'text-red-600' },
        { icon: BarChart3, label: '缺貨率', value: '-85%', color: 'text-green-600' },
        { icon: TrendingUp, label: '週轉率', value: '+65%', color: 'text-blue-600' },
        { icon: Users, label: '客戶滿意度', value: '+30%', color: 'text-purple-600' },
      ],
      tags: ['庫存優化', '需求預測', '供應鏈'],
    },
  ]

  const filteredCaseStudies = selectedIndustry === 'all' 
    ? caseStudies 
    : caseStudies.filter(study => study.industry === selectedIndustry)

  return (
    <div className="min-h-screen pt-20">
      {/* Hero Section */}
      <section className="section-spacing relative overflow-hidden">
        <div className="absolute inset-0 -z-10">
          <div className="absolute inset-0 bg-gradient-radial"></div>
        </div>

        <div className="container-custom">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="text-center max-w-4xl mx-auto"
          >
            <motion.div
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              className="inline-flex items-center space-x-2 badge badge-secondary mb-6"
            >
              <Sparkles className="w-4 h-4" />
              <span>成功案例</span>
            </motion.div>

            <h1 className="heading-xl mb-6">
              客戶成功故事與 <span className="gradient-text">卓越成果</span>
            </h1>
            
            <p className="body-lg text-gray-600 mb-8 max-w-3xl mx-auto">
              探索我們如何幫助各行業領先企業實現數字化轉型，
              創造令人矚目的業務成果
            </p>

            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link href="/contact" className="btn-primary">
                開始您的成功故事
                <ArrowRight className="w-5 h-5 ml-2" />
              </Link>
              <Link href="/services" className="btn-secondary">
                了解我們的服務
              </Link>
            </div>
          </motion.div>
        </div>
      </section>

      {/* Filter Section */}
      <section className="py-8 bg-gray-50 sticky top-20 z-40">
        <div className="container-custom">
          <div className="flex items-center justify-between flex-wrap gap-4">
            <div className="flex items-center space-x-2">
              <Filter className="w-5 h-5 text-gray-600" />
              <span className="text-gray-700 font-medium">篩選行業：</span>
            </div>
            <div className="flex flex-wrap gap-2">
              {industries.map((industry) => (
                <button
                  key={industry.id}
                  onClick={() => setSelectedIndustry(industry.id)}
                  className={`px-4 py-2 rounded-full text-sm font-medium transition-all ${
                    selectedIndustry === industry.id
                      ? 'bg-indigo-600 text-white'
                      : 'bg-white text-gray-700 hover:bg-gray-100'
                  }`}
                >
                  {industry.name}
                </button>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Case Studies Grid */}
      <section className="section-spacing">
        <div className="container-custom">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {filteredCaseStudies.map((study, index) => (
              <motion.div
                key={study.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                className="group"
              >
                <div className="card-modern h-full flex flex-col overflow-hidden hover:shadow-2xl transition-all duration-300">
                  {/* Image */}
                  <div className="relative h-48 overflow-hidden">
                    <Image
                      src={study.image}
                      alt={study.title}
                      fill
                      className="object-cover group-hover:scale-105 transition-transform duration-300"
                    />
                    <div className="absolute top-4 left-4">
                      <span className="badge badge-primary">
                        {study.industryName}
                      </span>
                    </div>
                  </div>

                  {/* Content */}
                  <div className="p-6 flex-1 flex flex-col">
                    <div className="mb-4">
                      <h3 className="text-xl font-bold mb-2 group-hover:text-indigo-600 transition-colors">
                        {study.title}
                      </h3>
                      <div className="flex items-center space-x-4 text-sm text-gray-600 mb-3">
                        <div className="flex items-center space-x-1">
                          <Building2 className="w-4 h-4" />
                          <span>{study.client}</span>
                        </div>
                        <div className="flex items-center space-x-1">
                          <Calendar className="w-4 h-4" />
                          <span>{study.duration}</span>
                        </div>
                      </div>
                      <p className="text-gray-600">{study.description}</p>
                    </div>

                    {/* Key Results */}
                    <div className="grid grid-cols-2 gap-3 mb-6">
                      {study.results.slice(0, 2).map((result, idx) => (
                        <div key={idx} className="text-center p-3 bg-gray-50 rounded-lg">
                          <div className={`text-2xl font-bold ${result.color}`}>
                            {result.value}
                          </div>
                          <div className="text-xs text-gray-600">{result.label}</div>
                        </div>
                      ))}
                    </div>

                    {/* Tags */}
                    <div className="flex flex-wrap gap-2 mb-6">
                      {study.tags.map((tag, idx) => (
                        <span key={idx} className="text-xs px-2 py-1 bg-gray-100 text-gray-700 rounded-full">
                          {tag}
                        </span>
                      ))}
                    </div>

                    {/* CTA */}
                    <Link
                      href={`/case-studies/${study.id}`}
                      className="mt-auto inline-flex items-center text-indigo-600 font-semibold hover:text-indigo-700 transition-colors group/link"
                    >
                      查看完整案例
                      <ArrowRight className="w-4 h-4 ml-1 group-hover/link:translate-x-1 transition-transform" />
                    </Link>
                  </div>
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Stats Section */}
      <section className="section-spacing bg-gradient-to-b from-gray-50 to-white">
        <div className="container-custom">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            className="text-center mb-12"
          >
            <h2 className="heading-lg mb-6">
              我們的 <span className="gradient-text">整體成就</span>
            </h2>
          </motion.div>

          <div className="grid grid-cols-2 lg:grid-cols-4 gap-8">
            {[
              { icon: Target, value: '500+', label: '成功案例', gradient: 'from-indigo-600 to-purple-600' },
              { icon: Users, value: '300+', label: '企業客戶', gradient: 'from-purple-600 to-pink-600' },
              { icon: TrendingUp, value: '287%', label: '平均 ROI 提升', gradient: 'from-cyan-600 to-blue-600' },
              { icon: DollarSign, value: '$50M+', label: '為客戶創造價值', gradient: 'from-green-600 to-emerald-600' },
            ].map((stat, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, scale: 0.9 }}
                whileInView={{ opacity: 1, scale: 1 }}
                viewport={{ once: true }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                className="text-center"
              >
                <div className={`w-16 h-16 mx-auto mb-4 rounded-2xl bg-gradient-to-r ${stat.gradient} flex items-center justify-center`}>
                  <stat.icon className="w-8 h-8 text-white" />
                </div>
                <div className="text-3xl font-bold gradient-text mb-2">{stat.value}</div>
                <p className="text-gray-600">{stat.label}</p>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="section-spacing">
        <div className="container-custom">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            className="card-modern p-12 text-center bg-gradient-to-r from-indigo-50 to-purple-50 border-0"
          >
            <h2 className="heading-md mb-4">
              準備成為下一個成功案例？
            </h2>
            <p className="body-lg text-gray-600 mb-8 max-w-2xl mx-auto">
              讓我們的 AI 營銷專家為您量身定制解決方案，開啟成功之旅
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link href="/contact" className="btn-primary">
                立即開始
                <ArrowRight className="w-5 h-5 ml-2" />
              </Link>
              <Link href="/services" className="btn-secondary">
                了解服務詳情
              </Link>
            </div>
          </motion.div>
        </div>
      </section>
    </div>
  )
}