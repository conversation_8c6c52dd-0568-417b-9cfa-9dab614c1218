{"version": 3, "sources": ["../../../../../src/shared/lib/router/utils/index.ts"], "sourcesContent": ["export { getSortedRoutes, getSortedRouteObjects } from './sorted-routes'\nexport { isDynamicRoute } from './is-dynamic'\n"], "names": ["getSortedRouteObjects", "getSortedRoutes", "isDynamicRoute"], "mappings": ";;;;;;;;;;;;;;;;IAA0BA,qBAAqB;eAArBA,mCAAqB;;IAAtCC,eAAe;eAAfA,6BAAe;;IACfC,cAAc;eAAdA,yBAAc;;;8BADgC;2BACxB"}