{"version": 3, "sources": ["../../src/lib/resolve-from.ts"], "sourcesContent": ["// source: https://github.com/sindresorhus/resolve-from\nimport path from 'path'\nimport isError from './is-error'\nimport { realpathSync } from './realpath'\n\nconst Module = require('module')\n\nexport const resolveFrom = (\n  fromDirectory: string,\n  moduleId: string,\n  silent?: boolean\n) => {\n  if (typeof fromDirectory !== 'string') {\n    throw new TypeError(\n      `Expected \\`fromDir\\` to be of type \\`string\\`, got \\`${typeof fromDirectory}\\``\n    )\n  }\n\n  if (typeof moduleId !== 'string') {\n    throw new TypeError(\n      `Expected \\`moduleId\\` to be of type \\`string\\`, got \\`${typeof moduleId}\\``\n    )\n  }\n\n  try {\n    fromDirectory = realpathSync(fromDirectory)\n  } catch (error: unknown) {\n    if (isError(error) && error.code === 'ENOENT') {\n      fromDirectory = path.resolve(fromDirectory)\n    } else if (silent) {\n      return\n    } else {\n      throw error\n    }\n  }\n\n  const fromFile = path.join(fromDirectory, 'noop.js')\n\n  const resolveFileName = () =>\n    Module._resolveFilename(moduleId, {\n      id: fromFile,\n      filename: fromFile,\n      paths: Module._nodeModulePaths(fromDirectory),\n    })\n\n  if (silent) {\n    try {\n      return resolveFileName()\n    } catch (error) {\n      return\n    }\n  }\n\n  return resolveFileName()\n}\n"], "names": ["resolveFrom", "<PERSON><PERSON><PERSON>", "require", "fromDirectory", "moduleId", "silent", "TypeError", "realpathSync", "error", "isError", "code", "path", "resolve", "fromFile", "join", "resolveFileName", "_resolveFilename", "id", "filename", "paths", "_nodeModulePaths"], "mappings": "AAAA,uDAAuD;;;;;+BAO1CA;;;eAAAA;;;6DANI;gEACG;0BACS;;;;;;AAE7B,MAAMC,SAASC,QAAQ;AAEhB,MAAMF,cAAc,CACzBG,eACAC,UACAC;IAEA,IAAI,OAAOF,kBAAkB,UAAU;QACrC,MAAM,qBAEL,CAFK,IAAIG,UACR,CAAC,qDAAqD,EAAE,OAAOH,cAAc,EAAE,CAAC,GAD5E,qBAAA;mBAAA;wBAAA;0BAAA;QAEN;IACF;IAEA,IAAI,OAAOC,aAAa,UAAU;QAChC,MAAM,qBAEL,CAFK,IAAIE,UACR,CAAC,sDAAsD,EAAE,OAAOF,SAAS,EAAE,CAAC,GADxE,qBAAA;mBAAA;wBAAA;0BAAA;QAEN;IACF;IAEA,IAAI;QACFD,gBAAgBI,IAAAA,sBAAY,EAACJ;IAC/B,EAAE,OAAOK,OAAgB;QACvB,IAAIC,IAAAA,gBAAO,EAACD,UAAUA,MAAME,IAAI,KAAK,UAAU;YAC7CP,gBAAgBQ,aAAI,CAACC,OAAO,CAACT;QAC/B,OAAO,IAAIE,QAAQ;YACjB;QACF,OAAO;YACL,MAAMG;QACR;IACF;IAEA,MAAMK,WAAWF,aAAI,CAACG,IAAI,CAACX,eAAe;IAE1C,MAAMY,kBAAkB,IACtBd,OAAOe,gBAAgB,CAACZ,UAAU;YAChCa,IAAIJ;YACJK,UAAUL;YACVM,OAAOlB,OAAOmB,gBAAgB,CAACjB;QACjC;IAEF,IAAIE,QAAQ;QACV,IAAI;YACF,OAAOU;QACT,EAAE,OAAOP,OAAO;YACd;QACF;IACF;IAEA,OAAOO;AACT"}