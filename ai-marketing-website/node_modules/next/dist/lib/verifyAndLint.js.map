{"version": 3, "sources": ["../../src/lib/verifyAndLint.ts"], "sourcesContent": ["import { red } from './picocolors'\nimport { Worker } from './worker'\nimport { existsSync } from 'fs'\nimport { join } from 'path'\nimport { ESLINT_DEFAULT_DIRS } from './constants'\nimport type { Telemetry } from '../telemetry/storage'\nimport { eventLintCheckCompleted } from '../telemetry/events'\nimport { CompileError } from './compile-error'\nimport isError from './is-error'\n\nexport async function verifyAndLint(\n  dir: string,\n  cacheLocation: string,\n  configLintDirs: string[] | undefined,\n  enableWorkerThreads: boolean | undefined,\n  telemetry: Telemetry\n): Promise<void> {\n  let lintWorkers:\n    | (Worker & {\n        runLintCheck: typeof import('./eslint/runLintCheck').runLintCheck\n      })\n    | undefined\n\n  try {\n    lintWorkers = new Worker(require.resolve('./eslint/runLintCheck'), {\n      exposedMethods: ['runLintCheck'],\n      numWorkers: 1,\n      enableWorkerThreads,\n      maxRetries: 0,\n    }) as Worker & {\n      runLintCheck: typeof import('./eslint/runLintCheck').runLintCheck\n    }\n\n    const lintDirs = (configLintDirs ?? ESLINT_DEFAULT_DIRS).reduce(\n      (res: string[], d: string) => {\n        const currDir = join(dir, d)\n        if (!existsSync(currDir)) return res\n        res.push(currDir)\n        return res\n      },\n      []\n    )\n\n    const lintResults = await lintWorkers?.runLintCheck(dir, lintDirs, {\n      lintDuringBuild: true,\n      eslintOptions: {\n        cacheLocation,\n      },\n    })\n    const lintOutput =\n      typeof lintResults === 'string' ? lintResults : lintResults?.output\n\n    if (typeof lintResults !== 'string' && lintResults?.eventInfo) {\n      telemetry.record(\n        eventLintCheckCompleted({\n          ...lintResults.eventInfo,\n          buildLint: true,\n        })\n      )\n    }\n\n    if (typeof lintResults !== 'string' && lintResults?.isError && lintOutput) {\n      await telemetry.flush()\n      throw new CompileError(lintOutput)\n    }\n\n    if (lintOutput) {\n      console.log(lintOutput)\n    }\n  } catch (err) {\n    if (isError(err)) {\n      if (err.type === 'CompileError' || err instanceof CompileError) {\n        console.error(red('\\nFailed to compile.'))\n        console.error(err.message)\n        process.exit(1)\n      } else if (err.type === 'FatalError') {\n        console.error(err.message)\n        process.exit(1)\n      }\n    }\n    throw err\n  } finally {\n    try {\n      lintWorkers?.end()\n    } catch {}\n  }\n}\n"], "names": ["verifyAndLint", "dir", "cacheLocation", "configLintDirs", "enableWorkerThreads", "telemetry", "lintWorkers", "Worker", "require", "resolve", "exposedMethods", "numWorkers", "maxRetries", "lintDirs", "ESLINT_DEFAULT_DIRS", "reduce", "res", "d", "currDir", "join", "existsSync", "push", "lintResults", "runLintCheck", "lintDuringBuild", "eslintOptions", "lintOutput", "output", "eventInfo", "record", "eventLintCheckCompleted", "buildLint", "isError", "flush", "CompileError", "console", "log", "err", "type", "error", "red", "message", "process", "exit", "end"], "mappings": ";;;;+BAUsBA;;;eAAAA;;;4BAVF;wBACG;oBACI;sBACN;2BACe;wBAEI;8BACX;gEACT;;;;;;AAEb,eAAeA,cACpBC,GAAW,EACXC,aAAqB,EACrBC,cAAoC,EACpCC,mBAAwC,EACxCC,SAAoB;IAEpB,IAAIC;IAMJ,IAAI;QACFA,cAAc,IAAIC,cAAM,CAACC,QAAQC,OAAO,CAAC,0BAA0B;YACjEC,gBAAgB;gBAAC;aAAe;YAChCC,YAAY;YACZP;YACAQ,YAAY;QACd;QAIA,MAAMC,WAAW,AAACV,CAAAA,kBAAkBW,8BAAmB,AAAD,EAAGC,MAAM,CAC7D,CAACC,KAAeC;YACd,MAAMC,UAAUC,IAAAA,UAAI,EAAClB,KAAKgB;YAC1B,IAAI,CAACG,IAAAA,cAAU,EAACF,UAAU,OAAOF;YACjCA,IAAIK,IAAI,CAACH;YACT,OAAOF;QACT,GACA,EAAE;QAGJ,MAAMM,cAAc,OAAMhB,+BAAAA,YAAaiB,YAAY,CAACtB,KAAKY,UAAU;YACjEW,iBAAiB;YACjBC,eAAe;gBACbvB;YACF;QACF;QACA,MAAMwB,aACJ,OAAOJ,gBAAgB,WAAWA,cAAcA,+BAAAA,YAAaK,MAAM;QAErE,IAAI,OAAOL,gBAAgB,aAAYA,+BAAAA,YAAaM,SAAS,GAAE;YAC7DvB,UAAUwB,MAAM,CACdC,IAAAA,+BAAuB,EAAC;gBACtB,GAAGR,YAAYM,SAAS;gBACxBG,WAAW;YACb;QAEJ;QAEA,IAAI,OAAOT,gBAAgB,aAAYA,+BAAAA,YAAaU,OAAO,KAAIN,YAAY;YACzE,MAAMrB,UAAU4B,KAAK;YACrB,MAAM,qBAA4B,CAA5B,IAAIC,0BAAY,CAACR,aAAjB,qBAAA;uBAAA;4BAAA;8BAAA;YAA2B;QACnC;QAEA,IAAIA,YAAY;YACdS,QAAQC,GAAG,CAACV;QACd;IACF,EAAE,OAAOW,KAAK;QACZ,IAAIL,IAAAA,gBAAO,EAACK,MAAM;YAChB,IAAIA,IAAIC,IAAI,KAAK,kBAAkBD,eAAeH,0BAAY,EAAE;gBAC9DC,QAAQI,KAAK,CAACC,IAAAA,eAAG,EAAC;gBAClBL,QAAQI,KAAK,CAACF,IAAII,OAAO;gBACzBC,QAAQC,IAAI,CAAC;YACf,OAAO,IAAIN,IAAIC,IAAI,KAAK,cAAc;gBACpCH,QAAQI,KAAK,CAACF,IAAII,OAAO;gBACzBC,QAAQC,IAAI,CAAC;YACf;QACF;QACA,MAAMN;IACR,SAAU;QACR,IAAI;YACF/B,+BAAAA,YAAasC,GAAG;QAClB,EAAE,OAAM,CAAC;IACX;AACF"}