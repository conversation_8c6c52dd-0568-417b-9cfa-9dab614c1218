{"version": 3, "sources": ["../../../../src/lib/metadata/resolvers/resolve-icons.ts"], "sourcesContent": ["import type { ResolvedMetadata } from '../types/metadata-interface'\nimport type { Icon, IconDescriptor } from '../types/metadata-types'\nimport type { FieldResolver } from '../types/resolvers'\nimport { resolveAsArrayOrUndefined } from '../generate/utils'\nimport { isStringOrURL } from './resolve-url'\nimport { IconKeys } from '../constants'\n\nexport function resolveIcon(icon: Icon): IconDescriptor {\n  if (isStringOrURL(icon)) return { url: icon }\n  else if (Array.isArray(icon)) return icon\n  return icon\n}\n\nexport const resolveIcons: FieldResolver<'icons'> = (icons) => {\n  if (!icons) {\n    return null\n  }\n\n  const resolved: ResolvedMetadata['icons'] = {\n    icon: [],\n    apple: [],\n  }\n  if (Array.isArray(icons)) {\n    resolved.icon = icons.map(resolveIcon).filter(Boolean)\n  } else if (isStringOrURL(icons)) {\n    resolved.icon = [resolveIcon(icons)]\n  } else {\n    for (const key of IconKeys) {\n      const values = resolveAsArrayOrUndefined(icons[key])\n      if (values) resolved[key] = values.map(resolveIcon)\n    }\n  }\n  return resolved\n}\n"], "names": ["resolveIcon", "resolveIcons", "icon", "isStringOrURL", "url", "Array", "isArray", "icons", "resolved", "apple", "map", "filter", "Boolean", "key", "IconKeys", "values", "resolveAsArrayOrUndefined"], "mappings": ";;;;;;;;;;;;;;;IAOgBA,WAAW;eAAXA;;IAMHC,YAAY;eAAZA;;;uBAV6B;4BACZ;2BACL;AAElB,SAASD,YAAYE,IAAU;IACpC,IAAIC,IAAAA,yBAAa,EAACD,OAAO,OAAO;QAAEE,KAAKF;IAAK;SACvC,IAAIG,MAAMC,OAAO,CAACJ,OAAO,OAAOA;IACrC,OAAOA;AACT;AAEO,MAAMD,eAAuC,CAACM;IACnD,IAAI,CAACA,OAAO;QACV,OAAO;IACT;IAEA,MAAMC,WAAsC;QAC1CN,MAAM,EAAE;QACRO,OAAO,EAAE;IACX;IACA,IAAIJ,MAAMC,OAAO,CAACC,QAAQ;QACxBC,SAASN,IAAI,GAAGK,MAAMG,GAAG,CAACV,aAAaW,MAAM,CAACC;IAChD,OAAO,IAAIT,IAAAA,yBAAa,EAACI,QAAQ;QAC/BC,SAASN,IAAI,GAAG;YAACF,YAAYO;SAAO;IACtC,OAAO;QACL,KAAK,MAAMM,OAAOC,mBAAQ,CAAE;YAC1B,MAAMC,SAASC,IAAAA,gCAAyB,EAACT,KAAK,CAACM,IAAI;YACnD,IAAIE,QAAQP,QAAQ,CAACK,IAAI,GAAGE,OAAOL,GAAG,CAACV;QACzC;IACF;IACA,OAAOQ;AACT"}