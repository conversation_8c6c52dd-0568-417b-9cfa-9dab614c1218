{"version": 3, "sources": ["../../../src/lib/helpers/get-reserved-port.ts"], "sourcesContent": ["/** https://fetch.spec.whatwg.org/#port-blocking */\nexport const KNOWN_RESERVED_PORTS = {\n  1: 'tcpmux',\n  7: 'echo',\n  9: 'discard',\n  11: 'systat',\n  13: 'daytime',\n  15: 'netstat',\n  17: 'qotd',\n  19: 'chargen',\n  20: 'ftp-data',\n  21: 'ftp',\n  22: 'ssh',\n  23: 'telnet',\n  25: 'smtp',\n  37: 'time',\n  42: 'name',\n  43: 'nicname',\n  53: 'domain',\n  69: 'tftp',\n  77: 'rje',\n  79: 'finger',\n  87: 'link',\n  95: 'supdup',\n  101: 'hostname',\n  102: 'iso-tsap',\n  103: 'gppitnp',\n  104: 'acr-nema',\n  109: 'pop2',\n  110: 'pop3',\n  111: 'sunrpc',\n  113: 'auth',\n  115: 'sftp',\n  117: 'uucp-path',\n  119: 'nntp',\n  123: 'ntp',\n  135: 'epmap',\n  137: 'netbios-ns',\n  139: 'netbios-ssn',\n  143: 'imap',\n  161: 'snmp',\n  179: 'bgp',\n  389: 'ldap',\n  427: 'svrloc',\n  465: 'submissions',\n  512: 'exec',\n  513: 'login',\n  514: 'shell',\n  515: 'printer',\n  526: 'tempo',\n  530: 'courier',\n  531: 'chat',\n  532: 'netnews',\n  540: 'uucp',\n  548: 'afp',\n  554: 'rtsp',\n  556: 'remotefs',\n  563: 'nntps',\n  587: 'submission',\n  601: 'syslog-conn',\n  636: 'ldaps',\n  989: 'ftps-data',\n  990: 'ftps',\n  993: 'imaps',\n  995: 'pop3s',\n  1719: 'h323gatestat',\n  1720: 'h323hostcall',\n  1723: 'pptp',\n  2049: 'nfs',\n  3659: 'apple-sasl',\n  4045: 'npp',\n  5060: 'sip',\n  5061: 'sips',\n  6000: 'x11',\n  6566: 'sane-port',\n  6665: 'ircu',\n  6666: 'ircu',\n  6667: 'ircu',\n  6668: 'ircu',\n  6669: 'ircu',\n  6697: 'ircs-u',\n  10080: 'amanda',\n} as const\n\ntype ReservedPort = keyof typeof KNOWN_RESERVED_PORTS\n\nexport function isPortIsReserved(port: number): port is ReservedPort {\n  return port in KNOWN_RESERVED_PORTS\n}\n\nexport function getReservedPortExplanation(port: ReservedPort): string {\n  return (\n    `Bad port: \"${port}\" is reserved for ${KNOWN_RESERVED_PORTS[port]}\\n` +\n    'Read more: https://nextjs.org/docs/messages/reserved-port'\n  )\n}\n"], "names": ["KNOWN_RESERVED_PORTS", "getReservedPortExplanation", "isPortIsReserved", "port"], "mappings": "AAAA,iDAAiD;;;;;;;;;;;;;;;;IACpCA,oBAAoB;eAApBA;;IAyFGC,0BAA0B;eAA1BA;;IAJAC,gBAAgB;eAAhBA;;;AArFT,MAAMF,uBAAuB;IAClC,GAAG;IACH,GAAG;IACH,GAAG;IACH,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,OAAO;AACT;AAIO,SAASE,iBAAiBC,IAAY;IAC3C,OAAOA,QAAQH;AACjB;AAEO,SAASC,2BAA2BE,IAAkB;IAC3D,OACE,CAAC,WAAW,EAAEA,KAAK,kBAAkB,EAAEH,oBAAoB,CAACG,KAAK,CAAC,EAAE,CAAC,GACrE;AAEJ"}