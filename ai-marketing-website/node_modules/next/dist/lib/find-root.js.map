{"version": 3, "sources": ["../../src/lib/find-root.ts"], "sourcesContent": ["import { dirname } from 'path'\nimport findUp from 'next/dist/compiled/find-up'\n\nexport function findRootLockFile(cwd: string) {\n  return findUp.sync(\n    [\n      'pnpm-lock.yaml',\n      'package-lock.json',\n      'yarn.lock',\n      'bun.lock',\n      'bun.lockb',\n    ],\n    {\n      cwd,\n    }\n  )\n}\n\nexport function findRootDir(cwd: string) {\n  const lockFile = findRootLockFile(cwd)\n  return lockFile ? dirname(lockFile) : undefined\n}\n"], "names": ["findRootDir", "findRootLockFile", "cwd", "findUp", "sync", "lockFile", "dirname", "undefined"], "mappings": ";;;;;;;;;;;;;;;IAkBgBA,WAAW;eAAXA;;IAfAC,gBAAgB;eAAhBA;;;sBAHQ;+DACL;;;;;;AAEZ,SAASA,iBAAiBC,GAAW;IAC1C,OAAOC,eAAM,CAACC,IAAI,CAChB;QACE;QACA;QACA;QACA;QACA;KACD,EACD;QACEF;IACF;AAEJ;AAEO,SAASF,YAAYE,GAAW;IACrC,MAAMG,WAAWJ,iBAAiBC;IAClC,OAAOG,WAAWC,IAAAA,aAAO,EAACD,YAAYE;AACxC"}