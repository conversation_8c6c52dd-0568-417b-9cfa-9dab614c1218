{"version": 3, "sources": ["../../src/lib/turbopack-warning.ts"], "sourcesContent": ["import type { NextConfig } from '../server/config-shared'\nimport path from 'path'\nimport loadConfig from '../server/config'\nimport * as Log from '../build/output/log'\nimport {\n  PHASE_DEVELOPMENT_SERVER,\n  PHASE_PRODUCTION_BUILD,\n} from '../shared/lib/constants'\n\nconst unsupportedTurbopackNextConfigOptions = [\n  // is this supported?\n  // 'amp',\n  // 'experimental.amp',\n\n  // Left to be implemented (priority)\n  // 'experimental.clientRouterFilter',\n  // 'experimental.optimizePackageImports',\n  // 'compiler.emotion',\n  // 'compiler.reactRemoveProperties',\n  // 'compiler.relay',\n  // 'compiler.removeConsole',\n  // 'compiler.styledComponents',\n  'experimental.fetchCacheKeyPrefix',\n\n  // Left to be implemented\n  // 'excludeDefaultMomentLocales',\n  // 'experimental.optimizeServerReact',\n  'experimental.clientRouterFilterAllowedRate',\n  // 'experimental.serverMinification',\n  // 'experimental.serverSourceMaps',\n\n  'experimental.allowedRevalidateHeaderKeys',\n  'experimental.extensionAlias',\n  'experimental.fallbackNodePolyfills',\n\n  'experimental.sri.algorithm',\n  'experimental.swcTraceProfiling',\n  'experimental.typedRoutes',\n\n  // Left to be implemented (Might not be needed for Turbopack)\n  'experimental.craCompat',\n  'experimental.disablePostcssPresetEnv',\n  'experimental.esmExternals',\n  // This is used to force swc-loader to run regardless of finding Babel.\n  'experimental.forceSwcTransforms',\n  'experimental.fullySpecified',\n  'experimental.urlImports',\n  'experimental.slowModuleDetection',\n]\n\n// The following will need to be supported by `next build --turbopack`\nconst unsupportedProductionSpecificTurbopackNextConfigOptions: string[] = [\n  // TODO: Support disabling sourcemaps, currently they're always enabled.\n  // 'productionBrowserSourceMaps',\n]\n\n// check for babelrc, swc plugins\nexport async function validateTurboNextConfig({\n  dir,\n  isDev,\n}: {\n  dir: string\n  isDev?: boolean\n}) {\n  const { getPkgManager } =\n    require('../lib/helpers/get-pkg-manager') as typeof import('../lib/helpers/get-pkg-manager')\n  const { getBabelConfigFile } =\n    require('../build/get-babel-config-file') as typeof import('../build/get-babel-config-file')\n  const { defaultConfig } =\n    require('../server/config-shared') as typeof import('../server/config-shared')\n  const { bold, cyan, red, underline } =\n    require('../lib/picocolors') as typeof import('../lib/picocolors')\n  const { interopDefault } =\n    require('../lib/interop-default') as typeof import('../lib/interop-default')\n\n  let unsupportedParts = ''\n  let babelrc = await getBabelConfigFile(dir)\n  if (babelrc) babelrc = path.basename(babelrc)\n\n  let hasWebpackConfig = false\n  let hasTurboConfig = false\n\n  let unsupportedConfig: string[] = []\n  let rawNextConfig: NextConfig = {}\n\n  const phase = isDev ? PHASE_DEVELOPMENT_SERVER : PHASE_PRODUCTION_BUILD\n  try {\n    rawNextConfig = interopDefault(\n      await loadConfig(phase, dir, {\n        rawConfig: true,\n      })\n    ) as NextConfig\n\n    if (typeof rawNextConfig === 'function') {\n      rawNextConfig = (rawNextConfig as any)(phase, {\n        defaultConfig,\n      })\n    }\n\n    const flattenKeys = (obj: any, prefix: string = ''): string[] => {\n      let keys: string[] = []\n\n      for (const key in obj) {\n        if (typeof obj?.[key] === 'undefined') {\n          continue\n        }\n\n        const pre = prefix.length ? `${prefix}.` : ''\n\n        if (\n          typeof obj[key] === 'object' &&\n          !Array.isArray(obj[key]) &&\n          obj[key] !== null\n        ) {\n          keys = keys.concat(flattenKeys(obj[key], pre + key))\n        } else {\n          keys.push(pre + key)\n        }\n      }\n\n      return keys\n    }\n\n    const getDeepValue = (obj: any, keys: string | string[]): any => {\n      if (typeof keys === 'string') {\n        keys = keys.split('.')\n      }\n      if (keys.length === 1) {\n        return obj?.[keys?.[0]]\n      }\n      return getDeepValue(obj?.[keys?.[0]], keys.slice(1))\n    }\n\n    const customKeys = flattenKeys(rawNextConfig)\n\n    let unsupportedKeys = isDev\n      ? unsupportedTurbopackNextConfigOptions\n      : [\n          ...unsupportedTurbopackNextConfigOptions,\n          ...unsupportedProductionSpecificTurbopackNextConfigOptions,\n        ]\n\n    for (const key of customKeys) {\n      if (key.startsWith('webpack') && rawNextConfig.webpack) {\n        hasWebpackConfig = true\n      }\n      if (key.startsWith('turbopack')) {\n        hasTurboConfig = true\n      }\n\n      let isUnsupported =\n        unsupportedKeys.some(\n          (unsupportedKey) =>\n            // Either the key matches (or is a more specific subkey) of\n            // unsupportedKey, or the key is the path to a specific subkey.\n            // | key     | unsupportedKey |\n            // |---------|----------------|\n            // | foo     | foo            |\n            // | foo.bar | foo            |\n            // | foo     | foo.bar        |\n            key.startsWith(unsupportedKey) ||\n            unsupportedKey.startsWith(`${key}.`)\n        ) &&\n        getDeepValue(rawNextConfig, key) !== getDeepValue(defaultConfig, key)\n\n      if (isUnsupported) {\n        unsupportedConfig.push(key)\n      }\n    }\n  } catch (e) {\n    Log.error('Unexpected error occurred while checking config', e)\n  }\n\n  const feedbackMessage = `Learn more about Next.js and Turbopack: ${underline(\n    'https://nextjs.org/docs/architecture/turbopack'\n  )}\\n`\n\n  if (hasWebpackConfig && !hasTurboConfig) {\n    Log.warn(\n      `Webpack is configured while Turbopack is not, which may cause problems.`\n    )\n    Log.warn(\n      `See instructions if you need to configure Turbopack:\\n  https://nextjs.org/docs/app/api-reference/next-config-js/turbo\\n`\n    )\n  }\n\n  if (babelrc) {\n    unsupportedParts += `Babel detected (${cyan(\n      babelrc\n    )})\\n  Babel is not yet supported. To use Turbopack at the moment,\\n  you'll need to remove your usage of Babel.`\n  }\n\n  if (\n    unsupportedConfig.length === 1 &&\n    unsupportedConfig[0] === 'experimental.optimizePackageImports'\n  ) {\n    Log.warn(\n      `'experimental.optimizePackageImports' is not yet supported by Turbopack and will be ignored.`\n    )\n  } else if (unsupportedConfig.length) {\n    unsupportedParts += `\\n\\n- Unsupported Next.js configuration option(s) (${cyan(\n      'next.config.js'\n    )})\\n  To use Turbopack, remove the following configuration options:\\n${unsupportedConfig\n      .map((name) => `    - ${red(name)}\\n`)\n      .join('')}`\n  }\n\n  if (unsupportedParts) {\n    const pkgManager = getPkgManager(dir)\n\n    Log.error(\n      `You are using configuration and/or tools that are not yet\\nsupported by Next.js with Turbopack:\\n${unsupportedParts}\\n\nIf you cannot make the changes above, but still want to try out\\nNext.js with Turbopack, create the Next.js playground app\\nby running the following commands:\n\n  ${bold(\n    cyan(\n      `${\n        pkgManager === 'npm'\n          ? 'npx create-next-app'\n          : `${pkgManager} create next-app`\n      } --example with-turbopack with-turbopack-app`\n    )\n  )}\\n  cd with-turbopack-app\\n  ${pkgManager} run dev\n        `\n    )\n\n    Log.warn(feedbackMessage)\n\n    process.exit(1)\n  }\n\n  return rawNextConfig\n}\n"], "names": ["validateTurboNextConfig", "unsupportedTurbopackNextConfigOptions", "unsupportedProductionSpecificTurbopackNextConfigOptions", "dir", "isDev", "getPkgManager", "require", "getBabelConfigFile", "defaultConfig", "bold", "cyan", "red", "underline", "interopDefault", "unsupportedParts", "babelrc", "path", "basename", "hasWebpackConfig", "hasTurboConfig", "unsupportedConfig", "rawNextConfig", "phase", "PHASE_DEVELOPMENT_SERVER", "PHASE_PRODUCTION_BUILD", "loadConfig", "rawConfig", "flatten<PERSON>eys", "obj", "prefix", "keys", "key", "pre", "length", "Array", "isArray", "concat", "push", "getDeepValue", "split", "slice", "customKeys", "unsupportedKeys", "startsWith", "webpack", "isUnsupported", "some", "unsupported<PERSON>ey", "e", "Log", "error", "feedbackMessage", "warn", "map", "name", "join", "pkgManager", "process", "exit"], "mappings": ";;;;+BAyDsBA;;;eAAAA;;;6DAxDL;+DACM;6DACF;2BAId;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEP,MAAMC,wCAAwC;IAC5C,qBAAqB;IACrB,SAAS;IACT,sBAAsB;IAEtB,oCAAoC;IACpC,qCAAqC;IACrC,yCAAyC;IACzC,sBAAsB;IACtB,oCAAoC;IACpC,oBAAoB;IACpB,4BAA4B;IAC5B,+BAA+B;IAC/B;IAEA,yBAAyB;IACzB,iCAAiC;IACjC,sCAAsC;IACtC;IACA,qCAAqC;IACrC,mCAAmC;IAEnC;IACA;IACA;IAEA;IACA;IACA;IAEA,6DAA6D;IAC7D;IACA;IACA;IACA,uEAAuE;IACvE;IACA;IACA;IACA;CACD;AAED,sEAAsE;AACtE,MAAMC,0DAAoE,EAGzE;AAGM,eAAeF,wBAAwB,EAC5CG,GAAG,EACHC,KAAK,EAIN;IACC,MAAM,EAAEC,aAAa,EAAE,GACrBC,QAAQ;IACV,MAAM,EAAEC,kBAAkB,EAAE,GAC1BD,QAAQ;IACV,MAAM,EAAEE,aAAa,EAAE,GACrBF,QAAQ;IACV,MAAM,EAAEG,IAAI,EAAEC,IAAI,EAAEC,GAAG,EAAEC,SAAS,EAAE,GAClCN,QAAQ;IACV,MAAM,EAAEO,cAAc,EAAE,GACtBP,QAAQ;IAEV,IAAIQ,mBAAmB;IACvB,IAAIC,UAAU,MAAMR,mBAAmBJ;IACvC,IAAIY,SAASA,UAAUC,aAAI,CAACC,QAAQ,CAACF;IAErC,IAAIG,mBAAmB;IACvB,IAAIC,iBAAiB;IAErB,IAAIC,oBAA8B,EAAE;IACpC,IAAIC,gBAA4B,CAAC;IAEjC,MAAMC,QAAQlB,QAAQmB,mCAAwB,GAAGC,iCAAsB;IACvE,IAAI;QACFH,gBAAgBR,eACd,MAAMY,IAAAA,eAAU,EAACH,OAAOnB,KAAK;YAC3BuB,WAAW;QACb;QAGF,IAAI,OAAOL,kBAAkB,YAAY;YACvCA,gBAAgB,AAACA,cAAsBC,OAAO;gBAC5Cd;YACF;QACF;QAEA,MAAMmB,cAAc,CAACC,KAAUC,SAAiB,EAAE;YAChD,IAAIC,OAAiB,EAAE;YAEvB,IAAK,MAAMC,OAAOH,IAAK;gBACrB,IAAI,QAAOA,uBAAAA,GAAK,CAACG,IAAI,MAAK,aAAa;oBACrC;gBACF;gBAEA,MAAMC,MAAMH,OAAOI,MAAM,GAAG,GAAGJ,OAAO,CAAC,CAAC,GAAG;gBAE3C,IACE,OAAOD,GAAG,CAACG,IAAI,KAAK,YACpB,CAACG,MAAMC,OAAO,CAACP,GAAG,CAACG,IAAI,KACvBH,GAAG,CAACG,IAAI,KAAK,MACb;oBACAD,OAAOA,KAAKM,MAAM,CAACT,YAAYC,GAAG,CAACG,IAAI,EAAEC,MAAMD;gBACjD,OAAO;oBACLD,KAAKO,IAAI,CAACL,MAAMD;gBAClB;YACF;YAEA,OAAOD;QACT;QAEA,MAAMQ,eAAe,CAACV,KAAUE;YAC9B,IAAI,OAAOA,SAAS,UAAU;gBAC5BA,OAAOA,KAAKS,KAAK,CAAC;YACpB;YACA,IAAIT,KAAKG,MAAM,KAAK,GAAG;gBACrB,OAAOL,uBAAAA,GAAK,CAACE,wBAAAA,IAAM,CAAC,EAAE,CAAC;YACzB;YACA,OAAOQ,aAAaV,uBAAAA,GAAK,CAACE,wBAAAA,IAAM,CAAC,EAAE,CAAC,EAAEA,KAAKU,KAAK,CAAC;QACnD;QAEA,MAAMC,aAAad,YAAYN;QAE/B,IAAIqB,kBAAkBtC,QAClBH,wCACA;eACKA;eACAC;SACJ;QAEL,KAAK,MAAM6B,OAAOU,WAAY;YAC5B,IAAIV,IAAIY,UAAU,CAAC,cAActB,cAAcuB,OAAO,EAAE;gBACtD1B,mBAAmB;YACrB;YACA,IAAIa,IAAIY,UAAU,CAAC,cAAc;gBAC/BxB,iBAAiB;YACnB;YAEA,IAAI0B,gBACFH,gBAAgBI,IAAI,CAClB,CAACC,iBACC,2DAA2D;gBAC3D,+DAA+D;gBAC/D,+BAA+B;gBAC/B,+BAA+B;gBAC/B,+BAA+B;gBAC/B,+BAA+B;gBAC/B,+BAA+B;gBAC/BhB,IAAIY,UAAU,CAACI,mBACfA,eAAeJ,UAAU,CAAC,GAAGZ,IAAI,CAAC,CAAC,MAEvCO,aAAajB,eAAeU,SAASO,aAAa9B,eAAeuB;YAEnE,IAAIc,eAAe;gBACjBzB,kBAAkBiB,IAAI,CAACN;YACzB;QACF;IACF,EAAE,OAAOiB,GAAG;QACVC,KAAIC,KAAK,CAAC,mDAAmDF;IAC/D;IAEA,MAAMG,kBAAkB,CAAC,wCAAwC,EAAEvC,UACjE,kDACA,EAAE,CAAC;IAEL,IAAIM,oBAAoB,CAACC,gBAAgB;QACvC8B,KAAIG,IAAI,CACN,CAAC,uEAAuE,CAAC;QAE3EH,KAAIG,IAAI,CACN,CAAC,wHAAwH,CAAC;IAE9H;IAEA,IAAIrC,SAAS;QACXD,oBAAoB,CAAC,gBAAgB,EAAEJ,KACrCK,SACA,8GAA8G,CAAC;IACnH;IAEA,IACEK,kBAAkBa,MAAM,KAAK,KAC7Bb,iBAAiB,CAAC,EAAE,KAAK,uCACzB;QACA6B,KAAIG,IAAI,CACN,CAAC,4FAA4F,CAAC;IAElG,OAAO,IAAIhC,kBAAkBa,MAAM,EAAE;QACnCnB,oBAAoB,CAAC,mDAAmD,EAAEJ,KACxE,kBACA,oEAAoE,EAAEU,kBACrEiC,GAAG,CAAC,CAACC,OAAS,CAAC,MAAM,EAAE3C,IAAI2C,MAAM,EAAE,CAAC,EACpCC,IAAI,CAAC,KAAK;IACf;IAEA,IAAIzC,kBAAkB;QACpB,MAAM0C,aAAanD,cAAcF;QAEjC8C,KAAIC,KAAK,CACP,CAAC,iGAAiG,EAAEpC,iBAAiB;;;EAGzH,EAAEL,KACAC,KACE,GACE8C,eAAe,QACX,wBACA,GAAGA,WAAW,gBAAgB,CAAC,CACpC,4CAA4C,CAAC,GAEhD,6BAA6B,EAAEA,WAAW;QACtC,CAAC;QAGLP,KAAIG,IAAI,CAACD;QAETM,QAAQC,IAAI,CAAC;IACf;IAEA,OAAOrC;AACT"}