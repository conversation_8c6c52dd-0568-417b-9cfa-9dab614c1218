{"version": 3, "sources": ["../../src/lib/detect-typo.ts"], "sourcesContent": ["// the minimum number of operations required to convert string a to string b.\nfunction minDistance(a: string, b: string, threshold: number): number {\n  const m = a.length\n  const n = b.length\n\n  if (m < n) {\n    return minDistance(b, a, threshold)\n  }\n\n  if (n === 0) {\n    return m\n  }\n\n  let previousRow = Array.from({ length: n + 1 }, (_, i) => i)\n\n  for (let i = 0; i < m; i++) {\n    const s1 = a[i]\n    let currentRow = [i + 1]\n    for (let j = 0; j < n; j++) {\n      const s2 = b[j]\n      const insertions = previousRow[j + 1] + 1\n      const deletions = currentRow[j] + 1\n      const substitutions = previousRow[j] + Number(s1 !== s2)\n      currentRow.push(Math.min(insertions, deletions, substitutions))\n    }\n    previousRow = currentRow\n  }\n  return previousRow[previousRow.length - 1]\n}\n\nexport function detectTypo(input: string, options: string[], threshold = 2) {\n  const potentialTypos = options\n    .map((o) => ({\n      option: o,\n      distance: minDistance(o, input, threshold),\n    }))\n    .filter(({ distance }) => distance <= threshold && distance > 0)\n    .sort((a, b) => a.distance - b.distance)\n\n  if (potentialTypos.length) {\n    return potentialTypos[0].option\n  }\n  return null\n}\n"], "names": ["detectTypo", "minDistance", "a", "b", "threshold", "m", "length", "n", "previousRow", "Array", "from", "_", "i", "s1", "currentRow", "j", "s2", "insertions", "deletions", "substitutions", "Number", "push", "Math", "min", "input", "options", "potentialTypos", "map", "o", "option", "distance", "filter", "sort"], "mappings": "AAAA,6EAA6E;;;;;+BA8B7DA;;;eAAAA;;;AA7BhB,SAASC,YAAYC,CAAS,EAAEC,CAAS,EAAEC,SAAiB;IAC1D,MAAMC,IAAIH,EAAEI,MAAM;IAClB,MAAMC,IAAIJ,EAAEG,MAAM;IAElB,IAAID,IAAIE,GAAG;QACT,OAAON,YAAYE,GAAGD,GAAGE;IAC3B;IAEA,IAAIG,MAAM,GAAG;QACX,OAAOF;IACT;IAEA,IAAIG,cAAcC,MAAMC,IAAI,CAAC;QAAEJ,QAAQC,IAAI;IAAE,GAAG,CAACI,GAAGC,IAAMA;IAE1D,IAAK,IAAIA,IAAI,GAAGA,IAAIP,GAAGO,IAAK;QAC1B,MAAMC,KAAKX,CAAC,CAACU,EAAE;QACf,IAAIE,aAAa;YAACF,IAAI;SAAE;QACxB,IAAK,IAAIG,IAAI,GAAGA,IAAIR,GAAGQ,IAAK;YAC1B,MAAMC,KAAKb,CAAC,CAACY,EAAE;YACf,MAAME,aAAaT,WAAW,CAACO,IAAI,EAAE,GAAG;YACxC,MAAMG,YAAYJ,UAAU,CAACC,EAAE,GAAG;YAClC,MAAMI,gBAAgBX,WAAW,CAACO,EAAE,GAAGK,OAAOP,OAAOG;YACrDF,WAAWO,IAAI,CAACC,KAAKC,GAAG,CAACN,YAAYC,WAAWC;QAClD;QACAX,cAAcM;IAChB;IACA,OAAON,WAAW,CAACA,YAAYF,MAAM,GAAG,EAAE;AAC5C;AAEO,SAASN,WAAWwB,KAAa,EAAEC,OAAiB,EAAErB,YAAY,CAAC;IACxE,MAAMsB,iBAAiBD,QACpBE,GAAG,CAAC,CAACC,IAAO,CAAA;YACXC,QAAQD;YACRE,UAAU7B,YAAY2B,GAAGJ,OAAOpB;QAClC,CAAA,GACC2B,MAAM,CAAC,CAAC,EAAED,QAAQ,EAAE,GAAKA,YAAY1B,aAAa0B,WAAW,GAC7DE,IAAI,CAAC,CAAC9B,GAAGC,IAAMD,EAAE4B,QAAQ,GAAG3B,EAAE2B,QAAQ;IAEzC,IAAIJ,eAAepB,MAAM,EAAE;QACzB,OAAOoB,cAAc,CAAC,EAAE,CAACG,MAAM;IACjC;IACA,OAAO;AACT"}