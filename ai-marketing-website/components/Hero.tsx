'use client'

import { motion } from 'framer-motion'
import Link from 'next/link'
import { 
  ArrowR<PERSON>, 
  <PERSON><PERSON>les, 
  TrendingUp, 
  Users, 
  Brain,
  Play,
  CheckCircle,
  X
} from 'lucide-react'
import { useState } from 'react'

const Hero = () => {
  const [isVideoModalOpen, setIsVideoModalOpen] = useState(false)

  const features = [
    { icon: Brain, text: 'AI 驅動的智能分析' },
    { icon: TrendingUp, text: '營銷效果提升 300%' },
    { icon: Users, text: '已服務 500+ 企業客戶' },
  ]

  const clients = ['Google', 'Microsoft', 'Amazon', 'Meta', 'Apple']

  return (
    <section className="relative min-h-screen flex items-center pt-20 pb-20 overflow-hidden">
      {/* Animated Background Elements */}
      <div className="absolute inset-0 -z-10">
        <motion.div
          animate={{
            scale: [1, 1.2, 1],
            rotate: [0, 180, 360],
          }}
          transition={{
            duration: 20,
            repeat: Infinity,
            repeatType: "reverse",
          }}
          className="absolute top-1/4 left-1/4 w-96 h-96 bg-gradient-to-r from-indigo-400 to-purple-400 rounded-full blur-3xl opacity-20"
        />
        <motion.div
          animate={{
            scale: [1.2, 1, 1.2],
            rotate: [360, 180, 0],
          }}
          transition={{
            duration: 25,
            repeat: Infinity,
            repeatType: "reverse",
          }}
          className="absolute bottom-1/4 right-1/4 w-96 h-96 bg-gradient-to-r from-purple-400 to-pink-400 rounded-full blur-3xl opacity-20"
        />
      </div>

      <div className="container-custom">
        <div className="grid lg:grid-cols-2 gap-12 items-center">
          {/* Left Content */}
          <motion.div
            initial={{ opacity: 0, x: -50 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8 }}
          >
            {/* Badge */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              className="inline-flex items-center space-x-2 badge badge-primary mb-6"
            >
              <Sparkles className="w-4 h-4" />
              <span>香港領先的 AI 營銷專家</span>
            </motion.div>

            {/* Main Heading */}
            <h1 className="heading-xl mb-6">
              用 <span className="gradient-text">人工智能</span>
              <br />
              重新定義營銷
            </h1>

            {/* Subtitle */}
            <p className="body-lg mb-8 text-gray-600">
              透過尖端的 AI 技術和數據分析，我們幫助企業實現營銷自動化、
              精準定位目標客戶，並將營銷投資回報率提升至新高度。
            </p>

            {/* Features List */}
            <div className="space-y-3 mb-8">
              {features.map((feature, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.6, delay: 0.2 + index * 0.1 }}
                  className="flex items-center space-x-3"
                >
                  <div className="p-2 rounded-lg bg-indigo-100">
                    <feature.icon className="w-5 h-5 text-indigo-600" />
                  </div>
                  <span className="text-gray-700 font-medium">{feature.text}</span>
                </motion.div>
              ))}
            </div>

            {/* CTA Buttons */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.5 }}
              className="flex flex-wrap gap-4 mb-12"
            >
              <Link href="/contact" className="btn-primary">
                開始免費諮詢
                <ArrowRight className="inline-block ml-2 w-5 h-5" />
              </Link>
              <button
                onClick={() => setIsVideoModalOpen(true)}
                className="btn-secondary flex items-center"
              >
                <Play className="w-5 h-5 mr-2" />
                觀看演示視頻
              </button>
            </motion.div>

            {/* Trust Indicators */}
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 1, delay: 0.8 }}
            >
              <p className="text-sm text-gray-500 mb-4">受到全球領先企業的信任</p>
              <div className="flex flex-wrap items-center gap-8">
                {clients.map((client, index) => (
                  <div
                    key={index}
                    className="text-gray-400 font-semibold text-lg opacity-60 hover:opacity-100 transition-opacity"
                  >
                    {client}
                  </div>
                ))}
              </div>
            </motion.div>
          </motion.div>

          {/* Right Visual */}
          <motion.div
            initial={{ opacity: 0, x: 50 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8 }}
            className="relative lg:h-[600px]"
          >
            {/* 3D Card Stack */}
            <div className="relative w-full h-full">
              {/* Main Card */}
              <motion.div
                whileHover={{ scale: 1.02 }}
                className="absolute top-0 right-0 w-full max-w-md"
              >
                <div className="card-modern p-8 relative overflow-hidden">
                  <div className="absolute top-0 right-0 w-32 h-32 bg-gradient-to-br from-indigo-500 to-purple-500 rounded-full blur-3xl opacity-20"></div>
                  <div className="relative z-10">
                    <div className="w-16 h-16 bg-gradient-to-r from-indigo-600 to-purple-600 rounded-2xl flex items-center justify-center mb-6">
                      <Brain className="w-8 h-8 text-white" />
                    </div>
                    <h3 className="text-2xl font-bold mb-4">AI 營銷大腦</h3>
                    <p className="text-gray-600 mb-6">
                      整合機器學習、自然語言處理和預測分析，為您的營銷策略提供智能決策支持
                    </p>
                    <div className="space-y-3">
                      <div className="flex items-center space-x-2">
                        <CheckCircle className="w-5 h-5 text-green-500" />
                        <span className="text-sm">實時數據分析</span>
                      </div>
                      <div className="flex items-center space-x-2">
                        <CheckCircle className="w-5 h-5 text-green-500" />
                        <span className="text-sm">自動化營銷流程</span>
                      </div>
                      <div className="flex items-center space-x-2">
                        <CheckCircle className="w-5 h-5 text-green-500" />
                        <span className="text-sm">個性化客戶體驗</span>
                      </div>
                    </div>
                  </div>
                </div>
              </motion.div>

              {/* Floating Elements */}
              <motion.div
                animate={{ y: [0, -20, 0] }}
                transition={{ duration: 4, repeat: Infinity }}
                className="absolute top-20 left-0 card-modern p-4 shadow-xl"
              >
                <div className="flex items-center space-x-3">
                  <div className="w-12 h-12 bg-green-100 rounded-xl flex items-center justify-center">
                    <TrendingUp className="w-6 h-6 text-green-600" />
                  </div>
                  <div>
                    <p className="text-sm text-gray-500">轉化率提升</p>
                    <p className="text-xl font-bold text-green-600">+287%</p>
                  </div>
                </div>
              </motion.div>

              <motion.div
                animate={{ y: [0, 20, 0] }}
                transition={{ duration: 5, repeat: Infinity }}
                className="absolute bottom-20 left-10 card-modern p-4 shadow-xl"
              >
                <div className="flex items-center space-x-3">
                  <div className="w-12 h-12 bg-purple-100 rounded-xl flex items-center justify-center">
                    <Users className="w-6 h-6 text-purple-600" />
                  </div>
                  <div>
                    <p className="text-sm text-gray-500">活躍用戶</p>
                    <p className="text-xl font-bold text-purple-600">2.5M+</p>
                  </div>
                </div>
              </motion.div>

              {/* Decorative Circles */}
              <motion.div
                animate={{ rotate: 360 }}
                transition={{ duration: 20, repeat: Infinity, ease: "linear" }}
                className="absolute -bottom-10 -right-10 w-40 h-40 border-4 border-indigo-200 rounded-full"
              />
              <motion.div
                animate={{ rotate: -360 }}
                transition={{ duration: 15, repeat: Infinity, ease: "linear" }}
                className="absolute -top-5 -left-5 w-32 h-32 border-4 border-purple-200 rounded-full"
              />
            </div>
          </motion.div>
        </div>
      </div>

      {/* Video Modal */}
      {isVideoModalOpen && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          className="fixed inset-0 z-50 flex items-center justify-center bg-black/80 p-4"
          onClick={() => setIsVideoModalOpen(false)}
        >
          <motion.div
            initial={{ scale: 0.9 }}
            animate={{ scale: 1 }}
            exit={{ scale: 0.9 }}
            className="relative w-full max-w-4xl aspect-video bg-gray-900 rounded-2xl overflow-hidden"
            onClick={(e) => e.stopPropagation()}
          >
            <button
              onClick={() => setIsVideoModalOpen(false)}
              className="absolute top-4 right-4 p-2 bg-white/10 hover:bg-white/20 rounded-full text-white transition-colors"
            >
              <X size={24} />
            </button>
            <div className="w-full h-full flex items-center justify-center">
              <p className="text-white text-xl">視頻演示內容</p>
            </div>
          </motion.div>
        </motion.div>
      )}
    </section>
  )
}

export default Hero