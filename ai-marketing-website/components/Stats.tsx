'use client'

import { motion, useMotionValue, useTransform, animate } from 'framer-motion'
import { useEffect, useRef, useState } from 'react'
import { 
  TrendingUp, 
  Users, 
  Award, 
  Globe,
  ArrowUpRight
} from 'lucide-react'

const Stats = () => {
  const [isVisible, setIsVisible] = useState(false)
  const sectionRef = useRef<HTMLDivElement>(null)

  const stats = [
    {
      icon: TrendingUp,
      value: 287,
      suffix: '%',
      label: '平均 ROI 提升',
      description: '客戶營銷投資回報率平均增長',
      color: 'from-green-500 to-emerald-500',
    },
    {
      icon: Users,
      value: 500,
      suffix: '+',
      label: '服務企業',
      description: '信任我們的企業客戶數量',
      color: 'from-indigo-500 to-purple-500',
    },
    {
      icon: Award,
      value: 98,
      suffix: '%',
      label: '客戶滿意度',
      description: '客戶對我們服務的滿意評分',
      color: 'from-purple-500 to-pink-500',
    },
    {
      icon: Globe,
      value: 15,
      suffix: '+',
      label: '國家地區',
      description: '我們的服務覆蓋全球範圍',
      color: 'from-cyan-500 to-blue-500',
    },
  ]

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsVisible(true)
        }
      },
      { threshold: 0.1 }
    )

    if (sectionRef.current) {
      observer.observe(sectionRef.current)
    }

    return () => observer.disconnect()
  }, [])

  return (
    <section ref={sectionRef} className="section-spacing relative">
      {/* Background Pattern */}
      <div className="absolute inset-0 -z-10">
        <div className="absolute inset-0 bg-gradient-to-b from-gray-50 to-white"></div>
        <div className="absolute inset-0" style={{
          backgroundImage: `radial-gradient(circle at 1px 1px, rgb(229 231 235) 1px, transparent 1px)`,
          backgroundSize: '40px 40px',
          opacity: 0.3,
        }}></div>
      </div>

      <div className="container-custom">
        {/* Section Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6 }}
          className="text-center mb-16"
        >
          <h2 className="heading-lg mb-6">
            用數據說話的 <span className="gradient-text">卓越成果</span>
          </h2>
          <p className="body-lg text-gray-600 max-w-3xl mx-auto">
            我們的 AI 營銷解決方案已經幫助數百家企業實現了業績突破，
            這些數字見證了我們的專業實力
          </p>
        </motion.div>

        {/* Stats Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {stats.map((stat, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6, delay: index * 0.1 }}
              className="relative group"
            >
              <div className="card-modern p-8 h-full relative overflow-hidden">
                {/* Background Gradient */}
                <div className={`absolute top-0 right-0 w-full h-full bg-gradient-to-br ${stat.color} opacity-5 group-hover:opacity-10 transition-opacity`}></div>
                
                {/* Icon */}
                <div className={`w-14 h-14 bg-gradient-to-r ${stat.color} rounded-2xl flex items-center justify-center mb-6`}>
                  <stat.icon className="w-7 h-7 text-white" />
                </div>

                {/* Animated Number */}
                <div className="mb-4">
                  <CountingNumber
                    value={stat.value}
                    suffix={stat.suffix}
                    isVisible={isVisible}
                  />
                </div>

                {/* Label */}
                <h3 className="text-xl font-bold mb-2 text-gray-900">{stat.label}</h3>
                
                {/* Description */}
                <p className="text-gray-600 text-sm leading-relaxed">{stat.description}</p>

                {/* Hover Effect Arrow */}
                <motion.div
                  initial={{ opacity: 0, x: -10, y: 10 }}
                  whileHover={{ opacity: 1, x: 0, y: 0 }}
                  className="absolute bottom-4 right-4"
                >
                  <ArrowUpRight className="w-5 h-5 text-gray-400" />
                </motion.div>
              </div>
            </motion.div>
          ))}
        </div>

        {/* Bottom Section */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6, delay: 0.4 }}
          className="mt-16 text-center"
        >
          <div className="card-modern p-8 md:p-12 bg-gradient-to-r from-indigo-50 to-purple-50 border-0">
            <h3 className="heading-md mb-4">
              準備加入成功企業的行列嗎？
            </h3>
            <p className="body-lg text-gray-600 mb-8 max-w-2xl mx-auto">
              讓我們的 AI 營銷專家為您量身定制解決方案，開啟業績增長新篇章
            </p>
            <a href="/case-studies" className="btn-primary">
              查看成功案例
              <ArrowUpRight className="inline-block ml-2 w-5 h-5" />
            </a>
          </div>
        </motion.div>
      </div>
    </section>
  )
}

// Counting Number Component
const CountingNumber = ({ 
  value, 
  suffix, 
  isVisible 
}: { 
  value: number
  suffix: string
  isVisible: boolean 
}) => {
  const count = useMotionValue(0)
  const rounded = useTransform(count, Math.round)

  useEffect(() => {
    if (isVisible) {
      const animation = animate(count, value, {
        duration: 2,
        ease: 'easeOut',
      })
      return animation.stop
    }
  }, [isVisible, value, count])

  return (
    <div className="flex items-baseline">
      <motion.span className="text-5xl font-bold gradient-text">
        {rounded}
      </motion.span>
      <span className="text-3xl font-bold gradient-text ml-1">
        {suffix}
      </span>
    </div>
  )
}

export default Stats