'use client'

import { motion } from 'framer-motion'
import Link from 'next/link'
import { 
  Brain, 
  Target, 
  TrendingUp, 
  BarChart3,
  Sparkles,
  ArrowRight,
  CheckCircle2,
  Zap
} from 'lucide-react'

const Services = () => {
  const services = [
    {
      icon: Brain,
      title: 'AI 營銷策略諮詢',
      description: '為您的企業制定量身定制的 AI 營銷策略，從市場分析到執行方案，全程專業指導',
      features: [
        '市場趨勢分析與預測',
        '競爭對手智能分析',
        'AI 技術選型建議',
        '實施路線圖規劃',
      ],
      gradient: 'from-indigo-600 to-purple-600',
      link: '/services#strategy',
    },
    {
      icon: Target,
      title: '精準營銷自動化',
      description: '運用 AI 技術實現營銷流程自動化，精準觸達目標客戶，提升轉化效率',
      features: [
        '客戶行為預測模型',
        '個性化內容生成',
        '多渠道自動化營銷',
        '實時效果優化',
      ],
      gradient: 'from-purple-600 to-pink-600',
      link: '/services#automation',
    },
    {
      icon: BarChart3,
      title: '數據分析與洞察',
      description: '深度挖掘數據價值，提供可視化的業務洞察，助您做出明智決策',
      features: [
        '全渠道數據整合',
        '預測性分析報告',
        '實時數據儀表板',
        'ROI 追踪與優化',
      ],
      gradient: 'from-cyan-600 to-blue-600',
      link: '/services#analytics',
    },
    {
      icon: TrendingUp,
      title: '增長駭客服務',
      description: '結合 AI 技術和增長駭客方法論，快速實現業務指數級增長',
      features: [
        'A/B 測試自動化',
        '病毒式增長策略',
        '用戶留存優化',
        '增長指標監控',
      ],
      gradient: 'from-green-600 to-emerald-600',
      link: '/services#growth',
    },
  ]

  return (
    <section className="section-spacing relative bg-gradient-to-b from-white to-gray-50">
      {/* Background Pattern */}
      <div className="absolute inset-0 -z-10">
        <div className="absolute inset-0 bg-gradient-mesh opacity-20"></div>
      </div>

      <div className="container-custom">
        {/* Section Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6 }}
          className="text-center mb-16"
        >
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            whileInView={{ opacity: 1, scale: 1 }}
            viewport={{ once: true }}
            className="inline-flex items-center space-x-2 badge badge-primary mb-6"
          >
            <Sparkles className="w-4 h-4" />
            <span>專業服務</span>
          </motion.div>

          <h2 className="heading-lg mb-6">
            全方位 <span className="gradient-text">AI 營銷服務</span>
          </h2>
          
          <p className="body-lg text-gray-600 max-w-3xl mx-auto">
            從策略制定到技術實施，我們提供端到端的 AI 營銷解決方案，
            確保您的每一分投入都能獲得最大回報
          </p>
        </motion.div>

        {/* Services Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {services.map((service, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6, delay: index * 0.1 }}
              className="group"
            >
              <div className="card-modern h-full p-8 lg:p-10 relative overflow-hidden hover:shadow-2xl transition-all duration-300">
                {/* Background Gradient */}
                <div className={`absolute -top-20 -right-20 w-60 h-60 bg-gradient-to-br ${service.gradient} rounded-full blur-3xl opacity-10 group-hover:opacity-20 transition-opacity`}></div>
                
                <div className="relative z-10">
                  {/* Icon and Title */}
                  <div className="flex items-start space-x-4 mb-6">
                    <div className={`w-16 h-16 bg-gradient-to-r ${service.gradient} rounded-2xl flex items-center justify-center flex-shrink-0 group-hover:scale-110 transition-transform`}>
                      <service.icon className="w-8 h-8 text-white" />
                    </div>
                    <div>
                      <h3 className="text-2xl font-bold mb-2 text-gray-900">{service.title}</h3>
                      <p className="text-gray-600 leading-relaxed">{service.description}</p>
                    </div>
                  </div>

                  {/* Features List */}
                  <div className="mb-8">
                    <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
                      {service.features.map((feature, idx) => (
                        <div key={idx} className="flex items-center space-x-2">
                          <CheckCircle2 className="w-5 h-5 text-green-500 flex-shrink-0" />
                          <span className="text-sm text-gray-700">{feature}</span>
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* CTA */}
                  <Link 
                    href={service.link}
                    className="inline-flex items-center text-indigo-600 font-semibold hover:text-indigo-700 transition-colors group/link"
                  >
                    了解詳情
                    <ArrowRight className="w-5 h-5 ml-2 group-hover/link:translate-x-1 transition-transform" />
                  </Link>
                </div>

                {/* Decorative Element */}
                <div className="absolute bottom-0 right-0 w-32 h-32 opacity-10">
                  <Zap className="w-full h-full text-gray-400" />
                </div>
              </div>
            </motion.div>
          ))}
        </div>

        {/* Process Section */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6, delay: 0.4 }}
          className="mt-20"
        >
          <div className="card-modern p-8 md:p-12 bg-gradient-to-r from-indigo-50 via-purple-50 to-pink-50 border-0">
            <div className="text-center mb-12">
              <h3 className="heading-md mb-4">我們的服務流程</h3>
              <p className="body-lg text-gray-600 max-w-2xl mx-auto">
                簡單四步，開啟您的 AI 營銷轉型之旅
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
              {[
                { step: '01', title: '需求分析', desc: '深入了解您的業務目標和挑戰' },
                { step: '02', title: '方案設計', desc: '制定個性化的 AI 營銷解決方案' },
                { step: '03', title: '實施部署', desc: '專業團隊協助技術實施和整合' },
                { step: '04', title: '持續優化', desc: '監控效果並持續優化提升' },
              ].map((item, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  viewport={{ once: true }}
                  transition={{ duration: 0.6, delay: 0.6 + index * 0.1 }}
                  className="text-center"
                >
                  <div className="relative mb-4">
                    <div className="text-5xl font-bold gradient-text opacity-20">{item.step}</div>
                    <div className="absolute inset-0 flex items-center justify-center">
                      <div className="w-3 h-3 bg-indigo-600 rounded-full"></div>
                    </div>
                  </div>
                  <h4 className="font-semibold mb-2 text-gray-900">{item.title}</h4>
                  <p className="text-sm text-gray-600">{item.desc}</p>
                </motion.div>
              ))}
            </div>
          </div>
        </motion.div>

        {/* Bottom CTA */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6, delay: 0.8 }}
          className="text-center mt-16"
        >
          <p className="text-gray-600 mb-6">
            不確定哪種服務最適合您？讓我們的專家為您提供免費諮詢
          </p>
          <Link href="/contact" className="btn-primary">
            獲取免費諮詢
            <ArrowRight className="inline-block ml-2 w-5 h-5" />
          </Link>
        </motion.div>
      </div>
    </section>
  )
}

export default Services