'use client'

import { motion } from 'framer-motion'
import Image from 'next/image'
import Link from 'next/link'
import { 
  ArrowRight, 
  TrendingUp, 
  Users, 
  DollarSign,
  BarChart3,
  Quote
} from 'lucide-react'

const CaseStudies = () => {
  const caseStudies = [
    {
      id: 1,
      title: '電商巨頭營收增長 350%',
      client: 'TechMart Asia',
      industry: '電子商務',
      image: '/images/placeholder.svg',
      description: '透過 AI 個性化推薦系統和智能營銷自動化，幫助亞洲最大電商平台實現營收翻倍增長',
      results: [
        { icon: TrendingUp, label: '營收增長', value: '+350%' },
        { icon: Users, label: '客戶留存', value: '+85%' },
        { icon: DollarSign, label: 'ROI', value: '12:1' },
      ],
      testimonial: 'AI Marketing Pro 的解決方案徹底改變了我們的營銷方式，效果超出預期！',
      gradient: 'from-indigo-600 to-purple-600',
    },
    {
      id: 2,
      title: '金融科技獲客成本降低 60%',
      client: 'FinanceFlow',
      industry: '金融科技',
      image: '/images/placeholder.svg',
      description: '運用 AI 精準定位和預測模型，大幅降低獲客成本，提升轉化率',
      results: [
        { icon: DollarSign, label: '獲客成本', value: '-60%' },
        { icon: BarChart3, label: '轉化率', value: '+120%' },
        { icon: Users, label: '新用戶', value: '+200K' },
      ],
      testimonial: '專業的團隊和創新的 AI 技術，讓我們在競爭激烈的市場中脫穎而出。',
      gradient: 'from-purple-600 to-pink-600',
    },
    {
      id: 3,
      title: '時尚品牌社交媒體互動率提升 500%',
      client: 'StyleVogue',
      industry: '時尚零售',
      image: '/images/placeholder.svg',
      description: '透過 AI 內容生成和社交媒體自動化，顯著提升品牌影響力和客戶互動',
      results: [
        { icon: Users, label: '互動率', value: '+500%' },
        { icon: TrendingUp, label: '品牌曝光', value: '+280%' },
        { icon: DollarSign, label: '銷售額', value: '+180%' },
      ],
      testimonial: 'AI 驅動的內容策略讓我們的品牌煥然一新，客戶參與度創歷史新高。',
      gradient: 'from-pink-600 to-orange-600',
    },
  ]

  return (
    <section className="section-spacing relative">
      {/* Background */}
      <div className="absolute inset-0 -z-10 bg-gradient-to-b from-gray-50 to-white"></div>

      <div className="container-custom">
        {/* Section Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6 }}
          className="text-center mb-16"
        >
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            whileInView={{ opacity: 1, scale: 1 }}
            viewport={{ once: true }}
            className="inline-flex items-center space-x-2 badge badge-secondary mb-6"
          >
            <BarChart3 className="w-4 h-4" />
            <span>成功案例</span>
          </motion.div>

          <h2 className="heading-lg mb-6">
            客戶成功故事與 <span className="gradient-text">卓越成果</span>
          </h2>
          
          <p className="body-lg text-gray-600 max-w-3xl mx-auto">
            了解我們如何幫助各行業領先企業實現營銷轉型，
            創造令人矚目的業務成果
          </p>
        </motion.div>

        {/* Case Studies Grid */}
        <div className="space-y-20">
          {caseStudies.map((study, index) => (
            <motion.div
              key={study.id}
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.8, delay: index * 0.2 }}
              className={`grid grid-cols-1 lg:grid-cols-2 gap-12 items-center ${
                index % 2 === 1 ? 'lg:flex-row-reverse' : ''
              }`}
            >
              {/* Content */}
              <div className={`${index % 2 === 1 ? 'lg:order-2' : ''}`}>
                <div className="mb-6">
                  <span className="text-sm font-medium text-gray-500 uppercase tracking-wider">
                    {study.industry}
                  </span>
                  <h3 className="heading-md mt-2 mb-4">{study.title}</h3>
                  <p className="text-lg text-gray-600 mb-6">{study.description}</p>
                </div>

                {/* Results */}
                <div className="grid grid-cols-3 gap-4 mb-8">
                  {study.results.map((result, idx) => (
                    <div key={idx} className="text-center">
                      <div className={`w-12 h-12 mx-auto mb-3 bg-gradient-to-r ${study.gradient} rounded-xl flex items-center justify-center`}>
                        <result.icon className="w-6 h-6 text-white" />
                      </div>
                      <div className="text-2xl font-bold gradient-text">{result.value}</div>
                      <div className="text-sm text-gray-600">{result.label}</div>
                    </div>
                  ))}
                </div>

                {/* Testimonial */}
                <div className="bg-gray-50 rounded-2xl p-6 mb-6">
                  <Quote className="w-8 h-8 text-gray-300 mb-3" />
                  <p className="text-gray-700 italic mb-4">{study.testimonial}</p>
                  <p className="text-sm font-semibold text-gray-900">{study.client}</p>
                </div>

                {/* CTA */}
                <Link 
                  href={`/case-studies/${study.id}`}
                  className="inline-flex items-center text-indigo-600 font-semibold hover:text-indigo-700 transition-colors group"
                >
                  查看完整案例
                  <ArrowRight className="w-5 h-5 ml-2 group-hover:translate-x-1 transition-transform" />
                </Link>
              </div>

              {/* Image */}
              <div className={`${index % 2 === 1 ? 'lg:order-1' : ''}`}>
                <motion.div
                  whileHover={{ scale: 1.02 }}
                  transition={{ type: "spring", stiffness: 300 }}
                  className="relative"
                >
                  <div className={`absolute inset-0 bg-gradient-to-r ${study.gradient} rounded-3xl blur-2xl opacity-20`}></div>
                  <div className="relative card-modern p-8 overflow-hidden">
                    <Image
                      src={study.image}
                      alt={study.title}
                      width={600}
                      height={400}
                      className="w-full h-auto rounded-xl"
                    />
                    
                    {/* Overlay Stats */}
                    <div className="absolute bottom-8 left-8 right-8">
                      <div className="glass-light rounded-2xl p-6">
                        <div className="flex items-center justify-between">
                          <div>
                            <p className="text-sm text-gray-600 mb-1">客戶</p>
                            <p className="text-xl font-bold text-gray-900">{study.client}</p>
                          </div>
                          <div className={`w-16 h-16 bg-gradient-to-r ${study.gradient} rounded-2xl flex items-center justify-center`}>
                            <TrendingUp className="w-8 h-8 text-white" />
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </motion.div>
              </div>
            </motion.div>
          ))}
        </div>

        {/* Bottom CTA */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6, delay: 0.4 }}
          className="text-center mt-20"
        >
          <div className="card-modern p-8 md:p-12 bg-gradient-to-r from-indigo-50 to-purple-50 border-0">
            <h3 className="heading-md mb-4">
              想要成為下一個成功案例？
            </h3>
            <p className="body-lg text-gray-600 mb-8 max-w-2xl mx-auto">
              讓我們的 AI 營銷專家為您打造專屬的成功方案
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link href="/contact" className="btn-primary">
                立即開始
                <ArrowRight className="inline-block ml-2 w-5 h-5" />
              </Link>
              <Link href="/case-studies" className="btn-secondary">
                查看更多案例
              </Link>
            </div>
          </div>
        </motion.div>
      </div>
    </section>
  )
}

export default CaseStudies