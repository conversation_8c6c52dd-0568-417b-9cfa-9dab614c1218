'use client'

import Link from 'next/link'
import { motion } from 'framer-motion'
import { 
  Sparkles, 
  Mail, 
  Phone, 
  MapPin, 
  Facebook, 
  Twitter, 
  Linkedin, 
  Instagram,
  ArrowRight,
  Heart
} from 'lucide-react'

const Footer = () => {
  const currentYear = new Date().getFullYear()

  const footerLinks = {
    company: [
      { name: '關於我們', href: '/about' },
      { name: '團隊介紹', href: '/about#team' },
      { name: '企業文化', href: '/about#culture' },
      { name: '聯繫我們', href: '/contact' },
    ],
    services: [
      { name: 'AI 營銷策略', href: '/services#strategy' },
      { name: '數據分析', href: '/services#analytics' },
      { name: '自動化營銷', href: '/services#automation' },
      { name: '客戶洞察', href: '/services#insights' },
    ],
    resources: [
      { name: '部落格', href: '/blog' },
      { name: '案例研究', href: '/case-studies' },
      { name: '白皮書', href: '/resources' },
      { name: '常見問題', href: '/faq' },
    ],
    legal: [
      { name: '隱私政策', href: '/privacy' },
      { name: '服務條款', href: '/terms' },
      { name: 'Cookie 政策', href: '/cookies' },
    ],
  }

  const socialLinks = [
    { name: 'Facebook', icon: Facebook, href: '#' },
    { name: 'Twitter', icon: Twitter, href: '#' },
    { name: 'LinkedIn', icon: Linkedin, href: '#' },
    { name: 'Instagram', icon: Instagram, href: '#' },
  ]

  return (
    <footer className="relative mt-20">
      {/* Newsletter Section */}
      <section className="relative -mb-px">
        <div className="container-custom">
          <div className="card-modern card-hover rounded-2xl p-8 md:p-12 relative overflow-hidden">
            {/* Background Pattern */}
            <div className="absolute inset-0 bg-gradient-mesh opacity-30"></div>
            
            <div className="relative z-10 grid md:grid-cols-2 gap-8 items-center">
              <div>
                <h3 className="heading-md mb-4">
                  訂閱我們的 <span className="gradient-text">AI 營銷週報</span>
                </h3>
                <p className="text-gray-600 body-lg">
                  獲取最新的 AI 營銷趨勢、案例分析和實用技巧
                </p>
              </div>
              <div>
                <form className="flex flex-col sm:flex-row gap-4">
                  <input
                    type="email"
                    placeholder="您的電子郵件"
                    className="flex-1 px-6 py-4 rounded-xl border border-gray-200 focus:border-indigo-500 focus:outline-none focus:ring-2 focus:ring-indigo-200 transition-all"
                  />
                  <button type="submit" className="btn-primary whitespace-nowrap">
                    訂閱
                    <ArrowRight className="inline-block ml-2 w-5 h-5" />
                  </button>
                </form>
                <p className="text-sm text-gray-500 mt-3">
                  我們尊重您的隱私，絕不會發送垃圾郵件
                </p>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Main Footer */}
      <div className="bg-gradient-to-b from-gray-50 to-white pt-20 pb-8">
        <div className="container-custom">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-6 gap-8 mb-12">
            {/* Brand Section */}
            <div className="lg:col-span-2">
              <Link href="/" className="flex items-center space-x-3 mb-6">
                <div className="relative">
                  <div className="absolute inset-0 bg-gradient-to-r from-indigo-600 to-purple-600 rounded-xl opacity-75 blur-xl"></div>
                  <div className="relative bg-gradient-to-r from-indigo-600 to-purple-600 p-3 rounded-xl">
                    <Sparkles className="w-6 h-6 text-white" />
                  </div>
                </div>
                <div>
                  <h2 className="text-xl font-bold gradient-text">AI Marketing Pro</h2>
                  <p className="text-xs text-gray-600">智能營銷解決方案</p>
                </div>
              </Link>
              <p className="text-gray-600 mb-6 leading-relaxed">
                我們是領先的 AI 營銷諮詢公司，致力於為企業提供創新的人工智能解決方案，助力業務增長和數字化轉型。
              </p>
              {/* Social Links */}
              <div className="flex space-x-4">
                {socialLinks.map((social) => (
                  <motion.a
                    key={social.name}
                    href={social.href}
                    whileHover={{ scale: 1.1 }}
                    whileTap={{ scale: 0.95 }}
                    className="p-3 rounded-xl bg-gray-100 text-gray-600 hover:bg-indigo-100 hover:text-indigo-600 transition-colors"
                    aria-label={social.name}
                  >
                    <social.icon size={20} />
                  </motion.a>
                ))}
              </div>
            </div>

            {/* Links Sections */}
            <div>
              <h3 className="font-semibold text-gray-900 mb-4">公司</h3>
              <ul className="space-y-3">
                {footerLinks.company.map((link) => (
                  <li key={link.name}>
                    <Link href={link.href} className="text-gray-600 hover:text-indigo-600 transition-colors">
                      {link.name}
                    </Link>
                  </li>
                ))}
              </ul>
            </div>

            <div>
              <h3 className="font-semibold text-gray-900 mb-4">服務</h3>
              <ul className="space-y-3">
                {footerLinks.services.map((link) => (
                  <li key={link.name}>
                    <Link href={link.href} className="text-gray-600 hover:text-indigo-600 transition-colors">
                      {link.name}
                    </Link>
                  </li>
                ))}
              </ul>
            </div>

            <div>
              <h3 className="font-semibold text-gray-900 mb-4">資源</h3>
              <ul className="space-y-3">
                {footerLinks.resources.map((link) => (
                  <li key={link.name}>
                    <Link href={link.href} className="text-gray-600 hover:text-indigo-600 transition-colors">
                      {link.name}
                    </Link>
                  </li>
                ))}
              </ul>
            </div>

            <div>
              <h3 className="font-semibold text-gray-900 mb-4">聯繫資訊</h3>
              <ul className="space-y-3 text-gray-600">
                <li className="flex items-start space-x-3">
                  <MapPin size={20} className="text-indigo-600 mt-0.5" />
                  <span>香港中環德輔道中88號</span>
                </li>
                <li className="flex items-center space-x-3">
                  <Phone size={20} className="text-indigo-600" />
                  <a href="tel:+85212345678" className="hover:text-indigo-600 transition-colors">
                    +852 1234 5678
                  </a>
                </li>
                <li className="flex items-center space-x-3">
                  <Mail size={20} className="text-indigo-600" />
                  <a href="mailto:<EMAIL>" className="hover:text-indigo-600 transition-colors">
                    <EMAIL>
                  </a>
                </li>
              </ul>
            </div>
          </div>

          {/* Bottom Bar */}
          <div className="pt-8 border-t border-gray-200">
            <div className="flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0">
              <div className="flex items-center space-x-1 text-gray-600">
                <span>© {currentYear} AI Marketing Pro. 版權所有</span>
                <span className="mx-2">•</span>
                <span className="flex items-center">
                  Made with <Heart size={16} className="mx-1 text-red-500 fill-current" /> in Hong Kong
                </span>
              </div>
              <div className="flex space-x-6 text-sm">
                {footerLinks.legal.map((link) => (
                  <Link 
                    key={link.name} 
                    href={link.href}
                    className="text-gray-600 hover:text-indigo-600 transition-colors"
                  >
                    {link.name}
                  </Link>
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>
    </footer>
  )
}

export default Footer