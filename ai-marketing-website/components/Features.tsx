'use client'

import { motion } from 'framer-motion'
import { 
  Brain, 
  BarChart3, 
  Zap, 
  Users, 
  Target, 
  Shield,
  ArrowRight,
  CheckCircle
} from 'lucide-react'

const Features = () => {
  const features = [
    {
      icon: Brain,
      title: 'AI 驅動智能分析',
      description: '運用最先進的機器學習算法，深度分析客戶行為模式，預測市場趨勢',
      gradient: 'from-indigo-500 to-purple-500',
      benefits: ['實時數據處理', '預測性分析', '智能決策建議'],
    },
    {
      icon: Target,
      title: '精準客戶定位',
      description: '透過 AI 分析海量數據，精確識別並定位您的理想客戶群體',
      gradient: 'from-purple-500 to-pink-500',
      benefits: ['客戶畫像建模', '行為預測', '個性化推薦'],
    },
    {
      icon: Zap,
      title: '營銷自動化',
      description: '自動化您的營銷流程，提升效率，讓團隊專注於策略創新',
      gradient: 'from-cyan-500 to-blue-500',
      benefits: ['工作流程自動化', '智能觸發營銷', 'A/B 測試優化'],
    },
    {
      icon: BarChart3,
      title: '數據驅動決策',
      description: '將複雜數據轉化為清晰洞察，為營銷策略提供有力支撐',
      gradient: 'from-green-500 to-emerald-500',
      benefits: ['實時儀表板', 'ROI 追踪', '競爭對手分析'],
    },
    {
      icon: Users,
      title: '客戶體驗優化',
      description: '打造個性化的客戶旅程，提升客戶滿意度和忠誠度',
      gradient: 'from-orange-500 to-red-500',
      benefits: ['客戶旅程映射', '情感分析', '實時個性化'],
    },
    {
      icon: Shield,
      title: '數據安全保護',
      description: '採用企業級安全標準，確保您的數據和客戶信息安全無虞',
      gradient: 'from-slate-600 to-slate-800',
      benefits: ['端到端加密', 'GDPR 合規', '定期安全審計'],
    },
  ]

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
      },
    },
  }

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        type: 'spring',
        stiffness: 100,
      },
    },
  }

  return (
    <section className="section-spacing relative">
      {/* Background Pattern */}
      <div className="absolute inset-0 -z-10">
        <div className="absolute inset-0 bg-gradient-radial opacity-50"></div>
      </div>

      <div className="container-custom">
        {/* Section Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6 }}
          className="text-center mb-16"
        >
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            whileInView={{ opacity: 1, scale: 1 }}
            viewport={{ once: true }}
            className="inline-flex items-center space-x-2 badge badge-secondary mb-6"
          >
            <Zap className="w-4 h-4" />
            <span>強大功能</span>
          </motion.div>
          
          <h2 className="heading-lg mb-6">
            為現代營銷而生的 <span className="gradient-text">AI 功能套件</span>
          </h2>
          
          <p className="body-lg text-gray-600 max-w-3xl mx-auto">
            我們的 AI 平台整合了最先進的技術，為您提供全方位的營銷解決方案，
            從數據分析到客戶互動，全程智能化管理
          </p>
        </motion.div>

        {/* Features Grid */}
        <motion.div
          variants={containerVariants}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true }}
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8"
        >
          {features.map((feature, index) => (
            <motion.div
              key={index}
              variants={itemVariants}
              whileHover={{ y: -5 }}
              className="group"
            >
              <div className="card-modern card-hover h-full p-8 relative overflow-hidden">
                {/* Gradient Background */}
                <div className={`absolute top-0 right-0 w-32 h-32 bg-gradient-to-br ${feature.gradient} rounded-full blur-3xl opacity-10 group-hover:opacity-20 transition-opacity`}></div>
                
                <div className="relative z-10">
                  {/* Icon */}
                  <div className={`w-14 h-14 bg-gradient-to-r ${feature.gradient} rounded-2xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform`}>
                    <feature.icon className="w-7 h-7 text-white" />
                  </div>

                  {/* Content */}
                  <h3 className="text-xl font-bold mb-3 text-gray-900">{feature.title}</h3>
                  <p className="text-gray-600 mb-6 leading-relaxed">{feature.description}</p>

                  {/* Benefits List */}
                  <ul className="space-y-2 mb-6">
                    {feature.benefits.map((benefit, idx) => (
                      <li key={idx} className="flex items-center space-x-2 text-sm text-gray-600">
                        <CheckCircle className="w-4 h-4 text-green-500 flex-shrink-0" />
                        <span>{benefit}</span>
                      </li>
                    ))}
                  </ul>

                  {/* Learn More Link */}
                  <a 
                    href="#" 
                    className="inline-flex items-center text-indigo-600 font-medium hover:text-indigo-700 transition-colors group/link"
                  >
                    了解更多
                    <ArrowRight className="w-4 h-4 ml-1 group-hover/link:translate-x-1 transition-transform" />
                  </a>
                </div>
              </div>
            </motion.div>
          ))}
        </motion.div>

        {/* Bottom CTA */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6, delay: 0.3 }}
          className="text-center mt-16"
        >
          <p className="text-gray-600 mb-6">
            想要深入了解我們的功能如何幫助您的業務增長？
          </p>
          <a href="/contact" className="btn-primary">
            預約產品演示
            <ArrowRight className="inline-block ml-2 w-5 h-5" />
          </a>
        </motion.div>
      </div>
    </section>
  )
}

export default Features