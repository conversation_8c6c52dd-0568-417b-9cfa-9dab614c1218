'use client'

import { motion } from 'framer-motion'
import Link from 'next/link'
import { 
  ArrowRight, 
  CheckCircle, 
  Sparkles, 
  Zap,
  MessageCircle,
  Clock
} from 'lucide-react'

const CTASection = () => {
  const benefits = [
    { icon: CheckCircle, text: '免費營銷策略評估' },
    { icon: MessageCircle, text: '專業 AI 顧問一對一諮詢' },
    { icon: Zap, text: '定制化解決方案建議' },
    { icon: Clock, text: '30 天無風險試用期' },
  ]

  return (
    <section className="section-spacing relative overflow-hidden">
      {/* Background Effects */}
      <div className="absolute inset-0 -z-10">
        <div className="absolute inset-0 bg-gradient-to-br from-indigo-50 via-purple-50 to-pink-50"></div>
        <div className="absolute top-0 left-0 w-full h-full">
          <motion.div
            animate={{
              scale: [1, 1.2, 1],
              opacity: [0.3, 0.2, 0.3],
            }}
            transition={{
              duration: 8,
              repeat: Infinity,
              repeatType: "reverse",
            }}
            className="absolute top-1/4 left-1/4 w-96 h-96 bg-gradient-to-r from-indigo-400 to-purple-400 rounded-full blur-3xl"
          />
          <motion.div
            animate={{
              scale: [1.2, 1, 1.2],
              opacity: [0.2, 0.3, 0.2],
            }}
            transition={{
              duration: 10,
              repeat: Infinity,
              repeatType: "reverse",
            }}
            className="absolute bottom-1/4 right-1/4 w-96 h-96 bg-gradient-to-r from-purple-400 to-pink-400 rounded-full blur-3xl"
          />
        </div>
      </div>

      <div className="container-custom relative z-10">
        <div className="max-w-5xl mx-auto">
          {/* Content Grid */}
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            {/* Left Content */}
            <motion.div
              initial={{ opacity: 0, x: -30 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8 }}
              viewport={{ once: true }}
            >
              {/* Badge */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6 }}
                viewport={{ once: true }}
                className="inline-flex items-center space-x-2 badge badge-primary mb-6"
              >
                <Sparkles className="w-4 h-4" />
                <span>限時優惠</span>
              </motion.div>

              {/* Main Heading */}
              <h2 className="heading-lg mb-6">
                準備讓 AI 為您的
                <br />
                <span className="gradient-text">營銷業績加速</span>？
              </h2>

              {/* Subtitle */}
              <p className="body-lg text-gray-600 mb-8">
                不要讓競爭對手搶先一步。現在就聯繫我們，
                讓專業的 AI 營銷團隊為您量身打造成功策略。
              </p>

              {/* CTA Buttons */}
              <div className="flex flex-col sm:flex-row gap-4 mb-8">
                <Link
                  href="/contact"
                  className="btn-primary group"
                >
                  <span>立即免費諮詢</span>
                  <ArrowRight className="w-5 h-5 ml-2 group-hover:translate-x-1 transition-transform" />
                </Link>
                <Link
                  href="/services"
                  className="btn-secondary"
                >
                  了解服務詳情
                </Link>
              </div>

              {/* Trust Text */}
              <p className="text-sm text-gray-500">
                已有 300+ 家企業選擇我們的 AI 營銷解決方案
              </p>
            </motion.div>

            {/* Right Content - Benefits Card */}
            <motion.div
              initial={{ opacity: 0, x: 30 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8 }}
              viewport={{ once: true }}
            >
              <div className="card-modern p-8 relative overflow-hidden">
                {/* Decorative Element */}
                <div className="absolute top-0 right-0 w-32 h-32 bg-gradient-to-br from-indigo-500 to-purple-500 rounded-full blur-3xl opacity-10"></div>
                
                <h3 className="text-2xl font-bold mb-6 relative z-10">
                  立即開始，您將獲得：
                </h3>

                {/* Benefits List */}
                <div className="space-y-4 relative z-10">
                  {benefits.map((benefit, index) => (
                    <motion.div
                      key={index}
                      initial={{ opacity: 0, x: -20 }}
                      whileInView={{ opacity: 1, x: 0 }}
                      transition={{ duration: 0.6, delay: 0.2 + index * 0.1 }}
                      viewport={{ once: true }}
                      className="flex items-start space-x-3"
                    >
                      <div className="w-10 h-10 rounded-lg bg-gradient-to-r from-indigo-500 to-purple-500 flex items-center justify-center flex-shrink-0">
                        <benefit.icon className="w-5 h-5 text-white" />
                      </div>
                      <p className="text-gray-700 pt-2">{benefit.text}</p>
                    </motion.div>
                  ))}
                </div>

                {/* Bottom Stats */}
                <div className="mt-8 pt-8 border-t border-gray-200">
                  <div className="grid grid-cols-3 gap-4 text-center">
                    <div>
                      <div className="text-2xl font-bold gradient-text">24h</div>
                      <div className="text-xs text-gray-600">快速響應</div>
                    </div>
                    <div>
                      <div className="text-2xl font-bold gradient-text">100%</div>
                      <div className="text-xs text-gray-600">客製化</div>
                    </div>
                    <div>
                      <div className="text-2xl font-bold gradient-text">0</div>
                      <div className="text-xs text-gray-600">隱藏費用</div>
                    </div>
                  </div>
                </div>
              </div>
            </motion.div>
          </div>

          {/* Bottom Section - Company Logos */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.4 }}
            viewport={{ once: true }}
            className="mt-20 text-center"
          >
            <p className="text-sm text-gray-600 mb-8">受到領先企業的信任</p>
            <div className="flex flex-wrap justify-center items-center gap-8 opacity-60">
              {['TechCorp', 'InnovateCo', 'FutureStart', 'GlobalBrand', 'SmartSolutions'].map((company, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, scale: 0.8 }}
                  whileInView={{ opacity: 1, scale: 1 }}
                  transition={{ duration: 0.5, delay: 0.5 + index * 0.1 }}
                  viewport={{ once: true }}
                  className="text-gray-400 font-semibold text-lg"
                >
                  {company}
                </motion.div>
              ))}
            </div>
          </motion.div>
        </div>
      </div>

      {/* Decorative Elements */}
      <motion.div
        animate={{ rotate: 360 }}
        transition={{ duration: 20, repeat: Infinity, ease: "linear" }}
        className="absolute top-10 right-10 w-16 h-16 border-2 border-indigo-300 rounded-full hidden lg:block"
      >
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-2 h-2 bg-indigo-400 rounded-full"></div>
      </motion.div>

      <motion.div
        animate={{ rotate: -360 }}
        transition={{ duration: 25, repeat: Infinity, ease: "linear" }}
        className="absolute bottom-10 left-10 w-20 h-20 border-2 border-purple-300 rounded-full hidden lg:block"
      >
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-3 h-3 bg-purple-400 rounded-full"></div>
      </motion.div>
    </section>
  )
}

export default CTASection