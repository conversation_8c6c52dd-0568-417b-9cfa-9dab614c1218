'use client'

import { motion } from 'framer-motion'
import { Star, Quote } from 'lucide-react'
import { useState } from 'react'

const Testimonials = () => {
  const [activeIndex, setActiveIndex] = useState(0)

  const testimonials = [
    {
      id: 1,
      name: '張明華',
      role: 'CEO',
      company: 'TechVision Ltd',
      avatar: '張',
      content: 'AI Marketing Pro 的服務徹底改變了我們的營銷策略。在短短 6 個月內，我們的營收增長了 300%，客戶滿意度也達到歷史新高。他們的專業團隊不僅提供技術支持，更是我們值得信賴的戰略合作夥伴。',
      rating: 5,
      gradient: 'from-indigo-500 to-purple-500',
    },
    {
      id: 2,
      name: '李美玲',
      role: '營銷總監',
      company: 'Global Retail Group',
      avatar: '李',
      content: '與 AI Marketing Pro 合作是我們做過最正確的決定。他們的 AI 解決方案幫助我們精準定位目標客戶，營銷效率提升了 250%。最重要的是，他們真正理解我們的業務需求，提供的都是實用的解決方案。',
      rating: 5,
      gradient: 'from-purple-500 to-pink-500',
    },
    {
      id: 3,
      name: '王建國',
      role: '創始人',
      company: 'StartUp Hub',
      avatar: '王',
      content: '作為一家初創公司，我們需要快速增長但預算有限。AI Marketing Pro 為我們量身定制的解決方案不僅成本效益高，效果也超出預期。現在我們的獲客成本降低了 70%，用戶增長率提升了 400%。',
      rating: 5,
      gradient: 'from-cyan-500 to-blue-500',
    },
    {
      id: 4,
      name: '陳雅婷',
      role: 'CMO',
      company: 'Fashion Forward',
      avatar: '陳',
      content: 'AI Marketing Pro 的團隊專業、創新、反應迅速。他們幫助我們建立了完整的 AI 營銷體系，從數據分析到內容創作，全程自動化。我們的社交媒體互動率提升了 600%，品牌影響力顯著增強。',
      rating: 5,
      gradient: 'from-pink-500 to-orange-500',
    },
    {
      id: 5,
      name: '林志偉',
      role: '數據主管',
      company: 'Data Dynamics',
      avatar: '林',
      content: '他們的數據分析能力令人印象深刻。不僅幫我們整合了所有營銷數據，還提供了深入的洞察和預測。現在我們可以提前預知市場趨勢，制定更精準的營銷策略。ROI 提升了 450%！',
      rating: 5,
      gradient: 'from-green-500 to-emerald-500',
    },
  ]

  return (
    <section className="section-spacing relative overflow-hidden">
      {/* Background */}
      <div className="absolute inset-0 -z-10">
        <div className="absolute inset-0 bg-gradient-radial"></div>
      </div>

      <div className="container-custom">
        {/* Section Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6 }}
          className="text-center mb-16"
        >
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            whileInView={{ opacity: 1, scale: 1 }}
            viewport={{ once: true }}
            className="inline-flex items-center space-x-2 badge badge-primary mb-6"
          >
            <Star className="w-4 h-4" />
            <span>客戶評價</span>
          </motion.div>

          <h2 className="heading-lg mb-6">
            聽聽客戶怎麼說 <span className="gradient-text">真實反饋</span>
          </h2>
          
          <p className="body-lg text-gray-600 max-w-3xl mx-auto">
            我們的成功來自客戶的信任與支持，他們的評價是我們不斷進步的動力
          </p>
        </motion.div>

        {/* Main Testimonial Display */}
        <div className="max-w-4xl mx-auto mb-12">
          <motion.div
            key={activeIndex}
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: -20 }}
            transition={{ duration: 0.5 }}
            className="card-modern p-8 md:p-12 relative overflow-hidden"
          >
            {/* Quote Icon */}
            <Quote className="absolute top-8 left-8 w-16 h-16 text-gray-100" />

            {/* Content */}
            <div className="relative z-10">
              {/* Rating */}
              <div className="flex items-center mb-6">
                {[...Array(testimonials[activeIndex].rating)].map((_, i) => (
                  <Star key={i} className="w-5 h-5 fill-current text-yellow-400" />
                ))}
              </div>

              {/* Testimonial Text */}
              <p className="text-xl md:text-2xl text-gray-700 leading-relaxed mb-8">
                "{testimonials[activeIndex].content}"
              </p>

              {/* Author Info */}
              <div className="flex items-center">
                <div className={`w-16 h-16 rounded-full bg-gradient-to-r ${testimonials[activeIndex].gradient} flex items-center justify-center text-white text-2xl font-bold mr-4`}>
                  {testimonials[activeIndex].avatar}
                </div>
                <div>
                  <h4 className="text-lg font-semibold text-gray-900">
                    {testimonials[activeIndex].name}
                  </h4>
                  <p className="text-gray-600">
                    {testimonials[activeIndex].role} • {testimonials[activeIndex].company}
                  </p>
                </div>
              </div>
            </div>
          </motion.div>
        </div>

        {/* Testimonial Selector */}
        <div className="flex flex-wrap justify-center gap-4">
          {testimonials.map((testimonial, index) => (
            <motion.button
              key={testimonial.id}
              onClick={() => setActiveIndex(index)}
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              className={`p-4 rounded-xl transition-all duration-300 ${
                activeIndex === index
                  ? 'card-modern shadow-lg border-2 border-indigo-500'
                  : 'bg-white border-2 border-gray-200 hover:border-gray-300'
              }`}
            >
              <div className="flex items-center space-x-3">
                <div className={`w-10 h-10 rounded-full bg-gradient-to-r ${testimonial.gradient} flex items-center justify-center text-white font-semibold`}>
                  {testimonial.avatar}
                </div>
                <div className="text-left">
                  <p className="font-semibold text-sm text-gray-900">{testimonial.name}</p>
                  <p className="text-xs text-gray-600">{testimonial.company}</p>
                </div>
              </div>
            </motion.button>
          ))}
        </div>

        {/* Stats Section */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6, delay: 0.3 }}
          className="mt-20"
        >
          <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
            {[
              { value: '98%', label: '客戶滿意度' },
              { value: '4.9/5', label: '平均評分' },
              { value: '500+', label: '成功案例' },
              { value: '95%', label: '客戶續約率' },
            ].map((stat, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, scale: 0.9 }}
                whileInView={{ opacity: 1, scale: 1 }}
                viewport={{ once: true }}
                transition={{ duration: 0.5, delay: 0.4 + index * 0.1 }}
                className="text-center"
              >
                <div className="text-3xl md:text-4xl font-bold gradient-text mb-2">
                  {stat.value}
                </div>
                <p className="text-gray-600">{stat.label}</p>
              </motion.div>
            ))}
          </div>
        </motion.div>
      </div>
    </section>
  )
}

export default Testimonials