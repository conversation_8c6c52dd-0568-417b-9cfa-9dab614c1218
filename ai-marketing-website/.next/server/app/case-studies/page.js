/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/case-studies/page";
exports.ids = ["app/case-studies/page"];
exports.modules = {

/***/ "(rsc)/./app/case-studies/page.tsx":
/*!***********************************!*\
  !*** ./app/case-studies/page.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"/Users/<USER>/Desktop/Claude/ai-marketing-website/app/case-studies/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"/Users/<USER>/Desktop/Claude/ai-marketing-website/app/case-studies/page.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"5f7d66c3a0a7\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxLQUFVLEVBQUUsRUFBdUIiLCJzb3VyY2VzIjpbIi9Vc2Vycy9rdy9EZXNrdG9wL0NsYXVkZS9haS1tYXJrZXRpbmctd2Vic2l0ZS9hcHAvZ2xvYmFscy5jc3MiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCI1ZjdkNjZjM2EwYTdcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./app/globals.css\n");

/***/ }),

/***/ "(rsc)/./app/layout.tsx":
/*!************************!*\
  !*** ./app/layout.tsx ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"app/layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"app/layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./app/globals.css\");\n/* harmony import */ var _components_layout_Header__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/layout/Header */ \"(rsc)/./components/layout/Header.tsx\");\n/* harmony import */ var _components_layout_Footer__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/layout/Footer */ \"(rsc)/./components/layout/Footer.tsx\");\n\n\n\n\n\nconst metadata = {\n    title: 'AI Solutions Hub - 智能解決方案',\n    description: '提供尖端AI技術服務，助力企業數字化轉型'\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"zh-HK\",\n        className: \"dark\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: `${(next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_4___default().className)} bg-gray-950 text-gray-100 min-h-screen flex flex-col`,\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_Header__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/app/layout.tsx\",\n                    lineNumber: 22,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                    className: \"flex-grow\",\n                    children: children\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/app/layout.tsx\",\n                    lineNumber: 23,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_Footer__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/app/layout.tsx\",\n                    lineNumber: 26,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/app/layout.tsx\",\n            lineNumber: 21,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/app/layout.tsx\",\n        lineNumber: 20,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvbGF5b3V0LnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7O0FBTU1BO0FBSmdCO0FBQ3lCO0FBQ0E7QUFJeEMsTUFBTUcsV0FBcUI7SUFDaENDLE9BQU87SUFDUEMsYUFBYTtBQUNmLEVBQUM7QUFFYyxTQUFTQyxXQUFXLEVBQ2pDQyxRQUFRLEVBR1Q7SUFDQyxxQkFDRSw4REFBQ0M7UUFBS0MsTUFBSztRQUFRQyxXQUFVO2tCQUMzQiw0RUFBQ0M7WUFBS0QsV0FBVyxHQUFHViwySkFBZSxDQUFDLHFEQUFxRCxDQUFDOzs4QkFDeEYsOERBQUNDLGlFQUFNQTs7Ozs7OEJBQ1AsOERBQUNXO29CQUFLRixXQUFVOzhCQUNiSDs7Ozs7OzhCQUVILDhEQUFDTCxpRUFBTUE7Ozs7Ozs7Ozs7Ozs7Ozs7QUFJZiIsInNvdXJjZXMiOlsiL1VzZXJzL2t3L0Rlc2t0b3AvQ2xhdWRlL2FpLW1hcmtldGluZy13ZWJzaXRlL2FwcC9sYXlvdXQudHN4Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB0eXBlIHsgTWV0YWRhdGEgfSBmcm9tICduZXh0J1xuaW1wb3J0IHsgSW50ZXIgfSBmcm9tICduZXh0L2ZvbnQvZ29vZ2xlJ1xuaW1wb3J0ICcuL2dsb2JhbHMuY3NzJ1xuaW1wb3J0IEhlYWRlciBmcm9tICdAL2NvbXBvbmVudHMvbGF5b3V0L0hlYWRlcidcbmltcG9ydCBGb290ZXIgZnJvbSAnQC9jb21wb25lbnRzL2xheW91dC9Gb290ZXInXG5cbmNvbnN0IGludGVyID0gSW50ZXIoeyBzdWJzZXRzOiBbJ2xhdGluJ10gfSlcblxuZXhwb3J0IGNvbnN0IG1ldGFkYXRhOiBNZXRhZGF0YSA9IHtcbiAgdGl0bGU6ICdBSSBTb2x1dGlvbnMgSHViIC0g5pm66IO96Kej5rG65pa55qGIJyxcbiAgZGVzY3JpcHRpb246ICfmj5DkvpvlsJbnq69BSeaKgOihk+acjeWLme+8jOWKqeWKm+S8gealreaVuOWtl+WMlui9ieWeiycsXG59XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIFJvb3RMYXlvdXQoe1xuICBjaGlsZHJlbixcbn06IHtcbiAgY2hpbGRyZW46IFJlYWN0LlJlYWN0Tm9kZVxufSkge1xuICByZXR1cm4gKFxuICAgIDxodG1sIGxhbmc9XCJ6aC1IS1wiIGNsYXNzTmFtZT1cImRhcmtcIj5cbiAgICAgIDxib2R5IGNsYXNzTmFtZT17YCR7aW50ZXIuY2xhc3NOYW1lfSBiZy1ncmF5LTk1MCB0ZXh0LWdyYXktMTAwIG1pbi1oLXNjcmVlbiBmbGV4IGZsZXgtY29sYH0+XG4gICAgICAgIDxIZWFkZXIgLz5cbiAgICAgICAgPG1haW4gY2xhc3NOYW1lPVwiZmxleC1ncm93XCI+XG4gICAgICAgICAge2NoaWxkcmVufVxuICAgICAgICA8L21haW4+XG4gICAgICAgIDxGb290ZXIgLz5cbiAgICAgIDwvYm9keT5cbiAgICA8L2h0bWw+XG4gIClcbn0iXSwibmFtZXMiOlsiaW50ZXIiLCJIZWFkZXIiLCJGb290ZXIiLCJtZXRhZGF0YSIsInRpdGxlIiwiZGVzY3JpcHRpb24iLCJSb290TGF5b3V0IiwiY2hpbGRyZW4iLCJodG1sIiwibGFuZyIsImNsYXNzTmFtZSIsImJvZHkiLCJtYWluIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./components/layout/Footer.tsx":
/*!**************************************!*\
  !*** ./components/layout/Footer.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/layout/Footer.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/layout/Footer.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./components/layout/Header.tsx":
/*!**************************************!*\
  !*** ./components/layout/Header.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/layout/Header.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/layout/Header.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fcase-studies%2Fpage&page=%2Fcase-studies%2Fpage&appPaths=%2Fcase-studies%2Fpage&pagePath=private-next-app-dir%2Fcase-studies%2Fpage.tsx&appDir=%2FUsers%2Fkw%2FDesktop%2FClaude%2Fai-marketing-website%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fkw%2FDesktop%2FClaude%2Fai-marketing-website&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fcase-studies%2Fpage&page=%2Fcase-studies%2Fpage&appPaths=%2Fcase-studies%2Fpage&pagePath=private-next-app-dir%2Fcase-studies%2Fpage.tsx&appDir=%2FUsers%2Fkw%2FDesktop%2FClaude%2Fai-marketing-website%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fkw%2FDesktop%2FClaude%2Fai-marketing-website&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?cc4a\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/layout.tsx */ \"(rsc)/./app/layout.tsx\"));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\nconst page4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/case-studies/page.tsx */ \"(rsc)/./app/case-studies/page.tsx\"));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'case-studies',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page4, \"/Users/<USER>/Desktop/Claude/ai-marketing-website/app/case-studies/page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [module0, \"/Users/<USER>/Desktop/Claude/ai-marketing-website/app/layout.tsx\"],\n'not-found': [module1, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module2, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module3, \"next/dist/client/components/unauthorized-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"/Users/<USER>/Desktop/Claude/ai-marketing-website/app/case-studies/page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/case-studies/page\",\n        pathname: \"/case-studies\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fcase-studies%2Fpage&page=%2Fcase-studies%2Fpage&appPaths=%2Fcase-studies%2Fpage&pagePath=private-next-app-dir%2Fcase-studies%2Fpage.tsx&appDir=%2FUsers%2Fkw%2FDesktop%2FClaude%2Fai-marketing-website%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fkw%2FDesktop%2FClaude%2Fai-marketing-website&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fkw%2FDesktop%2FClaude%2Fai-marketing-website%2Fapp%2Fcase-studies%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*******************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fkw%2FDesktop%2FClaude%2Fai-marketing-website%2Fapp%2Fcase-studies%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*******************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/case-studies/page.tsx */ \"(rsc)/./app/case-studies/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGa3clMkZEZXNrdG9wJTJGQ2xhdWRlJTJGYWktbWFya2V0aW5nLXdlYnNpdGUlMkZhcHAlMkZjYXNlLXN0dWRpZXMlMkZwYWdlLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsa0tBQTRHIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCIvVXNlcnMva3cvRGVza3RvcC9DbGF1ZGUvYWktbWFya2V0aW5nLXdlYnNpdGUvYXBwL2Nhc2Utc3R1ZGllcy9wYWdlLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fkw%2FDesktop%2FClaude%2Fai-marketing-website%2Fapp%2Fcase-studies%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fkw%2FDesktop%2FClaude%2Fai-marketing-website%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkw%2FDesktop%2FClaude%2Fai-marketing-website%2Fcomponents%2Flayout%2FFooter.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkw%2FDesktop%2FClaude%2Fai-marketing-website%2Fcomponents%2Flayout%2FHeader.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkw%2FDesktop%2FClaude%2Fai-marketing-website%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fkw%2FDesktop%2FClaude%2Fai-marketing-website%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkw%2FDesktop%2FClaude%2Fai-marketing-website%2Fcomponents%2Flayout%2FFooter.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkw%2FDesktop%2FClaude%2Fai-marketing-website%2Fcomponents%2Flayout%2FHeader.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkw%2FDesktop%2FClaude%2Fai-marketing-website%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/layout/Footer.tsx */ \"(rsc)/./components/layout/Footer.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/layout/Header.tsx */ \"(rsc)/./components/layout/Header.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGa3clMkZEZXNrdG9wJTJGQ2xhdWRlJTJGYWktbWFya2V0aW5nLXdlYnNpdGUlMkZhcHAlMkZnbG9iYWxzLmNzcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjIlMkZVc2VycyUyRmt3JTJGRGVza3RvcCUyRkNsYXVkZSUyRmFpLW1hcmtldGluZy13ZWJzaXRlJTJGY29tcG9uZW50cyUyRmxheW91dCUyRkZvb3Rlci50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlMjJkZWZhdWx0JTIyJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGa3clMkZEZXNrdG9wJTJGQ2xhdWRlJTJGYWktbWFya2V0aW5nLXdlYnNpdGUlMkZjb21wb25lbnRzJTJGbGF5b3V0JTJGSGVhZGVyLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiUyMmRlZmF1bHQlMjIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyJTJGVXNlcnMlMkZrdyUyRkRlc2t0b3AlMkZDbGF1ZGUlMkZhaS1tYXJrZXRpbmctd2Vic2l0ZSUyRm5vZGVfbW9kdWxlcyUyRm5leHQlMkZmb250JTJGZ29vZ2xlJTJGdGFyZ2V0LmNzcyUzRiU3QiU1QyUyMnBhdGglNUMlMjIlM0ElNUMlMjJhcHAlMkZsYXlvdXQudHN4JTVDJTIyJTJDJTVDJTIyaW1wb3J0JTVDJTIyJTNBJTVDJTIySW50ZXIlNUMlMjIlMkMlNUMlMjJhcmd1bWVudHMlNUMlMjIlM0ElNUIlN0IlNUMlMjJzdWJzZXRzJTVDJTIyJTNBJTVCJTVDJTIybGF0aW4lNUMlMjIlNUQlN0QlNUQlMkMlNUMlMjJ2YXJpYWJsZU5hbWUlNUMlMjIlM0ElNUMlMjJpbnRlciU1QyUyMiU3RCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsd0tBQTRJO0FBQzVJO0FBQ0Esd0tBQTRJIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIsIHdlYnBhY2tFeHBvcnRzOiBbXCJkZWZhdWx0XCJdICovIFwiL1VzZXJzL2t3L0Rlc2t0b3AvQ2xhdWRlL2FpLW1hcmtldGluZy13ZWJzaXRlL2NvbXBvbmVudHMvbGF5b3V0L0Zvb3Rlci50c3hcIik7XG47XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiwgd2VicGFja0V4cG9ydHM6IFtcImRlZmF1bHRcIl0gKi8gXCIvVXNlcnMva3cvRGVza3RvcC9DbGF1ZGUvYWktbWFya2V0aW5nLXdlYnNpdGUvY29tcG9uZW50cy9sYXlvdXQvSGVhZGVyLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fkw%2FDesktop%2FClaude%2Fai-marketing-website%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkw%2FDesktop%2FClaude%2Fai-marketing-website%2Fcomponents%2Flayout%2FFooter.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkw%2FDesktop%2FClaude%2Fai-marketing-website%2Fcomponents%2Flayout%2FHeader.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkw%2FDesktop%2FClaude%2Fai-marketing-website%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fkw%2FDesktop%2FClaude%2Fai-marketing-website%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkw%2FDesktop%2FClaude%2Fai-marketing-website%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkw%2FDesktop%2FClaude%2Fai-marketing-website%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkw%2FDesktop%2FClaude%2Fai-marketing-website%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkw%2FDesktop%2FClaude%2Fai-marketing-website%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkw%2FDesktop%2FClaude%2Fai-marketing-website%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkw%2FDesktop%2FClaude%2Fai-marketing-website%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkw%2FDesktop%2FClaude%2Fai-marketing-website%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fkw%2FDesktop%2FClaude%2Fai-marketing-website%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkw%2FDesktop%2FClaude%2Fai-marketing-website%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkw%2FDesktop%2FClaude%2Fai-marketing-website%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkw%2FDesktop%2FClaude%2Fai-marketing-website%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkw%2FDesktop%2FClaude%2Fai-marketing-website%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkw%2FDesktop%2FClaude%2Fai-marketing-website%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkw%2FDesktop%2FClaude%2Fai-marketing-website%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkw%2FDesktop%2FClaude%2Fai-marketing-website%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fkw%2FDesktop%2FClaude%2Fai-marketing-website%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkw%2FDesktop%2FClaude%2Fai-marketing-website%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkw%2FDesktop%2FClaude%2Fai-marketing-website%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkw%2FDesktop%2FClaude%2Fai-marketing-website%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkw%2FDesktop%2FClaude%2Fai-marketing-website%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkw%2FDesktop%2FClaude%2Fai-marketing-website%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkw%2FDesktop%2FClaude%2Fai-marketing-website%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkw%2FDesktop%2FClaude%2Fai-marketing-website%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./app/case-studies/page.tsx":
/*!***********************************!*\
  !*** ./app/case-studies/page.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ CaseStudiesPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _barrel_optimize_names_ArrowUpRight_BarChart3_Building2_Heart_Package_Plane_ShoppingCart_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowUpRight,BarChart3,Building2,Heart,Package,Plane,ShoppingCart!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/shopping-cart.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowUpRight_BarChart3_Building2_Heart_Package_Plane_ShoppingCart_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowUpRight,BarChart3,Building2,Heart,Package,Plane,ShoppingCart!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/chart-column.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowUpRight_BarChart3_Building2_Heart_Package_Plane_ShoppingCart_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowUpRight,BarChart3,Building2,Heart,Package,Plane,ShoppingCart!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/building-2.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowUpRight_BarChart3_Building2_Heart_Package_Plane_ShoppingCart_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowUpRight,BarChart3,Building2,Heart,Package,Plane,ShoppingCart!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/heart.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowUpRight_BarChart3_Building2_Heart_Package_Plane_ShoppingCart_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowUpRight,BarChart3,Building2,Heart,Package,Plane,ShoppingCart!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/plane.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowUpRight_BarChart3_Building2_Heart_Package_Plane_ShoppingCart_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowUpRight,BarChart3,Building2,Heart,Package,Plane,ShoppingCart!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/package.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowUpRight_BarChart3_Building2_Heart_Package_Plane_ShoppingCart_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowUpRight,BarChart3,Building2,Heart,Package,Plane,ShoppingCart!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/arrow-up-right.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nconst caseStudies = [\n    {\n        id: 1,\n        icon: _barrel_optimize_names_ArrowUpRight_BarChart3_Building2_Heart_Package_Plane_ShoppingCart_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"],\n        category: '電子商務',\n        title: '電商智能推薦系統',\n        client: '大型零售集團',\n        challenge: '客戶面臨用戶留存率低、購買轉化率不理想的問題，傳統的推薦算法無法滿足個性化需求。',\n        solution: '我們開發了基於深度學習的推薦引擎，結合用戶行為分析和實時數據處理，提供千人千面的個性化推薦。',\n        results: [\n            {\n                metric: '銷售額增長',\n                value: '45%'\n            },\n            {\n                metric: '用戶參與度提升',\n                value: '60%'\n            },\n            {\n                metric: '退貨率降低',\n                value: '25%'\n            },\n            {\n                metric: 'ROI',\n                value: '320%'\n            }\n        ],\n        technologies: [\n            '深度學習',\n            '實時推薦',\n            '用戶畫像',\n            'A/B測試'\n        ]\n    },\n    {\n        id: 2,\n        icon: _barrel_optimize_names_ArrowUpRight_BarChart3_Building2_Heart_Package_Plane_ShoppingCart_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n        category: '金融科技',\n        title: '金融風險評估平台',\n        client: '國際投資銀行',\n        challenge: '傳統的風險評估模型無法應對快速變化的市場環境，需要更智能的風險預測系統。',\n        solution: '構建了AI驅動的風險評估平台，整合多源數據，實現實時風險監控和預測。',\n        results: [\n            {\n                metric: '風險預測準確率',\n                value: '95%'\n            },\n            {\n                metric: '處理速度提升',\n                value: '10x'\n            },\n            {\n                metric: '合規成本降低',\n                value: '40%'\n            },\n            {\n                metric: '異常檢測率',\n                value: '99.5%'\n            }\n        ],\n        technologies: [\n            '機器學習',\n            '異常檢測',\n            '實時分析',\n            '自然語言處理'\n        ]\n    },\n    {\n        id: 3,\n        icon: _barrel_optimize_names_ArrowUpRight_BarChart3_Building2_Heart_Package_Plane_ShoppingCart_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        category: '智慧城市',\n        title: '智慧城市管理系統',\n        client: '政府機構',\n        challenge: '城市管理數據分散，無法有效整合資源，市民服務效率低下。',\n        solution: '開發了綜合性的智慧城市平台，整合IoT數據、交通信息、公共服務等多個系統。',\n        results: [\n            {\n                metric: '能源效率提升',\n                value: '35%'\n            },\n            {\n                metric: '交通擁堵減少',\n                value: '20%'\n            },\n            {\n                metric: '市民滿意度提升',\n                value: '50%'\n            },\n            {\n                metric: '運營成本降低',\n                value: '30%'\n            }\n        ],\n        technologies: [\n            '物聯網',\n            '大數據分析',\n            '預測建模',\n            '計算機視覺'\n        ]\n    },\n    {\n        id: 4,\n        icon: _barrel_optimize_names_ArrowUpRight_BarChart3_Building2_Heart_Package_Plane_ShoppingCart_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n        category: '醫療健康',\n        title: 'AI輔助診斷系統',\n        client: '連鎖醫療機構',\n        challenge: '醫生工作負荷大，診斷效率需要提升，同時要確保診斷準確性。',\n        solution: '開發了基於深度學習的醫療影像分析系統，輔助醫生進行疾病診斷。',\n        results: [\n            {\n                metric: '診斷準確率',\n                value: '98%'\n            },\n            {\n                metric: '診斷時間減少',\n                value: '70%'\n            },\n            {\n                metric: '誤診率降低',\n                value: '85%'\n            },\n            {\n                metric: '患者滿意度',\n                value: '92%'\n            }\n        ],\n        technologies: [\n            '深度學習',\n            '醫療影像分析',\n            '自然語言處理',\n            '知識圖譜'\n        ]\n    },\n    {\n        id: 5,\n        icon: _barrel_optimize_names_ArrowUpRight_BarChart3_Building2_Heart_Package_Plane_ShoppingCart_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n        category: '航空業',\n        title: '智能客服系統',\n        client: '國際航空公司',\n        challenge: '客服壓力大，響應時間長，多語言支持不足，客戶滿意度有待提升。',\n        solution: '部署了多語言AI客服系統，支持語音和文字交互，24/7全天候服務。',\n        results: [\n            {\n                metric: '響應時間縮短',\n                value: '90%'\n            },\n            {\n                metric: '客服成本降低',\n                value: '60%'\n            },\n            {\n                metric: '客戶滿意度',\n                value: '88%'\n            },\n            {\n                metric: '問題解決率',\n                value: '85%'\n            }\n        ],\n        technologies: [\n            '自然語言處理',\n            '語音識別',\n            '機器翻譯',\n            '情感分析'\n        ]\n    },\n    {\n        id: 6,\n        icon: _barrel_optimize_names_ArrowUpRight_BarChart3_Building2_Heart_Package_Plane_ShoppingCart_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n        category: '物流運輸',\n        title: '智能調度系統',\n        client: '物流配送公司',\n        challenge: '配送路線規劃效率低，車輛利用率不高，配送成本居高不下。',\n        solution: '開發了AI驅動的智能調度系統，實時優化配送路線和資源分配。',\n        results: [\n            {\n                metric: '配送效率提升',\n                value: '40%'\n            },\n            {\n                metric: '燃料成本降低',\n                value: '25%'\n            },\n            {\n                metric: '準時送達率',\n                value: '96%'\n            },\n            {\n                metric: '車輛利用率',\n                value: '85%'\n            }\n        ],\n        technologies: [\n            '路線優化',\n            '預測分析',\n            '實時調度',\n            '機器學習'\n        ]\n    }\n];\nfunction CaseStudiesPage() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"pt-24 pb-20\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"section-padding py-20\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-5xl mx-auto\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            y: 20\n                        },\n                        animate: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        transition: {\n                            duration: 0.5\n                        },\n                        className: \"text-center mb-16\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-5xl md:text-6xl font-bold mb-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"gradient-text\",\n                                    children: \"成功案例\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/app/case-studies/page.tsx\",\n                                    lineNumber: 118,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/app/case-studies/page.tsx\",\n                                lineNumber: 117,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xl text-gray-300 max-w-3xl mx-auto\",\n                                children: \"探索我們如何幫助各行各業的客戶實現AI驅動的業務轉型\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/app/case-studies/page.tsx\",\n                                lineNumber: 120,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/app/case-studies/page.tsx\",\n                        lineNumber: 111,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/app/case-studies/page.tsx\",\n                    lineNumber: 110,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/app/case-studies/page.tsx\",\n                lineNumber: 109,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"section-padding\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\",\n                        children: caseStudies.map((study, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    y: 20\n                                },\n                                whileInView: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                viewport: {\n                                    once: true\n                                },\n                                transition: {\n                                    duration: 0.5,\n                                    delay: index * 0.1\n                                },\n                                className: \"glass-effect rounded-2xl overflow-hidden card-hover group\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-8\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between mb-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-12 h-12 bg-gradient-to-br from-purple-500 to-pink-500 rounded-xl flex items-center justify-center\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(study.icon, {\n                                                        className: \"w-6 h-6 text-white\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/app/case-studies/page.tsx\",\n                                                        lineNumber: 142,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/app/case-studies/page.tsx\",\n                                                    lineNumber: 141,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm text-purple-400\",\n                                                    children: study.category\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/app/case-studies/page.tsx\",\n                                                    lineNumber: 144,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/app/case-studies/page.tsx\",\n                                            lineNumber: 140,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-2xl font-semibold mb-2 text-white\",\n                                            children: study.title\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/app/case-studies/page.tsx\",\n                                            lineNumber: 147,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-400 mb-4\",\n                                            children: study.client\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/app/case-studies/page.tsx\",\n                                            lineNumber: 148,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                            className: \"text-sm font-semibold text-gray-300 mb-2\",\n                                                            children: \"挑戰\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/app/case-studies/page.tsx\",\n                                                            lineNumber: 152,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-400\",\n                                                            children: study.challenge\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/app/case-studies/page.tsx\",\n                                                            lineNumber: 153,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/app/case-studies/page.tsx\",\n                                                    lineNumber: 151,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                            className: \"text-sm font-semibold text-gray-300 mb-2\",\n                                                            children: \"解決方案\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/app/case-studies/page.tsx\",\n                                                            lineNumber: 157,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-400\",\n                                                            children: study.solution\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/app/case-studies/page.tsx\",\n                                                            lineNumber: 158,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/app/case-studies/page.tsx\",\n                                                    lineNumber: 156,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                            className: \"text-sm font-semibold text-gray-300 mb-2\",\n                                                            children: \"成果\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/app/case-studies/page.tsx\",\n                                                            lineNumber: 162,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"grid grid-cols-2 gap-2\",\n                                                            children: study.results.map((result, idx)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"text-xl font-bold gradient-text\",\n                                                                            children: result.value\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/app/case-studies/page.tsx\",\n                                                                            lineNumber: 166,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"text-xs text-gray-400\",\n                                                                            children: result.metric\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/app/case-studies/page.tsx\",\n                                                                            lineNumber: 167,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, idx, true, {\n                                                                    fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/app/case-studies/page.tsx\",\n                                                                    lineNumber: 165,\n                                                                    columnNumber: 27\n                                                                }, this))\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/app/case-studies/page.tsx\",\n                                                            lineNumber: 163,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/app/case-studies/page.tsx\",\n                                                    lineNumber: 161,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                            className: \"text-sm font-semibold text-gray-300 mb-2\",\n                                                            children: \"技術棧\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/app/case-studies/page.tsx\",\n                                                            lineNumber: 174,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex flex-wrap gap-2\",\n                                                            children: study.technologies.map((tech, idx)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-xs px-3 py-1 glass-effect rounded-full text-gray-300\",\n                                                                    children: tech\n                                                                }, idx, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/app/case-studies/page.tsx\",\n                                                                    lineNumber: 177,\n                                                                    columnNumber: 27\n                                                                }, this))\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/app/case-studies/page.tsx\",\n                                                            lineNumber: 175,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/app/case-studies/page.tsx\",\n                                                    lineNumber: 173,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/app/case-studies/page.tsx\",\n                                            lineNumber: 150,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                            href: `/case-studies/${study.id}`,\n                                            className: \"inline-flex items-center mt-6 text-purple-400 hover:text-purple-300 transition-colors group\",\n                                            children: [\n                                                \"查看詳情\",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowUpRight_BarChart3_Building2_Heart_Package_Plane_ShoppingCart_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                    className: \"w-4 h-4 ml-1 group-hover:translate-x-1 group-hover:-translate-y-1 transition-transform\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/app/case-studies/page.tsx\",\n                                                    lineNumber: 193,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/app/case-studies/page.tsx\",\n                                            lineNumber: 188,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/app/case-studies/page.tsx\",\n                                    lineNumber: 139,\n                                    columnNumber: 17\n                                }, this)\n                            }, study.id, false, {\n                                fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/app/case-studies/page.tsx\",\n                                lineNumber: 131,\n                                columnNumber: 15\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/app/case-studies/page.tsx\",\n                        lineNumber: 129,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/app/case-studies/page.tsx\",\n                    lineNumber: 128,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/app/case-studies/page.tsx\",\n                lineNumber: 127,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"section-padding py-20\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-4xl mx-auto text-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            y: 20\n                        },\n                        whileInView: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        viewport: {\n                            once: true\n                        },\n                        transition: {\n                            duration: 0.5\n                        },\n                        className: \"glass-effect rounded-3xl p-12\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-3xl font-bold mb-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"gradient-text\",\n                                    children: \"想成為下一個成功案例？\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/app/case-studies/page.tsx\",\n                                    lineNumber: 212,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/app/case-studies/page.tsx\",\n                                lineNumber: 211,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xl text-gray-300 mb-8\",\n                                children: \"讓我們一起探討如何用AI技術推動您的業務增長\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/app/case-studies/page.tsx\",\n                                lineNumber: 214,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                href: \"/contact\",\n                                className: \"inline-flex items-center px-8 py-4 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full text-white font-semibold hover:shadow-lg hover:shadow-purple-500/50 transition-all duration-300\",\n                                children: \"開始對話 →\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/app/case-studies/page.tsx\",\n                                lineNumber: 217,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/app/case-studies/page.tsx\",\n                        lineNumber: 204,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/app/case-studies/page.tsx\",\n                    lineNumber: 203,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/app/case-studies/page.tsx\",\n                lineNumber: 202,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/app/case-studies/page.tsx\",\n        lineNumber: 108,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/case-studies/page.tsx\n");

/***/ }),

/***/ "(ssr)/./components/layout/Footer.tsx":
/*!**************************************!*\
  !*** ./components/layout/Footer.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Footer)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Brain_Github_Linkedin_Mail_Twitter_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Brain,Github,Linkedin,Mail,Twitter!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/twitter.js\");\n/* harmony import */ var _barrel_optimize_names_Brain_Github_Linkedin_Mail_Twitter_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Brain,Github,Linkedin,Mail,Twitter!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/linkedin.js\");\n/* harmony import */ var _barrel_optimize_names_Brain_Github_Linkedin_Mail_Twitter_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Brain,Github,Linkedin,Mail,Twitter!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/github.js\");\n/* harmony import */ var _barrel_optimize_names_Brain_Github_Linkedin_Mail_Twitter_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Brain,Github,Linkedin,Mail,Twitter!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/mail.js\");\n/* harmony import */ var _barrel_optimize_names_Brain_Github_Linkedin_Mail_Twitter_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Brain,Github,Linkedin,Mail,Twitter!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/brain.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nconst footerLinks = {\n    company: [\n        {\n            label: '關於我們',\n            href: '/about'\n        },\n        {\n            label: '團隊',\n            href: '/about#team'\n        },\n        {\n            label: '職業機會',\n            href: '/careers'\n        },\n        {\n            label: '新聞',\n            href: '/news'\n        }\n    ],\n    services: [\n        {\n            label: 'AI 諮詢',\n            href: '/services#consulting'\n        },\n        {\n            label: '機器學習',\n            href: '/services#ml'\n        },\n        {\n            label: '自然語言處理',\n            href: '/services#nlp'\n        },\n        {\n            label: '計算機視覺',\n            href: '/services#cv'\n        }\n    ],\n    resources: [\n        {\n            label: '博客',\n            href: '/blog'\n        },\n        {\n            label: '案例研究',\n            href: '/case-studies'\n        },\n        {\n            label: '文檔',\n            href: '/docs'\n        },\n        {\n            label: 'API',\n            href: '/api'\n        }\n    ],\n    support: [\n        {\n            label: '聯繫我們',\n            href: '/contact'\n        },\n        {\n            label: '常見問題',\n            href: '/faq'\n        },\n        {\n            label: '隱私政策',\n            href: '/privacy'\n        },\n        {\n            label: '服務條款',\n            href: '/terms'\n        }\n    ]\n};\nconst socialLinks = [\n    {\n        icon: _barrel_optimize_names_Brain_Github_Linkedin_Mail_Twitter_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"],\n        href: '#',\n        label: 'Twitter'\n    },\n    {\n        icon: _barrel_optimize_names_Brain_Github_Linkedin_Mail_Twitter_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n        href: '#',\n        label: 'LinkedIn'\n    },\n    {\n        icon: _barrel_optimize_names_Brain_Github_Linkedin_Mail_Twitter_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        href: '#',\n        label: 'GitHub'\n    },\n    {\n        icon: _barrel_optimize_names_Brain_Github_Linkedin_Mail_Twitter_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n        href: '#',\n        label: 'Email'\n    }\n];\nfunction Footer() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n        className: \"glass-effect border-t border-gray-800 mt-20\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"section-padding py-12\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"lg:col-span-1\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                    href: \"/\",\n                                    className: \"flex items-center space-x-2 mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Brain_Github_Linkedin_Mail_Twitter_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                            className: \"w-8 h-8 text-purple-500\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/layout/Footer.tsx\",\n                                            lineNumber: 47,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-xl font-bold gradient-text\",\n                                            children: \"AI Solutions Hub\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/layout/Footer.tsx\",\n                                            lineNumber: 48,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/layout/Footer.tsx\",\n                                    lineNumber: 46,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-400 text-sm mb-4\",\n                                    children: \"推動AI創新，助力企業轉型\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/layout/Footer.tsx\",\n                                    lineNumber: 50,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex space-x-4\",\n                                    children: socialLinks.map((social)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: social.href,\n                                            \"aria-label\": social.label,\n                                            className: \"text-gray-400 hover:text-purple-500 transition-colors duration-300\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(social.icon, {\n                                                size: 20\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/layout/Footer.tsx\",\n                                                lineNumber: 61,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, social.label, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/layout/Footer.tsx\",\n                                            lineNumber: 55,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/layout/Footer.tsx\",\n                                    lineNumber: 53,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/layout/Footer.tsx\",\n                            lineNumber: 45,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-white font-semibold mb-4\",\n                                    children: \"公司\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/layout/Footer.tsx\",\n                                    lineNumber: 68,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    className: \"space-y-2\",\n                                    children: footerLinks.company.map((link)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                href: link.href,\n                                                className: \"text-gray-400 hover:text-white transition-colors duration-300 text-sm\",\n                                                children: link.label\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/layout/Footer.tsx\",\n                                                lineNumber: 72,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, link.href, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/layout/Footer.tsx\",\n                                            lineNumber: 71,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/layout/Footer.tsx\",\n                                    lineNumber: 69,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/layout/Footer.tsx\",\n                            lineNumber: 67,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-white font-semibold mb-4\",\n                                    children: \"服務\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/layout/Footer.tsx\",\n                                    lineNumber: 84,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    className: \"space-y-2\",\n                                    children: footerLinks.services.map((link)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                href: link.href,\n                                                className: \"text-gray-400 hover:text-white transition-colors duration-300 text-sm\",\n                                                children: link.label\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/layout/Footer.tsx\",\n                                                lineNumber: 88,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, link.href, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/layout/Footer.tsx\",\n                                            lineNumber: 87,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/layout/Footer.tsx\",\n                                    lineNumber: 85,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/layout/Footer.tsx\",\n                            lineNumber: 83,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-white font-semibold mb-4\",\n                                    children: \"資源\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/layout/Footer.tsx\",\n                                    lineNumber: 100,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    className: \"space-y-2\",\n                                    children: footerLinks.resources.map((link)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                href: link.href,\n                                                className: \"text-gray-400 hover:text-white transition-colors duration-300 text-sm\",\n                                                children: link.label\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/layout/Footer.tsx\",\n                                                lineNumber: 104,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, link.href, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/layout/Footer.tsx\",\n                                            lineNumber: 103,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/layout/Footer.tsx\",\n                                    lineNumber: 101,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/layout/Footer.tsx\",\n                            lineNumber: 99,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-white font-semibold mb-4\",\n                                    children: \"支援\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/layout/Footer.tsx\",\n                                    lineNumber: 116,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    className: \"space-y-2\",\n                                    children: footerLinks.support.map((link)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                href: link.href,\n                                                className: \"text-gray-400 hover:text-white transition-colors duration-300 text-sm\",\n                                                children: link.label\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/layout/Footer.tsx\",\n                                                lineNumber: 120,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, link.href, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/layout/Footer.tsx\",\n                                            lineNumber: 119,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/layout/Footer.tsx\",\n                                    lineNumber: 117,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/layout/Footer.tsx\",\n                            lineNumber: 115,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/layout/Footer.tsx\",\n                    lineNumber: 44,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-8 pt-8 border-t border-gray-800 flex flex-col md:flex-row justify-between items-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-400 text-sm\",\n                            children: \"\\xa9 2024 AI Solutions Hub. 保留所有權利。\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/layout/Footer.tsx\",\n                            lineNumber: 133,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-400 text-sm mt-2 md:mt-0\",\n                            children: \"用心打造 | 香港\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/layout/Footer.tsx\",\n                            lineNumber: 136,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/layout/Footer.tsx\",\n                    lineNumber: 132,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/layout/Footer.tsx\",\n            lineNumber: 43,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/layout/Footer.tsx\",\n        lineNumber: 42,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9jb21wb25lbnRzL2xheW91dC9Gb290ZXIudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7O0FBRTRCO0FBQ3lDO0FBRXJFLE1BQU1NLGNBQWM7SUFDbEJDLFNBQVM7UUFDUDtZQUFFQyxPQUFPO1lBQVFDLE1BQU07UUFBUztRQUNoQztZQUFFRCxPQUFPO1lBQU1DLE1BQU07UUFBYztRQUNuQztZQUFFRCxPQUFPO1lBQVFDLE1BQU07UUFBVztRQUNsQztZQUFFRCxPQUFPO1lBQU1DLE1BQU07UUFBUTtLQUM5QjtJQUNEQyxVQUFVO1FBQ1I7WUFBRUYsT0FBTztZQUFTQyxNQUFNO1FBQXVCO1FBQy9DO1lBQUVELE9BQU87WUFBUUMsTUFBTTtRQUFlO1FBQ3RDO1lBQUVELE9BQU87WUFBVUMsTUFBTTtRQUFnQjtRQUN6QztZQUFFRCxPQUFPO1lBQVNDLE1BQU07UUFBZTtLQUN4QztJQUNERSxXQUFXO1FBQ1Q7WUFBRUgsT0FBTztZQUFNQyxNQUFNO1FBQVE7UUFDN0I7WUFBRUQsT0FBTztZQUFRQyxNQUFNO1FBQWdCO1FBQ3ZDO1lBQUVELE9BQU87WUFBTUMsTUFBTTtRQUFRO1FBQzdCO1lBQUVELE9BQU87WUFBT0MsTUFBTTtRQUFPO0tBQzlCO0lBQ0RHLFNBQVM7UUFDUDtZQUFFSixPQUFPO1lBQVFDLE1BQU07UUFBVztRQUNsQztZQUFFRCxPQUFPO1lBQVFDLE1BQU07UUFBTztRQUM5QjtZQUFFRCxPQUFPO1lBQVFDLE1BQU07UUFBVztRQUNsQztZQUFFRCxPQUFPO1lBQVFDLE1BQU07UUFBUztLQUNqQztBQUNIO0FBRUEsTUFBTUksY0FBYztJQUNsQjtRQUFFQyxNQUFNWiw4R0FBT0E7UUFBRU8sTUFBTTtRQUFLRCxPQUFPO0lBQVU7SUFDN0M7UUFBRU0sTUFBTVgsOEdBQVFBO1FBQUVNLE1BQU07UUFBS0QsT0FBTztJQUFXO0lBQy9DO1FBQUVNLE1BQU1WLDhHQUFNQTtRQUFFSyxNQUFNO1FBQUtELE9BQU87SUFBUztJQUMzQztRQUFFTSxNQUFNVCw4R0FBSUE7UUFBRUksTUFBTTtRQUFLRCxPQUFPO0lBQVE7Q0FDekM7QUFFYyxTQUFTTztJQUN0QixxQkFDRSw4REFBQ0M7UUFBT0MsV0FBVTtrQkFDaEIsNEVBQUNDO1lBQUlELFdBQVU7OzhCQUNiLDhEQUFDQztvQkFBSUQsV0FBVTs7c0NBQ2IsOERBQUNDOzRCQUFJRCxXQUFVOzs4Q0FDYiw4REFBQ2pCLGtEQUFJQTtvQ0FBQ1MsTUFBSztvQ0FBSVEsV0FBVTs7c0RBQ3ZCLDhEQUFDaEIsOEdBQUtBOzRDQUFDZ0IsV0FBVTs7Ozs7O3NEQUNqQiw4REFBQ0U7NENBQUtGLFdBQVU7c0RBQWtDOzs7Ozs7Ozs7Ozs7OENBRXBELDhEQUFDRztvQ0FBRUgsV0FBVTs4Q0FBNkI7Ozs7Ozs4Q0FHMUMsOERBQUNDO29DQUFJRCxXQUFVOzhDQUNaSixZQUFZUSxHQUFHLENBQUMsQ0FBQ0MsdUJBQ2hCLDhEQUFDQzs0Q0FFQ2QsTUFBTWEsT0FBT2IsSUFBSTs0Q0FDakJlLGNBQVlGLE9BQU9kLEtBQUs7NENBQ3hCUyxXQUFVO3NEQUVWLDRFQUFDSyxPQUFPUixJQUFJO2dEQUFDVyxNQUFNOzs7Ozs7MkNBTGRILE9BQU9kLEtBQUs7Ozs7Ozs7Ozs7Ozs7Ozs7c0NBV3pCLDhEQUFDVTs7OENBQ0MsOERBQUNRO29DQUFHVCxXQUFVOzhDQUFnQzs7Ozs7OzhDQUM5Qyw4REFBQ1U7b0NBQUdWLFdBQVU7OENBQ1hYLFlBQVlDLE9BQU8sQ0FBQ2MsR0FBRyxDQUFDLENBQUNPLHFCQUN4Qiw4REFBQ0M7c0RBQ0MsNEVBQUM3QixrREFBSUE7Z0RBQ0hTLE1BQU1tQixLQUFLbkIsSUFBSTtnREFDZlEsV0FBVTswREFFVFcsS0FBS3BCLEtBQUs7Ozs7OzsyQ0FMTm9CLEtBQUtuQixJQUFJOzs7Ozs7Ozs7Ozs7Ozs7O3NDQVl4Qiw4REFBQ1M7OzhDQUNDLDhEQUFDUTtvQ0FBR1QsV0FBVTs4Q0FBZ0M7Ozs7Ozs4Q0FDOUMsOERBQUNVO29DQUFHVixXQUFVOzhDQUNYWCxZQUFZSSxRQUFRLENBQUNXLEdBQUcsQ0FBQyxDQUFDTyxxQkFDekIsOERBQUNDO3NEQUNDLDRFQUFDN0Isa0RBQUlBO2dEQUNIUyxNQUFNbUIsS0FBS25CLElBQUk7Z0RBQ2ZRLFdBQVU7MERBRVRXLEtBQUtwQixLQUFLOzs7Ozs7MkNBTE5vQixLQUFLbkIsSUFBSTs7Ozs7Ozs7Ozs7Ozs7OztzQ0FZeEIsOERBQUNTOzs4Q0FDQyw4REFBQ1E7b0NBQUdULFdBQVU7OENBQWdDOzs7Ozs7OENBQzlDLDhEQUFDVTtvQ0FBR1YsV0FBVTs4Q0FDWFgsWUFBWUssU0FBUyxDQUFDVSxHQUFHLENBQUMsQ0FBQ08scUJBQzFCLDhEQUFDQztzREFDQyw0RUFBQzdCLGtEQUFJQTtnREFDSFMsTUFBTW1CLEtBQUtuQixJQUFJO2dEQUNmUSxXQUFVOzBEQUVUVyxLQUFLcEIsS0FBSzs7Ozs7OzJDQUxOb0IsS0FBS25CLElBQUk7Ozs7Ozs7Ozs7Ozs7Ozs7c0NBWXhCLDhEQUFDUzs7OENBQ0MsOERBQUNRO29DQUFHVCxXQUFVOzhDQUFnQzs7Ozs7OzhDQUM5Qyw4REFBQ1U7b0NBQUdWLFdBQVU7OENBQ1hYLFlBQVlNLE9BQU8sQ0FBQ1MsR0FBRyxDQUFDLENBQUNPLHFCQUN4Qiw4REFBQ0M7c0RBQ0MsNEVBQUM3QixrREFBSUE7Z0RBQ0hTLE1BQU1tQixLQUFLbkIsSUFBSTtnREFDZlEsV0FBVTswREFFVFcsS0FBS3BCLEtBQUs7Ozs7OzsyQ0FMTm9CLEtBQUtuQixJQUFJOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzhCQWExQiw4REFBQ1M7b0JBQUlELFdBQVU7O3NDQUNiLDhEQUFDRzs0QkFBRUgsV0FBVTtzQ0FBd0I7Ozs7OztzQ0FHckMsOERBQUNHOzRCQUFFSCxXQUFVO3NDQUFxQzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFPNUQiLCJzb3VyY2VzIjpbIi9Vc2Vycy9rdy9EZXNrdG9wL0NsYXVkZS9haS1tYXJrZXRpbmctd2Vic2l0ZS9jb21wb25lbnRzL2xheW91dC9Gb290ZXIudHN4Il0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50J1xuXG5pbXBvcnQgTGluayBmcm9tICduZXh0L2xpbmsnXG5pbXBvcnQgeyBCcmFpbiwgVHdpdHRlciwgTGlua2VkaW4sIEdpdGh1YiwgTWFpbCB9IGZyb20gJ2x1Y2lkZS1yZWFjdCdcblxuY29uc3QgZm9vdGVyTGlua3MgPSB7XG4gIGNvbXBhbnk6IFtcbiAgICB7IGxhYmVsOiAn6Zec5pa85oiR5YCRJywgaHJlZjogJy9hYm91dCcgfSxcbiAgICB7IGxhYmVsOiAn5ZyY6ZqKJywgaHJlZjogJy9hYm91dCN0ZWFtJyB9LFxuICAgIHsgbGFiZWw6ICfogbfmpa3mqZ/mnIMnLCBocmVmOiAnL2NhcmVlcnMnIH0sXG4gICAgeyBsYWJlbDogJ+aWsOiBnicsIGhyZWY6ICcvbmV3cycgfSxcbiAgXSxcbiAgc2VydmljZXM6IFtcbiAgICB7IGxhYmVsOiAnQUkg6Kuu6KmiJywgaHJlZjogJy9zZXJ2aWNlcyNjb25zdWx0aW5nJyB9LFxuICAgIHsgbGFiZWw6ICfmqZ/lmajlrbjnv5InLCBocmVmOiAnL3NlcnZpY2VzI21sJyB9LFxuICAgIHsgbGFiZWw6ICfoh6rnhLboqp7oqIDomZXnkIYnLCBocmVmOiAnL3NlcnZpY2VzI25scCcgfSxcbiAgICB7IGxhYmVsOiAn6KiI566X5qmf6KaW6Ka6JywgaHJlZjogJy9zZXJ2aWNlcyNjdicgfSxcbiAgXSxcbiAgcmVzb3VyY2VzOiBbXG4gICAgeyBsYWJlbDogJ+WNmuWuoicsIGhyZWY6ICcvYmxvZycgfSxcbiAgICB7IGxhYmVsOiAn5qGI5L6L56CU56m2JywgaHJlZjogJy9jYXNlLXN0dWRpZXMnIH0sXG4gICAgeyBsYWJlbDogJ+aWh+aqlCcsIGhyZWY6ICcvZG9jcycgfSxcbiAgICB7IGxhYmVsOiAnQVBJJywgaHJlZjogJy9hcGknIH0sXG4gIF0sXG4gIHN1cHBvcnQ6IFtcbiAgICB7IGxhYmVsOiAn6IGv57mr5oiR5YCRJywgaHJlZjogJy9jb250YWN0JyB9LFxuICAgIHsgbGFiZWw6ICfluLjopovllY/poYwnLCBocmVmOiAnL2ZhcScgfSxcbiAgICB7IGxhYmVsOiAn6Zqx56eB5pS/562WJywgaHJlZjogJy9wcml2YWN5JyB9LFxuICAgIHsgbGFiZWw6ICfmnI3li5nmop3mrL4nLCBocmVmOiAnL3Rlcm1zJyB9LFxuICBdLFxufVxuXG5jb25zdCBzb2NpYWxMaW5rcyA9IFtcbiAgeyBpY29uOiBUd2l0dGVyLCBocmVmOiAnIycsIGxhYmVsOiAnVHdpdHRlcicgfSxcbiAgeyBpY29uOiBMaW5rZWRpbiwgaHJlZjogJyMnLCBsYWJlbDogJ0xpbmtlZEluJyB9LFxuICB7IGljb246IEdpdGh1YiwgaHJlZjogJyMnLCBsYWJlbDogJ0dpdEh1YicgfSxcbiAgeyBpY29uOiBNYWlsLCBocmVmOiAnIycsIGxhYmVsOiAnRW1haWwnIH0sXG5dXG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIEZvb3RlcigpIHtcbiAgcmV0dXJuIChcbiAgICA8Zm9vdGVyIGNsYXNzTmFtZT1cImdsYXNzLWVmZmVjdCBib3JkZXItdCBib3JkZXItZ3JheS04MDAgbXQtMjBcIj5cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwic2VjdGlvbi1wYWRkaW5nIHB5LTEyXCI+XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZ3JpZCBncmlkLWNvbHMtMSBtZDpncmlkLWNvbHMtMiBsZzpncmlkLWNvbHMtNSBnYXAtOFwiPlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibGc6Y29sLXNwYW4tMVwiPlxuICAgICAgICAgICAgPExpbmsgaHJlZj1cIi9cIiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTIgbWItNFwiPlxuICAgICAgICAgICAgICA8QnJhaW4gY2xhc3NOYW1lPVwidy04IGgtOCB0ZXh0LXB1cnBsZS01MDBcIiAvPlxuICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXhsIGZvbnQtYm9sZCBncmFkaWVudC10ZXh0XCI+QUkgU29sdXRpb25zIEh1Yjwvc3Bhbj5cbiAgICAgICAgICAgIDwvTGluaz5cbiAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtZ3JheS00MDAgdGV4dC1zbSBtYi00XCI+XG4gICAgICAgICAgICAgIOaOqOWLlUFJ5Ym15paw77yM5Yqp5Yqb5LyB5qWt6L2J5Z6LXG4gICAgICAgICAgICA8L3A+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggc3BhY2UteC00XCI+XG4gICAgICAgICAgICAgIHtzb2NpYWxMaW5rcy5tYXAoKHNvY2lhbCkgPT4gKFxuICAgICAgICAgICAgICAgIDxhXG4gICAgICAgICAgICAgICAgICBrZXk9e3NvY2lhbC5sYWJlbH1cbiAgICAgICAgICAgICAgICAgIGhyZWY9e3NvY2lhbC5ocmVmfVxuICAgICAgICAgICAgICAgICAgYXJpYS1sYWJlbD17c29jaWFsLmxhYmVsfVxuICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTQwMCBob3Zlcjp0ZXh0LXB1cnBsZS01MDAgdHJhbnNpdGlvbi1jb2xvcnMgZHVyYXRpb24tMzAwXCJcbiAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICA8c29jaWFsLmljb24gc2l6ZT17MjB9IC8+XG4gICAgICAgICAgICAgICAgPC9hPlxuICAgICAgICAgICAgICApKX1cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgIDxoMyBjbGFzc05hbWU9XCJ0ZXh0LXdoaXRlIGZvbnQtc2VtaWJvbGQgbWItNFwiPuWFrOWPuDwvaDM+XG4gICAgICAgICAgICA8dWwgY2xhc3NOYW1lPVwic3BhY2UteS0yXCI+XG4gICAgICAgICAgICAgIHtmb290ZXJMaW5rcy5jb21wYW55Lm1hcCgobGluaykgPT4gKFxuICAgICAgICAgICAgICAgIDxsaSBrZXk9e2xpbmsuaHJlZn0+XG4gICAgICAgICAgICAgICAgICA8TGlua1xuICAgICAgICAgICAgICAgICAgICBocmVmPXtsaW5rLmhyZWZ9XG4gICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInRleHQtZ3JheS00MDAgaG92ZXI6dGV4dC13aGl0ZSB0cmFuc2l0aW9uLWNvbG9ycyBkdXJhdGlvbi0zMDAgdGV4dC1zbVwiXG4gICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgIHtsaW5rLmxhYmVsfVxuICAgICAgICAgICAgICAgICAgPC9MaW5rPlxuICAgICAgICAgICAgICAgIDwvbGk+XG4gICAgICAgICAgICAgICkpfVxuICAgICAgICAgICAgPC91bD5cbiAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICA8aDMgY2xhc3NOYW1lPVwidGV4dC13aGl0ZSBmb250LXNlbWlib2xkIG1iLTRcIj7mnI3li5k8L2gzPlxuICAgICAgICAgICAgPHVsIGNsYXNzTmFtZT1cInNwYWNlLXktMlwiPlxuICAgICAgICAgICAgICB7Zm9vdGVyTGlua3Muc2VydmljZXMubWFwKChsaW5rKSA9PiAoXG4gICAgICAgICAgICAgICAgPGxpIGtleT17bGluay5ocmVmfT5cbiAgICAgICAgICAgICAgICAgIDxMaW5rXG4gICAgICAgICAgICAgICAgICAgIGhyZWY9e2xpbmsuaHJlZn1cbiAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTQwMCBob3Zlcjp0ZXh0LXdoaXRlIHRyYW5zaXRpb24tY29sb3JzIGR1cmF0aW9uLTMwMCB0ZXh0LXNtXCJcbiAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAge2xpbmsubGFiZWx9XG4gICAgICAgICAgICAgICAgICA8L0xpbms+XG4gICAgICAgICAgICAgICAgPC9saT5cbiAgICAgICAgICAgICAgKSl9XG4gICAgICAgICAgICA8L3VsPlxuICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgIDxoMyBjbGFzc05hbWU9XCJ0ZXh0LXdoaXRlIGZvbnQtc2VtaWJvbGQgbWItNFwiPuizh+a6kDwvaDM+XG4gICAgICAgICAgICA8dWwgY2xhc3NOYW1lPVwic3BhY2UteS0yXCI+XG4gICAgICAgICAgICAgIHtmb290ZXJMaW5rcy5yZXNvdXJjZXMubWFwKChsaW5rKSA9PiAoXG4gICAgICAgICAgICAgICAgPGxpIGtleT17bGluay5ocmVmfT5cbiAgICAgICAgICAgICAgICAgIDxMaW5rXG4gICAgICAgICAgICAgICAgICAgIGhyZWY9e2xpbmsuaHJlZn1cbiAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTQwMCBob3Zlcjp0ZXh0LXdoaXRlIHRyYW5zaXRpb24tY29sb3JzIGR1cmF0aW9uLTMwMCB0ZXh0LXNtXCJcbiAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAge2xpbmsubGFiZWx9XG4gICAgICAgICAgICAgICAgICA8L0xpbms+XG4gICAgICAgICAgICAgICAgPC9saT5cbiAgICAgICAgICAgICAgKSl9XG4gICAgICAgICAgICA8L3VsPlxuICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgIDxoMyBjbGFzc05hbWU9XCJ0ZXh0LXdoaXRlIGZvbnQtc2VtaWJvbGQgbWItNFwiPuaUr+aPtDwvaDM+XG4gICAgICAgICAgICA8dWwgY2xhc3NOYW1lPVwic3BhY2UteS0yXCI+XG4gICAgICAgICAgICAgIHtmb290ZXJMaW5rcy5zdXBwb3J0Lm1hcCgobGluaykgPT4gKFxuICAgICAgICAgICAgICAgIDxsaSBrZXk9e2xpbmsuaHJlZn0+XG4gICAgICAgICAgICAgICAgICA8TGlua1xuICAgICAgICAgICAgICAgICAgICBocmVmPXtsaW5rLmhyZWZ9XG4gICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInRleHQtZ3JheS00MDAgaG92ZXI6dGV4dC13aGl0ZSB0cmFuc2l0aW9uLWNvbG9ycyBkdXJhdGlvbi0zMDAgdGV4dC1zbVwiXG4gICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgIHtsaW5rLmxhYmVsfVxuICAgICAgICAgICAgICAgICAgPC9MaW5rPlxuICAgICAgICAgICAgICAgIDwvbGk+XG4gICAgICAgICAgICAgICkpfVxuICAgICAgICAgICAgPC91bD5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtdC04IHB0LTggYm9yZGVyLXQgYm9yZGVyLWdyYXktODAwIGZsZXggZmxleC1jb2wgbWQ6ZmxleC1yb3cganVzdGlmeS1iZXR3ZWVuIGl0ZW1zLWNlbnRlclwiPlxuICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtZ3JheS00MDAgdGV4dC1zbVwiPlxuICAgICAgICAgICAgwqkgMjAyNCBBSSBTb2x1dGlvbnMgSHViLiDkv53nlZnmiYDmnInmrIrliKnjgIJcbiAgICAgICAgICA8L3A+XG4gICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTQwMCB0ZXh0LXNtIG10LTIgbWQ6bXQtMFwiPlxuICAgICAgICAgICAg55So5b+D5omT6YCgIHwg6aaZ5rivXG4gICAgICAgICAgPC9wPlxuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvZGl2PlxuICAgIDwvZm9vdGVyPlxuICApXG59Il0sIm5hbWVzIjpbIkxpbmsiLCJCcmFpbiIsIlR3aXR0ZXIiLCJMaW5rZWRpbiIsIkdpdGh1YiIsIk1haWwiLCJmb290ZXJMaW5rcyIsImNvbXBhbnkiLCJsYWJlbCIsImhyZWYiLCJzZXJ2aWNlcyIsInJlc291cmNlcyIsInN1cHBvcnQiLCJzb2NpYWxMaW5rcyIsImljb24iLCJGb290ZXIiLCJmb290ZXIiLCJjbGFzc05hbWUiLCJkaXYiLCJzcGFuIiwicCIsIm1hcCIsInNvY2lhbCIsImEiLCJhcmlhLWxhYmVsIiwic2l6ZSIsImgzIiwidWwiLCJsaW5rIiwibGkiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./components/layout/Footer.tsx\n");

/***/ }),

/***/ "(ssr)/./components/layout/Header.tsx":
/*!**************************************!*\
  !*** ./components/layout/Header.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Header)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _barrel_optimize_names_Brain_Menu_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Brain,Menu,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/brain.js\");\n/* harmony import */ var _barrel_optimize_names_Brain_Menu_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Brain,Menu,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_Brain_Menu_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Brain,Menu,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/menu.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\nconst navLinks = [\n    {\n        href: '/',\n        label: '首頁'\n    },\n    {\n        href: '/about',\n        label: '關於我們'\n    },\n    {\n        href: '/services',\n        label: '服務'\n    },\n    {\n        href: '/case-studies',\n        label: '案例研究'\n    },\n    {\n        href: '/blog',\n        label: '博客'\n    },\n    {\n        href: '/contact',\n        label: '聯繫我們'\n    }\n];\nfunction Header() {\n    const [isScrolled, setIsScrolled] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isMobileMenuOpen, setIsMobileMenuOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Header.useEffect\": ()=>{\n            const handleScroll = {\n                \"Header.useEffect.handleScroll\": ()=>{\n                    setIsScrolled(window.scrollY > 20);\n                }\n            }[\"Header.useEffect.handleScroll\"];\n            window.addEventListener('scroll', handleScroll);\n            return ({\n                \"Header.useEffect\": ()=>window.removeEventListener('scroll', handleScroll)\n            })[\"Header.useEffect\"];\n        }\n    }[\"Header.useEffect\"], []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.header, {\n        initial: {\n            y: -100\n        },\n        animate: {\n            y: 0\n        },\n        className: `fixed top-0 left-0 right-0 z-50 transition-all duration-300 ${isScrolled ? 'glass-effect shadow-lg' : 'bg-transparent'}`,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n            className: \"section-padding py-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                            href: \"/\",\n                            className: \"flex items-center space-x-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Brain_Menu_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                    className: \"w-8 h-8 text-purple-500\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/layout/Header.tsx\",\n                                    lineNumber: 40,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-xl font-bold gradient-text\",\n                                    children: \"AI Solutions Hub\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/layout/Header.tsx\",\n                                    lineNumber: 41,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/layout/Header.tsx\",\n                            lineNumber: 39,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"hidden md:flex items-center space-x-8\",\n                            children: [\n                                navLinks.map((link)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: link.href,\n                                        className: \"text-gray-300 hover:text-white transition-colors duration-300\",\n                                        children: link.label\n                                    }, link.href, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/layout/Header.tsx\",\n                                        lineNumber: 46,\n                                        columnNumber: 15\n                                    }, this)),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: \"px-6 py-2 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full text-white font-semibold hover:shadow-lg hover:shadow-purple-500/50 transition-all duration-300\",\n                                    children: \"開始使用\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/layout/Header.tsx\",\n                                    lineNumber: 54,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/layout/Header.tsx\",\n                            lineNumber: 44,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            className: \"md:hidden text-gray-300\",\n                            onClick: ()=>setIsMobileMenuOpen(!isMobileMenuOpen),\n                            children: isMobileMenuOpen ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Brain_Menu_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                size: 24\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/layout/Header.tsx\",\n                                lineNumber: 63,\n                                columnNumber: 33\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Brain_Menu_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                size: 24\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/layout/Header.tsx\",\n                                lineNumber: 63,\n                                columnNumber: 51\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/layout/Header.tsx\",\n                            lineNumber: 59,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/layout/Header.tsx\",\n                    lineNumber: 38,\n                    columnNumber: 9\n                }, this),\n                isMobileMenuOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        y: -20\n                    },\n                    animate: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    className: \"md:hidden mt-4 py-4 border-t border-gray-800\",\n                    children: [\n                        navLinks.map((link)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                href: link.href,\n                                className: \"block py-2 text-gray-300 hover:text-white transition-colors duration-300\",\n                                onClick: ()=>setIsMobileMenuOpen(false),\n                                children: link.label\n                            }, link.href, false, {\n                                fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/layout/Header.tsx\",\n                                lineNumber: 74,\n                                columnNumber: 15\n                            }, this)),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            className: \"mt-4 w-full px-6 py-2 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full text-white font-semibold\",\n                            children: \"開始使用\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/layout/Header.tsx\",\n                            lineNumber: 83,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/layout/Header.tsx\",\n                    lineNumber: 68,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/layout/Header.tsx\",\n            lineNumber: 37,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/layout/Header.tsx\",\n        lineNumber: 30,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/layout/Header.tsx\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fkw%2FDesktop%2FClaude%2Fai-marketing-website%2Fapp%2Fcase-studies%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*******************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fkw%2FDesktop%2FClaude%2Fai-marketing-website%2Fapp%2Fcase-studies%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*******************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/case-studies/page.tsx */ \"(ssr)/./app/case-studies/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGa3clMkZEZXNrdG9wJTJGQ2xhdWRlJTJGYWktbWFya2V0aW5nLXdlYnNpdGUlMkZhcHAlMkZjYXNlLXN0dWRpZXMlMkZwYWdlLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsa0tBQTRHIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCIvVXNlcnMva3cvRGVza3RvcC9DbGF1ZGUvYWktbWFya2V0aW5nLXdlYnNpdGUvYXBwL2Nhc2Utc3R1ZGllcy9wYWdlLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fkw%2FDesktop%2FClaude%2Fai-marketing-website%2Fapp%2Fcase-studies%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fkw%2FDesktop%2FClaude%2Fai-marketing-website%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkw%2FDesktop%2FClaude%2Fai-marketing-website%2Fcomponents%2Flayout%2FFooter.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkw%2FDesktop%2FClaude%2Fai-marketing-website%2Fcomponents%2Flayout%2FHeader.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkw%2FDesktop%2FClaude%2Fai-marketing-website%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fkw%2FDesktop%2FClaude%2Fai-marketing-website%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkw%2FDesktop%2FClaude%2Fai-marketing-website%2Fcomponents%2Flayout%2FFooter.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkw%2FDesktop%2FClaude%2Fai-marketing-website%2Fcomponents%2Flayout%2FHeader.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkw%2FDesktop%2FClaude%2Fai-marketing-website%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/layout/Footer.tsx */ \"(ssr)/./components/layout/Footer.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/layout/Header.tsx */ \"(ssr)/./components/layout/Header.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGa3clMkZEZXNrdG9wJTJGQ2xhdWRlJTJGYWktbWFya2V0aW5nLXdlYnNpdGUlMkZhcHAlMkZnbG9iYWxzLmNzcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjIlMkZVc2VycyUyRmt3JTJGRGVza3RvcCUyRkNsYXVkZSUyRmFpLW1hcmtldGluZy13ZWJzaXRlJTJGY29tcG9uZW50cyUyRmxheW91dCUyRkZvb3Rlci50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlMjJkZWZhdWx0JTIyJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGa3clMkZEZXNrdG9wJTJGQ2xhdWRlJTJGYWktbWFya2V0aW5nLXdlYnNpdGUlMkZjb21wb25lbnRzJTJGbGF5b3V0JTJGSGVhZGVyLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiUyMmRlZmF1bHQlMjIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyJTJGVXNlcnMlMkZrdyUyRkRlc2t0b3AlMkZDbGF1ZGUlMkZhaS1tYXJrZXRpbmctd2Vic2l0ZSUyRm5vZGVfbW9kdWxlcyUyRm5leHQlMkZmb250JTJGZ29vZ2xlJTJGdGFyZ2V0LmNzcyUzRiU3QiU1QyUyMnBhdGglNUMlMjIlM0ElNUMlMjJhcHAlMkZsYXlvdXQudHN4JTVDJTIyJTJDJTVDJTIyaW1wb3J0JTVDJTIyJTNBJTVDJTIySW50ZXIlNUMlMjIlMkMlNUMlMjJhcmd1bWVudHMlNUMlMjIlM0ElNUIlN0IlNUMlMjJzdWJzZXRzJTVDJTIyJTNBJTVCJTVDJTIybGF0aW4lNUMlMjIlNUQlN0QlNUQlMkMlNUMlMjJ2YXJpYWJsZU5hbWUlNUMlMjIlM0ElNUMlMjJpbnRlciU1QyUyMiU3RCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsd0tBQTRJO0FBQzVJO0FBQ0Esd0tBQTRJIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIsIHdlYnBhY2tFeHBvcnRzOiBbXCJkZWZhdWx0XCJdICovIFwiL1VzZXJzL2t3L0Rlc2t0b3AvQ2xhdWRlL2FpLW1hcmtldGluZy13ZWJzaXRlL2NvbXBvbmVudHMvbGF5b3V0L0Zvb3Rlci50c3hcIik7XG47XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiwgd2VicGFja0V4cG9ydHM6IFtcImRlZmF1bHRcIl0gKi8gXCIvVXNlcnMva3cvRGVza3RvcC9DbGF1ZGUvYWktbWFya2V0aW5nLXdlYnNpdGUvY29tcG9uZW50cy9sYXlvdXQvSGVhZGVyLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fkw%2FDesktop%2FClaude%2Fai-marketing-website%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkw%2FDesktop%2FClaude%2Fai-marketing-website%2Fcomponents%2Flayout%2FFooter.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkw%2FDesktop%2FClaude%2Fai-marketing-website%2Fcomponents%2Flayout%2FHeader.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkw%2FDesktop%2FClaude%2Fai-marketing-website%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fkw%2FDesktop%2FClaude%2Fai-marketing-website%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkw%2FDesktop%2FClaude%2Fai-marketing-website%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkw%2FDesktop%2FClaude%2Fai-marketing-website%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkw%2FDesktop%2FClaude%2Fai-marketing-website%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkw%2FDesktop%2FClaude%2Fai-marketing-website%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkw%2FDesktop%2FClaude%2Fai-marketing-website%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkw%2FDesktop%2FClaude%2Fai-marketing-website%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkw%2FDesktop%2FClaude%2Fai-marketing-website%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fkw%2FDesktop%2FClaude%2Fai-marketing-website%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkw%2FDesktop%2FClaude%2Fai-marketing-website%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkw%2FDesktop%2FClaude%2Fai-marketing-website%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkw%2FDesktop%2FClaude%2Fai-marketing-website%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkw%2FDesktop%2FClaude%2Fai-marketing-website%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkw%2FDesktop%2FClaude%2Fai-marketing-website%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkw%2FDesktop%2FClaude%2Fai-marketing-website%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkw%2FDesktop%2FClaude%2Fai-marketing-website%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fkw%2FDesktop%2FClaude%2Fai-marketing-website%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkw%2FDesktop%2FClaude%2Fai-marketing-website%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkw%2FDesktop%2FClaude%2Fai-marketing-website%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkw%2FDesktop%2FClaude%2Fai-marketing-website%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkw%2FDesktop%2FClaude%2Fai-marketing-website%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkw%2FDesktop%2FClaude%2Fai-marketing-website%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkw%2FDesktop%2FClaude%2Fai-marketing-website%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkw%2FDesktop%2FClaude%2Fai-marketing-website%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/framer-motion","vendor-chunks/motion-dom","vendor-chunks/lucide-react","vendor-chunks/motion-utils","vendor-chunks/@swc"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fcase-studies%2Fpage&page=%2Fcase-studies%2Fpage&appPaths=%2Fcase-studies%2Fpage&pagePath=private-next-app-dir%2Fcase-studies%2Fpage.tsx&appDir=%2FUsers%2Fkw%2FDesktop%2FClaude%2Fai-marketing-website%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fkw%2FDesktop%2FClaude%2Fai-marketing-website&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();