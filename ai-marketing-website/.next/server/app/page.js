/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/page";
exports.ids = ["app/page"];
exports.modules = {

/***/ "(rsc)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"5f7d66c3a0a7\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxLQUFVLEVBQUUsRUFBdUIiLCJzb3VyY2VzIjpbIi9Vc2Vycy9rdy9EZXNrdG9wL0NsYXVkZS9haS1tYXJrZXRpbmctd2Vic2l0ZS9hcHAvZ2xvYmFscy5jc3MiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCI1ZjdkNjZjM2EwYTdcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./app/globals.css\n");

/***/ }),

/***/ "(rsc)/./app/layout.tsx":
/*!************************!*\
  !*** ./app/layout.tsx ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"app/layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"app/layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./app/globals.css\");\n/* harmony import */ var _components_layout_Header__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/layout/Header */ \"(rsc)/./components/layout/Header.tsx\");\n/* harmony import */ var _components_layout_Footer__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/layout/Footer */ \"(rsc)/./components/layout/Footer.tsx\");\n\n\n\n\n\nconst metadata = {\n    title: 'AI Solutions Hub - 智能解決方案',\n    description: '提供尖端AI技術服務，助力企業數字化轉型'\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"zh-HK\",\n        className: \"dark\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: `${(next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_4___default().className)} bg-gray-950 text-gray-100 min-h-screen flex flex-col`,\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_Header__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/app/layout.tsx\",\n                    lineNumber: 22,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                    className: \"flex-grow\",\n                    children: children\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/app/layout.tsx\",\n                    lineNumber: 23,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_Footer__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/app/layout.tsx\",\n                    lineNumber: 26,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/app/layout.tsx\",\n            lineNumber: 21,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/app/layout.tsx\",\n        lineNumber: 20,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvbGF5b3V0LnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7O0FBTU1BO0FBSmdCO0FBQ3lCO0FBQ0E7QUFJeEMsTUFBTUcsV0FBcUI7SUFDaENDLE9BQU87SUFDUEMsYUFBYTtBQUNmLEVBQUM7QUFFYyxTQUFTQyxXQUFXLEVBQ2pDQyxRQUFRLEVBR1Q7SUFDQyxxQkFDRSw4REFBQ0M7UUFBS0MsTUFBSztRQUFRQyxXQUFVO2tCQUMzQiw0RUFBQ0M7WUFBS0QsV0FBVyxHQUFHViwySkFBZSxDQUFDLHFEQUFxRCxDQUFDOzs4QkFDeEYsOERBQUNDLGlFQUFNQTs7Ozs7OEJBQ1AsOERBQUNXO29CQUFLRixXQUFVOzhCQUNiSDs7Ozs7OzhCQUVILDhEQUFDTCxpRUFBTUE7Ozs7Ozs7Ozs7Ozs7Ozs7QUFJZiIsInNvdXJjZXMiOlsiL1VzZXJzL2t3L0Rlc2t0b3AvQ2xhdWRlL2FpLW1hcmtldGluZy13ZWJzaXRlL2FwcC9sYXlvdXQudHN4Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB0eXBlIHsgTWV0YWRhdGEgfSBmcm9tICduZXh0J1xuaW1wb3J0IHsgSW50ZXIgfSBmcm9tICduZXh0L2ZvbnQvZ29vZ2xlJ1xuaW1wb3J0ICcuL2dsb2JhbHMuY3NzJ1xuaW1wb3J0IEhlYWRlciBmcm9tICdAL2NvbXBvbmVudHMvbGF5b3V0L0hlYWRlcidcbmltcG9ydCBGb290ZXIgZnJvbSAnQC9jb21wb25lbnRzL2xheW91dC9Gb290ZXInXG5cbmNvbnN0IGludGVyID0gSW50ZXIoeyBzdWJzZXRzOiBbJ2xhdGluJ10gfSlcblxuZXhwb3J0IGNvbnN0IG1ldGFkYXRhOiBNZXRhZGF0YSA9IHtcbiAgdGl0bGU6ICdBSSBTb2x1dGlvbnMgSHViIC0g5pm66IO96Kej5rG65pa55qGIJyxcbiAgZGVzY3JpcHRpb246ICfmj5DkvpvlsJbnq69BSeaKgOihk+acjeWLme+8jOWKqeWKm+S8gealreaVuOWtl+WMlui9ieWeiycsXG59XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIFJvb3RMYXlvdXQoe1xuICBjaGlsZHJlbixcbn06IHtcbiAgY2hpbGRyZW46IFJlYWN0LlJlYWN0Tm9kZVxufSkge1xuICByZXR1cm4gKFxuICAgIDxodG1sIGxhbmc9XCJ6aC1IS1wiIGNsYXNzTmFtZT1cImRhcmtcIj5cbiAgICAgIDxib2R5IGNsYXNzTmFtZT17YCR7aW50ZXIuY2xhc3NOYW1lfSBiZy1ncmF5LTk1MCB0ZXh0LWdyYXktMTAwIG1pbi1oLXNjcmVlbiBmbGV4IGZsZXgtY29sYH0+XG4gICAgICAgIDxIZWFkZXIgLz5cbiAgICAgICAgPG1haW4gY2xhc3NOYW1lPVwiZmxleC1ncm93XCI+XG4gICAgICAgICAge2NoaWxkcmVufVxuICAgICAgICA8L21haW4+XG4gICAgICAgIDxGb290ZXIgLz5cbiAgICAgIDwvYm9keT5cbiAgICA8L2h0bWw+XG4gIClcbn0iXSwibmFtZXMiOlsiaW50ZXIiLCJIZWFkZXIiLCJGb290ZXIiLCJtZXRhZGF0YSIsInRpdGxlIiwiZGVzY3JpcHRpb24iLCJSb290TGF5b3V0IiwiY2hpbGRyZW4iLCJodG1sIiwibGFuZyIsImNsYXNzTmFtZSIsImJvZHkiLCJtYWluIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./app/page.tsx":
/*!**********************!*\
  !*** ./app/page.tsx ***!
  \**********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"/Users/<USER>/Desktop/Claude/ai-marketing-website/app/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"/Users/<USER>/Desktop/Claude/ai-marketing-website/app/page.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./components/layout/Footer.tsx":
/*!**************************************!*\
  !*** ./components/layout/Footer.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/layout/Footer.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/layout/Footer.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./components/layout/Header.tsx":
/*!**************************************!*\
  !*** ./components/layout/Header.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/layout/Header.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/layout/Header.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2FUsers%2Fkw%2FDesktop%2FClaude%2Fai-marketing-website%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fkw%2FDesktop%2FClaude%2Fai-marketing-website&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2FUsers%2Fkw%2FDesktop%2FClaude%2Fai-marketing-website%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fkw%2FDesktop%2FClaude%2Fai-marketing-website&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?cc4a\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/layout.tsx */ \"(rsc)/./app/layout.tsx\"));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\nconst page4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/page.tsx */ \"(rsc)/./app/page.tsx\"));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page4, \"/Users/<USER>/Desktop/Claude/ai-marketing-website/app/page.tsx\"],\n          \n        }]\n      },\n        {\n        'layout': [module0, \"/Users/<USER>/Desktop/Claude/ai-marketing-website/app/layout.tsx\"],\n'not-found': [module1, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module2, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module3, \"next/dist/client/components/unauthorized-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"/Users/<USER>/Desktop/Claude/ai-marketing-website/app/page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/page\",\n        pathname: \"/\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2FUsers%2Fkw%2FDesktop%2FClaude%2Fai-marketing-website%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fkw%2FDesktop%2FClaude%2Fai-marketing-website&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fkw%2FDesktop%2FClaude%2Fai-marketing-website%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkw%2FDesktop%2FClaude%2Fai-marketing-website%2Fcomponents%2Flayout%2FFooter.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkw%2FDesktop%2FClaude%2Fai-marketing-website%2Fcomponents%2Flayout%2FHeader.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkw%2FDesktop%2FClaude%2Fai-marketing-website%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fkw%2FDesktop%2FClaude%2Fai-marketing-website%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkw%2FDesktop%2FClaude%2Fai-marketing-website%2Fcomponents%2Flayout%2FFooter.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkw%2FDesktop%2FClaude%2Fai-marketing-website%2Fcomponents%2Flayout%2FHeader.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkw%2FDesktop%2FClaude%2Fai-marketing-website%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/layout/Footer.tsx */ \"(rsc)/./components/layout/Footer.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/layout/Header.tsx */ \"(rsc)/./components/layout/Header.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGa3clMkZEZXNrdG9wJTJGQ2xhdWRlJTJGYWktbWFya2V0aW5nLXdlYnNpdGUlMkZhcHAlMkZnbG9iYWxzLmNzcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjIlMkZVc2VycyUyRmt3JTJGRGVza3RvcCUyRkNsYXVkZSUyRmFpLW1hcmtldGluZy13ZWJzaXRlJTJGY29tcG9uZW50cyUyRmxheW91dCUyRkZvb3Rlci50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlMjJkZWZhdWx0JTIyJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGa3clMkZEZXNrdG9wJTJGQ2xhdWRlJTJGYWktbWFya2V0aW5nLXdlYnNpdGUlMkZjb21wb25lbnRzJTJGbGF5b3V0JTJGSGVhZGVyLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiUyMmRlZmF1bHQlMjIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyJTJGVXNlcnMlMkZrdyUyRkRlc2t0b3AlMkZDbGF1ZGUlMkZhaS1tYXJrZXRpbmctd2Vic2l0ZSUyRm5vZGVfbW9kdWxlcyUyRm5leHQlMkZmb250JTJGZ29vZ2xlJTJGdGFyZ2V0LmNzcyUzRiU3QiU1QyUyMnBhdGglNUMlMjIlM0ElNUMlMjJhcHAlMkZsYXlvdXQudHN4JTVDJTIyJTJDJTVDJTIyaW1wb3J0JTVDJTIyJTNBJTVDJTIySW50ZXIlNUMlMjIlMkMlNUMlMjJhcmd1bWVudHMlNUMlMjIlM0ElNUIlN0IlNUMlMjJzdWJzZXRzJTVDJTIyJTNBJTVCJTVDJTIybGF0aW4lNUMlMjIlNUQlN0QlNUQlMkMlNUMlMjJ2YXJpYWJsZU5hbWUlNUMlMjIlM0ElNUMlMjJpbnRlciU1QyUyMiU3RCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsd0tBQTRJO0FBQzVJO0FBQ0Esd0tBQTRJIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIsIHdlYnBhY2tFeHBvcnRzOiBbXCJkZWZhdWx0XCJdICovIFwiL1VzZXJzL2t3L0Rlc2t0b3AvQ2xhdWRlL2FpLW1hcmtldGluZy13ZWJzaXRlL2NvbXBvbmVudHMvbGF5b3V0L0Zvb3Rlci50c3hcIik7XG47XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiwgd2VicGFja0V4cG9ydHM6IFtcImRlZmF1bHRcIl0gKi8gXCIvVXNlcnMva3cvRGVza3RvcC9DbGF1ZGUvYWktbWFya2V0aW5nLXdlYnNpdGUvY29tcG9uZW50cy9sYXlvdXQvSGVhZGVyLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fkw%2FDesktop%2FClaude%2Fai-marketing-website%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkw%2FDesktop%2FClaude%2Fai-marketing-website%2Fcomponents%2Flayout%2FFooter.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkw%2FDesktop%2FClaude%2Fai-marketing-website%2Fcomponents%2Flayout%2FHeader.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkw%2FDesktop%2FClaude%2Fai-marketing-website%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fkw%2FDesktop%2FClaude%2Fai-marketing-website%2Fapp%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fkw%2FDesktop%2FClaude%2Fai-marketing-website%2Fapp%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/page.tsx */ \"(rsc)/./app/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGa3clMkZEZXNrdG9wJTJGQ2xhdWRlJTJGYWktbWFya2V0aW5nLXdlYnNpdGUlMkZhcHAlMkZwYWdlLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsd0lBQStGIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCIvVXNlcnMva3cvRGVza3RvcC9DbGF1ZGUvYWktbWFya2V0aW5nLXdlYnNpdGUvYXBwL3BhZ2UudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fkw%2FDesktop%2FClaude%2Fai-marketing-website%2Fapp%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fkw%2FDesktop%2FClaude%2Fai-marketing-website%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkw%2FDesktop%2FClaude%2Fai-marketing-website%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkw%2FDesktop%2FClaude%2Fai-marketing-website%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkw%2FDesktop%2FClaude%2Fai-marketing-website%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkw%2FDesktop%2FClaude%2Fai-marketing-website%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkw%2FDesktop%2FClaude%2Fai-marketing-website%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkw%2FDesktop%2FClaude%2Fai-marketing-website%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkw%2FDesktop%2FClaude%2Fai-marketing-website%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fkw%2FDesktop%2FClaude%2Fai-marketing-website%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkw%2FDesktop%2FClaude%2Fai-marketing-website%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkw%2FDesktop%2FClaude%2Fai-marketing-website%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkw%2FDesktop%2FClaude%2Fai-marketing-website%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkw%2FDesktop%2FClaude%2Fai-marketing-website%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkw%2FDesktop%2FClaude%2Fai-marketing-website%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkw%2FDesktop%2FClaude%2Fai-marketing-website%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkw%2FDesktop%2FClaude%2Fai-marketing-website%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fkw%2FDesktop%2FClaude%2Fai-marketing-website%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkw%2FDesktop%2FClaude%2Fai-marketing-website%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkw%2FDesktop%2FClaude%2Fai-marketing-website%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkw%2FDesktop%2FClaude%2Fai-marketing-website%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkw%2FDesktop%2FClaude%2Fai-marketing-website%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkw%2FDesktop%2FClaude%2Fai-marketing-website%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkw%2FDesktop%2FClaude%2Fai-marketing-website%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkw%2FDesktop%2FClaude%2Fai-marketing-website%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./app/page.tsx":
/*!**********************!*\
  !*** ./app/page.tsx ***!
  \**********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_Hero__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/Hero */ \"(ssr)/./components/Hero.tsx\");\n/* harmony import */ var _components_Features__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/Features */ \"(ssr)/./components/Features.tsx\");\n/* harmony import */ var _components_Services__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/Services */ \"(ssr)/./components/Services.tsx\");\n/* harmony import */ var _components_Stats__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/Stats */ \"(ssr)/./components/Stats.tsx\");\n/* harmony import */ var _components_CaseStudies__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/CaseStudies */ \"(ssr)/./components/CaseStudies.tsx\");\n/* harmony import */ var _components_Testimonials__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/Testimonials */ \"(ssr)/./components/Testimonials.tsx\");\n/* harmony import */ var _components_ui_CTASection__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/CTASection */ \"(ssr)/./components/ui/CTASection.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\nfunction Home() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"overflow-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Hero__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {}, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/app/page.tsx\",\n                lineNumber: 14,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Features__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/app/page.tsx\",\n                lineNumber: 15,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Services__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/app/page.tsx\",\n                lineNumber: 16,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Stats__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/app/page.tsx\",\n                lineNumber: 17,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_CaseStudies__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {}, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/app/page.tsx\",\n                lineNumber: 18,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Testimonials__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {}, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/app/page.tsx\",\n                lineNumber: 19,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_CTASection__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {}, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/app/page.tsx\",\n                lineNumber: 20,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/app/page.tsx\",\n        lineNumber: 13,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9hcHAvcGFnZS50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7QUFFb0M7QUFDUTtBQUNBO0FBQ047QUFDWTtBQUNFO0FBQ0Q7QUFFcEMsU0FBU087SUFDdEIscUJBQ0UsOERBQUNDO1FBQUlDLFdBQVU7OzBCQUNiLDhEQUFDVCx3REFBSUE7Ozs7OzBCQUNMLDhEQUFDQyw0REFBUUE7Ozs7OzBCQUNULDhEQUFDQyw0REFBUUE7Ozs7OzBCQUNULDhEQUFDQyx5REFBS0E7Ozs7OzBCQUNOLDhEQUFDQywrREFBV0E7Ozs7OzBCQUNaLDhEQUFDQyxnRUFBWUE7Ozs7OzBCQUNiLDhEQUFDQyxpRUFBVUE7Ozs7Ozs7Ozs7O0FBR2pCIiwic291cmNlcyI6WyIvVXNlcnMva3cvRGVza3RvcC9DbGF1ZGUvYWktbWFya2V0aW5nLXdlYnNpdGUvYXBwL3BhZ2UudHN4Il0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50J1xuXG5pbXBvcnQgSGVybyBmcm9tICdAL2NvbXBvbmVudHMvSGVybydcbmltcG9ydCBGZWF0dXJlcyBmcm9tICdAL2NvbXBvbmVudHMvRmVhdHVyZXMnXG5pbXBvcnQgU2VydmljZXMgZnJvbSAnQC9jb21wb25lbnRzL1NlcnZpY2VzJ1xuaW1wb3J0IFN0YXRzIGZyb20gJ0AvY29tcG9uZW50cy9TdGF0cydcbmltcG9ydCBDYXNlU3R1ZGllcyBmcm9tICdAL2NvbXBvbmVudHMvQ2FzZVN0dWRpZXMnXG5pbXBvcnQgVGVzdGltb25pYWxzIGZyb20gJ0AvY29tcG9uZW50cy9UZXN0aW1vbmlhbHMnXG5pbXBvcnQgQ1RBU2VjdGlvbiBmcm9tICdAL2NvbXBvbmVudHMvdWkvQ1RBU2VjdGlvbidcblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gSG9tZSgpIHtcbiAgcmV0dXJuIChcbiAgICA8ZGl2IGNsYXNzTmFtZT1cIm92ZXJmbG93LWhpZGRlblwiPlxuICAgICAgPEhlcm8gLz5cbiAgICAgIDxGZWF0dXJlcyAvPlxuICAgICAgPFNlcnZpY2VzIC8+XG4gICAgICA8U3RhdHMgLz5cbiAgICAgIDxDYXNlU3R1ZGllcyAvPlxuICAgICAgPFRlc3RpbW9uaWFscyAvPlxuICAgICAgPENUQVNlY3Rpb24gLz5cbiAgICA8L2Rpdj5cbiAgKVxufSJdLCJuYW1lcyI6WyJIZXJvIiwiRmVhdHVyZXMiLCJTZXJ2aWNlcyIsIlN0YXRzIiwiQ2FzZVN0dWRpZXMiLCJUZXN0aW1vbmlhbHMiLCJDVEFTZWN0aW9uIiwiSG9tZSIsImRpdiIsImNsYXNzTmFtZSJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./app/page.tsx\n");

/***/ }),

/***/ "(ssr)/./components/CaseStudies.tsx":
/*!************************************!*\
  !*** ./components/CaseStudies.tsx ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ CaseStudies)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _barrel_optimize_names_ArrowUpRight_BarChart3_Building2_ShoppingCart_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowUpRight,BarChart3,Building2,ShoppingCart!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/shopping-cart.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowUpRight_BarChart3_Building2_ShoppingCart_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowUpRight,BarChart3,Building2,ShoppingCart!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/chart-column.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowUpRight_BarChart3_Building2_ShoppingCart_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowUpRight,BarChart3,Building2,ShoppingCart!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/building-2.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowUpRight_BarChart3_Building2_ShoppingCart_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowUpRight,BarChart3,Building2,ShoppingCart!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/arrow-up-right.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nconst caseStudies = [\n    {\n        icon: _barrel_optimize_names_ArrowUpRight_BarChart3_Building2_ShoppingCart_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"],\n        title: '電商智能推薦系統',\n        client: '大型零售集團',\n        description: '通過AI驅動的個性化推薦引擎，提升轉化率45%',\n        results: [\n            '銷售額增長45%',\n            '用戶參與度提升60%',\n            '退貨率降低25%'\n        ],\n        image: '/images/case-study-1.jpg'\n    },\n    {\n        icon: _barrel_optimize_names_ArrowUpRight_BarChart3_Building2_ShoppingCart_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n        title: '金融風險評估平台',\n        client: '國際投資銀行',\n        description: '實時風險監控和預測，降低投資風險30%',\n        results: [\n            '風險預測準確率95%',\n            '處理速度提升10倍',\n            '合規成本降低40%'\n        ],\n        image: '/images/case-study-2.jpg'\n    },\n    {\n        icon: _barrel_optimize_names_ArrowUpRight_BarChart3_Building2_ShoppingCart_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        title: '智慧城市管理系統',\n        client: '政府機構',\n        description: '整合城市數據，優化資源分配和公共服務',\n        results: [\n            '能源效率提升35%',\n            '交通擁堵減少20%',\n            '市民滿意度提升50%'\n        ],\n        image: '/images/case-study-3.jpg'\n    }\n];\nfunction CaseStudies() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        className: \"py-20 section-padding\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-7xl mx-auto\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        y: 20\n                    },\n                    whileInView: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    viewport: {\n                        once: true\n                    },\n                    transition: {\n                        duration: 0.5\n                    },\n                    className: \"text-center mb-16\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-4xl md:text-5xl font-bold mb-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"gradient-text\",\n                                children: \"成功案例\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/CaseStudies.tsx\",\n                                lineNumber: 47,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/CaseStudies.tsx\",\n                            lineNumber: 46,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xl text-gray-300 max-w-3xl mx-auto\",\n                            children: \"看看我們如何幫助客戶實現數字化轉型\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/CaseStudies.tsx\",\n                            lineNumber: 49,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/CaseStudies.tsx\",\n                    lineNumber: 39,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 lg:grid-cols-3 gap-8\",\n                    children: caseStudies.map((study, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                            initial: {\n                                opacity: 0,\n                                y: 20\n                            },\n                            whileInView: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            viewport: {\n                                once: true\n                            },\n                            transition: {\n                                duration: 0.5,\n                                delay: index * 0.1\n                            },\n                            className: \"glass-effect rounded-2xl overflow-hidden card-hover group\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative h-48 bg-gradient-to-br from-purple-900/50 to-pink-900/50 flex items-center justify-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(study.icon, {\n                                            className: \"w-16 h-16 text-white/20\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/CaseStudies.tsx\",\n                                            lineNumber: 65,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute inset-0 bg-gradient-to-t from-gray-900 to-transparent\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/CaseStudies.tsx\",\n                                            lineNumber: 66,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/CaseStudies.tsx\",\n                                    lineNumber: 64,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm text-purple-400 mb-2\",\n                                            children: study.client\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/CaseStudies.tsx\",\n                                            lineNumber: 70,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-xl font-semibold mb-3 text-white\",\n                                            children: study.title\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/CaseStudies.tsx\",\n                                            lineNumber: 71,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-400 mb-4\",\n                                            children: study.description\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/CaseStudies.tsx\",\n                                            lineNumber: 72,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-2 mb-4\",\n                                            children: study.results.map((result, idx)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center text-sm text-gray-300\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"w-1.5 h-1.5 bg-green-500 rounded-full mr-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/CaseStudies.tsx\",\n                                                            lineNumber: 77,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        result\n                                                    ]\n                                                }, idx, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/CaseStudies.tsx\",\n                                                    lineNumber: 76,\n                                                    columnNumber: 21\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/CaseStudies.tsx\",\n                                            lineNumber: 74,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                            href: \"/case-studies\",\n                                            className: \"inline-flex items-center text-purple-400 hover:text-purple-300 transition-colors group\",\n                                            children: [\n                                                \"查看詳情\",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowUpRight_BarChart3_Building2_ShoppingCart_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                    className: \"w-4 h-4 ml-1 group-hover:translate-x-1 group-hover:-translate-y-1 transition-transform\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/CaseStudies.tsx\",\n                                                    lineNumber: 88,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/CaseStudies.tsx\",\n                                            lineNumber: 83,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/CaseStudies.tsx\",\n                                    lineNumber: 69,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, index, true, {\n                            fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/CaseStudies.tsx\",\n                            lineNumber: 56,\n                            columnNumber: 13\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/CaseStudies.tsx\",\n                    lineNumber: 54,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/CaseStudies.tsx\",\n            lineNumber: 38,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/CaseStudies.tsx\",\n        lineNumber: 37,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/CaseStudies.tsx\n");

/***/ }),

/***/ "(ssr)/./components/Features.tsx":
/*!*********************************!*\
  !*** ./components/Features.tsx ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Features)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _barrel_optimize_names_Brain_Layers_Shield_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! __barrel_optimize__?names=Brain,Layers,Shield,TrendingUp,Users,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/brain.js\");\n/* harmony import */ var _barrel_optimize_names_Brain_Layers_Shield_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Brain,Layers,Shield,TrendingUp,Users,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _barrel_optimize_names_Brain_Layers_Shield_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Brain,Layers,Shield,TrendingUp,Users,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_Brain_Layers_Shield_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Brain,Layers,Shield,TrendingUp,Users,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/layers.js\");\n/* harmony import */ var _barrel_optimize_names_Brain_Layers_Shield_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Brain,Layers,Shield,TrendingUp,Users,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_Brain_Layers_Shield_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Brain,Layers,Shield,TrendingUp,Users,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nconst features = [\n    {\n        icon: _barrel_optimize_names_Brain_Layers_Shield_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_1__[\"default\"],\n        title: '深度學習',\n        description: '運用最先進的神經網絡技術，提供精準的預測和分析能力'\n    },\n    {\n        icon: _barrel_optimize_names_Brain_Layers_Shield_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"],\n        title: '實時處理',\n        description: '高效能的數據處理能力，毫秒級響應時間，滿足即時需求'\n    },\n    {\n        icon: _barrel_optimize_names_Brain_Layers_Shield_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n        title: '安全可靠',\n        description: '企業級安全標準，端到端加密，確保數據隱私和安全'\n    },\n    {\n        icon: _barrel_optimize_names_Brain_Layers_Shield_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        title: '靈活集成',\n        description: '無縫整合現有系統，提供豐富的API接口和SDK支持'\n    },\n    {\n        icon: _barrel_optimize_names_Brain_Layers_Shield_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n        title: '專業團隊',\n        description: '匯聚頂尖AI專家和工程師，提供全方位技術支持'\n    },\n    {\n        icon: _barrel_optimize_names_Brain_Layers_Shield_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n        title: '持續優化',\n        description: '基於反饋持續改進模型，確保解決方案始終保持最佳性能'\n    }\n];\nfunction Features() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        className: \"py-20 section-padding\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-7xl mx-auto\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        y: 20\n                    },\n                    whileInView: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    viewport: {\n                        once: true\n                    },\n                    transition: {\n                        duration: 0.5\n                    },\n                    className: \"text-center mb-16\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-4xl md:text-5xl font-bold mb-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"gradient-text\",\n                                children: \"核心優勢\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Features.tsx\",\n                                lineNumber: 51,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Features.tsx\",\n                            lineNumber: 50,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xl text-gray-300 max-w-3xl mx-auto\",\n                            children: \"我們提供全面的AI解決方案，助力企業在數字時代保持競爭優勢\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Features.tsx\",\n                            lineNumber: 53,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Features.tsx\",\n                    lineNumber: 43,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\",\n                    children: features.map((feature, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                            initial: {\n                                opacity: 0,\n                                y: 20\n                            },\n                            whileInView: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            viewport: {\n                                once: true\n                            },\n                            transition: {\n                                duration: 0.5,\n                                delay: index * 0.1\n                            },\n                            className: \"glass-effect p-8 rounded-2xl card-hover group\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-12 h-12 bg-gradient-to-br from-purple-500 to-pink-500 rounded-lg flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(feature.icon, {\n                                        className: \"w-6 h-6 text-white\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Features.tsx\",\n                                        lineNumber: 69,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Features.tsx\",\n                                    lineNumber: 68,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-xl font-semibold mb-2 text-white\",\n                                    children: feature.title\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Features.tsx\",\n                                    lineNumber: 71,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-400\",\n                                    children: feature.description\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Features.tsx\",\n                                    lineNumber: 72,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, index, true, {\n                            fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Features.tsx\",\n                            lineNumber: 60,\n                            columnNumber: 13\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Features.tsx\",\n                    lineNumber: 58,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Features.tsx\",\n            lineNumber: 42,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Features.tsx\",\n        lineNumber: 41,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/Features.tsx\n");

/***/ }),

/***/ "(ssr)/./components/Hero.tsx":
/*!*****************************!*\
  !*** ./components/Hero.tsx ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Hero)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Sparkles!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/sparkles.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Sparkles!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/arrow-right.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nfunction Hero() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        className: \"relative min-h-screen flex items-center justify-center section-padding pt-24\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 bg-gradient-to-br from-purple-900/20 via-transparent to-pink-900/20\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Hero.tsx\",\n                lineNumber: 10,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative z-10 text-center max-w-5xl mx-auto\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            y: 20\n                        },\n                        animate: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        transition: {\n                            duration: 0.5\n                        },\n                        className: \"inline-flex items-center space-x-2 px-4 py-2 glass-effect rounded-full mb-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                className: \"w-4 h-4 text-purple-400\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Hero.tsx\",\n                                lineNumber: 19,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-sm text-gray-300\",\n                                children: \"引領AI技術創新\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Hero.tsx\",\n                                lineNumber: 20,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Hero.tsx\",\n                        lineNumber: 13,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.h1, {\n                        initial: {\n                            opacity: 0,\n                            y: 20\n                        },\n                        animate: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        transition: {\n                            duration: 0.5,\n                            delay: 0.1\n                        },\n                        className: \"text-5xl md:text-7xl font-bold mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"gradient-text\",\n                                children: \"賦能未來\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Hero.tsx\",\n                                lineNumber: 29,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Hero.tsx\",\n                                lineNumber: 30,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-white\",\n                                children: \"智能解決方案\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Hero.tsx\",\n                                lineNumber: 31,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Hero.tsx\",\n                        lineNumber: 23,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.p, {\n                        initial: {\n                            opacity: 0,\n                            y: 20\n                        },\n                        animate: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        transition: {\n                            duration: 0.5,\n                            delay: 0.2\n                        },\n                        className: \"text-xl md:text-2xl text-gray-300 mb-8 max-w-3xl mx-auto\",\n                        children: \"運用尖端人工智能技術，為您的企業打造定制化解決方案， 助力數字化轉型，開創無限可能\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Hero.tsx\",\n                        lineNumber: 34,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            y: 20\n                        },\n                        animate: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        transition: {\n                            duration: 0.5,\n                            delay: 0.3\n                        },\n                        className: \"flex flex-col sm:flex-row gap-4 justify-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                href: \"/contact\",\n                                className: \"group px-8 py-4 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full text-white font-semibold hover:shadow-lg hover:shadow-purple-500/50 transition-all duration-300 flex items-center justify-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"立即開始\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Hero.tsx\",\n                                        lineNumber: 54,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                        className: \"w-4 h-4 group-hover:translate-x-1 transition-transform\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Hero.tsx\",\n                                        lineNumber: 55,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Hero.tsx\",\n                                lineNumber: 50,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                href: \"/case-studies\",\n                                className: \"px-8 py-4 glass-effect rounded-full text-white font-semibold hover:bg-gray-800/50 transition-all duration-300\",\n                                children: \"查看案例\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Hero.tsx\",\n                                lineNumber: 57,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Hero.tsx\",\n                        lineNumber: 44,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                        initial: {\n                            opacity: 0\n                        },\n                        animate: {\n                            opacity: 1\n                        },\n                        transition: {\n                            duration: 1,\n                            delay: 0.5\n                        },\n                        className: \"mt-16 grid grid-cols-2 md:grid-cols-4 gap-8\",\n                        children: [\n                            {\n                                label: '成功案例',\n                                value: '500+'\n                            },\n                            {\n                                label: '客戶滿意度',\n                                value: '99%'\n                            },\n                            {\n                                label: '節省成本',\n                                value: '40%'\n                            },\n                            {\n                                label: '效率提升',\n                                value: '3x'\n                            }\n                        ].map((stat, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-3xl font-bold gradient-text\",\n                                        children: stat.value\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Hero.tsx\",\n                                        lineNumber: 78,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-400 text-sm mt-1\",\n                                        children: stat.label\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Hero.tsx\",\n                                        lineNumber: 79,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, index, true, {\n                                fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Hero.tsx\",\n                                lineNumber: 77,\n                                columnNumber: 13\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Hero.tsx\",\n                        lineNumber: 65,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Hero.tsx\",\n                lineNumber: 12,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute bottom-0 left-0 right-0 h-32 bg-gradient-to-t from-gray-950 to-transparent\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Hero.tsx\",\n                lineNumber: 85,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Hero.tsx\",\n        lineNumber: 9,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/Hero.tsx\n");

/***/ }),

/***/ "(ssr)/./components/Services.tsx":
/*!*********************************!*\
  !*** ./components/Services.tsx ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Services)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _barrel_optimize_names_Bot_Database_Eye_MessageSquare_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Database,Eye,MessageSquare!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/message-square.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Database_Eye_MessageSquare_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Database,Eye,MessageSquare!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Database_Eye_MessageSquare_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Database,Eye,MessageSquare!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/database.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Database_Eye_MessageSquare_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Database,Eye,MessageSquare!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/bot.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nconst services = [\n    {\n        icon: _barrel_optimize_names_Bot_Database_Eye_MessageSquare_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"],\n        title: '自然語言處理',\n        description: '文本分析、情感識別、智能對話系統',\n        features: [\n            '多語言支持',\n            '情感分析',\n            '意圖識別',\n            '文本生成'\n        ],\n        link: '/services#nlp'\n    },\n    {\n        icon: _barrel_optimize_names_Bot_Database_Eye_MessageSquare_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n        title: '計算機視覺',\n        description: '圖像識別、視頻分析、物體檢測',\n        features: [\n            '人臉識別',\n            '物體追蹤',\n            '場景理解',\n            '視頻分析'\n        ],\n        link: '/services#cv'\n    },\n    {\n        icon: _barrel_optimize_names_Bot_Database_Eye_MessageSquare_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        title: '大數據分析',\n        description: '數據挖掘、預測分析、商業智能',\n        features: [\n            '預測建模',\n            '異常檢測',\n            '數據可視化',\n            '實時分析'\n        ],\n        link: '/services#analytics'\n    },\n    {\n        icon: _barrel_optimize_names_Bot_Database_Eye_MessageSquare_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n        title: 'AI 自動化',\n        description: '流程自動化、智能決策、機器人流程自動化',\n        features: [\n            '工作流優化',\n            '智能調度',\n            '自動化測試',\n            '決策支持'\n        ],\n        link: '/services#automation'\n    }\n];\nfunction Services() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        className: \"py-20 section-padding bg-gradient-to-b from-transparent via-purple-900/10 to-transparent\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-7xl mx-auto\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        y: 20\n                    },\n                    whileInView: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    viewport: {\n                        once: true\n                    },\n                    transition: {\n                        duration: 0.5\n                    },\n                    className: \"text-center mb-16\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-4xl md:text-5xl font-bold mb-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"gradient-text\",\n                                children: \"專業服務\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Services.tsx\",\n                                lineNumber: 50,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Services.tsx\",\n                            lineNumber: 49,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xl text-gray-300 max-w-3xl mx-auto\",\n                            children: \"從概念到實施，我們提供端到端的AI解決方案\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Services.tsx\",\n                            lineNumber: 52,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Services.tsx\",\n                    lineNumber: 42,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-2 gap-8\",\n                    children: services.map((service, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                            initial: {\n                                opacity: 0,\n                                x: index % 2 === 0 ? -20 : 20\n                            },\n                            whileInView: {\n                                opacity: 1,\n                                x: 0\n                            },\n                            viewport: {\n                                once: true\n                            },\n                            transition: {\n                                duration: 0.5,\n                                delay: index * 0.1\n                            },\n                            className: \"glass-effect p-8 rounded-2xl card-hover group\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-start space-x-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-14 h-14 bg-gradient-to-br from-purple-500 to-pink-500 rounded-xl flex items-center justify-center flex-shrink-0 group-hover:scale-110 transition-transform duration-300\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(service.icon, {\n                                            className: \"w-7 h-7 text-white\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Services.tsx\",\n                                            lineNumber: 69,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Services.tsx\",\n                                        lineNumber: 68,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-2xl font-semibold mb-2 text-white\",\n                                                children: service.title\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Services.tsx\",\n                                                lineNumber: 72,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-400 mb-4\",\n                                                children: service.description\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Services.tsx\",\n                                                lineNumber: 73,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                className: \"space-y-2 mb-4\",\n                                                children: service.features.map((feature, idx)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        className: \"flex items-center text-gray-300 text-sm\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"w-1.5 h-1.5 bg-purple-500 rounded-full mr-2\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Services.tsx\",\n                                                                lineNumber: 77,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            feature\n                                                        ]\n                                                    }, idx, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Services.tsx\",\n                                                        lineNumber: 76,\n                                                        columnNumber: 23\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Services.tsx\",\n                                                lineNumber: 74,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                href: service.link,\n                                                className: \"inline-flex items-center text-purple-400 hover:text-purple-300 transition-colors\",\n                                                children: \"了解更多 →\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Services.tsx\",\n                                                lineNumber: 82,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Services.tsx\",\n                                        lineNumber: 71,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Services.tsx\",\n                                lineNumber: 67,\n                                columnNumber: 15\n                            }, this)\n                        }, index, false, {\n                            fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Services.tsx\",\n                            lineNumber: 59,\n                            columnNumber: 13\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Services.tsx\",\n                    lineNumber: 57,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Services.tsx\",\n            lineNumber: 41,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Services.tsx\",\n        lineNumber: 40,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/Services.tsx\n");

/***/ }),

/***/ "(ssr)/./components/Stats.tsx":
/*!******************************!*\
  !*** ./components/Stats.tsx ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Stats)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/utils/use-in-view.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nconst stats = [\n    {\n        label: '服務客戶',\n        value: 500,\n        suffix: '+'\n    },\n    {\n        label: '完成項目',\n        value: 1200,\n        suffix: '+'\n    },\n    {\n        label: '團隊成員',\n        value: 150,\n        suffix: '+'\n    },\n    {\n        label: '客戶滿意度',\n        value: 99,\n        suffix: '%'\n    }\n];\nfunction Counter({ end, suffix }) {\n    const [count, setCount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const ref = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const isInView = (0,framer_motion__WEBPACK_IMPORTED_MODULE_2__.useInView)(ref, {\n        once: true\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Counter.useEffect\": ()=>{\n            if (isInView) {\n                let startTime;\n                const duration = 2000;\n                const updateCount = {\n                    \"Counter.useEffect.updateCount\": (timestamp)=>{\n                        if (!startTime) startTime = timestamp;\n                        const progress = (timestamp - startTime) / duration;\n                        if (progress < 1) {\n                            setCount(Math.floor(end * progress));\n                            requestAnimationFrame(updateCount);\n                        } else {\n                            setCount(end);\n                        }\n                    }\n                }[\"Counter.useEffect.updateCount\"];\n                requestAnimationFrame(updateCount);\n            }\n        }\n    }[\"Counter.useEffect\"], [\n        isInView,\n        end\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: \"text-5xl md:text-6xl font-bold gradient-text\",\n        children: [\n            count,\n            suffix\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Stats.tsx\",\n        lineNumber: 41,\n        columnNumber: 5\n    }, this);\n}\nfunction Stats() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        className: \"py-20 section-padding\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-7xl mx-auto\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        y: 20\n                    },\n                    whileInView: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    viewport: {\n                        once: true\n                    },\n                    transition: {\n                        duration: 0.5\n                    },\n                    className: \"text-center mb-16\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-4xl md:text-5xl font-bold mb-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-white\",\n                                children: \"用數據說話\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Stats.tsx\",\n                                lineNumber: 59,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Stats.tsx\",\n                            lineNumber: 58,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xl text-gray-300 max-w-3xl mx-auto\",\n                            children: \"我們的成績反映了客戶的信任和我們的專業能力\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Stats.tsx\",\n                            lineNumber: 61,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Stats.tsx\",\n                    lineNumber: 51,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-2 md:grid-cols-4 gap-8\",\n                    children: stats.map((stat, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                            initial: {\n                                opacity: 0,\n                                y: 20\n                            },\n                            whileInView: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            viewport: {\n                                once: true\n                            },\n                            transition: {\n                                duration: 0.5,\n                                delay: index * 0.1\n                            },\n                            className: \"text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Counter, {\n                                    end: stat.value,\n                                    suffix: stat.suffix\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Stats.tsx\",\n                                    lineNumber: 76,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-gray-400 mt-2\",\n                                    children: stat.label\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Stats.tsx\",\n                                    lineNumber: 77,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, index, true, {\n                            fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Stats.tsx\",\n                            lineNumber: 68,\n                            columnNumber: 13\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Stats.tsx\",\n                    lineNumber: 66,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Stats.tsx\",\n            lineNumber: 50,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Stats.tsx\",\n        lineNumber: 49,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/Stats.tsx\n");

/***/ }),

/***/ "(ssr)/./components/Testimonials.tsx":
/*!*************************************!*\
  !*** ./components/Testimonials.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Testimonials)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _barrel_optimize_names_Star_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Star!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/star.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nconst testimonials = [\n    {\n        name: '張明輝',\n        position: 'CEO',\n        company: '科技創新有限公司',\n        content: 'AI Solutions Hub的團隊幫助我們實現了業務流程的全面自動化，效率提升超過300%。他們的專業知識和服務態度都是一流的。',\n        rating: 5\n    },\n    {\n        name: '李雅婷',\n        position: 'CTO',\n        company: '金融科技集團',\n        content: '與AI Solutions Hub合作是我們做過的最正確的決定。他們的AI解決方案不僅技術先進，而且完美契合我們的業務需求。',\n        rating: 5\n    },\n    {\n        name: '王志強',\n        position: '營運總監',\n        company: '電商平台',\n        content: '推薦系統上線後，我們的轉化率和用戶留存率都有顯著提升。AI Solutions Hub的團隊在整個項目過程中都表現出極高的專業性。',\n        rating: 5\n    }\n];\nfunction Testimonials() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        className: \"py-20 section-padding bg-gradient-to-b from-transparent via-purple-900/10 to-transparent\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-7xl mx-auto\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        y: 20\n                    },\n                    whileInView: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    viewport: {\n                        once: true\n                    },\n                    transition: {\n                        duration: 0.5\n                    },\n                    className: \"text-center mb-16\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-4xl md:text-5xl font-bold mb-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"gradient-text\",\n                                children: \"客戶評價\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Testimonials.tsx\",\n                                lineNumber: 42,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Testimonials.tsx\",\n                            lineNumber: 41,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xl text-gray-300 max-w-3xl mx-auto\",\n                            children: \"聽聽我們的客戶怎麼說\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Testimonials.tsx\",\n                            lineNumber: 44,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Testimonials.tsx\",\n                    lineNumber: 34,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-3 gap-8\",\n                    children: testimonials.map((testimonial, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.div, {\n                            initial: {\n                                opacity: 0,\n                                y: 20\n                            },\n                            whileInView: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            viewport: {\n                                once: true\n                            },\n                            transition: {\n                                duration: 0.5,\n                                delay: index * 0.1\n                            },\n                            className: \"glass-effect p-8 rounded-2xl\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex mb-4\",\n                                    children: [\n                                        ...Array(testimonial.rating)\n                                    ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Star_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                            className: \"w-5 h-5 text-yellow-500 fill-current\"\n                                        }, i, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Testimonials.tsx\",\n                                            lineNumber: 61,\n                                            columnNumber: 19\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Testimonials.tsx\",\n                                    lineNumber: 59,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-300 mb-6 italic\",\n                                    children: [\n                                        '\"',\n                                        testimonial.content,\n                                        '\"'\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Testimonials.tsx\",\n                                    lineNumber: 65,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"font-semibold text-white\",\n                                            children: testimonial.name\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Testimonials.tsx\",\n                                            lineNumber: 68,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm text-gray-400\",\n                                            children: [\n                                                testimonial.position,\n                                                \" \\xb7 \",\n                                                testimonial.company\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Testimonials.tsx\",\n                                            lineNumber: 69,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Testimonials.tsx\",\n                                    lineNumber: 67,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, index, true, {\n                            fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Testimonials.tsx\",\n                            lineNumber: 51,\n                            columnNumber: 13\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Testimonials.tsx\",\n                    lineNumber: 49,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Testimonials.tsx\",\n            lineNumber: 33,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Testimonials.tsx\",\n        lineNumber: 32,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/Testimonials.tsx\n");

/***/ }),

/***/ "(ssr)/./components/layout/Footer.tsx":
/*!**************************************!*\
  !*** ./components/layout/Footer.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Footer)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Brain_Github_Linkedin_Mail_Twitter_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Brain,Github,Linkedin,Mail,Twitter!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/twitter.js\");\n/* harmony import */ var _barrel_optimize_names_Brain_Github_Linkedin_Mail_Twitter_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Brain,Github,Linkedin,Mail,Twitter!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/linkedin.js\");\n/* harmony import */ var _barrel_optimize_names_Brain_Github_Linkedin_Mail_Twitter_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Brain,Github,Linkedin,Mail,Twitter!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/github.js\");\n/* harmony import */ var _barrel_optimize_names_Brain_Github_Linkedin_Mail_Twitter_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Brain,Github,Linkedin,Mail,Twitter!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/mail.js\");\n/* harmony import */ var _barrel_optimize_names_Brain_Github_Linkedin_Mail_Twitter_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Brain,Github,Linkedin,Mail,Twitter!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/brain.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nconst footerLinks = {\n    company: [\n        {\n            label: '關於我們',\n            href: '/about'\n        },\n        {\n            label: '團隊',\n            href: '/about#team'\n        },\n        {\n            label: '職業機會',\n            href: '/careers'\n        },\n        {\n            label: '新聞',\n            href: '/news'\n        }\n    ],\n    services: [\n        {\n            label: 'AI 諮詢',\n            href: '/services#consulting'\n        },\n        {\n            label: '機器學習',\n            href: '/services#ml'\n        },\n        {\n            label: '自然語言處理',\n            href: '/services#nlp'\n        },\n        {\n            label: '計算機視覺',\n            href: '/services#cv'\n        }\n    ],\n    resources: [\n        {\n            label: '博客',\n            href: '/blog'\n        },\n        {\n            label: '案例研究',\n            href: '/case-studies'\n        },\n        {\n            label: '文檔',\n            href: '/docs'\n        },\n        {\n            label: 'API',\n            href: '/api'\n        }\n    ],\n    support: [\n        {\n            label: '聯繫我們',\n            href: '/contact'\n        },\n        {\n            label: '常見問題',\n            href: '/faq'\n        },\n        {\n            label: '隱私政策',\n            href: '/privacy'\n        },\n        {\n            label: '服務條款',\n            href: '/terms'\n        }\n    ]\n};\nconst socialLinks = [\n    {\n        icon: _barrel_optimize_names_Brain_Github_Linkedin_Mail_Twitter_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"],\n        href: '#',\n        label: 'Twitter'\n    },\n    {\n        icon: _barrel_optimize_names_Brain_Github_Linkedin_Mail_Twitter_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n        href: '#',\n        label: 'LinkedIn'\n    },\n    {\n        icon: _barrel_optimize_names_Brain_Github_Linkedin_Mail_Twitter_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        href: '#',\n        label: 'GitHub'\n    },\n    {\n        icon: _barrel_optimize_names_Brain_Github_Linkedin_Mail_Twitter_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n        href: '#',\n        label: 'Email'\n    }\n];\nfunction Footer() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n        className: \"glass-effect border-t border-gray-800 mt-20\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"section-padding py-12\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"lg:col-span-1\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                    href: \"/\",\n                                    className: \"flex items-center space-x-2 mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Brain_Github_Linkedin_Mail_Twitter_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                            className: \"w-8 h-8 text-purple-500\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/layout/Footer.tsx\",\n                                            lineNumber: 47,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-xl font-bold gradient-text\",\n                                            children: \"AI Solutions Hub\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/layout/Footer.tsx\",\n                                            lineNumber: 48,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/layout/Footer.tsx\",\n                                    lineNumber: 46,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-400 text-sm mb-4\",\n                                    children: \"推動AI創新，助力企業轉型\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/layout/Footer.tsx\",\n                                    lineNumber: 50,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex space-x-4\",\n                                    children: socialLinks.map((social)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: social.href,\n                                            \"aria-label\": social.label,\n                                            className: \"text-gray-400 hover:text-purple-500 transition-colors duration-300\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(social.icon, {\n                                                size: 20\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/layout/Footer.tsx\",\n                                                lineNumber: 61,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, social.label, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/layout/Footer.tsx\",\n                                            lineNumber: 55,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/layout/Footer.tsx\",\n                                    lineNumber: 53,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/layout/Footer.tsx\",\n                            lineNumber: 45,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-white font-semibold mb-4\",\n                                    children: \"公司\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/layout/Footer.tsx\",\n                                    lineNumber: 68,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    className: \"space-y-2\",\n                                    children: footerLinks.company.map((link)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                href: link.href,\n                                                className: \"text-gray-400 hover:text-white transition-colors duration-300 text-sm\",\n                                                children: link.label\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/layout/Footer.tsx\",\n                                                lineNumber: 72,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, link.href, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/layout/Footer.tsx\",\n                                            lineNumber: 71,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/layout/Footer.tsx\",\n                                    lineNumber: 69,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/layout/Footer.tsx\",\n                            lineNumber: 67,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-white font-semibold mb-4\",\n                                    children: \"服務\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/layout/Footer.tsx\",\n                                    lineNumber: 84,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    className: \"space-y-2\",\n                                    children: footerLinks.services.map((link)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                href: link.href,\n                                                className: \"text-gray-400 hover:text-white transition-colors duration-300 text-sm\",\n                                                children: link.label\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/layout/Footer.tsx\",\n                                                lineNumber: 88,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, link.href, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/layout/Footer.tsx\",\n                                            lineNumber: 87,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/layout/Footer.tsx\",\n                                    lineNumber: 85,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/layout/Footer.tsx\",\n                            lineNumber: 83,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-white font-semibold mb-4\",\n                                    children: \"資源\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/layout/Footer.tsx\",\n                                    lineNumber: 100,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    className: \"space-y-2\",\n                                    children: footerLinks.resources.map((link)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                href: link.href,\n                                                className: \"text-gray-400 hover:text-white transition-colors duration-300 text-sm\",\n                                                children: link.label\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/layout/Footer.tsx\",\n                                                lineNumber: 104,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, link.href, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/layout/Footer.tsx\",\n                                            lineNumber: 103,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/layout/Footer.tsx\",\n                                    lineNumber: 101,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/layout/Footer.tsx\",\n                            lineNumber: 99,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-white font-semibold mb-4\",\n                                    children: \"支援\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/layout/Footer.tsx\",\n                                    lineNumber: 116,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    className: \"space-y-2\",\n                                    children: footerLinks.support.map((link)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                href: link.href,\n                                                className: \"text-gray-400 hover:text-white transition-colors duration-300 text-sm\",\n                                                children: link.label\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/layout/Footer.tsx\",\n                                                lineNumber: 120,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, link.href, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/layout/Footer.tsx\",\n                                            lineNumber: 119,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/layout/Footer.tsx\",\n                                    lineNumber: 117,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/layout/Footer.tsx\",\n                            lineNumber: 115,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/layout/Footer.tsx\",\n                    lineNumber: 44,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-8 pt-8 border-t border-gray-800 flex flex-col md:flex-row justify-between items-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-400 text-sm\",\n                            children: \"\\xa9 2024 AI Solutions Hub. 保留所有權利。\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/layout/Footer.tsx\",\n                            lineNumber: 133,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-400 text-sm mt-2 md:mt-0\",\n                            children: \"用心打造 | 香港\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/layout/Footer.tsx\",\n                            lineNumber: 136,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/layout/Footer.tsx\",\n                    lineNumber: 132,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/layout/Footer.tsx\",\n            lineNumber: 43,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/layout/Footer.tsx\",\n        lineNumber: 42,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/layout/Footer.tsx\n");

/***/ }),

/***/ "(ssr)/./components/layout/Header.tsx":
/*!**************************************!*\
  !*** ./components/layout/Header.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Header)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _barrel_optimize_names_Brain_Menu_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Brain,Menu,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/brain.js\");\n/* harmony import */ var _barrel_optimize_names_Brain_Menu_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Brain,Menu,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_Brain_Menu_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Brain,Menu,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/menu.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\nconst navLinks = [\n    {\n        href: '/',\n        label: '首頁'\n    },\n    {\n        href: '/about',\n        label: '關於我們'\n    },\n    {\n        href: '/services',\n        label: '服務'\n    },\n    {\n        href: '/case-studies',\n        label: '案例研究'\n    },\n    {\n        href: '/blog',\n        label: '博客'\n    },\n    {\n        href: '/contact',\n        label: '聯繫我們'\n    }\n];\nfunction Header() {\n    const [isScrolled, setIsScrolled] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isMobileMenuOpen, setIsMobileMenuOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Header.useEffect\": ()=>{\n            const handleScroll = {\n                \"Header.useEffect.handleScroll\": ()=>{\n                    setIsScrolled(window.scrollY > 20);\n                }\n            }[\"Header.useEffect.handleScroll\"];\n            window.addEventListener('scroll', handleScroll);\n            return ({\n                \"Header.useEffect\": ()=>window.removeEventListener('scroll', handleScroll)\n            })[\"Header.useEffect\"];\n        }\n    }[\"Header.useEffect\"], []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.header, {\n        initial: {\n            y: -100\n        },\n        animate: {\n            y: 0\n        },\n        className: `fixed top-0 left-0 right-0 z-50 transition-all duration-300 ${isScrolled ? 'glass-effect shadow-lg' : 'bg-transparent'}`,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n            className: \"section-padding py-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                            href: \"/\",\n                            className: \"flex items-center space-x-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Brain_Menu_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                    className: \"w-8 h-8 text-purple-500\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/layout/Header.tsx\",\n                                    lineNumber: 40,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-xl font-bold gradient-text\",\n                                    children: \"AI Solutions Hub\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/layout/Header.tsx\",\n                                    lineNumber: 41,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/layout/Header.tsx\",\n                            lineNumber: 39,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"hidden md:flex items-center space-x-8\",\n                            children: [\n                                navLinks.map((link)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: link.href,\n                                        className: \"text-gray-300 hover:text-white transition-colors duration-300\",\n                                        children: link.label\n                                    }, link.href, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/layout/Header.tsx\",\n                                        lineNumber: 46,\n                                        columnNumber: 15\n                                    }, this)),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: \"px-6 py-2 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full text-white font-semibold hover:shadow-lg hover:shadow-purple-500/50 transition-all duration-300\",\n                                    children: \"開始使用\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/layout/Header.tsx\",\n                                    lineNumber: 54,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/layout/Header.tsx\",\n                            lineNumber: 44,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            className: \"md:hidden text-gray-300\",\n                            onClick: ()=>setIsMobileMenuOpen(!isMobileMenuOpen),\n                            children: isMobileMenuOpen ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Brain_Menu_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                size: 24\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/layout/Header.tsx\",\n                                lineNumber: 63,\n                                columnNumber: 33\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Brain_Menu_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                size: 24\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/layout/Header.tsx\",\n                                lineNumber: 63,\n                                columnNumber: 51\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/layout/Header.tsx\",\n                            lineNumber: 59,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/layout/Header.tsx\",\n                    lineNumber: 38,\n                    columnNumber: 9\n                }, this),\n                isMobileMenuOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        y: -20\n                    },\n                    animate: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    className: \"md:hidden mt-4 py-4 border-t border-gray-800\",\n                    children: [\n                        navLinks.map((link)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                href: link.href,\n                                className: \"block py-2 text-gray-300 hover:text-white transition-colors duration-300\",\n                                onClick: ()=>setIsMobileMenuOpen(false),\n                                children: link.label\n                            }, link.href, false, {\n                                fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/layout/Header.tsx\",\n                                lineNumber: 74,\n                                columnNumber: 15\n                            }, this)),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            className: \"mt-4 w-full px-6 py-2 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full text-white font-semibold\",\n                            children: \"開始使用\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/layout/Header.tsx\",\n                            lineNumber: 83,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/layout/Header.tsx\",\n                    lineNumber: 68,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/layout/Header.tsx\",\n            lineNumber: 37,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/layout/Header.tsx\",\n        lineNumber: 30,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/layout/Header.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/CTASection.tsx":
/*!**************************************!*\
  !*** ./components/ui/CTASection.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_ArrowRight_CheckCircle_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,CheckCircle,Sparkles!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/sparkles.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_CheckCircle_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,CheckCircle,Sparkles!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_CheckCircle_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,CheckCircle,Sparkles!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/arrow-right.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nconst CTASection = ()=>{\n    const benefits = [\n        '免費營銷策略評估',\n        '專業 AI 顧問一對一諮詢',\n        '定制化解決方案建議',\n        '30 天無風險試用期'\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        className: \"py-20 relative overflow-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 bg-gradient-to-br from-purple-900/20 via-gray-900 to-pink-900/20\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/ui/CTASection.tsx\",\n                lineNumber: 18,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute top-0 left-0 w-full h-full\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute top-1/4 left-1/4 w-96 h-96 bg-purple-500/10 rounded-full blur-3xl animate-pulse\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/ui/CTASection.tsx\",\n                        lineNumber: 20,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute bottom-1/4 right-1/4 w-96 h-96 bg-pink-500/10 rounded-full blur-3xl animate-pulse\",\n                        style: {\n                            animationDelay: '1s'\n                        }\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/ui/CTASection.tsx\",\n                        lineNumber: 21,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-[600px] h-[600px] bg-blue-500/5 rounded-full blur-3xl animate-pulse\",\n                        style: {\n                            animationDelay: '2s'\n                        }\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/ui/CTASection.tsx\",\n                        lineNumber: 22,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/ui/CTASection.tsx\",\n                lineNumber: 19,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"container mx-auto px-6 relative z-10\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"max-w-4xl mx-auto text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    y: 20\n                                },\n                                whileInView: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                transition: {\n                                    duration: 0.6\n                                },\n                                viewport: {\n                                    once: true\n                                },\n                                className: \"inline-flex items-center space-x-2 bg-gradient-to-r from-purple-500/20 to-pink-500/20 border border-purple-500/30 rounded-full px-6 py-2 mb-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_CheckCircle_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                        className: \"w-4 h-4 text-purple-400\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/ui/CTASection.tsx\",\n                                        lineNumber: 35,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm text-purple-300\",\n                                        children: \"立即開始您的 AI 營銷之旅\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/ui/CTASection.tsx\",\n                                        lineNumber: 36,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/ui/CTASection.tsx\",\n                                lineNumber: 28,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.h2, {\n                                initial: {\n                                    opacity: 0,\n                                    y: 30\n                                },\n                                whileInView: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                transition: {\n                                    duration: 0.8,\n                                    delay: 0.2\n                                },\n                                viewport: {\n                                    once: true\n                                },\n                                className: \"text-4xl md:text-6xl font-bold mb-6 leading-tight\",\n                                children: [\n                                    \"準備讓 AI 為您的\",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/ui/CTASection.tsx\",\n                                        lineNumber: 48,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"gradient-text\",\n                                        children: \"營銷業績加速\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/ui/CTASection.tsx\",\n                                        lineNumber: 49,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    \"？\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/ui/CTASection.tsx\",\n                                lineNumber: 40,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.p, {\n                                initial: {\n                                    opacity: 0,\n                                    y: 30\n                                },\n                                whileInView: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                transition: {\n                                    duration: 0.8,\n                                    delay: 0.4\n                                },\n                                viewport: {\n                                    once: true\n                                },\n                                className: \"text-xl text-gray-300 mb-12 leading-relaxed\",\n                                children: [\n                                    \"不要讓競爭對手搶先一步。現在就聯繫我們，\",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {\n                                        className: \"hidden md:block\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/ui/CTASection.tsx\",\n                                        lineNumber: 61,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    \"讓專業的 AI 營銷團隊為您量身打造成功策略。\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/ui/CTASection.tsx\",\n                                lineNumber: 53,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    y: 30\n                                },\n                                whileInView: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                transition: {\n                                    duration: 0.8,\n                                    delay: 0.6\n                                },\n                                viewport: {\n                                    once: true\n                                },\n                                className: \"grid grid-cols-1 md:grid-cols-2 gap-4 mb-12 max-w-2xl mx-auto\",\n                                children: benefits.map((benefit, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-3 text-left\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_CheckCircle_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                className: \"w-5 h-5 text-purple-400 flex-shrink-0\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/ui/CTASection.tsx\",\n                                                lineNumber: 75,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-gray-300\",\n                                                children: benefit\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/ui/CTASection.tsx\",\n                                                lineNumber: 76,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, index, true, {\n                                        fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/ui/CTASection.tsx\",\n                                        lineNumber: 74,\n                                        columnNumber: 15\n                                    }, undefined))\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/ui/CTASection.tsx\",\n                                lineNumber: 66,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    y: 30\n                                },\n                                whileInView: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                transition: {\n                                    duration: 0.8,\n                                    delay: 0.8\n                                },\n                                viewport: {\n                                    once: true\n                                },\n                                className: \"flex flex-col sm:flex-row gap-4 justify-center mb-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                        href: \"/contact\",\n                                        className: \"group bg-gradient-to-r from-purple-500 to-pink-500 text-white px-8 py-4 rounded-full font-semibold hover:shadow-lg hover:shadow-purple-500/25 transition-all duration-300 flex items-center justify-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"立即免費諮詢\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/ui/CTASection.tsx\",\n                                                lineNumber: 93,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_CheckCircle_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                className: \"w-5 h-5 group-hover:translate-x-1 transition-transform\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/ui/CTASection.tsx\",\n                                                lineNumber: 94,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/ui/CTASection.tsx\",\n                                        lineNumber: 89,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                        href: \"/services\",\n                                        className: \"border border-purple-500 text-purple-400 px-8 py-4 rounded-full font-semibold hover:bg-purple-500/10 transition-all duration-300\",\n                                        children: \"了解服務詳情\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/ui/CTASection.tsx\",\n                                        lineNumber: 96,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/ui/CTASection.tsx\",\n                                lineNumber: 82,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    y: 30\n                                },\n                                whileInView: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                transition: {\n                                    duration: 0.8,\n                                    delay: 1\n                                },\n                                viewport: {\n                                    once: true\n                                },\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-400 mb-4\",\n                                        children: \"已有 300+ 家企業選擇我們的 AI 營銷解決方案\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/ui/CTASection.tsx\",\n                                        lineNumber: 112,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-center items-center space-x-8 opacity-60\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-20 h-8 bg-gray-600 rounded opacity-50\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/ui/CTASection.tsx\",\n                                                lineNumber: 117,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-20 h-8 bg-gray-600 rounded opacity-50\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/ui/CTASection.tsx\",\n                                                lineNumber: 118,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-20 h-8 bg-gray-600 rounded opacity-50\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/ui/CTASection.tsx\",\n                                                lineNumber: 119,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-20 h-8 bg-gray-600 rounded opacity-50\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/ui/CTASection.tsx\",\n                                                lineNumber: 120,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/ui/CTASection.tsx\",\n                                        lineNumber: 115,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/ui/CTASection.tsx\",\n                                lineNumber: 105,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/ui/CTASection.tsx\",\n                        lineNumber: 26,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                        animate: {\n                            rotate: 360\n                        },\n                        transition: {\n                            duration: 20,\n                            repeat: Infinity,\n                            ease: \"linear\"\n                        },\n                        className: \"absolute top-10 right-10 w-16 h-16 border border-purple-500/30 rounded-full hidden lg:block\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-2 h-2 bg-purple-400 rounded-full\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/ui/CTASection.tsx\",\n                            lineNumber: 131,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/ui/CTASection.tsx\",\n                        lineNumber: 126,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                        animate: {\n                            rotate: -360\n                        },\n                        transition: {\n                            duration: 25,\n                            repeat: Infinity,\n                            ease: \"linear\"\n                        },\n                        className: \"absolute bottom-10 left-10 w-20 h-20 border border-pink-500/30 rounded-full hidden lg:block\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-3 h-3 bg-pink-400 rounded-full\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/ui/CTASection.tsx\",\n                            lineNumber: 139,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/ui/CTASection.tsx\",\n                        lineNumber: 134,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/ui/CTASection.tsx\",\n                lineNumber: 25,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/ui/CTASection.tsx\",\n        lineNumber: 16,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (CTASection);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/CTASection.tsx\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fkw%2FDesktop%2FClaude%2Fai-marketing-website%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkw%2FDesktop%2FClaude%2Fai-marketing-website%2Fcomponents%2Flayout%2FFooter.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkw%2FDesktop%2FClaude%2Fai-marketing-website%2Fcomponents%2Flayout%2FHeader.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkw%2FDesktop%2FClaude%2Fai-marketing-website%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fkw%2FDesktop%2FClaude%2Fai-marketing-website%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkw%2FDesktop%2FClaude%2Fai-marketing-website%2Fcomponents%2Flayout%2FFooter.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkw%2FDesktop%2FClaude%2Fai-marketing-website%2Fcomponents%2Flayout%2FHeader.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkw%2FDesktop%2FClaude%2Fai-marketing-website%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/layout/Footer.tsx */ \"(ssr)/./components/layout/Footer.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/layout/Header.tsx */ \"(ssr)/./components/layout/Header.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGa3clMkZEZXNrdG9wJTJGQ2xhdWRlJTJGYWktbWFya2V0aW5nLXdlYnNpdGUlMkZhcHAlMkZnbG9iYWxzLmNzcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjIlMkZVc2VycyUyRmt3JTJGRGVza3RvcCUyRkNsYXVkZSUyRmFpLW1hcmtldGluZy13ZWJzaXRlJTJGY29tcG9uZW50cyUyRmxheW91dCUyRkZvb3Rlci50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlMjJkZWZhdWx0JTIyJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGa3clMkZEZXNrdG9wJTJGQ2xhdWRlJTJGYWktbWFya2V0aW5nLXdlYnNpdGUlMkZjb21wb25lbnRzJTJGbGF5b3V0JTJGSGVhZGVyLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiUyMmRlZmF1bHQlMjIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyJTJGVXNlcnMlMkZrdyUyRkRlc2t0b3AlMkZDbGF1ZGUlMkZhaS1tYXJrZXRpbmctd2Vic2l0ZSUyRm5vZGVfbW9kdWxlcyUyRm5leHQlMkZmb250JTJGZ29vZ2xlJTJGdGFyZ2V0LmNzcyUzRiU3QiU1QyUyMnBhdGglNUMlMjIlM0ElNUMlMjJhcHAlMkZsYXlvdXQudHN4JTVDJTIyJTJDJTVDJTIyaW1wb3J0JTVDJTIyJTNBJTVDJTIySW50ZXIlNUMlMjIlMkMlNUMlMjJhcmd1bWVudHMlNUMlMjIlM0ElNUIlN0IlNUMlMjJzdWJzZXRzJTVDJTIyJTNBJTVCJTVDJTIybGF0aW4lNUMlMjIlNUQlN0QlNUQlMkMlNUMlMjJ2YXJpYWJsZU5hbWUlNUMlMjIlM0ElNUMlMjJpbnRlciU1QyUyMiU3RCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsd0tBQTRJO0FBQzVJO0FBQ0Esd0tBQTRJIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIsIHdlYnBhY2tFeHBvcnRzOiBbXCJkZWZhdWx0XCJdICovIFwiL1VzZXJzL2t3L0Rlc2t0b3AvQ2xhdWRlL2FpLW1hcmtldGluZy13ZWJzaXRlL2NvbXBvbmVudHMvbGF5b3V0L0Zvb3Rlci50c3hcIik7XG47XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiwgd2VicGFja0V4cG9ydHM6IFtcImRlZmF1bHRcIl0gKi8gXCIvVXNlcnMva3cvRGVza3RvcC9DbGF1ZGUvYWktbWFya2V0aW5nLXdlYnNpdGUvY29tcG9uZW50cy9sYXlvdXQvSGVhZGVyLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fkw%2FDesktop%2FClaude%2Fai-marketing-website%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkw%2FDesktop%2FClaude%2Fai-marketing-website%2Fcomponents%2Flayout%2FFooter.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkw%2FDesktop%2FClaude%2Fai-marketing-website%2Fcomponents%2Flayout%2FHeader.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkw%2FDesktop%2FClaude%2Fai-marketing-website%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fkw%2FDesktop%2FClaude%2Fai-marketing-website%2Fapp%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fkw%2FDesktop%2FClaude%2Fai-marketing-website%2Fapp%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/page.tsx */ \"(ssr)/./app/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGa3clMkZEZXNrdG9wJTJGQ2xhdWRlJTJGYWktbWFya2V0aW5nLXdlYnNpdGUlMkZhcHAlMkZwYWdlLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsd0lBQStGIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCIvVXNlcnMva3cvRGVza3RvcC9DbGF1ZGUvYWktbWFya2V0aW5nLXdlYnNpdGUvYXBwL3BhZ2UudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fkw%2FDesktop%2FClaude%2Fai-marketing-website%2Fapp%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fkw%2FDesktop%2FClaude%2Fai-marketing-website%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkw%2FDesktop%2FClaude%2Fai-marketing-website%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkw%2FDesktop%2FClaude%2Fai-marketing-website%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkw%2FDesktop%2FClaude%2Fai-marketing-website%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkw%2FDesktop%2FClaude%2Fai-marketing-website%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkw%2FDesktop%2FClaude%2Fai-marketing-website%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkw%2FDesktop%2FClaude%2Fai-marketing-website%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkw%2FDesktop%2FClaude%2Fai-marketing-website%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fkw%2FDesktop%2FClaude%2Fai-marketing-website%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkw%2FDesktop%2FClaude%2Fai-marketing-website%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkw%2FDesktop%2FClaude%2Fai-marketing-website%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkw%2FDesktop%2FClaude%2Fai-marketing-website%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkw%2FDesktop%2FClaude%2Fai-marketing-website%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkw%2FDesktop%2FClaude%2Fai-marketing-website%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkw%2FDesktop%2FClaude%2Fai-marketing-website%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkw%2FDesktop%2FClaude%2Fai-marketing-website%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fkw%2FDesktop%2FClaude%2Fai-marketing-website%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkw%2FDesktop%2FClaude%2Fai-marketing-website%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkw%2FDesktop%2FClaude%2Fai-marketing-website%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkw%2FDesktop%2FClaude%2Fai-marketing-website%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkw%2FDesktop%2FClaude%2Fai-marketing-website%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkw%2FDesktop%2FClaude%2Fai-marketing-website%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkw%2FDesktop%2FClaude%2Fai-marketing-website%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkw%2FDesktop%2FClaude%2Fai-marketing-website%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/framer-motion","vendor-chunks/motion-dom","vendor-chunks/lucide-react","vendor-chunks/motion-utils","vendor-chunks/@swc"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2FUsers%2Fkw%2FDesktop%2FClaude%2Fai-marketing-website%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fkw%2FDesktop%2FClaude%2Fai-marketing-website&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();