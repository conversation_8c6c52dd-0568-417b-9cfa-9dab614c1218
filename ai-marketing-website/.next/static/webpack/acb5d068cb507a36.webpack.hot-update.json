{"c": ["app/page", "webpack"], "r": [], "m": ["(app-pages-browser)/./app/page.tsx", "(app-pages-browser)/./components/CaseStudies.tsx", "(app-pages-browser)/./components/Features.tsx", "(app-pages-browser)/./components/Hero.tsx", "(app-pages-browser)/./components/Services.tsx", "(app-pages-browser)/./components/Stats.tsx", "(app-pages-browser)/./components/Testimonials.tsx", "(app-pages-browser)/./components/ui/CTASection.tsx", "(app-pages-browser)/./node_modules/framer-motion/dist/es/animation/animate/index.mjs", "(app-pages-browser)/./node_modules/framer-motion/dist/es/animation/animate/resolve-subjects.mjs", "(app-pages-browser)/./node_modules/framer-motion/dist/es/animation/animate/sequence.mjs", "(app-pages-browser)/./node_modules/framer-motion/dist/es/animation/animate/subject.mjs", "(app-pages-browser)/./node_modules/framer-motion/dist/es/animation/sequence/create.mjs", "(app-pages-browser)/./node_modules/framer-motion/dist/es/animation/sequence/utils/calc-repeat-duration.mjs", "(app-pages-browser)/./node_modules/framer-motion/dist/es/animation/sequence/utils/calc-time.mjs", "(app-pages-browser)/./node_modules/framer-motion/dist/es/animation/sequence/utils/edit.mjs", "(app-pages-browser)/./node_modules/framer-motion/dist/es/animation/sequence/utils/normalize-times.mjs", "(app-pages-browser)/./node_modules/framer-motion/dist/es/animation/sequence/utils/sort.mjs", "(app-pages-browser)/./node_modules/framer-motion/dist/es/animation/utils/create-visual-element.mjs", "(app-pages-browser)/./node_modules/framer-motion/dist/es/animation/utils/is-dom-keyframes.mjs", "(app-pages-browser)/./node_modules/framer-motion/dist/es/render/object/ObjectVisualElement.mjs", "(app-pages-browser)/./node_modules/framer-motion/dist/es/value/use-combine-values.mjs", "(app-pages-browser)/./node_modules/framer-motion/dist/es/value/use-computed.mjs", "(app-pages-browser)/./node_modules/framer-motion/dist/es/value/use-motion-value.mjs", "(app-pages-browser)/./node_modules/framer-motion/dist/es/value/use-transform.mjs", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-up-right.js", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/award.js", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/brain.js", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chart-column.js", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check.js", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/dollar-sign.js", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/globe.js", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/message-circle.js", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/play.js", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/quote.js", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shield.js", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/star.js", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/target.js", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-up.js", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/zap.js", "(app-pages-browser)/./node_modules/motion-dom/dist/es/animation/GroupAnimation.mjs", "(app-pages-browser)/./node_modules/motion-dom/dist/es/animation/GroupAnimationWithThen.mjs", "(app-pages-browser)/./node_modules/motion-dom/dist/es/utils/transform.mjs", "(app-pages-browser)/./node_modules/motion-utils/dist/es/easing/utils/get-easing-for-segment.mjs", "(app-pages-browser)/./node_modules/motion-utils/dist/es/wrap.mjs", "(app-pages-browser)/./node_modules/next/dist/api/image.js", "(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fkw%2FDesktop%2FClaude%2Fai-marketing-website%2Fapp%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!", "(app-pages-browser)/./node_modules/next/dist/client/image-component.js", "(app-pages-browser)/./node_modules/next/dist/compiled/picomatch/index.js", "(app-pages-browser)/./node_modules/next/dist/shared/lib/amp-context.shared-runtime.js", "(app-pages-browser)/./node_modules/next/dist/shared/lib/amp-mode.js", "(app-pages-browser)/./node_modules/next/dist/shared/lib/get-img-props.js", "(app-pages-browser)/./node_modules/next/dist/shared/lib/head.js", "(app-pages-browser)/./node_modules/next/dist/shared/lib/image-blur-svg.js", "(app-pages-browser)/./node_modules/next/dist/shared/lib/image-config-context.shared-runtime.js", "(app-pages-browser)/./node_modules/next/dist/shared/lib/image-config.js", "(app-pages-browser)/./node_modules/next/dist/shared/lib/image-external.js", "(app-pages-browser)/./node_modules/next/dist/shared/lib/image-loader.js", "(app-pages-browser)/./node_modules/next/dist/shared/lib/match-local-pattern.js", "(app-pages-browser)/./node_modules/next/dist/shared/lib/match-remote-pattern.js", "(app-pages-browser)/./node_modules/next/dist/shared/lib/router-context.shared-runtime.js", "(app-pages-browser)/./node_modules/next/dist/shared/lib/side-effect.js"]}