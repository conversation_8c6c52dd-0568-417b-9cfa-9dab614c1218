"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/contact/page",{

/***/ "(app-pages-browser)/./app/contact/page.tsx":
/*!******************************!*\
  !*** ./app/contact/page.tsx ***!
  \******************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ContactPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_ArrowRight_CheckCircle_Clock_Mail_MapPin_MessageSquare_Phone_Send_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,CheckCircle,Clock,Mail,MapPin,MessageSquare,Phone,Send,Sparkles!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/map-pin.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_CheckCircle_Clock_Mail_MapPin_MessageSquare_Phone_Send_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,CheckCircle,Clock,Mail,MapPin,MessageSquare,Phone,Send,Sparkles!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/phone.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_CheckCircle_Clock_Mail_MapPin_MessageSquare_Phone_Send_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,CheckCircle,Clock,Mail,MapPin,MessageSquare,Phone,Send,Sparkles!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/mail.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_CheckCircle_Clock_Mail_MapPin_MessageSquare_Phone_Send_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,CheckCircle,Clock,Mail,MapPin,MessageSquare,Phone,Send,Sparkles!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_CheckCircle_Clock_Mail_MapPin_MessageSquare_Phone_Send_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,CheckCircle,Clock,Mail,MapPin,MessageSquare,Phone,Send,Sparkles!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/sparkles.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_CheckCircle_Clock_Mail_MapPin_MessageSquare_Phone_Send_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,CheckCircle,Clock,Mail,MapPin,MessageSquare,Phone,Send,Sparkles!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/send.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_CheckCircle_Clock_Mail_MapPin_MessageSquare_Phone_Send_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,CheckCircle,Clock,Mail,MapPin,MessageSquare,Phone,Send,Sparkles!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_CheckCircle_Clock_Mail_MapPin_MessageSquare_Phone_Send_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,CheckCircle,Clock,Mail,MapPin,MessageSquare,Phone,Send,Sparkles!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/message-square.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_CheckCircle_Clock_Mail_MapPin_MessageSquare_Phone_Send_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,CheckCircle,Clock,Mail,MapPin,MessageSquare,Phone,Send,Sparkles!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-right.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nfunction ContactPage() {\n    _s();\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        name: '',\n        email: '',\n        company: '',\n        phone: '',\n        service: '',\n        message: ''\n    });\n    const [isSubmitting, setIsSubmitting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isSubmitted, setIsSubmitted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const services = [\n        'AI 營銷策略諮詢',\n        '精準營銷自動化',\n        '數據分析與洞察',\n        '增長駭客服務',\n        '其他'\n    ];\n    const contactInfo = [\n        {\n            icon: _barrel_optimize_names_ArrowRight_CheckCircle_Clock_Mail_MapPin_MessageSquare_Phone_Send_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"],\n            title: '辦公地址',\n            content: '香港中環德輔道中88號',\n            subContent: '中環廣場 18 樓'\n        },\n        {\n            icon: _barrel_optimize_names_ArrowRight_CheckCircle_Clock_Mail_MapPin_MessageSquare_Phone_Send_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n            title: '聯絡電話',\n            content: '+852 1234 5678',\n            subContent: '週一至週五 9:00-18:00'\n        },\n        {\n            icon: _barrel_optimize_names_ArrowRight_CheckCircle_Clock_Mail_MapPin_MessageSquare_Phone_Send_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n            title: '電子郵件',\n            content: '<EMAIL>',\n            subContent: '24小時內回覆'\n        },\n        {\n            icon: _barrel_optimize_names_ArrowRight_CheckCircle_Clock_Mail_MapPin_MessageSquare_Phone_Send_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n            title: '營業時間',\n            content: '週一至週五：9:00 - 18:00',\n            subContent: '週末及公眾假期休息'\n        }\n    ];\n    const benefits = [\n        '免費營銷策略評估',\n        '專業 AI 顧問一對一諮詢',\n        '定制化解決方案建議',\n        '無隱藏費用'\n    ];\n    const handleChange = (e)=>{\n        setFormData({\n            ...formData,\n            [e.target.name]: e.target.value\n        });\n    };\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        setIsSubmitting(true);\n        // 模擬提交延遲\n        await new Promise((resolve)=>setTimeout(resolve, 2000));\n        setIsSubmitting(false);\n        setIsSubmitted(true);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen pt-20\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"section-spacing relative overflow-hidden\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 -z-10\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute inset-0 bg-gradient-radial\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/app/contact/page.tsx\",\n                            lineNumber: 95,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/app/contact/page.tsx\",\n                        lineNumber: 94,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"container-custom\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                            initial: {\n                                opacity: 0,\n                                y: 20\n                            },\n                            animate: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            transition: {\n                                duration: 0.6\n                            },\n                            className: \"text-center max-w-4xl mx-auto\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                                    initial: {\n                                        opacity: 0,\n                                        scale: 0.9\n                                    },\n                                    animate: {\n                                        opacity: 1,\n                                        scale: 1\n                                    },\n                                    className: \"inline-flex items-center space-x-2 badge badge-primary mb-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_CheckCircle_Clock_Mail_MapPin_MessageSquare_Phone_Send_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                            className: \"w-4 h-4\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/app/contact/page.tsx\",\n                                            lineNumber: 110,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"聯絡我們\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/app/contact/page.tsx\",\n                                            lineNumber: 111,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/app/contact/page.tsx\",\n                                    lineNumber: 105,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"heading-xl mb-6\",\n                                    children: [\n                                        \"開啟您的 \",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"gradient-text\",\n                                            children: \"AI 營銷之旅\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/app/contact/page.tsx\",\n                                            lineNumber: 115,\n                                            columnNumber: 20\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/app/contact/page.tsx\",\n                                    lineNumber: 114,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"body-lg text-gray-600 mb-8 max-w-3xl mx-auto\",\n                                    children: \"我們的專家團隊隨時準備為您提供專業諮詢， 讓我們一起探討如何用 AI 技術推動您的業務增長\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/app/contact/page.tsx\",\n                                    lineNumber: 118,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/app/contact/page.tsx\",\n                            lineNumber: 99,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/app/contact/page.tsx\",\n                        lineNumber: 98,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/app/contact/page.tsx\",\n                lineNumber: 93,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"section-spacing bg-gradient-to-b from-gray-50 to-white\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container-custom\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid lg:grid-cols-2 gap-12\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    x: -50\n                                },\n                                whileInView: {\n                                    opacity: 1,\n                                    x: 0\n                                },\n                                viewport: {\n                                    once: true\n                                },\n                                transition: {\n                                    duration: 0.8\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"card-modern p-8\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-2xl font-bold mb-6\",\n                                            children: \"立即預約免費諮詢\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/app/contact/page.tsx\",\n                                            lineNumber: 138,\n                                            columnNumber: 17\n                                        }, this),\n                                        !isSubmitted ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                            onSubmit: handleSubmit,\n                                            className: \"space-y-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                                    children: \"您的姓名 *\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/app/contact/page.tsx\",\n                                                                    lineNumber: 144,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"text\",\n                                                                    name: \"name\",\n                                                                    required: true,\n                                                                    value: formData.name,\n                                                                    onChange: handleChange,\n                                                                    className: \"w-full px-4 py-3 rounded-lg border border-gray-300 focus:border-indigo-500 focus:ring-2 focus:ring-indigo-200 transition-all\",\n                                                                    placeholder: \"請輸入您的姓名\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/app/contact/page.tsx\",\n                                                                    lineNumber: 147,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/app/contact/page.tsx\",\n                                                            lineNumber: 143,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                                    children: \"電子郵件 *\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/app/contact/page.tsx\",\n                                                                    lineNumber: 159,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"email\",\n                                                                    name: \"email\",\n                                                                    required: true,\n                                                                    value: formData.email,\n                                                                    onChange: handleChange,\n                                                                    className: \"w-full px-4 py-3 rounded-lg border border-gray-300 focus:border-indigo-500 focus:ring-2 focus:ring-indigo-200 transition-all\",\n                                                                    placeholder: \"<EMAIL>\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/app/contact/page.tsx\",\n                                                                    lineNumber: 162,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/app/contact/page.tsx\",\n                                                            lineNumber: 158,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/app/contact/page.tsx\",\n                                                    lineNumber: 142,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                                    children: \"公司名稱\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/app/contact/page.tsx\",\n                                                                    lineNumber: 176,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"text\",\n                                                                    name: \"company\",\n                                                                    value: formData.company,\n                                                                    onChange: handleChange,\n                                                                    className: \"w-full px-4 py-3 rounded-lg border border-gray-300 focus:border-indigo-500 focus:ring-2 focus:ring-indigo-200 transition-all\",\n                                                                    placeholder: \"您的公司名稱\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/app/contact/page.tsx\",\n                                                                    lineNumber: 179,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/app/contact/page.tsx\",\n                                                            lineNumber: 175,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                                    children: \"聯絡電話\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/app/contact/page.tsx\",\n                                                                    lineNumber: 190,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"tel\",\n                                                                    name: \"phone\",\n                                                                    value: formData.phone,\n                                                                    onChange: handleChange,\n                                                                    className: \"w-full px-4 py-3 rounded-lg border border-gray-300 focus:border-indigo-500 focus:ring-2 focus:ring-indigo-200 transition-all\",\n                                                                    placeholder: \"+852 xxxx xxxx\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/app/contact/page.tsx\",\n                                                                    lineNumber: 193,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/app/contact/page.tsx\",\n                                                            lineNumber: 189,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/app/contact/page.tsx\",\n                                                    lineNumber: 174,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                            children: \"感興趣的服務 *\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/app/contact/page.tsx\",\n                                                            lineNumber: 205,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                            name: \"service\",\n                                                            required: true,\n                                                            value: formData.service,\n                                                            onChange: handleChange,\n                                                            className: \"w-full px-4 py-3 rounded-lg border border-gray-300 focus:border-indigo-500 focus:ring-2 focus:ring-indigo-200 transition-all\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: \"\",\n                                                                    children: \"請選擇服務類型\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/app/contact/page.tsx\",\n                                                                    lineNumber: 215,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                services.map((service)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: service,\n                                                                        children: service\n                                                                    }, service, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/app/contact/page.tsx\",\n                                                                        lineNumber: 217,\n                                                                        columnNumber: 27\n                                                                    }, this))\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/app/contact/page.tsx\",\n                                                            lineNumber: 208,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/app/contact/page.tsx\",\n                                                    lineNumber: 204,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                            children: \"項目詳情\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/app/contact/page.tsx\",\n                                                            lineNumber: 225,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                            name: \"message\",\n                                                            rows: 4,\n                                                            value: formData.message,\n                                                            onChange: handleChange,\n                                                            className: \"w-full px-4 py-3 rounded-lg border border-gray-300 focus:border-indigo-500 focus:ring-2 focus:ring-indigo-200 transition-all resize-none\",\n                                                            placeholder: \"請簡述您的需求或挑戰...\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/app/contact/page.tsx\",\n                                                            lineNumber: 228,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/app/contact/page.tsx\",\n                                                    lineNumber: 224,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    type: \"submit\",\n                                                    disabled: isSubmitting,\n                                                    className: \"w-full btn-primary disabled:opacity-50 disabled:cursor-not-allowed\",\n                                                    children: isSubmitting ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"flex items-center justify-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                className: \"animate-spin -ml-1 mr-3 h-5 w-5 text-white\",\n                                                                xmlns: \"http://www.w3.org/2000/svg\",\n                                                                fill: \"none\",\n                                                                viewBox: \"0 0 24 24\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                                                        className: \"opacity-25\",\n                                                                        cx: \"12\",\n                                                                        cy: \"12\",\n                                                                        r: \"10\",\n                                                                        stroke: \"currentColor\",\n                                                                        strokeWidth: \"4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/app/contact/page.tsx\",\n                                                                        lineNumber: 246,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        className: \"opacity-75\",\n                                                                        fill: \"currentColor\",\n                                                                        d: \"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/app/contact/page.tsx\",\n                                                                        lineNumber: 247,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/app/contact/page.tsx\",\n                                                                lineNumber: 245,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            \"提交中...\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/app/contact/page.tsx\",\n                                                        lineNumber: 244,\n                                                        columnNumber: 25\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"flex items-center justify-center\",\n                                                        children: [\n                                                            \"發送諮詢請求\",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_CheckCircle_Clock_Mail_MapPin_MessageSquare_Phone_Send_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                                className: \"w-5 h-5 ml-2\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/app/contact/page.tsx\",\n                                                                lineNumber: 254,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/app/contact/page.tsx\",\n                                                        lineNumber: 252,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/app/contact/page.tsx\",\n                                                    lineNumber: 238,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/app/contact/page.tsx\",\n                                            lineNumber: 141,\n                                            columnNumber: 19\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                                            initial: {\n                                                opacity: 0,\n                                                scale: 0.9\n                                            },\n                                            animate: {\n                                                opacity: 1,\n                                                scale: 1\n                                            },\n                                            className: \"text-center py-12\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-20 h-20 mx-auto mb-6 bg-green-100 rounded-full flex items-center justify-center\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_CheckCircle_Clock_Mail_MapPin_MessageSquare_Phone_Send_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                        className: \"w-10 h-10 text-green-600\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/app/contact/page.tsx\",\n                                                        lineNumber: 266,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/app/contact/page.tsx\",\n                                                    lineNumber: 265,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-2xl font-bold mb-4\",\n                                                    children: \"感謝您的諮詢！\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/app/contact/page.tsx\",\n                                                    lineNumber: 268,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-600 mb-6\",\n                                                    children: \"我們已收到您的訊息，專業顧問將在 24 小時內與您聯繫。\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/app/contact/page.tsx\",\n                                                    lineNumber: 269,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>{\n                                                        setIsSubmitted(false);\n                                                        setFormData({\n                                                            name: '',\n                                                            email: '',\n                                                            company: '',\n                                                            phone: '',\n                                                            service: '',\n                                                            message: ''\n                                                        });\n                                                    },\n                                                    className: \"text-indigo-600 font-semibold hover:text-indigo-700\",\n                                                    children: \"發送新的諮詢\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/app/contact/page.tsx\",\n                                                    lineNumber: 272,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/app/contact/page.tsx\",\n                                            lineNumber: 260,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/app/contact/page.tsx\",\n                                    lineNumber: 137,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/app/contact/page.tsx\",\n                                lineNumber: 131,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    x: 50\n                                },\n                                whileInView: {\n                                    opacity: 1,\n                                    x: 0\n                                },\n                                viewport: {\n                                    once: true\n                                },\n                                transition: {\n                                    duration: 0.8\n                                },\n                                className: \"space-y-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"card-modern p-8 bg-gradient-to-r from-indigo-50 to-purple-50 border-0\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-xl font-bold mb-6\",\n                                                children: \"您將獲得：\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/app/contact/page.tsx\",\n                                                lineNumber: 303,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-3\",\n                                                children: benefits.map((benefit, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                                                        initial: {\n                                                            opacity: 0,\n                                                            x: -20\n                                                        },\n                                                        whileInView: {\n                                                            opacity: 1,\n                                                            x: 0\n                                                        },\n                                                        viewport: {\n                                                            once: true\n                                                        },\n                                                        transition: {\n                                                            duration: 0.5,\n                                                            delay: index * 0.1\n                                                        },\n                                                        className: \"flex items-center space-x-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_CheckCircle_Clock_Mail_MapPin_MessageSquare_Phone_Send_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                                className: \"w-5 h-5 text-green-500 flex-shrink-0\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/app/contact/page.tsx\",\n                                                                lineNumber: 314,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-gray-700\",\n                                                                children: benefit\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/app/contact/page.tsx\",\n                                                                lineNumber: 315,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, index, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/app/contact/page.tsx\",\n                                                        lineNumber: 306,\n                                                        columnNumber: 21\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/app/contact/page.tsx\",\n                                                lineNumber: 304,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/app/contact/page.tsx\",\n                                        lineNumber: 302,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: contactInfo.map((info, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                                                initial: {\n                                                    opacity: 0,\n                                                    y: 20\n                                                },\n                                                whileInView: {\n                                                    opacity: 1,\n                                                    y: 0\n                                                },\n                                                viewport: {\n                                                    once: true\n                                                },\n                                                transition: {\n                                                    duration: 0.6,\n                                                    delay: index * 0.1\n                                                },\n                                                className: \"card-modern p-6\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-start space-x-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-12 h-12 bg-gradient-to-r from-indigo-600 to-purple-600 rounded-xl flex items-center justify-center flex-shrink-0\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(info.icon, {\n                                                                className: \"w-6 h-6 text-white\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/app/contact/page.tsx\",\n                                                                lineNumber: 334,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/app/contact/page.tsx\",\n                                                            lineNumber: 333,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                    className: \"font-semibold mb-1\",\n                                                                    children: info.title\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/app/contact/page.tsx\",\n                                                                    lineNumber: 337,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-gray-900\",\n                                                                    children: info.content\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/app/contact/page.tsx\",\n                                                                    lineNumber: 338,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm text-gray-600\",\n                                                                    children: info.subContent\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/app/contact/page.tsx\",\n                                                                    lineNumber: 339,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/app/contact/page.tsx\",\n                                                            lineNumber: 336,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/app/contact/page.tsx\",\n                                                    lineNumber: 332,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, index, false, {\n                                                fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/app/contact/page.tsx\",\n                                                lineNumber: 324,\n                                                columnNumber: 19\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/app/contact/page.tsx\",\n                                        lineNumber: 322,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"card-modern p-6 text-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_CheckCircle_Clock_Mail_MapPin_MessageSquare_Phone_Send_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                className: \"w-12 h-12 mx-auto mb-4 text-indigo-600\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/app/contact/page.tsx\",\n                                                lineNumber: 348,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"font-semibold mb-2\",\n                                                children: \"需要即時對話？\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/app/contact/page.tsx\",\n                                                lineNumber: 349,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-600 mb-4\",\n                                                children: \"我們的線上客服隨時為您解答疑問\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/app/contact/page.tsx\",\n                                                lineNumber: 350,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                className: \"btn-secondary w-full\",\n                                                children: \"開始線上對話\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/app/contact/page.tsx\",\n                                                lineNumber: 353,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/app/contact/page.tsx\",\n                                        lineNumber: 347,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/app/contact/page.tsx\",\n                                lineNumber: 294,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/app/contact/page.tsx\",\n                        lineNumber: 129,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/app/contact/page.tsx\",\n                    lineNumber: 128,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/app/contact/page.tsx\",\n                lineNumber: 127,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"section-spacing\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container-custom\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                            initial: {\n                                opacity: 0,\n                                y: 20\n                            },\n                            whileInView: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            viewport: {\n                                once: true\n                            },\n                            className: \"text-center mb-12\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"heading-lg mb-6\",\n                                    children: [\n                                        \"歡迎蒞臨我們的 \",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"gradient-text\",\n                                            children: \"辦公室\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/app/contact/page.tsx\",\n                                            lineNumber: 372,\n                                            columnNumber: 23\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/app/contact/page.tsx\",\n                                    lineNumber: 371,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"body-lg text-gray-600 max-w-3xl mx-auto\",\n                                    children: \"位於香港中環核心商業區，交通便利，歡迎預約拜訪\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/app/contact/page.tsx\",\n                                    lineNumber: 374,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/app/contact/page.tsx\",\n                            lineNumber: 365,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                            initial: {\n                                opacity: 0,\n                                scale: 0.95\n                            },\n                            whileInView: {\n                                opacity: 1,\n                                scale: 1\n                            },\n                            viewport: {\n                                once: true\n                            },\n                            transition: {\n                                duration: 0.8\n                            },\n                            className: \"card-modern p-2 overflow-hidden\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"aspect-video bg-gray-200 rounded-lg flex items-center justify-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_CheckCircle_Clock_Mail_MapPin_MessageSquare_Phone_Send_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                            className: \"w-16 h-16 text-gray-400 mx-auto mb-4\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/app/contact/page.tsx\",\n                                            lineNumber: 388,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600\",\n                                            children: \"互動地圖加載中...\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/app/contact/page.tsx\",\n                                            lineNumber: 389,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/app/contact/page.tsx\",\n                                    lineNumber: 387,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/app/contact/page.tsx\",\n                                lineNumber: 386,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/app/contact/page.tsx\",\n                            lineNumber: 379,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/app/contact/page.tsx\",\n                    lineNumber: 364,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/app/contact/page.tsx\",\n                lineNumber: 363,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"section-spacing bg-gradient-to-b from-gray-50 to-white\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container-custom\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                            initial: {\n                                opacity: 0,\n                                y: 20\n                            },\n                            whileInView: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            viewport: {\n                                once: true\n                            },\n                            className: \"text-center mb-12\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"heading-lg mb-6\",\n                                children: \"常見問題\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/app/contact/page.tsx\",\n                                lineNumber: 405,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/app/contact/page.tsx\",\n                            lineNumber: 399,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"max-w-3xl mx-auto space-y-4\",\n                            children: [\n                                {\n                                    question: '諮詢服務是否收費？',\n                                    answer: '首次諮詢完全免費，我們會為您提供初步的營銷策略建議和解決方案。'\n                                },\n                                {\n                                    question: '項目通常需要多長時間？',\n                                    answer: '根據項目複雜度不同，一般需要 2-6 個月。我們會在初次評估後提供詳細時間表。'\n                                },\n                                {\n                                    question: '你們服務哪些地區？',\n                                    answer: '我們的服務覆蓋全球，主要服務亞太地區，包括香港、中國大陸、東南亞等市場。'\n                                },\n                                {\n                                    question: '如何確保數據安全？',\n                                    answer: '我們採用企業級安全標準，包括端到端加密、定期安全審計，並完全符合 GDPR 等數據保護法規。'\n                                }\n                            ].map((faq, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                                    initial: {\n                                        opacity: 0,\n                                        y: 20\n                                    },\n                                    whileInView: {\n                                        opacity: 1,\n                                        y: 0\n                                    },\n                                    viewport: {\n                                        once: true\n                                    },\n                                    transition: {\n                                        duration: 0.6,\n                                        delay: index * 0.1\n                                    },\n                                    className: \"card-modern p-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"font-semibold mb-2\",\n                                            children: faq.question\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/app/contact/page.tsx\",\n                                            lineNumber: 435,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600\",\n                                            children: faq.answer\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/app/contact/page.tsx\",\n                                            lineNumber: 436,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, index, true, {\n                                    fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/app/contact/page.tsx\",\n                                    lineNumber: 427,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/app/contact/page.tsx\",\n                            lineNumber: 408,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                            initial: {\n                                opacity: 0,\n                                y: 20\n                            },\n                            whileInView: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            viewport: {\n                                once: true\n                            },\n                            className: \"text-center mt-12\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600 mb-4\",\n                                    children: \"還有其他問題？\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/app/contact/page.tsx\",\n                                    lineNumber: 447,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                    href: \"#\",\n                                    className: \"text-indigo-600 font-semibold hover:text-indigo-700\",\n                                    children: [\n                                        \"查看完整 FAQ\",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_CheckCircle_Clock_Mail_MapPin_MessageSquare_Phone_Send_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                            className: \"inline-block w-4 h-4 ml-1\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/app/contact/page.tsx\",\n                                            lineNumber: 450,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/app/contact/page.tsx\",\n                                    lineNumber: 448,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/app/contact/page.tsx\",\n                            lineNumber: 441,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/app/contact/page.tsx\",\n                    lineNumber: 398,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/app/contact/page.tsx\",\n                lineNumber: 397,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/app/contact/page.tsx\",\n        lineNumber: 91,\n        columnNumber: 5\n    }, this);\n}\n_s(ContactPage, \"gnnLvXbF0lCppG3H4E/MkJjDUP8=\");\n_c = ContactPage;\nvar _c;\n$RefreshReg$(_c, \"ContactPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/contact/page.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-right.js":
/*!*****************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/arrow-right.js ***!
  \*****************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __iconNode: () => (/* binding */ __iconNode),\n/* harmony export */   \"default\": () => (/* binding */ ArrowRight)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst __iconNode = [\n    [\n        \"path\",\n        {\n            d: \"M5 12h14\",\n            key: \"1ays0h\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"m12 5 7 7-7 7\",\n            key: \"xquz4c\"\n        }\n    ]\n];\nconst ArrowRight = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"arrow-right\", __iconNode);\n //# sourceMappingURL=arrow-right.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-right.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js":
/*!**********************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/circle-check-big.js ***!
  \**********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __iconNode: () => (/* binding */ __iconNode),\n/* harmony export */   \"default\": () => (/* binding */ CircleCheckBig)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst __iconNode = [\n    [\n        \"path\",\n        {\n            d: \"M21.801 10A10 10 0 1 1 17 3.335\",\n            key: \"yps3ct\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"m9 11 3 3L22 4\",\n            key: \"1pflzl\"\n        }\n    ]\n];\nconst CircleCheckBig = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"circle-check-big\", __iconNode);\n //# sourceMappingURL=circle-check-big.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/sparkles.js":
/*!**************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/sparkles.js ***!
  \**************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __iconNode: () => (/* binding */ __iconNode),\n/* harmony export */   \"default\": () => (/* binding */ Sparkles)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst __iconNode = [\n    [\n        \"path\",\n        {\n            d: \"M9.937 15.5A2 2 0 0 0 8.5 14.063l-6.135-1.582a.5.5 0 0 1 0-.962L8.5 9.936A2 2 0 0 0 9.937 8.5l1.582-6.135a.5.5 0 0 1 .963 0L14.063 8.5A2 2 0 0 0 15.5 9.937l6.135 1.581a.5.5 0 0 1 0 .964L15.5 14.063a2 2 0 0 0-1.437 1.437l-1.582 6.135a.5.5 0 0 1-.963 0z\",\n            key: \"4pj2yx\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M20 3v4\",\n            key: \"1olli1\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M22 5h-4\",\n            key: \"1gvqau\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M4 17v2\",\n            key: \"vumght\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M5 18H3\",\n            key: \"zchphs\"\n        }\n    ]\n];\nconst Sparkles = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"sparkles\", __iconNode);\n //# sourceMappingURL=sparkles.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/sparkles.js\n"));

/***/ })

});