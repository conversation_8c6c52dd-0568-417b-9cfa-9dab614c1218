"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./components/Hero.tsx":
/*!*****************************!*\
  !*** ./components/Hero.tsx ***!
  \*****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Brain_CheckCircle_Play_Sparkles_TrendingUp_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Brain,CheckCircle,Play,Sparkles,TrendingUp,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/brain.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Brain_CheckCircle_Play_Sparkles_TrendingUp_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Brain,CheckCircle,Play,Sparkles,TrendingUp,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Brain_CheckCircle_Play_Sparkles_TrendingUp_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Brain,CheckCircle,Play,Sparkles,TrendingUp,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Brain_CheckCircle_Play_Sparkles_TrendingUp_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Brain,CheckCircle,Play,Sparkles,TrendingUp,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/sparkles.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Brain_CheckCircle_Play_Sparkles_TrendingUp_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Brain,CheckCircle,Play,Sparkles,TrendingUp,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-right.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Brain_CheckCircle_Play_Sparkles_TrendingUp_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Brain,CheckCircle,Play,Sparkles,TrendingUp,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/play.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Brain_CheckCircle_Play_Sparkles_TrendingUp_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Brain,CheckCircle,Play,Sparkles,TrendingUp,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Brain_CheckCircle_Play_Sparkles_TrendingUp_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Brain,CheckCircle,Play,Sparkles,TrendingUp,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nconst Hero = ()=>{\n    _s();\n    const [isVideoModalOpen, setIsVideoModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const features = [\n        {\n            icon: _barrel_optimize_names_ArrowRight_Brain_CheckCircle_Play_Sparkles_TrendingUp_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n            text: 'AI 驅動的智能分析'\n        },\n        {\n            icon: _barrel_optimize_names_ArrowRight_Brain_CheckCircle_Play_Sparkles_TrendingUp_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n            text: '營銷效果提升 300%'\n        },\n        {\n            icon: _barrel_optimize_names_ArrowRight_Brain_CheckCircle_Play_Sparkles_TrendingUp_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n            text: '已服務 500+ 企業客戶'\n        }\n    ];\n    const clients = [\n        'Google',\n        'Microsoft',\n        'Amazon',\n        'Meta',\n        'Apple'\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        className: \"relative min-h-screen flex items-center pt-20 pb-20 overflow-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 -z-10\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                        animate: {\n                            scale: [\n                                1,\n                                1.2,\n                                1\n                            ],\n                            rotate: [\n                                0,\n                                180,\n                                360\n                            ]\n                        },\n                        transition: {\n                            duration: 20,\n                            repeat: Infinity,\n                            repeatType: \"reverse\"\n                        },\n                        className: \"absolute top-1/4 left-1/4 w-96 h-96 bg-gradient-to-r from-indigo-400 to-purple-400 rounded-full blur-3xl opacity-20\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Hero.tsx\",\n                        lineNumber: 32,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                        animate: {\n                            scale: [\n                                1.2,\n                                1,\n                                1.2\n                            ],\n                            rotate: [\n                                360,\n                                180,\n                                0\n                            ]\n                        },\n                        transition: {\n                            duration: 25,\n                            repeat: Infinity,\n                            repeatType: \"reverse\"\n                        },\n                        className: \"absolute bottom-1/4 right-1/4 w-96 h-96 bg-gradient-to-r from-purple-400 to-pink-400 rounded-full blur-3xl opacity-20\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Hero.tsx\",\n                        lineNumber: 44,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Hero.tsx\",\n                lineNumber: 31,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"container-custom\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid lg:grid-cols-2 gap-12 items-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                            initial: {\n                                opacity: 0,\n                                x: -50\n                            },\n                            animate: {\n                                opacity: 1,\n                                x: 0\n                            },\n                            transition: {\n                                duration: 0.8\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                                    initial: {\n                                        opacity: 0,\n                                        y: 20\n                                    },\n                                    animate: {\n                                        opacity: 1,\n                                        y: 0\n                                    },\n                                    transition: {\n                                        duration: 0.6\n                                    },\n                                    className: \"inline-flex items-center space-x-2 badge badge-primary mb-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Brain_CheckCircle_Play_Sparkles_TrendingUp_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                            className: \"w-4 h-4\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Hero.tsx\",\n                                            lineNumber: 73,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"香港領先的 AI 營銷專家\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Hero.tsx\",\n                                            lineNumber: 74,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Hero.tsx\",\n                                    lineNumber: 67,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"heading-xl mb-6\",\n                                    children: [\n                                        \"用 \",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"gradient-text\",\n                                            children: \"人工智能\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Hero.tsx\",\n                                            lineNumber: 79,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Hero.tsx\",\n                                            lineNumber: 80,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        \"重新定義營銷\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Hero.tsx\",\n                                    lineNumber: 78,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"body-lg mb-8 text-gray-600\",\n                                    children: \"透過尖端的 AI 技術和數據分析，我們幫助企業實現營銷自動化、 精準定位目標客戶，並將營銷投資回報率提升至新高度。\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Hero.tsx\",\n                                    lineNumber: 85,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-3 mb-8\",\n                                    children: features.map((feature, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                                            initial: {\n                                                opacity: 0,\n                                                x: -20\n                                            },\n                                            animate: {\n                                                opacity: 1,\n                                                x: 0\n                                            },\n                                            transition: {\n                                                duration: 0.6,\n                                                delay: 0.2 + index * 0.1\n                                            },\n                                            className: \"flex items-center space-x-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-2 rounded-lg bg-indigo-100\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(feature.icon, {\n                                                        className: \"w-5 h-5 text-indigo-600\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Hero.tsx\",\n                                                        lineNumber: 101,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Hero.tsx\",\n                                                    lineNumber: 100,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-gray-700 font-medium\",\n                                                    children: feature.text\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Hero.tsx\",\n                                                    lineNumber: 103,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, index, true, {\n                                            fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Hero.tsx\",\n                                            lineNumber: 93,\n                                            columnNumber: 17\n                                        }, undefined))\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Hero.tsx\",\n                                    lineNumber: 91,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                                    initial: {\n                                        opacity: 0,\n                                        y: 20\n                                    },\n                                    animate: {\n                                        opacity: 1,\n                                        y: 0\n                                    },\n                                    transition: {\n                                        duration: 0.8,\n                                        delay: 0.5\n                                    },\n                                    className: \"flex flex-wrap gap-4 mb-12\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                            href: \"/contact\",\n                                            className: \"btn-primary\",\n                                            children: [\n                                                \"開始免費諮詢\",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Brain_CheckCircle_Play_Sparkles_TrendingUp_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                    className: \"inline-block ml-2 w-5 h-5\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Hero.tsx\",\n                                                    lineNumber: 117,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Hero.tsx\",\n                                            lineNumber: 115,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setIsVideoModalOpen(true),\n                                            className: \"btn-secondary flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Brain_CheckCircle_Play_Sparkles_TrendingUp_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                    className: \"w-5 h-5 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Hero.tsx\",\n                                                    lineNumber: 123,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                \"觀看演示視頻\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Hero.tsx\",\n                                            lineNumber: 119,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Hero.tsx\",\n                                    lineNumber: 109,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                                    initial: {\n                                        opacity: 0\n                                    },\n                                    animate: {\n                                        opacity: 1\n                                    },\n                                    transition: {\n                                        duration: 1,\n                                        delay: 0.8\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-500 mb-4\",\n                                            children: \"受到全球領先企業的信任\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Hero.tsx\",\n                                            lineNumber: 134,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-wrap items-center gap-8\",\n                                            children: clients.map((client, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-gray-400 font-semibold text-lg opacity-60 hover:opacity-100 transition-opacity\",\n                                                    children: client\n                                                }, index, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Hero.tsx\",\n                                                    lineNumber: 137,\n                                                    columnNumber: 19\n                                                }, undefined))\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Hero.tsx\",\n                                            lineNumber: 135,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Hero.tsx\",\n                                    lineNumber: 129,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Hero.tsx\",\n                            lineNumber: 61,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                            initial: {\n                                opacity: 0,\n                                x: 50\n                            },\n                            animate: {\n                                opacity: 1,\n                                x: 0\n                            },\n                            transition: {\n                                duration: 0.8\n                            },\n                            className: \"relative lg:h-[600px]\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative w-full h-full\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                                        whileHover: {\n                                            scale: 1.02\n                                        },\n                                        className: \"absolute top-0 right-0 w-full max-w-md\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"card-modern p-8 relative overflow-hidden\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute top-0 right-0 w-32 h-32 bg-gradient-to-br from-indigo-500 to-purple-500 rounded-full blur-3xl opacity-20\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Hero.tsx\",\n                                                    lineNumber: 163,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"relative z-10\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-16 h-16 bg-gradient-to-r from-indigo-600 to-purple-600 rounded-2xl flex items-center justify-center mb-6\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Brain_CheckCircle_Play_Sparkles_TrendingUp_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                                className: \"w-8 h-8 text-white\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Hero.tsx\",\n                                                                lineNumber: 166,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Hero.tsx\",\n                                                            lineNumber: 165,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"text-2xl font-bold mb-4\",\n                                                            children: \"AI 營銷大腦\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Hero.tsx\",\n                                                            lineNumber: 168,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-600 mb-6\",\n                                                            children: \"整合機器學習、自然語言處理和預測分析，為您的營銷策略提供智能決策支持\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Hero.tsx\",\n                                                            lineNumber: 169,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-3\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center space-x-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Brain_CheckCircle_Play_Sparkles_TrendingUp_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                            className: \"w-5 h-5 text-green-500\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Hero.tsx\",\n                                                                            lineNumber: 174,\n                                                                            columnNumber: 25\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-sm\",\n                                                                            children: \"實時數據分析\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Hero.tsx\",\n                                                                            lineNumber: 175,\n                                                                            columnNumber: 25\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Hero.tsx\",\n                                                                    lineNumber: 173,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center space-x-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Brain_CheckCircle_Play_Sparkles_TrendingUp_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                            className: \"w-5 h-5 text-green-500\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Hero.tsx\",\n                                                                            lineNumber: 178,\n                                                                            columnNumber: 25\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-sm\",\n                                                                            children: \"自動化營銷流程\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Hero.tsx\",\n                                                                            lineNumber: 179,\n                                                                            columnNumber: 25\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Hero.tsx\",\n                                                                    lineNumber: 177,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center space-x-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Brain_CheckCircle_Play_Sparkles_TrendingUp_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                            className: \"w-5 h-5 text-green-500\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Hero.tsx\",\n                                                                            lineNumber: 182,\n                                                                            columnNumber: 25\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-sm\",\n                                                                            children: \"個性化客戶體驗\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Hero.tsx\",\n                                                                            lineNumber: 183,\n                                                                            columnNumber: 25\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Hero.tsx\",\n                                                                    lineNumber: 181,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Hero.tsx\",\n                                                            lineNumber: 172,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Hero.tsx\",\n                                                    lineNumber: 164,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Hero.tsx\",\n                                            lineNumber: 162,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Hero.tsx\",\n                                        lineNumber: 158,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                                        animate: {\n                                            y: [\n                                                0,\n                                                -20,\n                                                0\n                                            ]\n                                        },\n                                        transition: {\n                                            duration: 4,\n                                            repeat: Infinity\n                                        },\n                                        className: \"absolute top-20 left-0 card-modern p-4 shadow-xl\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-12 h-12 bg-green-100 rounded-xl flex items-center justify-center\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Brain_CheckCircle_Play_Sparkles_TrendingUp_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                        className: \"w-6 h-6 text-green-600\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Hero.tsx\",\n                                                        lineNumber: 198,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Hero.tsx\",\n                                                    lineNumber: 197,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-500\",\n                                                            children: \"轉化率提升\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Hero.tsx\",\n                                                            lineNumber: 201,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-xl font-bold text-green-600\",\n                                                            children: \"+287%\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Hero.tsx\",\n                                                            lineNumber: 202,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Hero.tsx\",\n                                                    lineNumber: 200,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Hero.tsx\",\n                                            lineNumber: 196,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Hero.tsx\",\n                                        lineNumber: 191,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                                        animate: {\n                                            y: [\n                                                0,\n                                                20,\n                                                0\n                                            ]\n                                        },\n                                        transition: {\n                                            duration: 5,\n                                            repeat: Infinity\n                                        },\n                                        className: \"absolute bottom-20 left-10 card-modern p-4 shadow-xl\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-12 h-12 bg-purple-100 rounded-xl flex items-center justify-center\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Brain_CheckCircle_Play_Sparkles_TrendingUp_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                        className: \"w-6 h-6 text-purple-600\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Hero.tsx\",\n                                                        lineNumber: 214,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Hero.tsx\",\n                                                    lineNumber: 213,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-500\",\n                                                            children: \"活躍用戶\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Hero.tsx\",\n                                                            lineNumber: 217,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-xl font-bold text-purple-600\",\n                                                            children: \"2.5M+\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Hero.tsx\",\n                                                            lineNumber: 218,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Hero.tsx\",\n                                                    lineNumber: 216,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Hero.tsx\",\n                                            lineNumber: 212,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Hero.tsx\",\n                                        lineNumber: 207,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                                        animate: {\n                                            rotate: 360\n                                        },\n                                        transition: {\n                                            duration: 20,\n                                            repeat: Infinity,\n                                            ease: \"linear\"\n                                        },\n                                        className: \"absolute -bottom-10 -right-10 w-40 h-40 border-4 border-indigo-200 rounded-full\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Hero.tsx\",\n                                        lineNumber: 224,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                                        animate: {\n                                            rotate: -360\n                                        },\n                                        transition: {\n                                            duration: 15,\n                                            repeat: Infinity,\n                                            ease: \"linear\"\n                                        },\n                                        className: \"absolute -top-5 -left-5 w-32 h-32 border-4 border-purple-200 rounded-full\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Hero.tsx\",\n                                        lineNumber: 229,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Hero.tsx\",\n                                lineNumber: 156,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Hero.tsx\",\n                            lineNumber: 149,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Hero.tsx\",\n                    lineNumber: 59,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Hero.tsx\",\n                lineNumber: 58,\n                columnNumber: 7\n            }, undefined),\n            isVideoModalOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                initial: {\n                    opacity: 0\n                },\n                animate: {\n                    opacity: 1\n                },\n                exit: {\n                    opacity: 0\n                },\n                className: \"fixed inset-0 z-50 flex items-center justify-center bg-black/80 p-4\",\n                onClick: ()=>setIsVideoModalOpen(false),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                    initial: {\n                        scale: 0.9\n                    },\n                    animate: {\n                        scale: 1\n                    },\n                    exit: {\n                        scale: 0.9\n                    },\n                    className: \"relative w-full max-w-4xl aspect-video bg-gray-900 rounded-2xl overflow-hidden\",\n                    onClick: (e)=>e.stopPropagation(),\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>setIsVideoModalOpen(false),\n                            className: \"absolute top-4 right-4 p-2 bg-white/10 hover:bg-white/20 rounded-full text-white transition-colors\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Brain_CheckCircle_Play_Sparkles_TrendingUp_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                size: 24\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Hero.tsx\",\n                                lineNumber: 259,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Hero.tsx\",\n                            lineNumber: 255,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-full h-full flex items-center justify-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-white text-xl\",\n                                children: \"視頻演示內容\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Hero.tsx\",\n                                lineNumber: 262,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Hero.tsx\",\n                            lineNumber: 261,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Hero.tsx\",\n                    lineNumber: 248,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Hero.tsx\",\n                lineNumber: 241,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Hero.tsx\",\n        lineNumber: 29,\n        columnNumber: 5\n    }, undefined);\n};\n_s(Hero, \"tZ2VFBxQsSrs68t58nYJQ0oEBGg=\");\n_c = Hero;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Hero);\nvar _c;\n$RefreshReg$(_c, \"Hero\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/Hero.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js":
/*!*******************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/x.js ***!
  \*******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __iconNode: () => (/* binding */ __iconNode),\n/* harmony export */   \"default\": () => (/* binding */ X)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst __iconNode = [\n    [\n        \"path\",\n        {\n            d: \"M18 6 6 18\",\n            key: \"1bl5f8\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"m6 6 12 12\",\n            key: \"d8bk6v\"\n        }\n    ]\n];\nconst X = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"x\", __iconNode);\n //# sourceMappingURL=x.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9sdWNpZGUtcmVhY3QvZGlzdC9lc20vaWNvbnMveC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7QUFHTyxNQUFNLFVBQXVCO0lBQ2xDO1FBQUMsTUFBUTtRQUFBO1lBQUUsR0FBRyxDQUFjO1lBQUEsS0FBSztRQUFBLENBQVU7S0FBQTtJQUMzQztRQUFDLENBQVE7UUFBQSxDQUFFO1lBQUEsRUFBRyxhQUFjO1lBQUEsSUFBSztRQUFVO0tBQUE7Q0FDN0M7QUFhTSxRQUFJLGtFQUFpQixNQUFLLENBQVUiLCJzb3VyY2VzIjpbIi9Vc2Vycy9rdy9zcmMvaWNvbnMveC50cyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgY3JlYXRlTHVjaWRlSWNvbiBmcm9tICcuLi9jcmVhdGVMdWNpZGVJY29uJztcbmltcG9ydCB7IEljb25Ob2RlIH0gZnJvbSAnLi4vdHlwZXMnO1xuXG5leHBvcnQgY29uc3QgX19pY29uTm9kZTogSWNvbk5vZGUgPSBbXG4gIFsncGF0aCcsIHsgZDogJ00xOCA2IDYgMTgnLCBrZXk6ICcxYmw1ZjgnIH1dLFxuICBbJ3BhdGgnLCB7IGQ6ICdtNiA2IDEyIDEyJywga2V5OiAnZDhiazZ2JyB9XSxcbl07XG5cbi8qKlxuICogQGNvbXBvbmVudCBAbmFtZSBYXG4gKiBAZGVzY3JpcHRpb24gTHVjaWRlIFNWRyBpY29uIGNvbXBvbmVudCwgcmVuZGVycyBTVkcgRWxlbWVudCB3aXRoIGNoaWxkcmVuLlxuICpcbiAqIEBwcmV2aWV3ICFbaW1nXShkYXRhOmltYWdlL3N2Zyt4bWw7YmFzZTY0LFBITjJaeUFnZUcxc2JuTTlJbWgwZEhBNkx5OTNkM2N1ZHpNdWIzSm5Mekl3TURBdmMzWm5JZ29nSUhkcFpIUm9QU0l5TkNJS0lDQm9aV2xuYUhROUlqSTBJZ29nSUhacFpYZENiM2c5SWpBZ01DQXlOQ0F5TkNJS0lDQm1hV3hzUFNKdWIyNWxJZ29nSUhOMGNtOXJaVDBpSXpBd01DSWdjM1I1YkdVOUltSmhZMnRuY205MWJtUXRZMjlzYjNJNklDTm1abVk3SUdKdmNtUmxjaTF5WVdScGRYTTZJREp3ZUNJS0lDQnpkSEp2YTJVdGQybGtkR2c5SWpJaUNpQWdjM1J5YjJ0bExXeHBibVZqWVhBOUluSnZkVzVrSWdvZ0lITjBjbTlyWlMxc2FXNWxhbTlwYmowaWNtOTFibVFpQ2o0S0lDQThjR0YwYUNCa1BTSk5NVGdnTmlBMklERTRJaUF2UGdvZ0lEeHdZWFJvSUdROUltMDJJRFlnTVRJZ01USWlJQzgrQ2p3dmMzWm5QZ289KSAtIGh0dHBzOi8vbHVjaWRlLmRldi9pY29ucy94XG4gKiBAc2VlIGh0dHBzOi8vbHVjaWRlLmRldi9ndWlkZS9wYWNrYWdlcy9sdWNpZGUtcmVhY3QgLSBEb2N1bWVudGF0aW9uXG4gKlxuICogQHBhcmFtIHtPYmplY3R9IHByb3BzIC0gTHVjaWRlIGljb25zIHByb3BzIGFuZCBhbnkgdmFsaWQgU1ZHIGF0dHJpYnV0ZVxuICogQHJldHVybnMge0pTWC5FbGVtZW50fSBKU1ggRWxlbWVudFxuICpcbiAqL1xuY29uc3QgWCA9IGNyZWF0ZUx1Y2lkZUljb24oJ3gnLCBfX2ljb25Ob2RlKTtcblxuZXhwb3J0IGRlZmF1bHQgWDtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\n"));

/***/ })

});