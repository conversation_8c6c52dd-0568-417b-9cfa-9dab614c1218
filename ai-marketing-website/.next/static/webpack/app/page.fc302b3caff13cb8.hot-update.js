"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./components/Stats.tsx":
/*!******************************!*\
  !*** ./components/Stats.tsx ***!
  \******************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/value/use-motion-value.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/value/use-transform.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/animation/animate/index.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_ArrowUpRight_Award_Globe_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowUpRight,Award,Globe,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowUpRight_Award_Globe_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowUpRight,Award,Globe,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowUpRight_Award_Globe_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowUpRight,Award,Globe,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/award.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowUpRight_Award_Globe_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowUpRight,Award,Globe,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/globe.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowUpRight_Award_Globe_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowUpRight,Award,Globe,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-up-right.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\nconst Stats = ()=>{\n    _s();\n    const [isVisible, setIsVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const sectionRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const stats = [\n        {\n            icon: _barrel_optimize_names_ArrowUpRight_Award_Globe_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"],\n            value: 287,\n            suffix: '%',\n            label: '平均 ROI 提升',\n            description: '客戶營銷投資回報率平均增長',\n            color: 'from-green-500 to-emerald-500'\n        },\n        {\n            icon: _barrel_optimize_names_ArrowUpRight_Award_Globe_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n            value: 500,\n            suffix: '+',\n            label: '服務企業',\n            description: '信任我們的企業客戶數量',\n            color: 'from-indigo-500 to-purple-500'\n        },\n        {\n            icon: _barrel_optimize_names_ArrowUpRight_Award_Globe_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n            value: 98,\n            suffix: '%',\n            label: '客戶滿意度',\n            description: '客戶對我們服務的滿意評分',\n            color: 'from-purple-500 to-pink-500'\n        },\n        {\n            icon: _barrel_optimize_names_ArrowUpRight_Award_Globe_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n            value: 15,\n            suffix: '+',\n            label: '國家地區',\n            description: '我們的服務覆蓋全球範圍',\n            color: 'from-cyan-500 to-blue-500'\n        }\n    ];\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Stats.useEffect\": ()=>{\n            const observer = new IntersectionObserver({\n                \"Stats.useEffect\": (param)=>{\n                    let [entry] = param;\n                    if (entry.isIntersecting) {\n                        setIsVisible(true);\n                    }\n                }\n            }[\"Stats.useEffect\"], {\n                threshold: 0.1\n            });\n            if (sectionRef.current) {\n                observer.observe(sectionRef.current);\n            }\n            return ({\n                \"Stats.useEffect\": ()=>observer.disconnect()\n            })[\"Stats.useEffect\"];\n        }\n    }[\"Stats.useEffect\"], []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        ref: sectionRef,\n        className: \"section-spacing relative\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 -z-10\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 bg-gradient-to-b from-gray-50 to-white\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Stats.tsx\",\n                        lineNumber: 73,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0\",\n                        style: {\n                            backgroundImage: \"radial-gradient(circle at 1px 1px, rgb(229 231 235) 1px, transparent 1px)\",\n                            backgroundSize: '40px 40px',\n                            opacity: 0.3\n                        }\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Stats.tsx\",\n                        lineNumber: 74,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Stats.tsx\",\n                lineNumber: 72,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"container-custom\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            y: 20\n                        },\n                        whileInView: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        viewport: {\n                            once: true\n                        },\n                        transition: {\n                            duration: 0.6\n                        },\n                        className: \"text-center mb-16\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"heading-lg mb-6\",\n                                children: [\n                                    \"用數據說話的 \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"gradient-text\",\n                                        children: \"卓越成果\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Stats.tsx\",\n                                        lineNumber: 91,\n                                        columnNumber: 20\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Stats.tsx\",\n                                lineNumber: 90,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"body-lg text-gray-600 max-w-3xl mx-auto\",\n                                children: \"我們的 AI 營銷解決方案已經幫助數百家企業實現了業績突破， 這些數字見證了我們的專業實力\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Stats.tsx\",\n                                lineNumber: 93,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Stats.tsx\",\n                        lineNumber: 83,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8\",\n                        children: stats.map((stat, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    y: 20\n                                },\n                                whileInView: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                viewport: {\n                                    once: true\n                                },\n                                transition: {\n                                    duration: 0.6,\n                                    delay: index * 0.1\n                                },\n                                className: \"relative group\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"card-modern p-8 h-full relative overflow-hidden\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute top-0 right-0 w-full h-full bg-gradient-to-br \".concat(stat.color, \" opacity-5 group-hover:opacity-10 transition-opacity\")\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Stats.tsx\",\n                                            lineNumber: 112,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-14 h-14 bg-gradient-to-r \".concat(stat.color, \" rounded-2xl flex items-center justify-center mb-6\"),\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(stat.icon, {\n                                                className: \"w-7 h-7 text-white\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Stats.tsx\",\n                                                lineNumber: 116,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Stats.tsx\",\n                                            lineNumber: 115,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mb-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CountingNumber, {\n                                                value: stat.value,\n                                                suffix: stat.suffix,\n                                                isVisible: isVisible\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Stats.tsx\",\n                                                lineNumber: 121,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Stats.tsx\",\n                                            lineNumber: 120,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-xl font-bold mb-2 text-gray-900\",\n                                            children: stat.label\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Stats.tsx\",\n                                            lineNumber: 129,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600 text-sm leading-relaxed\",\n                                            children: stat.description\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Stats.tsx\",\n                                            lineNumber: 132,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                                            initial: {\n                                                opacity: 0,\n                                                x: -10,\n                                                y: 10\n                                            },\n                                            whileHover: {\n                                                opacity: 1,\n                                                x: 0,\n                                                y: 0\n                                            },\n                                            className: \"absolute bottom-4 right-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowUpRight_Award_Globe_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                className: \"w-5 h-5 text-gray-400\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Stats.tsx\",\n                                                lineNumber: 140,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Stats.tsx\",\n                                            lineNumber: 135,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Stats.tsx\",\n                                    lineNumber: 110,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, index, false, {\n                                fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Stats.tsx\",\n                                lineNumber: 102,\n                                columnNumber: 13\n                            }, undefined))\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Stats.tsx\",\n                        lineNumber: 100,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            y: 20\n                        },\n                        whileInView: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        viewport: {\n                            once: true\n                        },\n                        transition: {\n                            duration: 0.6,\n                            delay: 0.4\n                        },\n                        className: \"mt-16 text-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"card-modern p-8 md:p-12 bg-gradient-to-r from-indigo-50 to-purple-50 border-0\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"heading-md mb-4\",\n                                    children: \"準備加入成功企業的行列嗎？\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Stats.tsx\",\n                                    lineNumber: 156,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"body-lg text-gray-600 mb-8 max-w-2xl mx-auto\",\n                                    children: \"讓我們的 AI 營銷專家為您量身定制解決方案，開啟業績增長新篇章\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Stats.tsx\",\n                                    lineNumber: 159,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                    href: \"/case-studies\",\n                                    className: \"btn-primary\",\n                                    children: [\n                                        \"查看成功案例\",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowUpRight_Award_Globe_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                            className: \"inline-block ml-2 w-5 h-5\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Stats.tsx\",\n                                            lineNumber: 164,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Stats.tsx\",\n                                    lineNumber: 162,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Stats.tsx\",\n                            lineNumber: 155,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Stats.tsx\",\n                        lineNumber: 148,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Stats.tsx\",\n                lineNumber: 81,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Stats.tsx\",\n        lineNumber: 70,\n        columnNumber: 5\n    }, undefined);\n};\n_s(Stats, \"t6xTCIDW8wBKaEAVt1RBRErIU/M=\");\n_c = Stats;\n// Counting Number Component\nconst CountingNumber = (param)=>{\n    let { value, suffix, isVisible } = param;\n    _s1();\n    const count = (0,framer_motion__WEBPACK_IMPORTED_MODULE_8__.useMotionValue)(0);\n    const rounded = (0,framer_motion__WEBPACK_IMPORTED_MODULE_9__.useTransform)(count, Math.round);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"CountingNumber.useEffect\": ()=>{\n            if (isVisible) {\n                const animation = (0,framer_motion__WEBPACK_IMPORTED_MODULE_10__.animate)(count, value, {\n                    duration: 2,\n                    ease: 'easeOut'\n                });\n                return animation.stop;\n            }\n        }\n    }[\"CountingNumber.useEffect\"], [\n        isVisible,\n        value,\n        count\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex items-baseline\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.span, {\n                className: \"text-5xl font-bold gradient-text\",\n                children: rounded\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Stats.tsx\",\n                lineNumber: 198,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"text-3xl font-bold gradient-text ml-1\",\n                children: suffix\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Stats.tsx\",\n                lineNumber: 201,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Stats.tsx\",\n        lineNumber: 197,\n        columnNumber: 5\n    }, undefined);\n};\n_s1(CountingNumber, \"Xwv6a+OUhW4aFeMrSXmDsCTZQqQ=\", false, function() {\n    return [\n        framer_motion__WEBPACK_IMPORTED_MODULE_8__.useMotionValue,\n        framer_motion__WEBPACK_IMPORTED_MODULE_9__.useTransform\n    ];\n});\n_c1 = CountingNumber;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Stats);\nvar _c, _c1;\n$RefreshReg$(_c, \"Stats\");\n$RefreshReg$(_c1, \"CountingNumber\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/Stats.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/animation/animate/index.mjs":
/*!************************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/animation/animate/index.mjs ***!
  \************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   animate: () => (/* binding */ animate),\n/* harmony export */   createScopedAnimate: () => (/* binding */ createScopedAnimate)\n/* harmony export */ });\n/* harmony import */ var motion_dom__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! motion-dom */ \"(app-pages-browser)/./node_modules/motion-dom/dist/es/animation/GroupAnimationWithThen.mjs\");\n/* harmony import */ var motion_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! motion-utils */ \"(app-pages-browser)/./node_modules/motion-utils/dist/es/array.mjs\");\n/* harmony import */ var _sequence_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./sequence.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/animation/animate/sequence.mjs\");\n/* harmony import */ var _subject_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./subject.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/animation/animate/subject.mjs\");\n\n\n\n\n\nfunction isSequence(value) {\n    return Array.isArray(value) && value.some(Array.isArray);\n}\n/**\n * Creates an animation function that is optionally scoped\n * to a specific element.\n */\nfunction createScopedAnimate(scope) {\n    /**\n     * Implementation\n     */\n    function scopedAnimate(subjectOrSequence, optionsOrKeyframes, options) {\n        let animations = [];\n        if (isSequence(subjectOrSequence)) {\n            animations = (0,_sequence_mjs__WEBPACK_IMPORTED_MODULE_0__.animateSequence)(subjectOrSequence, optionsOrKeyframes, scope);\n        }\n        else {\n            animations = (0,_subject_mjs__WEBPACK_IMPORTED_MODULE_1__.animateSubject)(subjectOrSequence, optionsOrKeyframes, options, scope);\n        }\n        const animation = new motion_dom__WEBPACK_IMPORTED_MODULE_2__.GroupAnimationWithThen(animations);\n        if (scope) {\n            scope.animations.push(animation);\n            animation.finished.then(() => {\n                (0,motion_utils__WEBPACK_IMPORTED_MODULE_3__.removeItem)(scope.animations, animation);\n            });\n        }\n        return animation;\n    }\n    return scopedAnimate;\n}\nconst animate = createScopedAnimate();\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/animation/animate/index.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/animation/animate/resolve-subjects.mjs":
/*!***********************************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/animation/animate/resolve-subjects.mjs ***!
  \***********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   resolveSubjects: () => (/* binding */ resolveSubjects)\n/* harmony export */ });\n/* harmony import */ var motion_dom__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! motion-dom */ \"(app-pages-browser)/./node_modules/motion-dom/dist/es/utils/resolve-elements.mjs\");\n/* harmony import */ var _utils_is_dom_keyframes_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../utils/is-dom-keyframes.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/animation/utils/is-dom-keyframes.mjs\");\n\n\n\nfunction resolveSubjects(subject, keyframes, scope, selectorCache) {\n    if (typeof subject === \"string\" && (0,_utils_is_dom_keyframes_mjs__WEBPACK_IMPORTED_MODULE_0__.isDOMKeyframes)(keyframes)) {\n        return (0,motion_dom__WEBPACK_IMPORTED_MODULE_1__.resolveElements)(subject, scope, selectorCache);\n    }\n    else if (subject instanceof NodeList) {\n        return Array.from(subject);\n    }\n    else if (Array.isArray(subject)) {\n        return subject;\n    }\n    else {\n        return [subject];\n    }\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvYW5pbWF0aW9uL2FuaW1hdGUvcmVzb2x2ZS1zdWJqZWN0cy5tanMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQTZDO0FBQ2tCOztBQUUvRDtBQUNBLHVDQUF1QywyRUFBYztBQUNyRCxlQUFlLDJEQUFlO0FBQzlCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRTJCIiwic291cmNlcyI6WyIvVXNlcnMva3cvRGVza3RvcC9DbGF1ZGUvYWktbWFya2V0aW5nLXdlYnNpdGUvbm9kZV9tb2R1bGVzL2ZyYW1lci1tb3Rpb24vZGlzdC9lcy9hbmltYXRpb24vYW5pbWF0ZS9yZXNvbHZlLXN1YmplY3RzLm1qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyByZXNvbHZlRWxlbWVudHMgfSBmcm9tICdtb3Rpb24tZG9tJztcbmltcG9ydCB7IGlzRE9NS2V5ZnJhbWVzIH0gZnJvbSAnLi4vdXRpbHMvaXMtZG9tLWtleWZyYW1lcy5tanMnO1xuXG5mdW5jdGlvbiByZXNvbHZlU3ViamVjdHMoc3ViamVjdCwga2V5ZnJhbWVzLCBzY29wZSwgc2VsZWN0b3JDYWNoZSkge1xuICAgIGlmICh0eXBlb2Ygc3ViamVjdCA9PT0gXCJzdHJpbmdcIiAmJiBpc0RPTUtleWZyYW1lcyhrZXlmcmFtZXMpKSB7XG4gICAgICAgIHJldHVybiByZXNvbHZlRWxlbWVudHMoc3ViamVjdCwgc2NvcGUsIHNlbGVjdG9yQ2FjaGUpO1xuICAgIH1cbiAgICBlbHNlIGlmIChzdWJqZWN0IGluc3RhbmNlb2YgTm9kZUxpc3QpIHtcbiAgICAgICAgcmV0dXJuIEFycmF5LmZyb20oc3ViamVjdCk7XG4gICAgfVxuICAgIGVsc2UgaWYgKEFycmF5LmlzQXJyYXkoc3ViamVjdCkpIHtcbiAgICAgICAgcmV0dXJuIHN1YmplY3Q7XG4gICAgfVxuICAgIGVsc2Uge1xuICAgICAgICByZXR1cm4gW3N1YmplY3RdO1xuICAgIH1cbn1cblxuZXhwb3J0IHsgcmVzb2x2ZVN1YmplY3RzIH07XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/animation/animate/resolve-subjects.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/animation/animate/sequence.mjs":
/*!***************************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/animation/animate/sequence.mjs ***!
  \***************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   animateSequence: () => (/* binding */ animateSequence)\n/* harmony export */ });\n/* harmony import */ var motion_dom__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! motion-dom */ \"(app-pages-browser)/./node_modules/motion-dom/dist/es/animation/generators/spring/index.mjs\");\n/* harmony import */ var _sequence_create_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../sequence/create.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/animation/sequence/create.mjs\");\n/* harmony import */ var _subject_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./subject.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/animation/animate/subject.mjs\");\n\n\n\n\nfunction animateSequence(sequence, options, scope) {\n    const animations = [];\n    const animationDefinitions = (0,_sequence_create_mjs__WEBPACK_IMPORTED_MODULE_0__.createAnimationsFromSequence)(sequence, options, scope, { spring: motion_dom__WEBPACK_IMPORTED_MODULE_1__.spring });\n    animationDefinitions.forEach(({ keyframes, transition }, subject) => {\n        animations.push(...(0,_subject_mjs__WEBPACK_IMPORTED_MODULE_2__.animateSubject)(subject, keyframes, transition));\n    });\n    return animations;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvYW5pbWF0aW9uL2FuaW1hdGUvc2VxdWVuY2UubWpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7QUFBb0M7QUFDa0M7QUFDdkI7O0FBRS9DO0FBQ0E7QUFDQSxpQ0FBaUMsa0ZBQTRCLDZCQUE2QixNQUFNLGtEQUFFO0FBQ2xHLG9DQUFvQyx1QkFBdUI7QUFDM0QsMkJBQTJCLDREQUFjO0FBQ3pDLEtBQUs7QUFDTDtBQUNBOztBQUUyQiIsInNvdXJjZXMiOlsiL1VzZXJzL2t3L0Rlc2t0b3AvQ2xhdWRlL2FpLW1hcmtldGluZy13ZWJzaXRlL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvYW5pbWF0aW9uL2FuaW1hdGUvc2VxdWVuY2UubWpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IHNwcmluZyB9IGZyb20gJ21vdGlvbi1kb20nO1xuaW1wb3J0IHsgY3JlYXRlQW5pbWF0aW9uc0Zyb21TZXF1ZW5jZSB9IGZyb20gJy4uL3NlcXVlbmNlL2NyZWF0ZS5tanMnO1xuaW1wb3J0IHsgYW5pbWF0ZVN1YmplY3QgfSBmcm9tICcuL3N1YmplY3QubWpzJztcblxuZnVuY3Rpb24gYW5pbWF0ZVNlcXVlbmNlKHNlcXVlbmNlLCBvcHRpb25zLCBzY29wZSkge1xuICAgIGNvbnN0IGFuaW1hdGlvbnMgPSBbXTtcbiAgICBjb25zdCBhbmltYXRpb25EZWZpbml0aW9ucyA9IGNyZWF0ZUFuaW1hdGlvbnNGcm9tU2VxdWVuY2Uoc2VxdWVuY2UsIG9wdGlvbnMsIHNjb3BlLCB7IHNwcmluZyB9KTtcbiAgICBhbmltYXRpb25EZWZpbml0aW9ucy5mb3JFYWNoKCh7IGtleWZyYW1lcywgdHJhbnNpdGlvbiB9LCBzdWJqZWN0KSA9PiB7XG4gICAgICAgIGFuaW1hdGlvbnMucHVzaCguLi5hbmltYXRlU3ViamVjdChzdWJqZWN0LCBrZXlmcmFtZXMsIHRyYW5zaXRpb24pKTtcbiAgICB9KTtcbiAgICByZXR1cm4gYW5pbWF0aW9ucztcbn1cblxuZXhwb3J0IHsgYW5pbWF0ZVNlcXVlbmNlIH07XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/animation/animate/sequence.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/animation/animate/subject.mjs":
/*!**************************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/animation/animate/subject.mjs ***!
  \**************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   animateSubject: () => (/* binding */ animateSubject)\n/* harmony export */ });\n/* harmony import */ var motion_dom__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! motion-dom */ \"(app-pages-browser)/./node_modules/motion-dom/dist/es/value/utils/is-motion-value.mjs\");\n/* harmony import */ var motion_utils__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! motion-utils */ \"(app-pages-browser)/./node_modules/motion-utils/dist/es/errors.mjs\");\n/* harmony import */ var _render_store_mjs__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../../render/store.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/store.mjs\");\n/* harmony import */ var _interfaces_visual_element_target_mjs__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../interfaces/visual-element-target.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/animation/interfaces/visual-element-target.mjs\");\n/* harmony import */ var _utils_create_visual_element_mjs__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../utils/create-visual-element.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/animation/utils/create-visual-element.mjs\");\n/* harmony import */ var _utils_is_dom_keyframes_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../utils/is-dom-keyframes.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/animation/utils/is-dom-keyframes.mjs\");\n/* harmony import */ var _resolve_subjects_mjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./resolve-subjects.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/animation/animate/resolve-subjects.mjs\");\n/* harmony import */ var _single_value_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./single-value.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/animation/animate/single-value.mjs\");\n\n\n\n\n\n\n\n\n\nfunction isSingleValue(subject, keyframes) {\n    return ((0,motion_dom__WEBPACK_IMPORTED_MODULE_0__.isMotionValue)(subject) ||\n        typeof subject === \"number\" ||\n        (typeof subject === \"string\" && !(0,_utils_is_dom_keyframes_mjs__WEBPACK_IMPORTED_MODULE_1__.isDOMKeyframes)(keyframes)));\n}\n/**\n * Implementation\n */\nfunction animateSubject(subject, keyframes, options, scope) {\n    const animations = [];\n    if (isSingleValue(subject, keyframes)) {\n        animations.push((0,_single_value_mjs__WEBPACK_IMPORTED_MODULE_2__.animateSingleValue)(subject, (0,_utils_is_dom_keyframes_mjs__WEBPACK_IMPORTED_MODULE_1__.isDOMKeyframes)(keyframes)\n            ? keyframes.default || keyframes\n            : keyframes, options ? options.default || options : options));\n    }\n    else {\n        const subjects = (0,_resolve_subjects_mjs__WEBPACK_IMPORTED_MODULE_3__.resolveSubjects)(subject, keyframes, scope);\n        const numSubjects = subjects.length;\n        (0,motion_utils__WEBPACK_IMPORTED_MODULE_4__.invariant)(Boolean(numSubjects), \"No valid elements provided.\", \"no-valid-elements\");\n        for (let i = 0; i < numSubjects; i++) {\n            const thisSubject = subjects[i];\n            const createVisualElement = thisSubject instanceof Element\n                ? _utils_create_visual_element_mjs__WEBPACK_IMPORTED_MODULE_5__.createDOMVisualElement\n                : _utils_create_visual_element_mjs__WEBPACK_IMPORTED_MODULE_5__.createObjectVisualElement;\n            if (!_render_store_mjs__WEBPACK_IMPORTED_MODULE_6__.visualElementStore.has(thisSubject)) {\n                createVisualElement(thisSubject);\n            }\n            const visualElement = _render_store_mjs__WEBPACK_IMPORTED_MODULE_6__.visualElementStore.get(thisSubject);\n            const transition = { ...options };\n            /**\n             * Resolve stagger function if provided.\n             */\n            if (\"delay\" in transition &&\n                typeof transition.delay === \"function\") {\n                transition.delay = transition.delay(i, numSubjects);\n            }\n            animations.push(...(0,_interfaces_visual_element_target_mjs__WEBPACK_IMPORTED_MODULE_7__.animateTarget)(visualElement, { ...keyframes, transition }, {}));\n        }\n    }\n    return animations;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/animation/animate/subject.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/animation/sequence/create.mjs":
/*!**************************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/animation/sequence/create.mjs ***!
  \**************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createAnimationsFromSequence: () => (/* binding */ createAnimationsFromSequence),\n/* harmony export */   getValueTransition: () => (/* binding */ getValueTransition)\n/* harmony export */ });\n/* harmony import */ var motion_dom__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! motion-dom */ \"(app-pages-browser)/./node_modules/motion-dom/dist/es/animation/keyframes/offsets/default.mjs\");\n/* harmony import */ var motion_dom__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! motion-dom */ \"(app-pages-browser)/./node_modules/motion-dom/dist/es/animation/generators/utils/is-generator.mjs\");\n/* harmony import */ var motion_dom__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! motion-dom */ \"(app-pages-browser)/./node_modules/motion-dom/dist/es/animation/generators/utils/create-generator-easing.mjs\");\n/* harmony import */ var motion_dom__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! motion-dom */ \"(app-pages-browser)/./node_modules/motion-dom/dist/es/animation/keyframes/offsets/fill.mjs\");\n/* harmony import */ var motion_dom__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! motion-dom */ \"(app-pages-browser)/./node_modules/motion-dom/dist/es/value/utils/is-motion-value.mjs\");\n/* harmony import */ var motion_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! motion-utils */ \"(app-pages-browser)/./node_modules/motion-utils/dist/es/time-conversion.mjs\");\n/* harmony import */ var motion_utils__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! motion-utils */ \"(app-pages-browser)/./node_modules/motion-utils/dist/es/errors.mjs\");\n/* harmony import */ var motion_utils__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! motion-utils */ \"(app-pages-browser)/./node_modules/motion-utils/dist/es/easing/utils/get-easing-for-segment.mjs\");\n/* harmony import */ var motion_utils__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! motion-utils */ \"(app-pages-browser)/./node_modules/motion-utils/dist/es/progress.mjs\");\n/* harmony import */ var _animate_resolve_subjects_mjs__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ../animate/resolve-subjects.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/animation/animate/resolve-subjects.mjs\");\n/* harmony import */ var _utils_calc_repeat_duration_mjs__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./utils/calc-repeat-duration.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/animation/sequence/utils/calc-repeat-duration.mjs\");\n/* harmony import */ var _utils_calc_time_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./utils/calc-time.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/animation/sequence/utils/calc-time.mjs\");\n/* harmony import */ var _utils_edit_mjs__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./utils/edit.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/animation/sequence/utils/edit.mjs\");\n/* harmony import */ var _utils_normalize_times_mjs__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./utils/normalize-times.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/animation/sequence/utils/normalize-times.mjs\");\n/* harmony import */ var _utils_sort_mjs__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ./utils/sort.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/animation/sequence/utils/sort.mjs\");\n\n\n\n\n\n\n\n\n\nconst defaultSegmentEasing = \"easeInOut\";\nconst MAX_REPEAT = 20;\nfunction createAnimationsFromSequence(sequence, { defaultTransition = {}, ...sequenceTransition } = {}, scope, generators) {\n    const defaultDuration = defaultTransition.duration || 0.3;\n    const animationDefinitions = new Map();\n    const sequences = new Map();\n    const elementCache = {};\n    const timeLabels = new Map();\n    let prevTime = 0;\n    let currentTime = 0;\n    let totalDuration = 0;\n    /**\n     * Build the timeline by mapping over the sequence array and converting\n     * the definitions into keyframes and offsets with absolute time values.\n     * These will later get converted into relative offsets in a second pass.\n     */\n    for (let i = 0; i < sequence.length; i++) {\n        const segment = sequence[i];\n        /**\n         * If this is a timeline label, mark it and skip the rest of this iteration.\n         */\n        if (typeof segment === \"string\") {\n            timeLabels.set(segment, currentTime);\n            continue;\n        }\n        else if (!Array.isArray(segment)) {\n            timeLabels.set(segment.name, (0,_utils_calc_time_mjs__WEBPACK_IMPORTED_MODULE_0__.calcNextTime)(currentTime, segment.at, prevTime, timeLabels));\n            continue;\n        }\n        let [subject, keyframes, transition = {}] = segment;\n        /**\n         * If a relative or absolute time value has been specified we need to resolve\n         * it in relation to the currentTime.\n         */\n        if (transition.at !== undefined) {\n            currentTime = (0,_utils_calc_time_mjs__WEBPACK_IMPORTED_MODULE_0__.calcNextTime)(currentTime, transition.at, prevTime, timeLabels);\n        }\n        /**\n         * Keep track of the maximum duration in this definition. This will be\n         * applied to currentTime once the definition has been parsed.\n         */\n        let maxDuration = 0;\n        const resolveValueSequence = (valueKeyframes, valueTransition, valueSequence, elementIndex = 0, numSubjects = 0) => {\n            const valueKeyframesAsList = keyframesAsList(valueKeyframes);\n            const { delay = 0, times = (0,motion_dom__WEBPACK_IMPORTED_MODULE_1__.defaultOffset)(valueKeyframesAsList), type = \"keyframes\", repeat, repeatType, repeatDelay = 0, ...remainingTransition } = valueTransition;\n            let { ease = defaultTransition.ease || \"easeOut\", duration } = valueTransition;\n            /**\n             * Resolve stagger() if defined.\n             */\n            const calculatedDelay = typeof delay === \"function\"\n                ? delay(elementIndex, numSubjects)\n                : delay;\n            /**\n             * If this animation should and can use a spring, generate a spring easing function.\n             */\n            const numKeyframes = valueKeyframesAsList.length;\n            const createGenerator = (0,motion_dom__WEBPACK_IMPORTED_MODULE_2__.isGenerator)(type)\n                ? type\n                : generators?.[type || \"keyframes\"];\n            if (numKeyframes <= 2 && createGenerator) {\n                /**\n                 * As we're creating an easing function from a spring,\n                 * ideally we want to generate it using the real distance\n                 * between the two keyframes. However this isn't always\n                 * possible - in these situations we use 0-100.\n                 */\n                let absoluteDelta = 100;\n                if (numKeyframes === 2 &&\n                    isNumberKeyframesArray(valueKeyframesAsList)) {\n                    const delta = valueKeyframesAsList[1] - valueKeyframesAsList[0];\n                    absoluteDelta = Math.abs(delta);\n                }\n                const springTransition = { ...remainingTransition };\n                if (duration !== undefined) {\n                    springTransition.duration = (0,motion_utils__WEBPACK_IMPORTED_MODULE_3__.secondsToMilliseconds)(duration);\n                }\n                const springEasing = (0,motion_dom__WEBPACK_IMPORTED_MODULE_4__.createGeneratorEasing)(springTransition, absoluteDelta, createGenerator);\n                ease = springEasing.ease;\n                duration = springEasing.duration;\n            }\n            duration ?? (duration = defaultDuration);\n            const startTime = currentTime + calculatedDelay;\n            /**\n             * If there's only one time offset of 0, fill in a second with length 1\n             */\n            if (times.length === 1 && times[0] === 0) {\n                times[1] = 1;\n            }\n            /**\n             * Fill out if offset if fewer offsets than keyframes\n             */\n            const remainder = times.length - valueKeyframesAsList.length;\n            remainder > 0 && (0,motion_dom__WEBPACK_IMPORTED_MODULE_5__.fillOffset)(times, remainder);\n            /**\n             * If only one value has been set, ie [1], push a null to the start of\n             * the keyframe array. This will let us mark a keyframe at this point\n             * that will later be hydrated with the previous value.\n             */\n            valueKeyframesAsList.length === 1 &&\n                valueKeyframesAsList.unshift(null);\n            /**\n             * Handle repeat options\n             */\n            if (repeat) {\n                (0,motion_utils__WEBPACK_IMPORTED_MODULE_6__.invariant)(repeat < MAX_REPEAT, \"Repeat count too high, must be less than 20\", \"repeat-count-high\");\n                duration = (0,_utils_calc_repeat_duration_mjs__WEBPACK_IMPORTED_MODULE_7__.calculateRepeatDuration)(duration, repeat);\n                const originalKeyframes = [...valueKeyframesAsList];\n                const originalTimes = [...times];\n                ease = Array.isArray(ease) ? [...ease] : [ease];\n                const originalEase = [...ease];\n                for (let repeatIndex = 0; repeatIndex < repeat; repeatIndex++) {\n                    valueKeyframesAsList.push(...originalKeyframes);\n                    for (let keyframeIndex = 0; keyframeIndex < originalKeyframes.length; keyframeIndex++) {\n                        times.push(originalTimes[keyframeIndex] + (repeatIndex + 1));\n                        ease.push(keyframeIndex === 0\n                            ? \"linear\"\n                            : (0,motion_utils__WEBPACK_IMPORTED_MODULE_8__.getEasingForSegment)(originalEase, keyframeIndex - 1));\n                    }\n                }\n                (0,_utils_normalize_times_mjs__WEBPACK_IMPORTED_MODULE_9__.normalizeTimes)(times, repeat);\n            }\n            const targetTime = startTime + duration;\n            /**\n             * Add keyframes, mapping offsets to absolute time.\n             */\n            (0,_utils_edit_mjs__WEBPACK_IMPORTED_MODULE_10__.addKeyframes)(valueSequence, valueKeyframesAsList, ease, times, startTime, targetTime);\n            maxDuration = Math.max(calculatedDelay + duration, maxDuration);\n            totalDuration = Math.max(targetTime, totalDuration);\n        };\n        if ((0,motion_dom__WEBPACK_IMPORTED_MODULE_11__.isMotionValue)(subject)) {\n            const subjectSequence = getSubjectSequence(subject, sequences);\n            resolveValueSequence(keyframes, transition, getValueSequence(\"default\", subjectSequence));\n        }\n        else {\n            const subjects = (0,_animate_resolve_subjects_mjs__WEBPACK_IMPORTED_MODULE_12__.resolveSubjects)(subject, keyframes, scope, elementCache);\n            const numSubjects = subjects.length;\n            /**\n             * For every element in this segment, process the defined values.\n             */\n            for (let subjectIndex = 0; subjectIndex < numSubjects; subjectIndex++) {\n                /**\n                 * Cast necessary, but we know these are of this type\n                 */\n                keyframes = keyframes;\n                transition = transition;\n                const thisSubject = subjects[subjectIndex];\n                const subjectSequence = getSubjectSequence(thisSubject, sequences);\n                for (const key in keyframes) {\n                    resolveValueSequence(keyframes[key], getValueTransition(transition, key), getValueSequence(key, subjectSequence), subjectIndex, numSubjects);\n                }\n            }\n        }\n        prevTime = currentTime;\n        currentTime += maxDuration;\n    }\n    /**\n     * For every element and value combination create a new animation.\n     */\n    sequences.forEach((valueSequences, element) => {\n        for (const key in valueSequences) {\n            const valueSequence = valueSequences[key];\n            /**\n             * Arrange all the keyframes in ascending time order.\n             */\n            valueSequence.sort(_utils_sort_mjs__WEBPACK_IMPORTED_MODULE_13__.compareByTime);\n            const keyframes = [];\n            const valueOffset = [];\n            const valueEasing = [];\n            /**\n             * For each keyframe, translate absolute times into\n             * relative offsets based on the total duration of the timeline.\n             */\n            for (let i = 0; i < valueSequence.length; i++) {\n                const { at, value, easing } = valueSequence[i];\n                keyframes.push(value);\n                valueOffset.push((0,motion_utils__WEBPACK_IMPORTED_MODULE_14__.progress)(0, totalDuration, at));\n                valueEasing.push(easing || \"easeOut\");\n            }\n            /**\n             * If the first keyframe doesn't land on offset: 0\n             * provide one by duplicating the initial keyframe. This ensures\n             * it snaps to the first keyframe when the animation starts.\n             */\n            if (valueOffset[0] !== 0) {\n                valueOffset.unshift(0);\n                keyframes.unshift(keyframes[0]);\n                valueEasing.unshift(defaultSegmentEasing);\n            }\n            /**\n             * If the last keyframe doesn't land on offset: 1\n             * provide one with a null wildcard value. This will ensure it\n             * stays static until the end of the animation.\n             */\n            if (valueOffset[valueOffset.length - 1] !== 1) {\n                valueOffset.push(1);\n                keyframes.push(null);\n            }\n            if (!animationDefinitions.has(element)) {\n                animationDefinitions.set(element, {\n                    keyframes: {},\n                    transition: {},\n                });\n            }\n            const definition = animationDefinitions.get(element);\n            definition.keyframes[key] = keyframes;\n            definition.transition[key] = {\n                ...defaultTransition,\n                duration: totalDuration,\n                ease: valueEasing,\n                times: valueOffset,\n                ...sequenceTransition,\n            };\n        }\n    });\n    return animationDefinitions;\n}\nfunction getSubjectSequence(subject, sequences) {\n    !sequences.has(subject) && sequences.set(subject, {});\n    return sequences.get(subject);\n}\nfunction getValueSequence(name, sequences) {\n    if (!sequences[name])\n        sequences[name] = [];\n    return sequences[name];\n}\nfunction keyframesAsList(keyframes) {\n    return Array.isArray(keyframes) ? keyframes : [keyframes];\n}\nfunction getValueTransition(transition, key) {\n    return transition && transition[key]\n        ? {\n            ...transition,\n            ...transition[key],\n        }\n        : { ...transition };\n}\nconst isNumber = (keyframe) => typeof keyframe === \"number\";\nconst isNumberKeyframesArray = (keyframes) => keyframes.every(isNumber);\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/animation/sequence/create.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/animation/sequence/utils/calc-repeat-duration.mjs":
/*!**********************************************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/animation/sequence/utils/calc-repeat-duration.mjs ***!
  \**********************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   calculateRepeatDuration: () => (/* binding */ calculateRepeatDuration)\n/* harmony export */ });\nfunction calculateRepeatDuration(duration, repeat, _repeatDelay) {\n    return duration * (repeat + 1);\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvYW5pbWF0aW9uL3NlcXVlbmNlL3V0aWxzL2NhbGMtcmVwZWF0LWR1cmF0aW9uLm1qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7QUFDQTtBQUNBOztBQUVtQyIsInNvdXJjZXMiOlsiL1VzZXJzL2t3L0Rlc2t0b3AvQ2xhdWRlL2FpLW1hcmtldGluZy13ZWJzaXRlL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvYW5pbWF0aW9uL3NlcXVlbmNlL3V0aWxzL2NhbGMtcmVwZWF0LWR1cmF0aW9uLm1qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJmdW5jdGlvbiBjYWxjdWxhdGVSZXBlYXREdXJhdGlvbihkdXJhdGlvbiwgcmVwZWF0LCBfcmVwZWF0RGVsYXkpIHtcbiAgICByZXR1cm4gZHVyYXRpb24gKiAocmVwZWF0ICsgMSk7XG59XG5cbmV4cG9ydCB7IGNhbGN1bGF0ZVJlcGVhdER1cmF0aW9uIH07XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/animation/sequence/utils/calc-repeat-duration.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/animation/sequence/utils/calc-time.mjs":
/*!***********************************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/animation/sequence/utils/calc-time.mjs ***!
  \***********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   calcNextTime: () => (/* binding */ calcNextTime)\n/* harmony export */ });\n/**\n * Given a absolute or relative time definition and current/prev time state of the sequence,\n * calculate an absolute time for the next keyframes.\n */\nfunction calcNextTime(current, next, prev, labels) {\n    if (typeof next === \"number\") {\n        return next;\n    }\n    else if (next.startsWith(\"-\") || next.startsWith(\"+\")) {\n        return Math.max(0, current + parseFloat(next));\n    }\n    else if (next === \"<\") {\n        return prev;\n    }\n    else if (next.startsWith(\"<\")) {\n        return Math.max(0, prev + parseFloat(next.slice(1)));\n    }\n    else {\n        return labels.get(next) ?? current;\n    }\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvYW5pbWF0aW9uL3NlcXVlbmNlL3V0aWxzL2NhbGMtdGltZS5tanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFd0IiLCJzb3VyY2VzIjpbIi9Vc2Vycy9rdy9EZXNrdG9wL0NsYXVkZS9haS1tYXJrZXRpbmctd2Vic2l0ZS9ub2RlX21vZHVsZXMvZnJhbWVyLW1vdGlvbi9kaXN0L2VzL2FuaW1hdGlvbi9zZXF1ZW5jZS91dGlscy9jYWxjLXRpbWUubWpzIl0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogR2l2ZW4gYSBhYnNvbHV0ZSBvciByZWxhdGl2ZSB0aW1lIGRlZmluaXRpb24gYW5kIGN1cnJlbnQvcHJldiB0aW1lIHN0YXRlIG9mIHRoZSBzZXF1ZW5jZSxcbiAqIGNhbGN1bGF0ZSBhbiBhYnNvbHV0ZSB0aW1lIGZvciB0aGUgbmV4dCBrZXlmcmFtZXMuXG4gKi9cbmZ1bmN0aW9uIGNhbGNOZXh0VGltZShjdXJyZW50LCBuZXh0LCBwcmV2LCBsYWJlbHMpIHtcbiAgICBpZiAodHlwZW9mIG5leHQgPT09IFwibnVtYmVyXCIpIHtcbiAgICAgICAgcmV0dXJuIG5leHQ7XG4gICAgfVxuICAgIGVsc2UgaWYgKG5leHQuc3RhcnRzV2l0aChcIi1cIikgfHwgbmV4dC5zdGFydHNXaXRoKFwiK1wiKSkge1xuICAgICAgICByZXR1cm4gTWF0aC5tYXgoMCwgY3VycmVudCArIHBhcnNlRmxvYXQobmV4dCkpO1xuICAgIH1cbiAgICBlbHNlIGlmIChuZXh0ID09PSBcIjxcIikge1xuICAgICAgICByZXR1cm4gcHJldjtcbiAgICB9XG4gICAgZWxzZSBpZiAobmV4dC5zdGFydHNXaXRoKFwiPFwiKSkge1xuICAgICAgICByZXR1cm4gTWF0aC5tYXgoMCwgcHJldiArIHBhcnNlRmxvYXQobmV4dC5zbGljZSgxKSkpO1xuICAgIH1cbiAgICBlbHNlIHtcbiAgICAgICAgcmV0dXJuIGxhYmVscy5nZXQobmV4dCkgPz8gY3VycmVudDtcbiAgICB9XG59XG5cbmV4cG9ydCB7IGNhbGNOZXh0VGltZSB9O1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/animation/sequence/utils/calc-time.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/animation/sequence/utils/edit.mjs":
/*!******************************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/animation/sequence/utils/edit.mjs ***!
  \******************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   addKeyframes: () => (/* binding */ addKeyframes),\n/* harmony export */   eraseKeyframes: () => (/* binding */ eraseKeyframes)\n/* harmony export */ });\n/* harmony import */ var motion_dom__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! motion-dom */ \"(app-pages-browser)/./node_modules/motion-dom/dist/es/utils/mix/number.mjs\");\n/* harmony import */ var motion_utils__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! motion-utils */ \"(app-pages-browser)/./node_modules/motion-utils/dist/es/array.mjs\");\n/* harmony import */ var motion_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! motion-utils */ \"(app-pages-browser)/./node_modules/motion-utils/dist/es/easing/utils/get-easing-for-segment.mjs\");\n\n\n\nfunction eraseKeyframes(sequence, startTime, endTime) {\n    for (let i = 0; i < sequence.length; i++) {\n        const keyframe = sequence[i];\n        if (keyframe.at > startTime && keyframe.at < endTime) {\n            (0,motion_utils__WEBPACK_IMPORTED_MODULE_0__.removeItem)(sequence, keyframe);\n            // If we remove this item we have to push the pointer back one\n            i--;\n        }\n    }\n}\nfunction addKeyframes(sequence, keyframes, easing, offset, startTime, endTime) {\n    /**\n     * Erase every existing value between currentTime and targetTime,\n     * this will essentially splice this timeline into any currently\n     * defined ones.\n     */\n    eraseKeyframes(sequence, startTime, endTime);\n    for (let i = 0; i < keyframes.length; i++) {\n        sequence.push({\n            value: keyframes[i],\n            at: (0,motion_dom__WEBPACK_IMPORTED_MODULE_1__.mixNumber)(startTime, endTime, offset[i]),\n            easing: (0,motion_utils__WEBPACK_IMPORTED_MODULE_2__.getEasingForSegment)(easing, i),\n        });\n    }\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/animation/sequence/utils/edit.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/animation/sequence/utils/normalize-times.mjs":
/*!*****************************************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/animation/sequence/utils/normalize-times.mjs ***!
  \*****************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   normalizeTimes: () => (/* binding */ normalizeTimes)\n/* harmony export */ });\n/**\n * Take an array of times that represent repeated keyframes. For instance\n * if we have original times of [0, 0.5, 1] then our repeated times will\n * be [0, 0.5, 1, 1, 1.5, 2]. Loop over the times and scale them back\n * down to a 0-1 scale.\n */\nfunction normalizeTimes(times, repeat) {\n    for (let i = 0; i < times.length; i++) {\n        times[i] = times[i] / (repeat + 1);\n    }\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvYW5pbWF0aW9uL3NlcXVlbmNlL3V0aWxzL25vcm1hbGl6ZS10aW1lcy5tanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esb0JBQW9CLGtCQUFrQjtBQUN0QztBQUNBO0FBQ0E7O0FBRTBCIiwic291cmNlcyI6WyIvVXNlcnMva3cvRGVza3RvcC9DbGF1ZGUvYWktbWFya2V0aW5nLXdlYnNpdGUvbm9kZV9tb2R1bGVzL2ZyYW1lci1tb3Rpb24vZGlzdC9lcy9hbmltYXRpb24vc2VxdWVuY2UvdXRpbHMvbm9ybWFsaXplLXRpbWVzLm1qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIFRha2UgYW4gYXJyYXkgb2YgdGltZXMgdGhhdCByZXByZXNlbnQgcmVwZWF0ZWQga2V5ZnJhbWVzLiBGb3IgaW5zdGFuY2VcbiAqIGlmIHdlIGhhdmUgb3JpZ2luYWwgdGltZXMgb2YgWzAsIDAuNSwgMV0gdGhlbiBvdXIgcmVwZWF0ZWQgdGltZXMgd2lsbFxuICogYmUgWzAsIDAuNSwgMSwgMSwgMS41LCAyXS4gTG9vcCBvdmVyIHRoZSB0aW1lcyBhbmQgc2NhbGUgdGhlbSBiYWNrXG4gKiBkb3duIHRvIGEgMC0xIHNjYWxlLlxuICovXG5mdW5jdGlvbiBub3JtYWxpemVUaW1lcyh0aW1lcywgcmVwZWF0KSB7XG4gICAgZm9yIChsZXQgaSA9IDA7IGkgPCB0aW1lcy5sZW5ndGg7IGkrKykge1xuICAgICAgICB0aW1lc1tpXSA9IHRpbWVzW2ldIC8gKHJlcGVhdCArIDEpO1xuICAgIH1cbn1cblxuZXhwb3J0IHsgbm9ybWFsaXplVGltZXMgfTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/animation/sequence/utils/normalize-times.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/animation/sequence/utils/sort.mjs":
/*!******************************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/animation/sequence/utils/sort.mjs ***!
  \******************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   compareByTime: () => (/* binding */ compareByTime)\n/* harmony export */ });\nfunction compareByTime(a, b) {\n    if (a.at === b.at) {\n        if (a.value === null)\n            return 1;\n        if (b.value === null)\n            return -1;\n        return 0;\n    }\n    else {\n        return a.at - b.at;\n    }\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvYW5pbWF0aW9uL3NlcXVlbmNlL3V0aWxzL3NvcnQubWpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRXlCIiwic291cmNlcyI6WyIvVXNlcnMva3cvRGVza3RvcC9DbGF1ZGUvYWktbWFya2V0aW5nLXdlYnNpdGUvbm9kZV9tb2R1bGVzL2ZyYW1lci1tb3Rpb24vZGlzdC9lcy9hbmltYXRpb24vc2VxdWVuY2UvdXRpbHMvc29ydC5tanMiXSwic291cmNlc0NvbnRlbnQiOlsiZnVuY3Rpb24gY29tcGFyZUJ5VGltZShhLCBiKSB7XG4gICAgaWYgKGEuYXQgPT09IGIuYXQpIHtcbiAgICAgICAgaWYgKGEudmFsdWUgPT09IG51bGwpXG4gICAgICAgICAgICByZXR1cm4gMTtcbiAgICAgICAgaWYgKGIudmFsdWUgPT09IG51bGwpXG4gICAgICAgICAgICByZXR1cm4gLTE7XG4gICAgICAgIHJldHVybiAwO1xuICAgIH1cbiAgICBlbHNlIHtcbiAgICAgICAgcmV0dXJuIGEuYXQgLSBiLmF0O1xuICAgIH1cbn1cblxuZXhwb3J0IHsgY29tcGFyZUJ5VGltZSB9O1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/animation/sequence/utils/sort.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/animation/utils/create-visual-element.mjs":
/*!**************************************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/animation/utils/create-visual-element.mjs ***!
  \**************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createDOMVisualElement: () => (/* binding */ createDOMVisualElement),\n/* harmony export */   createObjectVisualElement: () => (/* binding */ createObjectVisualElement)\n/* harmony export */ });\n/* harmony import */ var motion_dom__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! motion-dom */ \"(app-pages-browser)/./node_modules/motion-dom/dist/es/utils/is-svg-element.mjs\");\n/* harmony import */ var motion_dom__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! motion-dom */ \"(app-pages-browser)/./node_modules/motion-dom/dist/es/utils/is-svg-svg-element.mjs\");\n/* harmony import */ var _render_html_HTMLVisualElement_mjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../render/html/HTMLVisualElement.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/html/HTMLVisualElement.mjs\");\n/* harmony import */ var _render_object_ObjectVisualElement_mjs__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../render/object/ObjectVisualElement.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/object/ObjectVisualElement.mjs\");\n/* harmony import */ var _render_store_mjs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../render/store.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/store.mjs\");\n/* harmony import */ var _render_svg_SVGVisualElement_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../render/svg/SVGVisualElement.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/svg/SVGVisualElement.mjs\");\n\n\n\n\n\n\nfunction createDOMVisualElement(element) {\n    const options = {\n        presenceContext: null,\n        props: {},\n        visualState: {\n            renderState: {\n                transform: {},\n                transformOrigin: {},\n                style: {},\n                vars: {},\n                attrs: {},\n            },\n            latestValues: {},\n        },\n    };\n    const node = (0,motion_dom__WEBPACK_IMPORTED_MODULE_0__.isSVGElement)(element) && !(0,motion_dom__WEBPACK_IMPORTED_MODULE_1__.isSVGSVGElement)(element)\n        ? new _render_svg_SVGVisualElement_mjs__WEBPACK_IMPORTED_MODULE_2__.SVGVisualElement(options)\n        : new _render_html_HTMLVisualElement_mjs__WEBPACK_IMPORTED_MODULE_3__.HTMLVisualElement(options);\n    node.mount(element);\n    _render_store_mjs__WEBPACK_IMPORTED_MODULE_4__.visualElementStore.set(element, node);\n}\nfunction createObjectVisualElement(subject) {\n    const options = {\n        presenceContext: null,\n        props: {},\n        visualState: {\n            renderState: {\n                output: {},\n            },\n            latestValues: {},\n        },\n    };\n    const node = new _render_object_ObjectVisualElement_mjs__WEBPACK_IMPORTED_MODULE_5__.ObjectVisualElement(options);\n    node.mount(subject);\n    _render_store_mjs__WEBPACK_IMPORTED_MODULE_4__.visualElementStore.set(subject, node);\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/animation/utils/create-visual-element.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/animation/utils/is-dom-keyframes.mjs":
/*!*********************************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/animation/utils/is-dom-keyframes.mjs ***!
  \*********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   isDOMKeyframes: () => (/* binding */ isDOMKeyframes)\n/* harmony export */ });\nfunction isDOMKeyframes(keyframes) {\n    return typeof keyframes === \"object\" && !Array.isArray(keyframes);\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvYW5pbWF0aW9uL3V0aWxzL2lzLWRvbS1rZXlmcmFtZXMubWpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQTtBQUNBO0FBQ0E7O0FBRTBCIiwic291cmNlcyI6WyIvVXNlcnMva3cvRGVza3RvcC9DbGF1ZGUvYWktbWFya2V0aW5nLXdlYnNpdGUvbm9kZV9tb2R1bGVzL2ZyYW1lci1tb3Rpb24vZGlzdC9lcy9hbmltYXRpb24vdXRpbHMvaXMtZG9tLWtleWZyYW1lcy5tanMiXSwic291cmNlc0NvbnRlbnQiOlsiZnVuY3Rpb24gaXNET01LZXlmcmFtZXMoa2V5ZnJhbWVzKSB7XG4gICAgcmV0dXJuIHR5cGVvZiBrZXlmcmFtZXMgPT09IFwib2JqZWN0XCIgJiYgIUFycmF5LmlzQXJyYXkoa2V5ZnJhbWVzKTtcbn1cblxuZXhwb3J0IHsgaXNET01LZXlmcmFtZXMgfTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/animation/utils/is-dom-keyframes.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/render/object/ObjectVisualElement.mjs":
/*!**********************************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/render/object/ObjectVisualElement.mjs ***!
  \**********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ObjectVisualElement: () => (/* binding */ ObjectVisualElement)\n/* harmony export */ });\n/* harmony import */ var _projection_geometry_models_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../projection/geometry/models.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/projection/geometry/models.mjs\");\n/* harmony import */ var _VisualElement_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../VisualElement.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/VisualElement.mjs\");\n\n\n\nfunction isObjectKey(key, object) {\n    return key in object;\n}\nclass ObjectVisualElement extends _VisualElement_mjs__WEBPACK_IMPORTED_MODULE_0__.VisualElement {\n    constructor() {\n        super(...arguments);\n        this.type = \"object\";\n    }\n    readValueFromInstance(instance, key) {\n        if (isObjectKey(key, instance)) {\n            const value = instance[key];\n            if (typeof value === \"string\" || typeof value === \"number\") {\n                return value;\n            }\n        }\n        return undefined;\n    }\n    getBaseTargetFromProps() {\n        return undefined;\n    }\n    removeValueFromRenderState(key, renderState) {\n        delete renderState.output[key];\n    }\n    measureInstanceViewportBox() {\n        return (0,_projection_geometry_models_mjs__WEBPACK_IMPORTED_MODULE_1__.createBox)();\n    }\n    build(renderState, latestValues) {\n        Object.assign(renderState.output, latestValues);\n    }\n    renderInstance(instance, { output }) {\n        Object.assign(instance, output);\n    }\n    sortInstanceNodePosition() {\n        return 0;\n    }\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/render/object/ObjectVisualElement.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/value/use-combine-values.mjs":
/*!*************************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/value/use-combine-values.mjs ***!
  \*************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useCombineMotionValues: () => (/* binding */ useCombineMotionValues)\n/* harmony export */ });\n/* harmony import */ var motion_dom__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! motion-dom */ \"(app-pages-browser)/./node_modules/motion-dom/dist/es/frameloop/frame.mjs\");\n/* harmony import */ var _utils_use_isomorphic_effect_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../utils/use-isomorphic-effect.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/use-isomorphic-effect.mjs\");\n/* harmony import */ var _use_motion_value_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./use-motion-value.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/value/use-motion-value.mjs\");\n\n\n\n\nfunction useCombineMotionValues(values, combineValues) {\n    /**\n     * Initialise the returned motion value. This remains the same between renders.\n     */\n    const value = (0,_use_motion_value_mjs__WEBPACK_IMPORTED_MODULE_0__.useMotionValue)(combineValues());\n    /**\n     * Create a function that will update the template motion value with the latest values.\n     * This is pre-bound so whenever a motion value updates it can schedule its\n     * execution in Framesync. If it's already been scheduled it won't be fired twice\n     * in a single frame.\n     */\n    const updateValue = () => value.set(combineValues());\n    /**\n     * Synchronously update the motion value with the latest values during the render.\n     * This ensures that within a React render, the styles applied to the DOM are up-to-date.\n     */\n    updateValue();\n    /**\n     * Subscribe to all motion values found within the template. Whenever any of them change,\n     * schedule an update.\n     */\n    (0,_utils_use_isomorphic_effect_mjs__WEBPACK_IMPORTED_MODULE_1__.useIsomorphicLayoutEffect)(() => {\n        const scheduleUpdate = () => motion_dom__WEBPACK_IMPORTED_MODULE_2__.frame.preRender(updateValue, false, true);\n        const subscriptions = values.map((v) => v.on(\"change\", scheduleUpdate));\n        return () => {\n            subscriptions.forEach((unsubscribe) => unsubscribe());\n            (0,motion_dom__WEBPACK_IMPORTED_MODULE_2__.cancelFrame)(updateValue);\n        };\n    });\n    return value;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/value/use-combine-values.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/value/use-computed.mjs":
/*!*******************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/value/use-computed.mjs ***!
  \*******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useComputed: () => (/* binding */ useComputed)\n/* harmony export */ });\n/* harmony import */ var motion_dom__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! motion-dom */ \"(app-pages-browser)/./node_modules/motion-dom/dist/es/value/index.mjs\");\n/* harmony import */ var _use_combine_values_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./use-combine-values.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/value/use-combine-values.mjs\");\n\n\n\nfunction useComputed(compute) {\n    /**\n     * Open session of collectMotionValues. Any MotionValue that calls get()\n     * will be saved into this array.\n     */\n    motion_dom__WEBPACK_IMPORTED_MODULE_0__.collectMotionValues.current = [];\n    compute();\n    const value = (0,_use_combine_values_mjs__WEBPACK_IMPORTED_MODULE_1__.useCombineMotionValues)(motion_dom__WEBPACK_IMPORTED_MODULE_0__.collectMotionValues.current, compute);\n    /**\n     * Synchronously close session of collectMotionValues.\n     */\n    motion_dom__WEBPACK_IMPORTED_MODULE_0__.collectMotionValues.current = undefined;\n    return value;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvdmFsdWUvdXNlLWNvbXB1dGVkLm1qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBaUQ7QUFDaUI7O0FBRWxFO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxJQUFJLDJEQUFtQjtBQUN2QjtBQUNBLGtCQUFrQiwrRUFBc0IsQ0FBQywyREFBbUI7QUFDNUQ7QUFDQTtBQUNBO0FBQ0EsSUFBSSwyREFBbUI7QUFDdkI7QUFDQTs7QUFFdUIiLCJzb3VyY2VzIjpbIi9Vc2Vycy9rdy9EZXNrdG9wL0NsYXVkZS9haS1tYXJrZXRpbmctd2Vic2l0ZS9ub2RlX21vZHVsZXMvZnJhbWVyLW1vdGlvbi9kaXN0L2VzL3ZhbHVlL3VzZS1jb21wdXRlZC5tanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgY29sbGVjdE1vdGlvblZhbHVlcyB9IGZyb20gJ21vdGlvbi1kb20nO1xuaW1wb3J0IHsgdXNlQ29tYmluZU1vdGlvblZhbHVlcyB9IGZyb20gJy4vdXNlLWNvbWJpbmUtdmFsdWVzLm1qcyc7XG5cbmZ1bmN0aW9uIHVzZUNvbXB1dGVkKGNvbXB1dGUpIHtcbiAgICAvKipcbiAgICAgKiBPcGVuIHNlc3Npb24gb2YgY29sbGVjdE1vdGlvblZhbHVlcy4gQW55IE1vdGlvblZhbHVlIHRoYXQgY2FsbHMgZ2V0KClcbiAgICAgKiB3aWxsIGJlIHNhdmVkIGludG8gdGhpcyBhcnJheS5cbiAgICAgKi9cbiAgICBjb2xsZWN0TW90aW9uVmFsdWVzLmN1cnJlbnQgPSBbXTtcbiAgICBjb21wdXRlKCk7XG4gICAgY29uc3QgdmFsdWUgPSB1c2VDb21iaW5lTW90aW9uVmFsdWVzKGNvbGxlY3RNb3Rpb25WYWx1ZXMuY3VycmVudCwgY29tcHV0ZSk7XG4gICAgLyoqXG4gICAgICogU3luY2hyb25vdXNseSBjbG9zZSBzZXNzaW9uIG9mIGNvbGxlY3RNb3Rpb25WYWx1ZXMuXG4gICAgICovXG4gICAgY29sbGVjdE1vdGlvblZhbHVlcy5jdXJyZW50ID0gdW5kZWZpbmVkO1xuICAgIHJldHVybiB2YWx1ZTtcbn1cblxuZXhwb3J0IHsgdXNlQ29tcHV0ZWQgfTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/value/use-computed.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/value/use-motion-value.mjs":
/*!***********************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/value/use-motion-value.mjs ***!
  \***********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useMotionValue: () => (/* binding */ useMotionValue)\n/* harmony export */ });\n/* harmony import */ var motion_dom__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! motion-dom */ \"(app-pages-browser)/./node_modules/motion-dom/dist/es/value/index.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var _context_MotionConfigContext_mjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../context/MotionConfigContext.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/context/MotionConfigContext.mjs\");\n/* harmony import */ var _utils_use_constant_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../utils/use-constant.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/use-constant.mjs\");\n\n\n\n\n\n/**\n * Creates a `MotionValue` to track the state and velocity of a value.\n *\n * Usually, these are created automatically. For advanced use-cases, like use with `useTransform`, you can create `MotionValue`s externally and pass them into the animated component via the `style` prop.\n *\n * ```jsx\n * export const MyComponent = () => {\n *   const scale = useMotionValue(1)\n *\n *   return <motion.div style={{ scale }} />\n * }\n * ```\n *\n * @param initial - The initial state.\n *\n * @public\n */\nfunction useMotionValue(initial) {\n    const value = (0,_utils_use_constant_mjs__WEBPACK_IMPORTED_MODULE_1__.useConstant)(() => (0,motion_dom__WEBPACK_IMPORTED_MODULE_2__.motionValue)(initial));\n    /**\n     * If this motion value is being used in static mode, like on\n     * the Framer canvas, force components to rerender when the motion\n     * value is updated.\n     */\n    const { isStatic } = (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(_context_MotionConfigContext_mjs__WEBPACK_IMPORTED_MODULE_3__.MotionConfigContext);\n    if (isStatic) {\n        const [, setLatest] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(initial);\n        (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => value.on(\"change\", setLatest), []);\n    }\n    return value;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/value/use-motion-value.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/value/use-transform.mjs":
/*!********************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/value/use-transform.mjs ***!
  \********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useTransform: () => (/* binding */ useTransform)\n/* harmony export */ });\n/* harmony import */ var motion_dom__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! motion-dom */ \"(app-pages-browser)/./node_modules/motion-dom/dist/es/utils/transform.mjs\");\n/* harmony import */ var _utils_use_constant_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../utils/use-constant.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/use-constant.mjs\");\n/* harmony import */ var _use_combine_values_mjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./use-combine-values.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/value/use-combine-values.mjs\");\n/* harmony import */ var _use_computed_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./use-computed.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/value/use-computed.mjs\");\n\n\n\n\n\nfunction useTransform(input, inputRangeOrTransformer, outputRange, options) {\n    if (typeof input === \"function\") {\n        return (0,_use_computed_mjs__WEBPACK_IMPORTED_MODULE_0__.useComputed)(input);\n    }\n    const transformer = typeof inputRangeOrTransformer === \"function\"\n        ? inputRangeOrTransformer\n        : (0,motion_dom__WEBPACK_IMPORTED_MODULE_1__.transform)(inputRangeOrTransformer, outputRange, options);\n    return Array.isArray(input)\n        ? useListTransform(input, transformer)\n        : useListTransform([input], ([latest]) => transformer(latest));\n}\nfunction useListTransform(values, transformer) {\n    const latest = (0,_utils_use_constant_mjs__WEBPACK_IMPORTED_MODULE_2__.useConstant)(() => []);\n    return (0,_use_combine_values_mjs__WEBPACK_IMPORTED_MODULE_3__.useCombineMotionValues)(values, () => {\n        latest.length = 0;\n        const numValues = values.length;\n        for (let i = 0; i < numValues; i++) {\n            latest[i] = values[i].get();\n        }\n        return transformer(latest);\n    });\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/value/use-transform.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/award.js":
/*!***********************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/award.js ***!
  \***********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __iconNode: () => (/* binding */ __iconNode),\n/* harmony export */   \"default\": () => (/* binding */ Award)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst __iconNode = [\n    [\n        \"path\",\n        {\n            d: \"m15.477 12.89 1.515 8.526a.5.5 0 0 1-.81.47l-3.58-2.687a1 1 0 0 0-1.197 0l-3.586 2.686a.5.5 0 0 1-.81-.469l1.514-8.526\",\n            key: \"1yiouv\"\n        }\n    ],\n    [\n        \"circle\",\n        {\n            cx: \"12\",\n            cy: \"8\",\n            r: \"6\",\n            key: \"1vp47v\"\n        }\n    ]\n];\nconst Award = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"award\", __iconNode);\n //# sourceMappingURL=award.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/award.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/globe.js":
/*!***********************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/globe.js ***!
  \***********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __iconNode: () => (/* binding */ __iconNode),\n/* harmony export */   \"default\": () => (/* binding */ Globe)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst __iconNode = [\n    [\n        \"circle\",\n        {\n            cx: \"12\",\n            cy: \"12\",\n            r: \"10\",\n            key: \"1mglay\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M12 2a14.5 14.5 0 0 0 0 20 14.5 14.5 0 0 0 0-20\",\n            key: \"13o1zl\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M2 12h20\",\n            key: \"9i4pu4\"\n        }\n    ]\n];\nconst Globe = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"globe\", __iconNode);\n //# sourceMappingURL=globe.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/globe.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/motion-dom/dist/es/animation/GroupAnimation.mjs":
/*!**********************************************************************!*\
  !*** ./node_modules/motion-dom/dist/es/animation/GroupAnimation.mjs ***!
  \**********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GroupAnimation: () => (/* binding */ GroupAnimation)\n/* harmony export */ });\nclass GroupAnimation {\n    constructor(animations) {\n        // Bound to accomadate common `return animation.stop` pattern\n        this.stop = () => this.runAll(\"stop\");\n        this.animations = animations.filter(Boolean);\n    }\n    get finished() {\n        return Promise.all(this.animations.map((animation) => animation.finished));\n    }\n    /**\n     * TODO: Filter out cancelled or stopped animations before returning\n     */\n    getAll(propName) {\n        return this.animations[0][propName];\n    }\n    setAll(propName, newValue) {\n        for (let i = 0; i < this.animations.length; i++) {\n            this.animations[i][propName] = newValue;\n        }\n    }\n    attachTimeline(timeline) {\n        const subscriptions = this.animations.map((animation) => animation.attachTimeline(timeline));\n        return () => {\n            subscriptions.forEach((cancel, i) => {\n                cancel && cancel();\n                this.animations[i].stop();\n            });\n        };\n    }\n    get time() {\n        return this.getAll(\"time\");\n    }\n    set time(time) {\n        this.setAll(\"time\", time);\n    }\n    get speed() {\n        return this.getAll(\"speed\");\n    }\n    set speed(speed) {\n        this.setAll(\"speed\", speed);\n    }\n    get state() {\n        return this.getAll(\"state\");\n    }\n    get startTime() {\n        return this.getAll(\"startTime\");\n    }\n    get duration() {\n        let max = 0;\n        for (let i = 0; i < this.animations.length; i++) {\n            max = Math.max(max, this.animations[i].duration);\n        }\n        return max;\n    }\n    runAll(methodName) {\n        this.animations.forEach((controls) => controls[methodName]());\n    }\n    play() {\n        this.runAll(\"play\");\n    }\n    pause() {\n        this.runAll(\"pause\");\n    }\n    cancel() {\n        this.runAll(\"cancel\");\n    }\n    complete() {\n        this.runAll(\"complete\");\n    }\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9tb3Rpb24tZG9tL2Rpc3QvZXMvYW5pbWF0aW9uL0dyb3VwQW5pbWF0aW9uLm1qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSx3QkFBd0IsNEJBQTRCO0FBQ3BEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGFBQWE7QUFDYjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHdCQUF3Qiw0QkFBNEI7QUFDcEQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFMEIiLCJzb3VyY2VzIjpbIi9Vc2Vycy9rdy9EZXNrdG9wL0NsYXVkZS9haS1tYXJrZXRpbmctd2Vic2l0ZS9ub2RlX21vZHVsZXMvbW90aW9uLWRvbS9kaXN0L2VzL2FuaW1hdGlvbi9Hcm91cEFuaW1hdGlvbi5tanMiXSwic291cmNlc0NvbnRlbnQiOlsiY2xhc3MgR3JvdXBBbmltYXRpb24ge1xuICAgIGNvbnN0cnVjdG9yKGFuaW1hdGlvbnMpIHtcbiAgICAgICAgLy8gQm91bmQgdG8gYWNjb21hZGF0ZSBjb21tb24gYHJldHVybiBhbmltYXRpb24uc3RvcGAgcGF0dGVyblxuICAgICAgICB0aGlzLnN0b3AgPSAoKSA9PiB0aGlzLnJ1bkFsbChcInN0b3BcIik7XG4gICAgICAgIHRoaXMuYW5pbWF0aW9ucyA9IGFuaW1hdGlvbnMuZmlsdGVyKEJvb2xlYW4pO1xuICAgIH1cbiAgICBnZXQgZmluaXNoZWQoKSB7XG4gICAgICAgIHJldHVybiBQcm9taXNlLmFsbCh0aGlzLmFuaW1hdGlvbnMubWFwKChhbmltYXRpb24pID0+IGFuaW1hdGlvbi5maW5pc2hlZCkpO1xuICAgIH1cbiAgICAvKipcbiAgICAgKiBUT0RPOiBGaWx0ZXIgb3V0IGNhbmNlbGxlZCBvciBzdG9wcGVkIGFuaW1hdGlvbnMgYmVmb3JlIHJldHVybmluZ1xuICAgICAqL1xuICAgIGdldEFsbChwcm9wTmFtZSkge1xuICAgICAgICByZXR1cm4gdGhpcy5hbmltYXRpb25zWzBdW3Byb3BOYW1lXTtcbiAgICB9XG4gICAgc2V0QWxsKHByb3BOYW1lLCBuZXdWYWx1ZSkge1xuICAgICAgICBmb3IgKGxldCBpID0gMDsgaSA8IHRoaXMuYW5pbWF0aW9ucy5sZW5ndGg7IGkrKykge1xuICAgICAgICAgICAgdGhpcy5hbmltYXRpb25zW2ldW3Byb3BOYW1lXSA9IG5ld1ZhbHVlO1xuICAgICAgICB9XG4gICAgfVxuICAgIGF0dGFjaFRpbWVsaW5lKHRpbWVsaW5lKSB7XG4gICAgICAgIGNvbnN0IHN1YnNjcmlwdGlvbnMgPSB0aGlzLmFuaW1hdGlvbnMubWFwKChhbmltYXRpb24pID0+IGFuaW1hdGlvbi5hdHRhY2hUaW1lbGluZSh0aW1lbGluZSkpO1xuICAgICAgICByZXR1cm4gKCkgPT4ge1xuICAgICAgICAgICAgc3Vic2NyaXB0aW9ucy5mb3JFYWNoKChjYW5jZWwsIGkpID0+IHtcbiAgICAgICAgICAgICAgICBjYW5jZWwgJiYgY2FuY2VsKCk7XG4gICAgICAgICAgICAgICAgdGhpcy5hbmltYXRpb25zW2ldLnN0b3AoKTtcbiAgICAgICAgICAgIH0pO1xuICAgICAgICB9O1xuICAgIH1cbiAgICBnZXQgdGltZSgpIHtcbiAgICAgICAgcmV0dXJuIHRoaXMuZ2V0QWxsKFwidGltZVwiKTtcbiAgICB9XG4gICAgc2V0IHRpbWUodGltZSkge1xuICAgICAgICB0aGlzLnNldEFsbChcInRpbWVcIiwgdGltZSk7XG4gICAgfVxuICAgIGdldCBzcGVlZCgpIHtcbiAgICAgICAgcmV0dXJuIHRoaXMuZ2V0QWxsKFwic3BlZWRcIik7XG4gICAgfVxuICAgIHNldCBzcGVlZChzcGVlZCkge1xuICAgICAgICB0aGlzLnNldEFsbChcInNwZWVkXCIsIHNwZWVkKTtcbiAgICB9XG4gICAgZ2V0IHN0YXRlKCkge1xuICAgICAgICByZXR1cm4gdGhpcy5nZXRBbGwoXCJzdGF0ZVwiKTtcbiAgICB9XG4gICAgZ2V0IHN0YXJ0VGltZSgpIHtcbiAgICAgICAgcmV0dXJuIHRoaXMuZ2V0QWxsKFwic3RhcnRUaW1lXCIpO1xuICAgIH1cbiAgICBnZXQgZHVyYXRpb24oKSB7XG4gICAgICAgIGxldCBtYXggPSAwO1xuICAgICAgICBmb3IgKGxldCBpID0gMDsgaSA8IHRoaXMuYW5pbWF0aW9ucy5sZW5ndGg7IGkrKykge1xuICAgICAgICAgICAgbWF4ID0gTWF0aC5tYXgobWF4LCB0aGlzLmFuaW1hdGlvbnNbaV0uZHVyYXRpb24pO1xuICAgICAgICB9XG4gICAgICAgIHJldHVybiBtYXg7XG4gICAgfVxuICAgIHJ1bkFsbChtZXRob2ROYW1lKSB7XG4gICAgICAgIHRoaXMuYW5pbWF0aW9ucy5mb3JFYWNoKChjb250cm9scykgPT4gY29udHJvbHNbbWV0aG9kTmFtZV0oKSk7XG4gICAgfVxuICAgIHBsYXkoKSB7XG4gICAgICAgIHRoaXMucnVuQWxsKFwicGxheVwiKTtcbiAgICB9XG4gICAgcGF1c2UoKSB7XG4gICAgICAgIHRoaXMucnVuQWxsKFwicGF1c2VcIik7XG4gICAgfVxuICAgIGNhbmNlbCgpIHtcbiAgICAgICAgdGhpcy5ydW5BbGwoXCJjYW5jZWxcIik7XG4gICAgfVxuICAgIGNvbXBsZXRlKCkge1xuICAgICAgICB0aGlzLnJ1bkFsbChcImNvbXBsZXRlXCIpO1xuICAgIH1cbn1cblxuZXhwb3J0IHsgR3JvdXBBbmltYXRpb24gfTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/motion-dom/dist/es/animation/GroupAnimation.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/motion-dom/dist/es/animation/GroupAnimationWithThen.mjs":
/*!******************************************************************************!*\
  !*** ./node_modules/motion-dom/dist/es/animation/GroupAnimationWithThen.mjs ***!
  \******************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GroupAnimationWithThen: () => (/* binding */ GroupAnimationWithThen)\n/* harmony export */ });\n/* harmony import */ var _GroupAnimation_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./GroupAnimation.mjs */ \"(app-pages-browser)/./node_modules/motion-dom/dist/es/animation/GroupAnimation.mjs\");\n\n\nclass GroupAnimationWithThen extends _GroupAnimation_mjs__WEBPACK_IMPORTED_MODULE_0__.GroupAnimation {\n    then(onResolve, _onReject) {\n        return this.finished.finally(onResolve).then(() => { });\n    }\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9tb3Rpb24tZG9tL2Rpc3QvZXMvYW5pbWF0aW9uL0dyb3VwQW5pbWF0aW9uV2l0aFRoZW4ubWpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQXNEOztBQUV0RCxxQ0FBcUMsK0RBQWM7QUFDbkQ7QUFDQSw4REFBOEQ7QUFDOUQ7QUFDQTs7QUFFa0MiLCJzb3VyY2VzIjpbIi9Vc2Vycy9rdy9EZXNrdG9wL0NsYXVkZS9haS1tYXJrZXRpbmctd2Vic2l0ZS9ub2RlX21vZHVsZXMvbW90aW9uLWRvbS9kaXN0L2VzL2FuaW1hdGlvbi9Hcm91cEFuaW1hdGlvbldpdGhUaGVuLm1qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBHcm91cEFuaW1hdGlvbiB9IGZyb20gJy4vR3JvdXBBbmltYXRpb24ubWpzJztcblxuY2xhc3MgR3JvdXBBbmltYXRpb25XaXRoVGhlbiBleHRlbmRzIEdyb3VwQW5pbWF0aW9uIHtcbiAgICB0aGVuKG9uUmVzb2x2ZSwgX29uUmVqZWN0KSB7XG4gICAgICAgIHJldHVybiB0aGlzLmZpbmlzaGVkLmZpbmFsbHkob25SZXNvbHZlKS50aGVuKCgpID0+IHsgfSk7XG4gICAgfVxufVxuXG5leHBvcnQgeyBHcm91cEFuaW1hdGlvbldpdGhUaGVuIH07XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/motion-dom/dist/es/animation/GroupAnimationWithThen.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/motion-dom/dist/es/utils/transform.mjs":
/*!*************************************************************!*\
  !*** ./node_modules/motion-dom/dist/es/utils/transform.mjs ***!
  \*************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   transform: () => (/* binding */ transform)\n/* harmony export */ });\n/* harmony import */ var _interpolate_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./interpolate.mjs */ \"(app-pages-browser)/./node_modules/motion-dom/dist/es/utils/interpolate.mjs\");\n\n\nfunction transform(...args) {\n    const useImmediate = !Array.isArray(args[0]);\n    const argOffset = useImmediate ? 0 : -1;\n    const inputValue = args[0 + argOffset];\n    const inputRange = args[1 + argOffset];\n    const outputRange = args[2 + argOffset];\n    const options = args[3 + argOffset];\n    const interpolator = (0,_interpolate_mjs__WEBPACK_IMPORTED_MODULE_0__.interpolate)(inputRange, outputRange, options);\n    return useImmediate ? interpolator(inputValue) : interpolator;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9tb3Rpb24tZG9tL2Rpc3QvZXMvdXRpbHMvdHJhbnNmb3JtLm1qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFnRDs7QUFFaEQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSx5QkFBeUIsNkRBQVc7QUFDcEM7QUFDQTs7QUFFcUIiLCJzb3VyY2VzIjpbIi9Vc2Vycy9rdy9EZXNrdG9wL0NsYXVkZS9haS1tYXJrZXRpbmctd2Vic2l0ZS9ub2RlX21vZHVsZXMvbW90aW9uLWRvbS9kaXN0L2VzL3V0aWxzL3RyYW5zZm9ybS5tanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgaW50ZXJwb2xhdGUgfSBmcm9tICcuL2ludGVycG9sYXRlLm1qcyc7XG5cbmZ1bmN0aW9uIHRyYW5zZm9ybSguLi5hcmdzKSB7XG4gICAgY29uc3QgdXNlSW1tZWRpYXRlID0gIUFycmF5LmlzQXJyYXkoYXJnc1swXSk7XG4gICAgY29uc3QgYXJnT2Zmc2V0ID0gdXNlSW1tZWRpYXRlID8gMCA6IC0xO1xuICAgIGNvbnN0IGlucHV0VmFsdWUgPSBhcmdzWzAgKyBhcmdPZmZzZXRdO1xuICAgIGNvbnN0IGlucHV0UmFuZ2UgPSBhcmdzWzEgKyBhcmdPZmZzZXRdO1xuICAgIGNvbnN0IG91dHB1dFJhbmdlID0gYXJnc1syICsgYXJnT2Zmc2V0XTtcbiAgICBjb25zdCBvcHRpb25zID0gYXJnc1szICsgYXJnT2Zmc2V0XTtcbiAgICBjb25zdCBpbnRlcnBvbGF0b3IgPSBpbnRlcnBvbGF0ZShpbnB1dFJhbmdlLCBvdXRwdXRSYW5nZSwgb3B0aW9ucyk7XG4gICAgcmV0dXJuIHVzZUltbWVkaWF0ZSA/IGludGVycG9sYXRvcihpbnB1dFZhbHVlKSA6IGludGVycG9sYXRvcjtcbn1cblxuZXhwb3J0IHsgdHJhbnNmb3JtIH07XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/motion-dom/dist/es/utils/transform.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/motion-utils/dist/es/easing/utils/get-easing-for-segment.mjs":
/*!***********************************************************************************!*\
  !*** ./node_modules/motion-utils/dist/es/easing/utils/get-easing-for-segment.mjs ***!
  \***********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getEasingForSegment: () => (/* binding */ getEasingForSegment)\n/* harmony export */ });\n/* harmony import */ var _wrap_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../wrap.mjs */ \"(app-pages-browser)/./node_modules/motion-utils/dist/es/wrap.mjs\");\n/* harmony import */ var _is_easing_array_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./is-easing-array.mjs */ \"(app-pages-browser)/./node_modules/motion-utils/dist/es/easing/utils/is-easing-array.mjs\");\n\n\n\nfunction getEasingForSegment(easing, i) {\n    return (0,_is_easing_array_mjs__WEBPACK_IMPORTED_MODULE_0__.isEasingArray)(easing) ? easing[(0,_wrap_mjs__WEBPACK_IMPORTED_MODULE_1__.wrap)(0, easing.length, i)] : easing;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9tb3Rpb24tdXRpbHMvZGlzdC9lcy9lYXNpbmcvdXRpbHMvZ2V0LWVhc2luZy1mb3Itc2VnbWVudC5tanMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQXNDO0FBQ2dCOztBQUV0RDtBQUNBLFdBQVcsbUVBQWEsa0JBQWtCLCtDQUFJO0FBQzlDOztBQUUrQiIsInNvdXJjZXMiOlsiL1VzZXJzL2t3L0Rlc2t0b3AvQ2xhdWRlL2FpLW1hcmtldGluZy13ZWJzaXRlL25vZGVfbW9kdWxlcy9tb3Rpb24tdXRpbHMvZGlzdC9lcy9lYXNpbmcvdXRpbHMvZ2V0LWVhc2luZy1mb3Itc2VnbWVudC5tanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgd3JhcCB9IGZyb20gJy4uLy4uL3dyYXAubWpzJztcbmltcG9ydCB7IGlzRWFzaW5nQXJyYXkgfSBmcm9tICcuL2lzLWVhc2luZy1hcnJheS5tanMnO1xuXG5mdW5jdGlvbiBnZXRFYXNpbmdGb3JTZWdtZW50KGVhc2luZywgaSkge1xuICAgIHJldHVybiBpc0Vhc2luZ0FycmF5KGVhc2luZykgPyBlYXNpbmdbd3JhcCgwLCBlYXNpbmcubGVuZ3RoLCBpKV0gOiBlYXNpbmc7XG59XG5cbmV4cG9ydCB7IGdldEVhc2luZ0ZvclNlZ21lbnQgfTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/motion-utils/dist/es/easing/utils/get-easing-for-segment.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/motion-utils/dist/es/wrap.mjs":
/*!****************************************************!*\
  !*** ./node_modules/motion-utils/dist/es/wrap.mjs ***!
  \****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   wrap: () => (/* binding */ wrap)\n/* harmony export */ });\nconst wrap = (min, max, v) => {\n    const rangeSize = max - min;\n    return ((((v - min) % rangeSize) + rangeSize) % rangeSize) + min;\n};\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9tb3Rpb24tdXRpbHMvZGlzdC9lcy93cmFwLm1qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7QUFDQTtBQUNBO0FBQ0E7O0FBRWdCIiwic291cmNlcyI6WyIvVXNlcnMva3cvRGVza3RvcC9DbGF1ZGUvYWktbWFya2V0aW5nLXdlYnNpdGUvbm9kZV9tb2R1bGVzL21vdGlvbi11dGlscy9kaXN0L2VzL3dyYXAubWpzIl0sInNvdXJjZXNDb250ZW50IjpbImNvbnN0IHdyYXAgPSAobWluLCBtYXgsIHYpID0+IHtcbiAgICBjb25zdCByYW5nZVNpemUgPSBtYXggLSBtaW47XG4gICAgcmV0dXJuICgoKCh2IC0gbWluKSAlIHJhbmdlU2l6ZSkgKyByYW5nZVNpemUpICUgcmFuZ2VTaXplKSArIG1pbjtcbn07XG5cbmV4cG9ydCB7IHdyYXAgfTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/motion-utils/dist/es/wrap.mjs\n"));

/***/ })

});