"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./components/Testimonials.tsx":
/*!*************************************!*\
  !*** ./components/Testimonials.tsx ***!
  \*************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _barrel_optimize_names_Quote_Star_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Quote,Star!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/star.js\");\n/* harmony import */ var _barrel_optimize_names_Quote_Star_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Quote,Star!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/quote.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nconst Testimonials = ()=>{\n    _s();\n    const [activeIndex, setActiveIndex] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const testimonials = [\n        {\n            id: 1,\n            name: '張明華',\n            role: 'CEO',\n            company: 'TechVision Ltd',\n            avatar: '張',\n            content: 'AI Marketing Pro 的服務徹底改變了我們的營銷策略。在短短 6 個月內，我們的營收增長了 300%，客戶滿意度也達到歷史新高。他們的專業團隊不僅提供技術支持，更是我們值得信賴的戰略合作夥伴。',\n            rating: 5,\n            gradient: 'from-indigo-500 to-purple-500'\n        },\n        {\n            id: 2,\n            name: '李美玲',\n            role: '營銷總監',\n            company: 'Global Retail Group',\n            avatar: '李',\n            content: '與 AI Marketing Pro 合作是我們做過最正確的決定。他們的 AI 解決方案幫助我們精準定位目標客戶，營銷效率提升了 250%。最重要的是，他們真正理解我們的業務需求，提供的都是實用的解決方案。',\n            rating: 5,\n            gradient: 'from-purple-500 to-pink-500'\n        },\n        {\n            id: 3,\n            name: '王建國',\n            role: '創始人',\n            company: 'StartUp Hub',\n            avatar: '王',\n            content: '作為一家初創公司，我們需要快速增長但預算有限。AI Marketing Pro 為我們量身定制的解決方案不僅成本效益高，效果也超出預期。現在我們的獲客成本降低了 70%，用戶增長率提升了 400%。',\n            rating: 5,\n            gradient: 'from-cyan-500 to-blue-500'\n        },\n        {\n            id: 4,\n            name: '陳雅婷',\n            role: 'CMO',\n            company: 'Fashion Forward',\n            avatar: '陳',\n            content: 'AI Marketing Pro 的團隊專業、創新、反應迅速。他們幫助我們建立了完整的 AI 營銷體系，從數據分析到內容創作，全程自動化。我們的社交媒體互動率提升了 600%，品牌影響力顯著增強。',\n            rating: 5,\n            gradient: 'from-pink-500 to-orange-500'\n        },\n        {\n            id: 5,\n            name: '林志偉',\n            role: '數據主管',\n            company: 'Data Dynamics',\n            avatar: '林',\n            content: '他們的數據分析能力令人印象深刻。不僅幫我們整合了所有營銷數據，還提供了深入的洞察和預測。現在我們可以提前預知市場趨勢，制定更精準的營銷策略。ROI 提升了 450%！',\n            rating: 5,\n            gradient: 'from-green-500 to-emerald-500'\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        className: \"section-spacing relative overflow-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 -z-10\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"absolute inset-0 bg-gradient-radial\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Testimonials.tsx\",\n                    lineNumber: 67,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Testimonials.tsx\",\n                lineNumber: 66,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"container-custom\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            y: 20\n                        },\n                        whileInView: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        viewport: {\n                            once: true\n                        },\n                        transition: {\n                            duration: 0.6\n                        },\n                        className: \"text-center mb-16\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    scale: 0.9\n                                },\n                                whileInView: {\n                                    opacity: 1,\n                                    scale: 1\n                                },\n                                viewport: {\n                                    once: true\n                                },\n                                className: \"inline-flex items-center space-x-2 badge badge-primary mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Quote_Star_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                        className: \"w-4 h-4\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Testimonials.tsx\",\n                                        lineNumber: 85,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"客戶評價\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Testimonials.tsx\",\n                                        lineNumber: 86,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Testimonials.tsx\",\n                                lineNumber: 79,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"heading-lg mb-6\",\n                                children: [\n                                    \"聽聽客戶怎麼說 \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"gradient-text\",\n                                        children: \"真實反饋\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Testimonials.tsx\",\n                                        lineNumber: 90,\n                                        columnNumber: 21\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Testimonials.tsx\",\n                                lineNumber: 89,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"body-lg text-gray-600 max-w-3xl mx-auto\",\n                                children: \"我們的成功來自客戶的信任與支持，他們的評價是我們不斷進步的動力\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Testimonials.tsx\",\n                                lineNumber: 93,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Testimonials.tsx\",\n                        lineNumber: 72,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"max-w-4xl mx-auto mb-12\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                            initial: {\n                                opacity: 0,\n                                x: 20\n                            },\n                            animate: {\n                                opacity: 1,\n                                x: 0\n                            },\n                            exit: {\n                                opacity: 0,\n                                x: -20\n                            },\n                            transition: {\n                                duration: 0.5\n                            },\n                            className: \"card-modern p-8 md:p-12 relative overflow-hidden\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Quote_Star_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                    className: \"absolute top-8 left-8 w-16 h-16 text-gray-100\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Testimonials.tsx\",\n                                    lineNumber: 109,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative z-10\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center mb-6\",\n                                            children: [\n                                                ...Array(testimonials[activeIndex].rating)\n                                            ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Quote_Star_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                    className: \"w-5 h-5 fill-current text-yellow-400\"\n                                                }, i, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Testimonials.tsx\",\n                                                    lineNumber: 116,\n                                                    columnNumber: 19\n                                                }, undefined))\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Testimonials.tsx\",\n                                            lineNumber: 114,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xl md:text-2xl text-gray-700 leading-relaxed mb-8\",\n                                            children: [\n                                                '\"',\n                                                testimonials[activeIndex].content,\n                                                '\"'\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Testimonials.tsx\",\n                                            lineNumber: 121,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-16 h-16 rounded-full bg-gradient-to-r \".concat(testimonials[activeIndex].gradient, \" flex items-center justify-center text-white text-2xl font-bold mr-4\"),\n                                                    children: testimonials[activeIndex].avatar\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Testimonials.tsx\",\n                                                    lineNumber: 127,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                            className: \"text-lg font-semibold text-gray-900\",\n                                                            children: testimonials[activeIndex].name\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Testimonials.tsx\",\n                                                            lineNumber: 131,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-600\",\n                                                            children: [\n                                                                testimonials[activeIndex].role,\n                                                                \" • \",\n                                                                testimonials[activeIndex].company\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Testimonials.tsx\",\n                                                            lineNumber: 134,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Testimonials.tsx\",\n                                                    lineNumber: 130,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Testimonials.tsx\",\n                                            lineNumber: 126,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Testimonials.tsx\",\n                                    lineNumber: 112,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, activeIndex, true, {\n                            fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Testimonials.tsx\",\n                            lineNumber: 100,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Testimonials.tsx\",\n                        lineNumber: 99,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-wrap justify-center gap-4\",\n                        children: testimonials.map((testimonial, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.button, {\n                                onClick: ()=>setActiveIndex(index),\n                                whileHover: {\n                                    scale: 1.05\n                                },\n                                whileTap: {\n                                    scale: 0.95\n                                },\n                                className: \"p-4 rounded-xl transition-all duration-300 \".concat(activeIndex === index ? 'card-modern shadow-lg border-2 border-indigo-500' : 'bg-white border-2 border-gray-200 hover:border-gray-300'),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-10 h-10 rounded-full bg-gradient-to-r \".concat(testimonial.gradient, \" flex items-center justify-center text-white font-semibold\"),\n                                            children: testimonial.avatar\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Testimonials.tsx\",\n                                            lineNumber: 158,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-left\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"font-semibold text-sm text-gray-900\",\n                                                    children: testimonial.name\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Testimonials.tsx\",\n                                                    lineNumber: 162,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xs text-gray-600\",\n                                                    children: testimonial.company\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Testimonials.tsx\",\n                                                    lineNumber: 163,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Testimonials.tsx\",\n                                            lineNumber: 161,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Testimonials.tsx\",\n                                    lineNumber: 157,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, testimonial.id, false, {\n                                fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Testimonials.tsx\",\n                                lineNumber: 146,\n                                columnNumber: 13\n                            }, undefined))\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Testimonials.tsx\",\n                        lineNumber: 144,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            y: 20\n                        },\n                        whileInView: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        viewport: {\n                            once: true\n                        },\n                        transition: {\n                            duration: 0.6,\n                            delay: 0.3\n                        },\n                        className: \"mt-20\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-2 md:grid-cols-4 gap-8\",\n                            children: [\n                                {\n                                    value: '98%',\n                                    label: '客戶滿意度'\n                                },\n                                {\n                                    value: '4.9/5',\n                                    label: '平均評分'\n                                },\n                                {\n                                    value: '500+',\n                                    label: '成功案例'\n                                },\n                                {\n                                    value: '95%',\n                                    label: '客戶續約率'\n                                }\n                            ].map((stat, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                                    initial: {\n                                        opacity: 0,\n                                        scale: 0.9\n                                    },\n                                    whileInView: {\n                                        opacity: 1,\n                                        scale: 1\n                                    },\n                                    viewport: {\n                                        once: true\n                                    },\n                                    transition: {\n                                        duration: 0.5,\n                                        delay: 0.4 + index * 0.1\n                                    },\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-3xl md:text-4xl font-bold gradient-text mb-2\",\n                                            children: stat.value\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Testimonials.tsx\",\n                                            lineNumber: 193,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600\",\n                                            children: stat.label\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Testimonials.tsx\",\n                                            lineNumber: 196,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, index, true, {\n                                    fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Testimonials.tsx\",\n                                    lineNumber: 185,\n                                    columnNumber: 15\n                                }, undefined))\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Testimonials.tsx\",\n                            lineNumber: 178,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Testimonials.tsx\",\n                        lineNumber: 171,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Testimonials.tsx\",\n                lineNumber: 70,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Testimonials.tsx\",\n        lineNumber: 64,\n        columnNumber: 5\n    }, undefined);\n};\n_s(Testimonials, \"rd+5N/MkYjuYD0I+B+MlySxQysU=\");\n_c = Testimonials;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Testimonials);\nvar _c;\n$RefreshReg$(_c, \"Testimonials\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/Testimonials.tsx\n"));

/***/ })

});