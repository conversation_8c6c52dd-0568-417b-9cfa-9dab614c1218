"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./components/Features.tsx":
/*!*********************************!*\
  !*** ./components/Features.tsx ***!
  \*********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BarChart3_Brain_CheckCircle_Shield_Target_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BarChart3,Brain,CheckCircle,Shield,Target,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/brain.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BarChart3_Brain_CheckCircle_Shield_Target_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BarChart3,Brain,CheckCircle,Shield,Target,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/target.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BarChart3_Brain_CheckCircle_Shield_Target_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BarChart3,Brain,CheckCircle,Shield,Target,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BarChart3_Brain_CheckCircle_Shield_Target_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BarChart3,Brain,CheckCircle,Shield,Target,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chart-column.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BarChart3_Brain_CheckCircle_Shield_Target_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BarChart3,Brain,CheckCircle,Shield,Target,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BarChart3_Brain_CheckCircle_Shield_Target_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BarChart3,Brain,CheckCircle,Shield,Target,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BarChart3_Brain_CheckCircle_Shield_Target_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BarChart3,Brain,CheckCircle,Shield,Target,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BarChart3_Brain_CheckCircle_Shield_Target_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BarChart3,Brain,CheckCircle,Shield,Target,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-right.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nconst Features = ()=>{\n    const features = [\n        {\n            icon: _barrel_optimize_names_ArrowRight_BarChart3_Brain_CheckCircle_Shield_Target_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_1__[\"default\"],\n            title: 'AI 驅動智能分析',\n            description: '運用最先進的機器學習算法，深度分析客戶行為模式，預測市場趨勢',\n            gradient: 'from-indigo-500 to-purple-500',\n            benefits: [\n                '實時數據處理',\n                '預測性分析',\n                '智能決策建議'\n            ]\n        },\n        {\n            icon: _barrel_optimize_names_ArrowRight_BarChart3_Brain_CheckCircle_Shield_Target_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"],\n            title: '精準客戶定位',\n            description: '透過 AI 分析海量數據，精確識別並定位您的理想客戶群體',\n            gradient: 'from-purple-500 to-pink-500',\n            benefits: [\n                '客戶畫像建模',\n                '行為預測',\n                '個性化推薦'\n            ]\n        },\n        {\n            icon: _barrel_optimize_names_ArrowRight_BarChart3_Brain_CheckCircle_Shield_Target_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n            title: '營銷自動化',\n            description: '自動化您的營銷流程，提升效率，讓團隊專注於策略創新',\n            gradient: 'from-cyan-500 to-blue-500',\n            benefits: [\n                '工作流程自動化',\n                '智能觸發營銷',\n                'A/B 測試優化'\n            ]\n        },\n        {\n            icon: _barrel_optimize_names_ArrowRight_BarChart3_Brain_CheckCircle_Shield_Target_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n            title: '數據驅動決策',\n            description: '將複雜數據轉化為清晰洞察，為營銷策略提供有力支撐',\n            gradient: 'from-green-500 to-emerald-500',\n            benefits: [\n                '實時儀表板',\n                'ROI 追踪',\n                '競爭對手分析'\n            ]\n        },\n        {\n            icon: _barrel_optimize_names_ArrowRight_BarChart3_Brain_CheckCircle_Shield_Target_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n            title: '客戶體驗優化',\n            description: '打造個性化的客戶旅程，提升客戶滿意度和忠誠度',\n            gradient: 'from-orange-500 to-red-500',\n            benefits: [\n                '客戶旅程映射',\n                '情感分析',\n                '實時個性化'\n            ]\n        },\n        {\n            icon: _barrel_optimize_names_ArrowRight_BarChart3_Brain_CheckCircle_Shield_Target_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n            title: '數據安全保護',\n            description: '採用企業級安全標準，確保您的數據和客戶信息安全無虞',\n            gradient: 'from-slate-600 to-slate-800',\n            benefits: [\n                '端到端加密',\n                'GDPR 合規',\n                '定期安全審計'\n            ]\n        }\n    ];\n    const containerVariants = {\n        hidden: {\n            opacity: 0\n        },\n        visible: {\n            opacity: 1,\n            transition: {\n                staggerChildren: 0.1\n            }\n        }\n    };\n    const itemVariants = {\n        hidden: {\n            y: 20,\n            opacity: 0\n        },\n        visible: {\n            y: 0,\n            opacity: 1,\n            transition: {\n                type: 'spring',\n                stiffness: 100\n            }\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        className: \"section-spacing relative\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 -z-10\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"absolute inset-0 bg-gradient-radial opacity-50\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Features.tsx\",\n                    lineNumber: 87,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Features.tsx\",\n                lineNumber: 86,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"container-custom\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            y: 20\n                        },\n                        whileInView: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        viewport: {\n                            once: true\n                        },\n                        transition: {\n                            duration: 0.6\n                        },\n                        className: \"text-center mb-16\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    scale: 0.9\n                                },\n                                whileInView: {\n                                    opacity: 1,\n                                    scale: 1\n                                },\n                                viewport: {\n                                    once: true\n                                },\n                                className: \"inline-flex items-center space-x-2 badge badge-secondary mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BarChart3_Brain_CheckCircle_Shield_Target_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                        className: \"w-4 h-4\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Features.tsx\",\n                                        lineNumber: 105,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"強大功能\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Features.tsx\",\n                                        lineNumber: 106,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Features.tsx\",\n                                lineNumber: 99,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"heading-lg mb-6\",\n                                children: [\n                                    \"為現代營銷而生的 \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"gradient-text\",\n                                        children: \"AI 功能套件\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Features.tsx\",\n                                        lineNumber: 110,\n                                        columnNumber: 22\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Features.tsx\",\n                                lineNumber: 109,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"body-lg text-gray-600 max-w-3xl mx-auto\",\n                                children: \"我們的 AI 平台整合了最先進的技術，為您提供全方位的營銷解決方案， 從數據分析到客戶互動，全程智能化管理\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Features.tsx\",\n                                lineNumber: 113,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Features.tsx\",\n                        lineNumber: 92,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                        variants: containerVariants,\n                        initial: \"hidden\",\n                        whileInView: \"visible\",\n                        viewport: {\n                            once: true\n                        },\n                        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\",\n                        children: features.map((feature, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                                variants: itemVariants,\n                                whileHover: {\n                                    y: -5\n                                },\n                                className: \"group\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"card-modern card-hover h-full p-8 relative overflow-hidden\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute top-0 right-0 w-32 h-32 bg-gradient-to-br \".concat(feature.gradient, \" rounded-full blur-3xl opacity-10 group-hover:opacity-20 transition-opacity\")\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Features.tsx\",\n                                            lineNumber: 136,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative z-10\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-14 h-14 bg-gradient-to-r \".concat(feature.gradient, \" rounded-2xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform\"),\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(feature.icon, {\n                                                        className: \"w-7 h-7 text-white\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Features.tsx\",\n                                                        lineNumber: 141,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Features.tsx\",\n                                                    lineNumber: 140,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-xl font-bold mb-3 text-gray-900\",\n                                                    children: feature.title\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Features.tsx\",\n                                                    lineNumber: 145,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-600 mb-6 leading-relaxed\",\n                                                    children: feature.description\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Features.tsx\",\n                                                    lineNumber: 146,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                    className: \"space-y-2 mb-6\",\n                                                    children: feature.benefits.map((benefit, idx)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            className: \"flex items-center space-x-2 text-sm text-gray-600\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BarChart3_Brain_CheckCircle_Shield_Target_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                                    className: \"w-4 h-4 text-green-500 flex-shrink-0\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Features.tsx\",\n                                                                    lineNumber: 152,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: benefit\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Features.tsx\",\n                                                                    lineNumber: 153,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, idx, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Features.tsx\",\n                                                            lineNumber: 151,\n                                                            columnNumber: 23\n                                                        }, undefined))\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Features.tsx\",\n                                                    lineNumber: 149,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"#\",\n                                                    className: \"inline-flex items-center text-indigo-600 font-medium hover:text-indigo-700 transition-colors group/link\",\n                                                    children: [\n                                                        \"了解更多\",\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BarChart3_Brain_CheckCircle_Shield_Target_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                            className: \"w-4 h-4 ml-1 group-hover/link:translate-x-1 transition-transform\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Features.tsx\",\n                                                            lineNumber: 164,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Features.tsx\",\n                                                    lineNumber: 159,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Features.tsx\",\n                                            lineNumber: 138,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Features.tsx\",\n                                    lineNumber: 134,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, index, false, {\n                                fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Features.tsx\",\n                                lineNumber: 128,\n                                columnNumber: 13\n                            }, undefined))\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Features.tsx\",\n                        lineNumber: 120,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            y: 20\n                        },\n                        whileInView: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        viewport: {\n                            once: true\n                        },\n                        transition: {\n                            duration: 0.6,\n                            delay: 0.3\n                        },\n                        className: \"text-center mt-16\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600 mb-6\",\n                                children: \"想要深入了解我們的功能如何幫助您的業務增長？\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Features.tsx\",\n                                lineNumber: 180,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                href: \"/contact\",\n                                className: \"btn-primary\",\n                                children: [\n                                    \"預約產品演示\",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BarChart3_Brain_CheckCircle_Shield_Target_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        className: \"inline-block ml-2 w-5 h-5\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Features.tsx\",\n                                        lineNumber: 185,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Features.tsx\",\n                                lineNumber: 183,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Features.tsx\",\n                        lineNumber: 173,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Features.tsx\",\n                lineNumber: 90,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Features.tsx\",\n        lineNumber: 84,\n        columnNumber: 5\n    }, undefined);\n};\n_c = Features;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Features);\nvar _c;\n$RefreshReg$(_c, \"Features\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/Features.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/target.js":
/*!************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/target.js ***!
  \************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __iconNode: () => (/* binding */ __iconNode),\n/* harmony export */   \"default\": () => (/* binding */ Target)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst __iconNode = [\n    [\n        \"circle\",\n        {\n            cx: \"12\",\n            cy: \"12\",\n            r: \"10\",\n            key: \"1mglay\"\n        }\n    ],\n    [\n        \"circle\",\n        {\n            cx: \"12\",\n            cy: \"12\",\n            r: \"6\",\n            key: \"1vlfrh\"\n        }\n    ],\n    [\n        \"circle\",\n        {\n            cx: \"12\",\n            cy: \"12\",\n            r: \"2\",\n            key: \"1c9p78\"\n        }\n    ]\n];\nconst Target = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"target\", __iconNode);\n //# sourceMappingURL=target.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9sdWNpZGUtcmVhY3QvZGlzdC9lc20vaWNvbnMvdGFyZ2V0LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7OztBQUdPLE1BQU0sVUFBdUI7SUFDbEM7UUFBQyxRQUFVO1FBQUE7WUFBRSxFQUFJO1lBQU0sQ0FBSSxRQUFNO1lBQUEsQ0FBRztZQUFNLEdBQUs7UUFBQSxDQUFVO0tBQUE7SUFDekQ7UUFBQyxRQUFVO1FBQUE7WUFBRSxFQUFJO1lBQU0sQ0FBSSxRQUFNO1lBQUEsQ0FBRztZQUFLLEdBQUs7UUFBQSxDQUFVO0tBQUE7SUFDeEQ7UUFBQyxDQUFVO1FBQUE7WUFBRSxDQUFJLFFBQU07WUFBQSxFQUFJO1lBQU0sQ0FBRztZQUFLLENBQUs7UUFBVTtLQUFBO0NBQzFEO0FBYU0sYUFBUyxrRUFBaUIsV0FBVSxDQUFVIiwic291cmNlcyI6WyIvVXNlcnMva3cvc3JjL2ljb25zL3RhcmdldC50cyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgY3JlYXRlTHVjaWRlSWNvbiBmcm9tICcuLi9jcmVhdGVMdWNpZGVJY29uJztcbmltcG9ydCB7IEljb25Ob2RlIH0gZnJvbSAnLi4vdHlwZXMnO1xuXG5leHBvcnQgY29uc3QgX19pY29uTm9kZTogSWNvbk5vZGUgPSBbXG4gIFsnY2lyY2xlJywgeyBjeDogJzEyJywgY3k6ICcxMicsIHI6ICcxMCcsIGtleTogJzFtZ2xheScgfV0sXG4gIFsnY2lyY2xlJywgeyBjeDogJzEyJywgY3k6ICcxMicsIHI6ICc2Jywga2V5OiAnMXZsZnJoJyB9XSxcbiAgWydjaXJjbGUnLCB7IGN4OiAnMTInLCBjeTogJzEyJywgcjogJzInLCBrZXk6ICcxYzlwNzgnIH1dLFxuXTtcblxuLyoqXG4gKiBAY29tcG9uZW50IEBuYW1lIFRhcmdldFxuICogQGRlc2NyaXB0aW9uIEx1Y2lkZSBTVkcgaWNvbiBjb21wb25lbnQsIHJlbmRlcnMgU1ZHIEVsZW1lbnQgd2l0aCBjaGlsZHJlbi5cbiAqXG4gKiBAcHJldmlldyAhW2ltZ10oZGF0YTppbWFnZS9zdmcreG1sO2Jhc2U2NCxQSE4yWnlBZ2VHMXNibk05SW1oMGRIQTZMeTkzZDNjdWR6TXViM0puTHpJd01EQXZjM1puSWdvZ0lIZHBaSFJvUFNJeU5DSUtJQ0JvWldsbmFIUTlJakkwSWdvZ0lIWnBaWGRDYjNnOUlqQWdNQ0F5TkNBeU5DSUtJQ0JtYVd4c1BTSnViMjVsSWdvZ0lITjBjbTlyWlQwaUl6QXdNQ0lnYzNSNWJHVTlJbUpoWTJ0bmNtOTFibVF0WTI5c2IzSTZJQ05tWm1ZN0lHSnZjbVJsY2kxeVlXUnBkWE02SURKd2VDSUtJQ0J6ZEhKdmEyVXRkMmxrZEdnOUlqSWlDaUFnYzNSeWIydGxMV3hwYm1WallYQTlJbkp2ZFc1a0lnb2dJSE4wY205clpTMXNhVzVsYW05cGJqMGljbTkxYm1RaUNqNEtJQ0E4WTJseVkyeGxJR040UFNJeE1pSWdZM2s5SWpFeUlpQnlQU0l4TUNJZ0x6NEtJQ0E4WTJseVkyeGxJR040UFNJeE1pSWdZM2s5SWpFeUlpQnlQU0kySWlBdlBnb2dJRHhqYVhKamJHVWdZM2c5SWpFeUlpQmplVDBpTVRJaUlISTlJaklpSUM4K0Nqd3ZjM1puUGdvPSkgLSBodHRwczovL2x1Y2lkZS5kZXYvaWNvbnMvdGFyZ2V0XG4gKiBAc2VlIGh0dHBzOi8vbHVjaWRlLmRldi9ndWlkZS9wYWNrYWdlcy9sdWNpZGUtcmVhY3QgLSBEb2N1bWVudGF0aW9uXG4gKlxuICogQHBhcmFtIHtPYmplY3R9IHByb3BzIC0gTHVjaWRlIGljb25zIHByb3BzIGFuZCBhbnkgdmFsaWQgU1ZHIGF0dHJpYnV0ZVxuICogQHJldHVybnMge0pTWC5FbGVtZW50fSBKU1ggRWxlbWVudFxuICpcbiAqL1xuY29uc3QgVGFyZ2V0ID0gY3JlYXRlTHVjaWRlSWNvbigndGFyZ2V0JywgX19pY29uTm9kZSk7XG5cbmV4cG9ydCBkZWZhdWx0IFRhcmdldDtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/target.js\n"));

/***/ })

});