"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./components/Services.tsx":
/*!*********************************!*\
  !*** ./components/Services.tsx ***!
  \*********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BarChart3_Brain_CheckCircle2_Sparkles_Target_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BarChart3,Brain,CheckCircle2,Sparkles,Target,TrendingUp,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/brain.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BarChart3_Brain_CheckCircle2_Sparkles_Target_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BarChart3,Brain,CheckCircle2,Sparkles,Target,TrendingUp,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/target.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BarChart3_Brain_CheckCircle2_Sparkles_Target_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BarChart3,Brain,CheckCircle2,Sparkles,Target,TrendingUp,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chart-column.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BarChart3_Brain_CheckCircle2_Sparkles_Target_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BarChart3,Brain,CheckCircle2,Sparkles,Target,TrendingUp,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BarChart3_Brain_CheckCircle2_Sparkles_Target_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BarChart3,Brain,CheckCircle2,Sparkles,Target,TrendingUp,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/sparkles.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BarChart3_Brain_CheckCircle2_Sparkles_Target_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BarChart3,Brain,CheckCircle2,Sparkles,Target,TrendingUp,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BarChart3_Brain_CheckCircle2_Sparkles_Target_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BarChart3,Brain,CheckCircle2,Sparkles,Target,TrendingUp,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-right.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BarChart3_Brain_CheckCircle2_Sparkles_Target_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BarChart3,Brain,CheckCircle2,Sparkles,Target,TrendingUp,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nconst Services = ()=>{\n    const services = [\n        {\n            icon: _barrel_optimize_names_ArrowRight_BarChart3_Brain_CheckCircle2_Sparkles_Target_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"],\n            title: 'AI 營銷策略諮詢',\n            description: '為您的企業制定量身定制的 AI 營銷策略，從市場分析到執行方案，全程專業指導',\n            features: [\n                '市場趨勢分析與預測',\n                '競爭對手智能分析',\n                'AI 技術選型建議',\n                '實施路線圖規劃'\n            ],\n            gradient: 'from-indigo-600 to-purple-600',\n            link: '/services#strategy'\n        },\n        {\n            icon: _barrel_optimize_names_ArrowRight_BarChart3_Brain_CheckCircle2_Sparkles_Target_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n            title: '精準營銷自動化',\n            description: '運用 AI 技術實現營銷流程自動化，精準觸達目標客戶，提升轉化效率',\n            features: [\n                '客戶行為預測模型',\n                '個性化內容生成',\n                '多渠道自動化營銷',\n                '實時效果優化'\n            ],\n            gradient: 'from-purple-600 to-pink-600',\n            link: '/services#automation'\n        },\n        {\n            icon: _barrel_optimize_names_ArrowRight_BarChart3_Brain_CheckCircle2_Sparkles_Target_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n            title: '數據分析與洞察',\n            description: '深度挖掘數據價值，提供可視化的業務洞察，助您做出明智決策',\n            features: [\n                '全渠道數據整合',\n                '預測性分析報告',\n                '實時數據儀表板',\n                'ROI 追踪與優化'\n            ],\n            gradient: 'from-cyan-600 to-blue-600',\n            link: '/services#analytics'\n        },\n        {\n            icon: _barrel_optimize_names_ArrowRight_BarChart3_Brain_CheckCircle2_Sparkles_Target_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n            title: '增長駭客服務',\n            description: '結合 AI 技術和增長駭客方法論，快速實現業務指數級增長',\n            features: [\n                'A/B 測試自動化',\n                '病毒式增長策略',\n                '用戶留存優化',\n                '增長指標監控'\n            ],\n            gradient: 'from-green-600 to-emerald-600',\n            link: '/services#growth'\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        className: \"section-spacing relative bg-gradient-to-b from-white to-gray-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 -z-10\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"absolute inset-0 bg-gradient-mesh opacity-20\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Services.tsx\",\n                    lineNumber: 76,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Services.tsx\",\n                lineNumber: 75,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"container-custom\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            y: 20\n                        },\n                        whileInView: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        viewport: {\n                            once: true\n                        },\n                        transition: {\n                            duration: 0.6\n                        },\n                        className: \"text-center mb-16\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    scale: 0.9\n                                },\n                                whileInView: {\n                                    opacity: 1,\n                                    scale: 1\n                                },\n                                viewport: {\n                                    once: true\n                                },\n                                className: \"inline-flex items-center space-x-2 badge badge-primary mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BarChart3_Brain_CheckCircle2_Sparkles_Target_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                        className: \"w-4 h-4\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Services.tsx\",\n                                        lineNumber: 94,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"專業服務\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Services.tsx\",\n                                        lineNumber: 95,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Services.tsx\",\n                                lineNumber: 88,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"heading-lg mb-6\",\n                                children: [\n                                    \"全方位 \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"gradient-text\",\n                                        children: \"AI 營銷服務\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Services.tsx\",\n                                        lineNumber: 99,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Services.tsx\",\n                                lineNumber: 98,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"body-lg text-gray-600 max-w-3xl mx-auto\",\n                                children: \"從策略制定到技術實施，我們提供端到端的 AI 營銷解決方案， 確保您的每一分投入都能獲得最大回報\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Services.tsx\",\n                                lineNumber: 102,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Services.tsx\",\n                        lineNumber: 81,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 lg:grid-cols-2 gap-8\",\n                        children: services.map((service, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    y: 20\n                                },\n                                whileInView: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                viewport: {\n                                    once: true\n                                },\n                                transition: {\n                                    duration: 0.6,\n                                    delay: index * 0.1\n                                },\n                                className: \"group\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"card-modern h-full p-8 lg:p-10 relative overflow-hidden hover:shadow-2xl transition-all duration-300\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute -top-20 -right-20 w-60 h-60 bg-gradient-to-br \".concat(service.gradient, \" rounded-full blur-3xl opacity-10 group-hover:opacity-20 transition-opacity\")\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Services.tsx\",\n                                            lineNumber: 121,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative z-10\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-start space-x-4 mb-6\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-16 h-16 bg-gradient-to-r \".concat(service.gradient, \" rounded-2xl flex items-center justify-center flex-shrink-0 group-hover:scale-110 transition-transform\"),\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(service.icon, {\n                                                                className: \"w-8 h-8 text-white\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Services.tsx\",\n                                                                lineNumber: 127,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Services.tsx\",\n                                                            lineNumber: 126,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                    className: \"text-2xl font-bold mb-2 text-gray-900\",\n                                                                    children: service.title\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Services.tsx\",\n                                                                    lineNumber: 130,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-gray-600 leading-relaxed\",\n                                                                    children: service.description\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Services.tsx\",\n                                                                    lineNumber: 131,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Services.tsx\",\n                                                            lineNumber: 129,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Services.tsx\",\n                                                    lineNumber: 125,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mb-8\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"grid grid-cols-1 sm:grid-cols-2 gap-3\",\n                                                        children: service.features.map((feature, idx)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BarChart3_Brain_CheckCircle2_Sparkles_Target_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                                        className: \"w-5 h-5 text-green-500 flex-shrink-0\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Services.tsx\",\n                                                                        lineNumber: 140,\n                                                                        columnNumber: 27\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-sm text-gray-700\",\n                                                                        children: feature\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Services.tsx\",\n                                                                        lineNumber: 141,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                ]\n                                                            }, idx, true, {\n                                                                fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Services.tsx\",\n                                                                lineNumber: 139,\n                                                                columnNumber: 25\n                                                            }, undefined))\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Services.tsx\",\n                                                        lineNumber: 137,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Services.tsx\",\n                                                    lineNumber: 136,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                    href: service.link,\n                                                    className: \"inline-flex items-center text-indigo-600 font-semibold hover:text-indigo-700 transition-colors group/link\",\n                                                    children: [\n                                                        \"了解詳情\",\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BarChart3_Brain_CheckCircle2_Sparkles_Target_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                            className: \"w-5 h-5 ml-2 group-hover/link:translate-x-1 transition-transform\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Services.tsx\",\n                                                            lineNumber: 153,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Services.tsx\",\n                                                    lineNumber: 148,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Services.tsx\",\n                                            lineNumber: 123,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute bottom-0 right-0 w-32 h-32 opacity-10\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BarChart3_Brain_CheckCircle2_Sparkles_Target_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                className: \"w-full h-full text-gray-400\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Services.tsx\",\n                                                lineNumber: 159,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Services.tsx\",\n                                            lineNumber: 158,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Services.tsx\",\n                                    lineNumber: 119,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, index, false, {\n                                fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Services.tsx\",\n                                lineNumber: 111,\n                                columnNumber: 13\n                            }, undefined))\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Services.tsx\",\n                        lineNumber: 109,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            y: 20\n                        },\n                        whileInView: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        viewport: {\n                            once: true\n                        },\n                        transition: {\n                            duration: 0.6,\n                            delay: 0.4\n                        },\n                        className: \"mt-20\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"card-modern p-8 md:p-12 bg-gradient-to-r from-indigo-50 via-purple-50 to-pink-50 border-0\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center mb-12\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"heading-md mb-4\",\n                                            children: \"我們的服務流程\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Services.tsx\",\n                                            lineNumber: 176,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"body-lg text-gray-600 max-w-2xl mx-auto\",\n                                            children: \"簡單四步，開啟您的 AI 營銷轉型之旅\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Services.tsx\",\n                                            lineNumber: 177,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Services.tsx\",\n                                    lineNumber: 175,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 md:grid-cols-4 gap-8\",\n                                    children: [\n                                        {\n                                            step: '01',\n                                            title: '需求分析',\n                                            desc: '深入了解您的業務目標和挑戰'\n                                        },\n                                        {\n                                            step: '02',\n                                            title: '方案設計',\n                                            desc: '制定個性化的 AI 營銷解決方案'\n                                        },\n                                        {\n                                            step: '03',\n                                            title: '實施部署',\n                                            desc: '專業團隊協助技術實施和整合'\n                                        },\n                                        {\n                                            step: '04',\n                                            title: '持續優化',\n                                            desc: '監控效果並持續優化提升'\n                                        }\n                                    ].map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                                            initial: {\n                                                opacity: 0,\n                                                y: 20\n                                            },\n                                            whileInView: {\n                                                opacity: 1,\n                                                y: 0\n                                            },\n                                            viewport: {\n                                                once: true\n                                            },\n                                            transition: {\n                                                duration: 0.6,\n                                                delay: 0.6 + index * 0.1\n                                            },\n                                            className: \"text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"relative mb-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-5xl font-bold gradient-text opacity-20\",\n                                                            children: item.step\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Services.tsx\",\n                                                            lineNumber: 198,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"absolute inset-0 flex items-center justify-center\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-3 h-3 bg-indigo-600 rounded-full\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Services.tsx\",\n                                                                lineNumber: 200,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Services.tsx\",\n                                                            lineNumber: 199,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Services.tsx\",\n                                                    lineNumber: 197,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"font-semibold mb-2 text-gray-900\",\n                                                    children: item.title\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Services.tsx\",\n                                                    lineNumber: 203,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-600\",\n                                                    children: item.desc\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Services.tsx\",\n                                                    lineNumber: 204,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, index, true, {\n                                            fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Services.tsx\",\n                                            lineNumber: 189,\n                                            columnNumber: 17\n                                        }, undefined))\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Services.tsx\",\n                                    lineNumber: 182,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Services.tsx\",\n                            lineNumber: 174,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Services.tsx\",\n                        lineNumber: 167,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            y: 20\n                        },\n                        whileInView: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        viewport: {\n                            once: true\n                        },\n                        transition: {\n                            duration: 0.6,\n                            delay: 0.8\n                        },\n                        className: \"text-center mt-16\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600 mb-6\",\n                                children: \"不確定哪種服務最適合您？讓我們的專家為您提供免費諮詢\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Services.tsx\",\n                                lineNumber: 219,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                href: \"/contact\",\n                                className: \"btn-primary\",\n                                children: [\n                                    \"獲取免費諮詢\",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BarChart3_Brain_CheckCircle2_Sparkles_Target_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        className: \"inline-block ml-2 w-5 h-5\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Services.tsx\",\n                                        lineNumber: 224,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Services.tsx\",\n                                lineNumber: 222,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Services.tsx\",\n                        lineNumber: 212,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Services.tsx\",\n                lineNumber: 79,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Services.tsx\",\n        lineNumber: 73,\n        columnNumber: 5\n    }, undefined);\n};\n_c = Services;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Services);\nvar _c;\n$RefreshReg$(_c, \"Services\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/Services.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check.js":
/*!******************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/circle-check.js ***!
  \******************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __iconNode: () => (/* binding */ __iconNode),\n/* harmony export */   \"default\": () => (/* binding */ CircleCheck)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst __iconNode = [\n    [\n        \"circle\",\n        {\n            cx: \"12\",\n            cy: \"12\",\n            r: \"10\",\n            key: \"1mglay\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"m9 12 2 2 4-4\",\n            key: \"dzmm74\"\n        }\n    ]\n];\nconst CircleCheck = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"circle-check\", __iconNode);\n //# sourceMappingURL=circle-check.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check.js\n"));

/***/ })

});