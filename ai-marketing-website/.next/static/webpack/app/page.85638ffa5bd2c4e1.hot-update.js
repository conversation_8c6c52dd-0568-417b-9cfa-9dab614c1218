"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./components/Hero.tsx":
/*!*****************************!*\
  !*** ./components/Hero.tsx ***!
  \*****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Brain_CheckCircle_Play_Sparkles_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Brain,CheckCircle,Play,Sparkles,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/brain.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Brain_CheckCircle_Play_Sparkles_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Brain,CheckCircle,Play,Sparkles,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Brain_CheckCircle_Play_Sparkles_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Brain,CheckCircle,Play,Sparkles,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Brain_CheckCircle_Play_Sparkles_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Brain,CheckCircle,Play,Sparkles,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/sparkles.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Brain_CheckCircle_Play_Sparkles_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Brain,CheckCircle,Play,Sparkles,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-right.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Brain_CheckCircle_Play_Sparkles_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Brain,CheckCircle,Play,Sparkles,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/play.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Brain_CheckCircle_Play_Sparkles_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Brain,CheckCircle,Play,Sparkles,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nconst Hero = ()=>{\n    _s();\n    const [isVideoModalOpen, setIsVideoModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const features = [\n        {\n            icon: _barrel_optimize_names_ArrowRight_Brain_CheckCircle_Play_Sparkles_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n            text: 'AI 驅動的智能分析'\n        },\n        {\n            icon: _barrel_optimize_names_ArrowRight_Brain_CheckCircle_Play_Sparkles_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n            text: '營銷效果提升 300%'\n        },\n        {\n            icon: _barrel_optimize_names_ArrowRight_Brain_CheckCircle_Play_Sparkles_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n            text: '已服務 500+ 企業客戶'\n        }\n    ];\n    const clients = [\n        'Google',\n        'Microsoft',\n        'Amazon',\n        'Meta',\n        'Apple'\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        className: \"relative min-h-screen flex items-center pt-20 pb-20 overflow-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 -z-10\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                        animate: {\n                            scale: [\n                                1,\n                                1.2,\n                                1\n                            ],\n                            rotate: [\n                                0,\n                                180,\n                                360\n                            ]\n                        },\n                        transition: {\n                            duration: 20,\n                            repeat: Infinity,\n                            repeatType: \"reverse\"\n                        },\n                        className: \"absolute top-1/4 left-1/4 w-96 h-96 bg-gradient-to-r from-indigo-400 to-purple-400 rounded-full blur-3xl opacity-20\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Hero.tsx\",\n                        lineNumber: 31,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                        animate: {\n                            scale: [\n                                1.2,\n                                1,\n                                1.2\n                            ],\n                            rotate: [\n                                360,\n                                180,\n                                0\n                            ]\n                        },\n                        transition: {\n                            duration: 25,\n                            repeat: Infinity,\n                            repeatType: \"reverse\"\n                        },\n                        className: \"absolute bottom-1/4 right-1/4 w-96 h-96 bg-gradient-to-r from-purple-400 to-pink-400 rounded-full blur-3xl opacity-20\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Hero.tsx\",\n                        lineNumber: 43,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Hero.tsx\",\n                lineNumber: 30,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"container-custom\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid lg:grid-cols-2 gap-12 items-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                            initial: {\n                                opacity: 0,\n                                x: -50\n                            },\n                            animate: {\n                                opacity: 1,\n                                x: 0\n                            },\n                            transition: {\n                                duration: 0.8\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                                    initial: {\n                                        opacity: 0,\n                                        y: 20\n                                    },\n                                    animate: {\n                                        opacity: 1,\n                                        y: 0\n                                    },\n                                    transition: {\n                                        duration: 0.6\n                                    },\n                                    className: \"inline-flex items-center space-x-2 badge badge-primary mb-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Brain_CheckCircle_Play_Sparkles_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                            className: \"w-4 h-4\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Hero.tsx\",\n                                            lineNumber: 72,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"香港領先的 AI 營銷專家\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Hero.tsx\",\n                                            lineNumber: 73,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Hero.tsx\",\n                                    lineNumber: 66,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"heading-xl mb-6\",\n                                    children: [\n                                        \"用 \",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"gradient-text\",\n                                            children: \"人工智能\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Hero.tsx\",\n                                            lineNumber: 78,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Hero.tsx\",\n                                            lineNumber: 79,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        \"重新定義營銷\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Hero.tsx\",\n                                    lineNumber: 77,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"body-lg mb-8 text-gray-600\",\n                                    children: \"透過尖端的 AI 技術和數據分析，我們幫助企業實現營銷自動化、 精準定位目標客戶，並將營銷投資回報率提升至新高度。\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Hero.tsx\",\n                                    lineNumber: 84,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-3 mb-8\",\n                                    children: features.map((feature, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                                            initial: {\n                                                opacity: 0,\n                                                x: -20\n                                            },\n                                            animate: {\n                                                opacity: 1,\n                                                x: 0\n                                            },\n                                            transition: {\n                                                duration: 0.6,\n                                                delay: 0.2 + index * 0.1\n                                            },\n                                            className: \"flex items-center space-x-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-2 rounded-lg bg-indigo-100\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(feature.icon, {\n                                                        className: \"w-5 h-5 text-indigo-600\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Hero.tsx\",\n                                                        lineNumber: 100,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Hero.tsx\",\n                                                    lineNumber: 99,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-gray-700 font-medium\",\n                                                    children: feature.text\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Hero.tsx\",\n                                                    lineNumber: 102,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, index, true, {\n                                            fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Hero.tsx\",\n                                            lineNumber: 92,\n                                            columnNumber: 17\n                                        }, undefined))\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Hero.tsx\",\n                                    lineNumber: 90,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                                    initial: {\n                                        opacity: 0,\n                                        y: 20\n                                    },\n                                    animate: {\n                                        opacity: 1,\n                                        y: 0\n                                    },\n                                    transition: {\n                                        duration: 0.8,\n                                        delay: 0.5\n                                    },\n                                    className: \"flex flex-wrap gap-4 mb-12\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                            href: \"/contact\",\n                                            className: \"btn-primary\",\n                                            children: [\n                                                \"開始免費諮詢\",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Brain_CheckCircle_Play_Sparkles_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                    className: \"inline-block ml-2 w-5 h-5\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Hero.tsx\",\n                                                    lineNumber: 116,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Hero.tsx\",\n                                            lineNumber: 114,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setIsVideoModalOpen(true),\n                                            className: \"btn-secondary flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Brain_CheckCircle_Play_Sparkles_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                    className: \"w-5 h-5 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Hero.tsx\",\n                                                    lineNumber: 122,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                \"觀看演示視頻\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Hero.tsx\",\n                                            lineNumber: 118,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Hero.tsx\",\n                                    lineNumber: 108,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                                    initial: {\n                                        opacity: 0\n                                    },\n                                    animate: {\n                                        opacity: 1\n                                    },\n                                    transition: {\n                                        duration: 1,\n                                        delay: 0.8\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-500 mb-4\",\n                                            children: \"受到全球領先企業的信任\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Hero.tsx\",\n                                            lineNumber: 133,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-wrap items-center gap-8\",\n                                            children: clients.map((client, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-gray-400 font-semibold text-lg opacity-60 hover:opacity-100 transition-opacity\",\n                                                    children: client\n                                                }, index, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Hero.tsx\",\n                                                    lineNumber: 136,\n                                                    columnNumber: 19\n                                                }, undefined))\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Hero.tsx\",\n                                            lineNumber: 134,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Hero.tsx\",\n                                    lineNumber: 128,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Hero.tsx\",\n                            lineNumber: 60,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                            initial: {\n                                opacity: 0,\n                                x: 50\n                            },\n                            animate: {\n                                opacity: 1,\n                                x: 0\n                            },\n                            transition: {\n                                duration: 0.8\n                            },\n                            className: \"relative lg:h-[600px]\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative w-full h-full\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                                        whileHover: {\n                                            scale: 1.02\n                                        },\n                                        className: \"absolute top-0 right-0 w-full max-w-md\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"card-modern p-8 relative overflow-hidden\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute top-0 right-0 w-32 h-32 bg-gradient-to-br from-indigo-500 to-purple-500 rounded-full blur-3xl opacity-20\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Hero.tsx\",\n                                                    lineNumber: 162,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"relative z-10\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-16 h-16 bg-gradient-to-r from-indigo-600 to-purple-600 rounded-2xl flex items-center justify-center mb-6\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Brain_CheckCircle_Play_Sparkles_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                                className: \"w-8 h-8 text-white\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Hero.tsx\",\n                                                                lineNumber: 165,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Hero.tsx\",\n                                                            lineNumber: 164,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"text-2xl font-bold mb-4\",\n                                                            children: \"AI 營銷大腦\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Hero.tsx\",\n                                                            lineNumber: 167,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-600 mb-6\",\n                                                            children: \"整合機器學習、自然語言處理和預測分析，為您的營銷策略提供智能決策支持\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Hero.tsx\",\n                                                            lineNumber: 168,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-3\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center space-x-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Brain_CheckCircle_Play_Sparkles_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                            className: \"w-5 h-5 text-green-500\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Hero.tsx\",\n                                                                            lineNumber: 173,\n                                                                            columnNumber: 25\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-sm\",\n                                                                            children: \"實時數據分析\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Hero.tsx\",\n                                                                            lineNumber: 174,\n                                                                            columnNumber: 25\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Hero.tsx\",\n                                                                    lineNumber: 172,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center space-x-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Brain_CheckCircle_Play_Sparkles_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                            className: \"w-5 h-5 text-green-500\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Hero.tsx\",\n                                                                            lineNumber: 177,\n                                                                            columnNumber: 25\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-sm\",\n                                                                            children: \"自動化營銷流程\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Hero.tsx\",\n                                                                            lineNumber: 178,\n                                                                            columnNumber: 25\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Hero.tsx\",\n                                                                    lineNumber: 176,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center space-x-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Brain_CheckCircle_Play_Sparkles_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                            className: \"w-5 h-5 text-green-500\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Hero.tsx\",\n                                                                            lineNumber: 181,\n                                                                            columnNumber: 25\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-sm\",\n                                                                            children: \"個性化客戶體驗\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Hero.tsx\",\n                                                                            lineNumber: 182,\n                                                                            columnNumber: 25\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Hero.tsx\",\n                                                                    lineNumber: 180,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Hero.tsx\",\n                                                            lineNumber: 171,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Hero.tsx\",\n                                                    lineNumber: 163,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Hero.tsx\",\n                                            lineNumber: 161,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Hero.tsx\",\n                                        lineNumber: 157,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                                        animate: {\n                                            y: [\n                                                0,\n                                                -20,\n                                                0\n                                            ]\n                                        },\n                                        transition: {\n                                            duration: 4,\n                                            repeat: Infinity\n                                        },\n                                        className: \"absolute top-20 left-0 card-modern p-4 shadow-xl\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-12 h-12 bg-green-100 rounded-xl flex items-center justify-center\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Brain_CheckCircle_Play_Sparkles_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                        className: \"w-6 h-6 text-green-600\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Hero.tsx\",\n                                                        lineNumber: 197,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Hero.tsx\",\n                                                    lineNumber: 196,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-500\",\n                                                            children: \"轉化率提升\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Hero.tsx\",\n                                                            lineNumber: 200,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-xl font-bold text-green-600\",\n                                                            children: \"+287%\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Hero.tsx\",\n                                                            lineNumber: 201,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Hero.tsx\",\n                                                    lineNumber: 199,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Hero.tsx\",\n                                            lineNumber: 195,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Hero.tsx\",\n                                        lineNumber: 190,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                                        animate: {\n                                            y: [\n                                                0,\n                                                20,\n                                                0\n                                            ]\n                                        },\n                                        transition: {\n                                            duration: 5,\n                                            repeat: Infinity\n                                        },\n                                        className: \"absolute bottom-20 left-10 card-modern p-4 shadow-xl\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-12 h-12 bg-purple-100 rounded-xl flex items-center justify-center\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Brain_CheckCircle_Play_Sparkles_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                        className: \"w-6 h-6 text-purple-600\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Hero.tsx\",\n                                                        lineNumber: 213,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Hero.tsx\",\n                                                    lineNumber: 212,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-500\",\n                                                            children: \"活躍用戶\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Hero.tsx\",\n                                                            lineNumber: 216,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-xl font-bold text-purple-600\",\n                                                            children: \"2.5M+\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Hero.tsx\",\n                                                            lineNumber: 217,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Hero.tsx\",\n                                                    lineNumber: 215,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Hero.tsx\",\n                                            lineNumber: 211,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Hero.tsx\",\n                                        lineNumber: 206,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                                        animate: {\n                                            rotate: 360\n                                        },\n                                        transition: {\n                                            duration: 20,\n                                            repeat: Infinity,\n                                            ease: \"linear\"\n                                        },\n                                        className: \"absolute -bottom-10 -right-10 w-40 h-40 border-4 border-indigo-200 rounded-full\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Hero.tsx\",\n                                        lineNumber: 223,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                                        animate: {\n                                            rotate: -360\n                                        },\n                                        transition: {\n                                            duration: 15,\n                                            repeat: Infinity,\n                                            ease: \"linear\"\n                                        },\n                                        className: \"absolute -top-5 -left-5 w-32 h-32 border-4 border-purple-200 rounded-full\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Hero.tsx\",\n                                        lineNumber: 228,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Hero.tsx\",\n                                lineNumber: 155,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Hero.tsx\",\n                            lineNumber: 148,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Hero.tsx\",\n                    lineNumber: 58,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Hero.tsx\",\n                lineNumber: 57,\n                columnNumber: 7\n            }, undefined),\n            isVideoModalOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                initial: {\n                    opacity: 0\n                },\n                animate: {\n                    opacity: 1\n                },\n                exit: {\n                    opacity: 0\n                },\n                className: \"fixed inset-0 z-50 flex items-center justify-center bg-black/80 p-4\",\n                onClick: ()=>setIsVideoModalOpen(false),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                    initial: {\n                        scale: 0.9\n                    },\n                    animate: {\n                        scale: 1\n                    },\n                    exit: {\n                        scale: 0.9\n                    },\n                    className: \"relative w-full max-w-4xl aspect-video bg-gray-900 rounded-2xl overflow-hidden\",\n                    onClick: (e)=>e.stopPropagation(),\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>setIsVideoModalOpen(false),\n                            className: \"absolute top-4 right-4 p-2 bg-white/10 hover:bg-white/20 rounded-full text-white transition-colors\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(X, {\n                                size: 24\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Hero.tsx\",\n                                lineNumber: 258,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Hero.tsx\",\n                            lineNumber: 254,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-full h-full flex items-center justify-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-white text-xl\",\n                                children: \"視頻演示內容\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Hero.tsx\",\n                                lineNumber: 261,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Hero.tsx\",\n                            lineNumber: 260,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Hero.tsx\",\n                    lineNumber: 247,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Hero.tsx\",\n                lineNumber: 240,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Desktop/Claude/ai-marketing-website/components/Hero.tsx\",\n        lineNumber: 28,\n        columnNumber: 5\n    }, undefined);\n};\n_s(Hero, \"tZ2VFBxQsSrs68t58nYJQ0oEBGg=\");\n_c = Hero;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Hero);\nvar _c;\n$RefreshReg$(_c, \"Hero\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL2NvbXBvbmVudHMvSGVyby50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUVzQztBQUNWO0FBU1A7QUFDVztBQUVoQyxNQUFNVSxPQUFPOztJQUNYLE1BQU0sQ0FBQ0Msa0JBQWtCQyxvQkFBb0IsR0FBR0gsK0NBQVFBLENBQUM7SUFFekQsTUFBTUksV0FBVztRQUNmO1lBQUVDLE1BQU1SLHVJQUFLQTtZQUFFUyxNQUFNO1FBQWE7UUFDbEM7WUFBRUQsTUFBTVYsdUlBQVVBO1lBQUVXLE1BQU07UUFBYztRQUN4QztZQUFFRCxNQUFNVCx1SUFBS0E7WUFBRVUsTUFBTTtRQUFnQjtLQUN0QztJQUVELE1BQU1DLFVBQVU7UUFBQztRQUFVO1FBQWE7UUFBVTtRQUFRO0tBQVE7SUFFbEUscUJBQ0UsOERBQUNDO1FBQVFDLFdBQVU7OzBCQUVqQiw4REFBQ0M7Z0JBQUlELFdBQVU7O2tDQUNiLDhEQUFDbEIsaURBQU1BLENBQUNtQixHQUFHO3dCQUNUQyxTQUFTOzRCQUNQQyxPQUFPO2dDQUFDO2dDQUFHO2dDQUFLOzZCQUFFOzRCQUNsQkMsUUFBUTtnQ0FBQztnQ0FBRztnQ0FBSzs2QkFBSTt3QkFDdkI7d0JBQ0FDLFlBQVk7NEJBQ1ZDLFVBQVU7NEJBQ1ZDLFFBQVFDOzRCQUNSQyxZQUFZO3dCQUNkO3dCQUNBVCxXQUFVOzs7Ozs7a0NBRVosOERBQUNsQixpREFBTUEsQ0FBQ21CLEdBQUc7d0JBQ1RDLFNBQVM7NEJBQ1BDLE9BQU87Z0NBQUM7Z0NBQUs7Z0NBQUc7NkJBQUk7NEJBQ3BCQyxRQUFRO2dDQUFDO2dDQUFLO2dDQUFLOzZCQUFFO3dCQUN2Qjt3QkFDQUMsWUFBWTs0QkFDVkMsVUFBVTs0QkFDVkMsUUFBUUM7NEJBQ1JDLFlBQVk7d0JBQ2Q7d0JBQ0FULFdBQVU7Ozs7Ozs7Ozs7OzswQkFJZCw4REFBQ0M7Z0JBQUlELFdBQVU7MEJBQ2IsNEVBQUNDO29CQUFJRCxXQUFVOztzQ0FFYiw4REFBQ2xCLGlEQUFNQSxDQUFDbUIsR0FBRzs0QkFDVFMsU0FBUztnQ0FBRUMsU0FBUztnQ0FBR0MsR0FBRyxDQUFDOzRCQUFHOzRCQUM5QlYsU0FBUztnQ0FBRVMsU0FBUztnQ0FBR0MsR0FBRzs0QkFBRTs0QkFDNUJQLFlBQVk7Z0NBQUVDLFVBQVU7NEJBQUk7OzhDQUc1Qiw4REFBQ3hCLGlEQUFNQSxDQUFDbUIsR0FBRztvQ0FDVFMsU0FBUzt3Q0FBRUMsU0FBUzt3Q0FBR0UsR0FBRztvQ0FBRztvQ0FDN0JYLFNBQVM7d0NBQUVTLFNBQVM7d0NBQUdFLEdBQUc7b0NBQUU7b0NBQzVCUixZQUFZO3dDQUFFQyxVQUFVO29DQUFJO29DQUM1Qk4sV0FBVTs7c0RBRVYsOERBQUNmLHVJQUFRQTs0Q0FBQ2UsV0FBVTs7Ozs7O3NEQUNwQiw4REFBQ2M7c0RBQUs7Ozs7Ozs7Ozs7Ozs4Q0FJUiw4REFBQ0M7b0NBQUdmLFdBQVU7O3dDQUFrQjtzREFDNUIsOERBQUNjOzRDQUFLZCxXQUFVO3NEQUFnQjs7Ozs7O3NEQUNsQyw4REFBQ2dCOzs7Ozt3Q0FBSzs7Ozs7Ozs4Q0FLUiw4REFBQ0M7b0NBQUVqQixXQUFVOzhDQUE2Qjs7Ozs7OzhDQU0xQyw4REFBQ0M7b0NBQUlELFdBQVU7OENBQ1pMLFNBQVN1QixHQUFHLENBQUMsQ0FBQ0MsU0FBU0Msc0JBQ3RCLDhEQUFDdEMsaURBQU1BLENBQUNtQixHQUFHOzRDQUVUUyxTQUFTO2dEQUFFQyxTQUFTO2dEQUFHQyxHQUFHLENBQUM7NENBQUc7NENBQzlCVixTQUFTO2dEQUFFUyxTQUFTO2dEQUFHQyxHQUFHOzRDQUFFOzRDQUM1QlAsWUFBWTtnREFBRUMsVUFBVTtnREFBS2UsT0FBTyxNQUFNRCxRQUFROzRDQUFJOzRDQUN0RHBCLFdBQVU7OzhEQUVWLDhEQUFDQztvREFBSUQsV0FBVTs4REFDYiw0RUFBQ21CLFFBQVF2QixJQUFJO3dEQUFDSSxXQUFVOzs7Ozs7Ozs7Ozs4REFFMUIsOERBQUNjO29EQUFLZCxXQUFVOzhEQUE2Qm1CLFFBQVF0QixJQUFJOzs7Ozs7OzJDQVRwRHVCOzs7Ozs7Ozs7OzhDQWVYLDhEQUFDdEMsaURBQU1BLENBQUNtQixHQUFHO29DQUNUUyxTQUFTO3dDQUFFQyxTQUFTO3dDQUFHRSxHQUFHO29DQUFHO29DQUM3QlgsU0FBUzt3Q0FBRVMsU0FBUzt3Q0FBR0UsR0FBRztvQ0FBRTtvQ0FDNUJSLFlBQVk7d0NBQUVDLFVBQVU7d0NBQUtlLE9BQU87b0NBQUk7b0NBQ3hDckIsV0FBVTs7c0RBRVYsOERBQUNqQixrREFBSUE7NENBQUN1QyxNQUFLOzRDQUFXdEIsV0FBVTs7Z0RBQWM7OERBRTVDLDhEQUFDaEIsdUlBQVVBO29EQUFDZ0IsV0FBVTs7Ozs7Ozs7Ozs7O3NEQUV4Qiw4REFBQ3VCOzRDQUNDQyxTQUFTLElBQU05QixvQkFBb0I7NENBQ25DTSxXQUFVOzs4REFFViw4REFBQ1gsdUlBQUlBO29EQUFDVyxXQUFVOzs7Ozs7Z0RBQWlCOzs7Ozs7Ozs7Ozs7OzhDQU1yQyw4REFBQ2xCLGlEQUFNQSxDQUFDbUIsR0FBRztvQ0FDVFMsU0FBUzt3Q0FBRUMsU0FBUztvQ0FBRTtvQ0FDdEJULFNBQVM7d0NBQUVTLFNBQVM7b0NBQUU7b0NBQ3RCTixZQUFZO3dDQUFFQyxVQUFVO3dDQUFHZSxPQUFPO29DQUFJOztzREFFdEMsOERBQUNKOzRDQUFFakIsV0FBVTtzREFBNkI7Ozs7OztzREFDMUMsOERBQUNDOzRDQUFJRCxXQUFVO3NEQUNaRixRQUFRb0IsR0FBRyxDQUFDLENBQUNPLFFBQVFMLHNCQUNwQiw4REFBQ25CO29EQUVDRCxXQUFVOzhEQUVUeUI7bURBSElMOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O3NDQVdmLDhEQUFDdEMsaURBQU1BLENBQUNtQixHQUFHOzRCQUNUUyxTQUFTO2dDQUFFQyxTQUFTO2dDQUFHQyxHQUFHOzRCQUFHOzRCQUM3QlYsU0FBUztnQ0FBRVMsU0FBUztnQ0FBR0MsR0FBRzs0QkFBRTs0QkFDNUJQLFlBQVk7Z0NBQUVDLFVBQVU7NEJBQUk7NEJBQzVCTixXQUFVO3NDQUdWLDRFQUFDQztnQ0FBSUQsV0FBVTs7a0RBRWIsOERBQUNsQixpREFBTUEsQ0FBQ21CLEdBQUc7d0NBQ1R5QixZQUFZOzRDQUFFdkIsT0FBTzt3Q0FBSzt3Q0FDMUJILFdBQVU7a0RBRVYsNEVBQUNDOzRDQUFJRCxXQUFVOzs4REFDYiw4REFBQ0M7b0RBQUlELFdBQVU7Ozs7Ozs4REFDZiw4REFBQ0M7b0RBQUlELFdBQVU7O3NFQUNiLDhEQUFDQzs0REFBSUQsV0FBVTtzRUFDYiw0RUFBQ1osdUlBQUtBO2dFQUFDWSxXQUFVOzs7Ozs7Ozs7OztzRUFFbkIsOERBQUMyQjs0REFBRzNCLFdBQVU7c0VBQTBCOzs7Ozs7c0VBQ3hDLDhEQUFDaUI7NERBQUVqQixXQUFVO3NFQUFxQjs7Ozs7O3NFQUdsQyw4REFBQ0M7NERBQUlELFdBQVU7OzhFQUNiLDhEQUFDQztvRUFBSUQsV0FBVTs7c0ZBQ2IsOERBQUNWLHdJQUFXQTs0RUFBQ1UsV0FBVTs7Ozs7O3NGQUN2Qiw4REFBQ2M7NEVBQUtkLFdBQVU7c0ZBQVU7Ozs7Ozs7Ozs7Ozs4RUFFNUIsOERBQUNDO29FQUFJRCxXQUFVOztzRkFDYiw4REFBQ1Ysd0lBQVdBOzRFQUFDVSxXQUFVOzs7Ozs7c0ZBQ3ZCLDhEQUFDYzs0RUFBS2QsV0FBVTtzRkFBVTs7Ozs7Ozs7Ozs7OzhFQUU1Qiw4REFBQ0M7b0VBQUlELFdBQVU7O3NGQUNiLDhEQUFDVix3SUFBV0E7NEVBQUNVLFdBQVU7Ozs7OztzRkFDdkIsOERBQUNjOzRFQUFLZCxXQUFVO3NGQUFVOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztrREFRcEMsOERBQUNsQixpREFBTUEsQ0FBQ21CLEdBQUc7d0NBQ1RDLFNBQVM7NENBQUVXLEdBQUc7Z0RBQUM7Z0RBQUcsQ0FBQztnREFBSTs2Q0FBRTt3Q0FBQzt3Q0FDMUJSLFlBQVk7NENBQUVDLFVBQVU7NENBQUdDLFFBQVFDO3dDQUFTO3dDQUM1Q1IsV0FBVTtrREFFViw0RUFBQ0M7NENBQUlELFdBQVU7OzhEQUNiLDhEQUFDQztvREFBSUQsV0FBVTs4REFDYiw0RUFBQ2QsdUlBQVVBO3dEQUFDYyxXQUFVOzs7Ozs7Ozs7Ozs4REFFeEIsOERBQUNDOztzRUFDQyw4REFBQ2dCOzREQUFFakIsV0FBVTtzRUFBd0I7Ozs7OztzRUFDckMsOERBQUNpQjs0REFBRWpCLFdBQVU7c0VBQW1DOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztrREFLdEQsOERBQUNsQixpREFBTUEsQ0FBQ21CLEdBQUc7d0NBQ1RDLFNBQVM7NENBQUVXLEdBQUc7Z0RBQUM7Z0RBQUc7Z0RBQUk7NkNBQUU7d0NBQUM7d0NBQ3pCUixZQUFZOzRDQUFFQyxVQUFVOzRDQUFHQyxRQUFRQzt3Q0FBUzt3Q0FDNUNSLFdBQVU7a0RBRVYsNEVBQUNDOzRDQUFJRCxXQUFVOzs4REFDYiw4REFBQ0M7b0RBQUlELFdBQVU7OERBQ2IsNEVBQUNiLHVJQUFLQTt3REFBQ2EsV0FBVTs7Ozs7Ozs7Ozs7OERBRW5CLDhEQUFDQzs7c0VBQ0MsOERBQUNnQjs0REFBRWpCLFdBQVU7c0VBQXdCOzs7Ozs7c0VBQ3JDLDhEQUFDaUI7NERBQUVqQixXQUFVO3NFQUFvQzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7a0RBTXZELDhEQUFDbEIsaURBQU1BLENBQUNtQixHQUFHO3dDQUNUQyxTQUFTOzRDQUFFRSxRQUFRO3dDQUFJO3dDQUN2QkMsWUFBWTs0Q0FBRUMsVUFBVTs0Q0FBSUMsUUFBUUM7NENBQVVvQixNQUFNO3dDQUFTO3dDQUM3RDVCLFdBQVU7Ozs7OztrREFFWiw4REFBQ2xCLGlEQUFNQSxDQUFDbUIsR0FBRzt3Q0FDVEMsU0FBUzs0Q0FBRUUsUUFBUSxDQUFDO3dDQUFJO3dDQUN4QkMsWUFBWTs0Q0FBRUMsVUFBVTs0Q0FBSUMsUUFBUUM7NENBQVVvQixNQUFNO3dDQUFTO3dDQUM3RDVCLFdBQVU7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7WUFRbkJQLGtDQUNDLDhEQUFDWCxpREFBTUEsQ0FBQ21CLEdBQUc7Z0JBQ1RTLFNBQVM7b0JBQUVDLFNBQVM7Z0JBQUU7Z0JBQ3RCVCxTQUFTO29CQUFFUyxTQUFTO2dCQUFFO2dCQUN0QmtCLE1BQU07b0JBQUVsQixTQUFTO2dCQUFFO2dCQUNuQlgsV0FBVTtnQkFDVndCLFNBQVMsSUFBTTlCLG9CQUFvQjswQkFFbkMsNEVBQUNaLGlEQUFNQSxDQUFDbUIsR0FBRztvQkFDVFMsU0FBUzt3QkFBRVAsT0FBTztvQkFBSTtvQkFDdEJELFNBQVM7d0JBQUVDLE9BQU87b0JBQUU7b0JBQ3BCMEIsTUFBTTt3QkFBRTFCLE9BQU87b0JBQUk7b0JBQ25CSCxXQUFVO29CQUNWd0IsU0FBUyxDQUFDTSxJQUFNQSxFQUFFQyxlQUFlOztzQ0FFakMsOERBQUNSOzRCQUNDQyxTQUFTLElBQU05QixvQkFBb0I7NEJBQ25DTSxXQUFVO3NDQUVWLDRFQUFDZ0M7Z0NBQUVDLE1BQU07Ozs7Ozs7Ozs7O3NDQUVYLDhEQUFDaEM7NEJBQUlELFdBQVU7c0NBQ2IsNEVBQUNpQjtnQ0FBRWpCLFdBQVU7MENBQXFCOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBT2hEO0dBNVBNUjtLQUFBQTtBQThQTixpRUFBZUEsSUFBSUEsRUFBQSIsInNvdXJjZXMiOlsiL1VzZXJzL2t3L0Rlc2t0b3AvQ2xhdWRlL2FpLW1hcmtldGluZy13ZWJzaXRlL2NvbXBvbmVudHMvSGVyby50c3giXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnXG5cbmltcG9ydCB7IG1vdGlvbiB9IGZyb20gJ2ZyYW1lci1tb3Rpb24nXG5pbXBvcnQgTGluayBmcm9tICduZXh0L2xpbmsnXG5pbXBvcnQgeyBcbiAgQXJyb3dSaWdodCwgXG4gIFNwYXJrbGVzLCBcbiAgVHJlbmRpbmdVcCwgXG4gIFVzZXJzLCBcbiAgQnJhaW4sXG4gIFBsYXksXG4gIENoZWNrQ2lyY2xlXG59IGZyb20gJ2x1Y2lkZS1yZWFjdCdcbmltcG9ydCB7IHVzZVN0YXRlIH0gZnJvbSAncmVhY3QnXG5cbmNvbnN0IEhlcm8gPSAoKSA9PiB7XG4gIGNvbnN0IFtpc1ZpZGVvTW9kYWxPcGVuLCBzZXRJc1ZpZGVvTW9kYWxPcGVuXSA9IHVzZVN0YXRlKGZhbHNlKVxuXG4gIGNvbnN0IGZlYXR1cmVzID0gW1xuICAgIHsgaWNvbjogQnJhaW4sIHRleHQ6ICdBSSDpqYXli5XnmoTmmbrog73liIbmnpAnIH0sXG4gICAgeyBpY29uOiBUcmVuZGluZ1VwLCB0ZXh0OiAn54ef6Yq35pWI5p6c5o+Q5Y2HIDMwMCUnIH0sXG4gICAgeyBpY29uOiBVc2VycywgdGV4dDogJ+W3suacjeWLmSA1MDArIOS8gealreWuouaIticgfSxcbiAgXVxuXG4gIGNvbnN0IGNsaWVudHMgPSBbJ0dvb2dsZScsICdNaWNyb3NvZnQnLCAnQW1hem9uJywgJ01ldGEnLCAnQXBwbGUnXVxuXG4gIHJldHVybiAoXG4gICAgPHNlY3Rpb24gY2xhc3NOYW1lPVwicmVsYXRpdmUgbWluLWgtc2NyZWVuIGZsZXggaXRlbXMtY2VudGVyIHB0LTIwIHBiLTIwIG92ZXJmbG93LWhpZGRlblwiPlxuICAgICAgey8qIEFuaW1hdGVkIEJhY2tncm91bmQgRWxlbWVudHMgKi99XG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cImFic29sdXRlIGluc2V0LTAgLXotMTBcIj5cbiAgICAgICAgPG1vdGlvbi5kaXZcbiAgICAgICAgICBhbmltYXRlPXt7XG4gICAgICAgICAgICBzY2FsZTogWzEsIDEuMiwgMV0sXG4gICAgICAgICAgICByb3RhdGU6IFswLCAxODAsIDM2MF0sXG4gICAgICAgICAgfX1cbiAgICAgICAgICB0cmFuc2l0aW9uPXt7XG4gICAgICAgICAgICBkdXJhdGlvbjogMjAsXG4gICAgICAgICAgICByZXBlYXQ6IEluZmluaXR5LFxuICAgICAgICAgICAgcmVwZWF0VHlwZTogXCJyZXZlcnNlXCIsXG4gICAgICAgICAgfX1cbiAgICAgICAgICBjbGFzc05hbWU9XCJhYnNvbHV0ZSB0b3AtMS80IGxlZnQtMS80IHctOTYgaC05NiBiZy1ncmFkaWVudC10by1yIGZyb20taW5kaWdvLTQwMCB0by1wdXJwbGUtNDAwIHJvdW5kZWQtZnVsbCBibHVyLTN4bCBvcGFjaXR5LTIwXCJcbiAgICAgICAgLz5cbiAgICAgICAgPG1vdGlvbi5kaXZcbiAgICAgICAgICBhbmltYXRlPXt7XG4gICAgICAgICAgICBzY2FsZTogWzEuMiwgMSwgMS4yXSxcbiAgICAgICAgICAgIHJvdGF0ZTogWzM2MCwgMTgwLCAwXSxcbiAgICAgICAgICB9fVxuICAgICAgICAgIHRyYW5zaXRpb249e3tcbiAgICAgICAgICAgIGR1cmF0aW9uOiAyNSxcbiAgICAgICAgICAgIHJlcGVhdDogSW5maW5pdHksXG4gICAgICAgICAgICByZXBlYXRUeXBlOiBcInJldmVyc2VcIixcbiAgICAgICAgICB9fVxuICAgICAgICAgIGNsYXNzTmFtZT1cImFic29sdXRlIGJvdHRvbS0xLzQgcmlnaHQtMS80IHctOTYgaC05NiBiZy1ncmFkaWVudC10by1yIGZyb20tcHVycGxlLTQwMCB0by1waW5rLTQwMCByb3VuZGVkLWZ1bGwgYmx1ci0zeGwgb3BhY2l0eS0yMFwiXG4gICAgICAgIC8+XG4gICAgICA8L2Rpdj5cblxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJjb250YWluZXItY3VzdG9tXCI+XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZ3JpZCBsZzpncmlkLWNvbHMtMiBnYXAtMTIgaXRlbXMtY2VudGVyXCI+XG4gICAgICAgICAgey8qIExlZnQgQ29udGVudCAqL31cbiAgICAgICAgICA8bW90aW9uLmRpdlxuICAgICAgICAgICAgaW5pdGlhbD17eyBvcGFjaXR5OiAwLCB4OiAtNTAgfX1cbiAgICAgICAgICAgIGFuaW1hdGU9e3sgb3BhY2l0eTogMSwgeDogMCB9fVxuICAgICAgICAgICAgdHJhbnNpdGlvbj17eyBkdXJhdGlvbjogMC44IH19XG4gICAgICAgICAgPlxuICAgICAgICAgICAgey8qIEJhZGdlICovfVxuICAgICAgICAgICAgPG1vdGlvbi5kaXZcbiAgICAgICAgICAgICAgaW5pdGlhbD17eyBvcGFjaXR5OiAwLCB5OiAyMCB9fVxuICAgICAgICAgICAgICBhbmltYXRlPXt7IG9wYWNpdHk6IDEsIHk6IDAgfX1cbiAgICAgICAgICAgICAgdHJhbnNpdGlvbj17eyBkdXJhdGlvbjogMC42IH19XG4gICAgICAgICAgICAgIGNsYXNzTmFtZT1cImlubGluZS1mbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTIgYmFkZ2UgYmFkZ2UtcHJpbWFyeSBtYi02XCJcbiAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgPFNwYXJrbGVzIGNsYXNzTmFtZT1cInctNCBoLTRcIiAvPlxuICAgICAgICAgICAgICA8c3Bhbj7pppnmuK/poJjlhYjnmoQgQUkg54ef6Yq35bCI5a62PC9zcGFuPlxuICAgICAgICAgICAgPC9tb3Rpb24uZGl2PlxuXG4gICAgICAgICAgICB7LyogTWFpbiBIZWFkaW5nICovfVxuICAgICAgICAgICAgPGgxIGNsYXNzTmFtZT1cImhlYWRpbmcteGwgbWItNlwiPlxuICAgICAgICAgICAgICDnlKggPHNwYW4gY2xhc3NOYW1lPVwiZ3JhZGllbnQtdGV4dFwiPuS6uuW3peaZuuiDvTwvc3Bhbj5cbiAgICAgICAgICAgICAgPGJyIC8+XG4gICAgICAgICAgICAgIOmHjeaWsOWumue+qeeHn+mKt1xuICAgICAgICAgICAgPC9oMT5cblxuICAgICAgICAgICAgey8qIFN1YnRpdGxlICovfVxuICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwiYm9keS1sZyBtYi04IHRleHQtZ3JheS02MDBcIj5cbiAgICAgICAgICAgICAg6YCP6YGO5bCW56uv55qEIEFJIOaKgOihk+WSjOaVuOaTmuWIhuaekO+8jOaIkeWAkeW5q+WKqeS8gealreWvpuePvueHn+mKt+iHquWLleWMluOAgVxuICAgICAgICAgICAgICDnsr7mupblrprkvY3nm67mqJnlrqLmiLbvvIzkuKblsIfnh5/pirfmipXos4flm57loLHnjofmj5DljYfoh7PmlrDpq5jluqbjgIJcbiAgICAgICAgICAgIDwvcD5cblxuICAgICAgICAgICAgey8qIEZlYXR1cmVzIExpc3QgKi99XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktMyBtYi04XCI+XG4gICAgICAgICAgICAgIHtmZWF0dXJlcy5tYXAoKGZlYXR1cmUsIGluZGV4KSA9PiAoXG4gICAgICAgICAgICAgICAgPG1vdGlvbi5kaXZcbiAgICAgICAgICAgICAgICAgIGtleT17aW5kZXh9XG4gICAgICAgICAgICAgICAgICBpbml0aWFsPXt7IG9wYWNpdHk6IDAsIHg6IC0yMCB9fVxuICAgICAgICAgICAgICAgICAgYW5pbWF0ZT17eyBvcGFjaXR5OiAxLCB4OiAwIH19XG4gICAgICAgICAgICAgICAgICB0cmFuc2l0aW9uPXt7IGR1cmF0aW9uOiAwLjYsIGRlbGF5OiAwLjIgKyBpbmRleCAqIDAuMSB9fVxuICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC0zXCJcbiAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInAtMiByb3VuZGVkLWxnIGJnLWluZGlnby0xMDBcIj5cbiAgICAgICAgICAgICAgICAgICAgPGZlYXR1cmUuaWNvbiBjbGFzc05hbWU9XCJ3LTUgaC01IHRleHQtaW5kaWdvLTYwMFwiIC8+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtZ3JheS03MDAgZm9udC1tZWRpdW1cIj57ZmVhdHVyZS50ZXh0fTwvc3Bhbj5cbiAgICAgICAgICAgICAgICA8L21vdGlvbi5kaXY+XG4gICAgICAgICAgICAgICkpfVxuICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgIHsvKiBDVEEgQnV0dG9ucyAqL31cbiAgICAgICAgICAgIDxtb3Rpb24uZGl2XG4gICAgICAgICAgICAgIGluaXRpYWw9e3sgb3BhY2l0eTogMCwgeTogMjAgfX1cbiAgICAgICAgICAgICAgYW5pbWF0ZT17eyBvcGFjaXR5OiAxLCB5OiAwIH19XG4gICAgICAgICAgICAgIHRyYW5zaXRpb249e3sgZHVyYXRpb246IDAuOCwgZGVsYXk6IDAuNSB9fVxuICAgICAgICAgICAgICBjbGFzc05hbWU9XCJmbGV4IGZsZXgtd3JhcCBnYXAtNCBtYi0xMlwiXG4gICAgICAgICAgICA+XG4gICAgICAgICAgICAgIDxMaW5rIGhyZWY9XCIvY29udGFjdFwiIGNsYXNzTmFtZT1cImJ0bi1wcmltYXJ5XCI+XG4gICAgICAgICAgICAgICAg6ZaL5aeL5YWN6LK76Kuu6KmiXG4gICAgICAgICAgICAgICAgPEFycm93UmlnaHQgY2xhc3NOYW1lPVwiaW5saW5lLWJsb2NrIG1sLTIgdy01IGgtNVwiIC8+XG4gICAgICAgICAgICAgIDwvTGluaz5cbiAgICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHNldElzVmlkZW9Nb2RhbE9wZW4odHJ1ZSl9XG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiYnRuLXNlY29uZGFyeSBmbGV4IGl0ZW1zLWNlbnRlclwiXG4gICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICA8UGxheSBjbGFzc05hbWU9XCJ3LTUgaC01IG1yLTJcIiAvPlxuICAgICAgICAgICAgICAgIOingOeci+a8lOekuuimlumgu1xuICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgIDwvbW90aW9uLmRpdj5cblxuICAgICAgICAgICAgey8qIFRydXN0IEluZGljYXRvcnMgKi99XG4gICAgICAgICAgICA8bW90aW9uLmRpdlxuICAgICAgICAgICAgICBpbml0aWFsPXt7IG9wYWNpdHk6IDAgfX1cbiAgICAgICAgICAgICAgYW5pbWF0ZT17eyBvcGFjaXR5OiAxIH19XG4gICAgICAgICAgICAgIHRyYW5zaXRpb249e3sgZHVyYXRpb246IDEsIGRlbGF5OiAwLjggfX1cbiAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LWdyYXktNTAwIG1iLTRcIj7lj5fliLDlhajnkIPpoJjlhYjkvIHmpa3nmoTkv6Hku7s8L3A+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBmbGV4LXdyYXAgaXRlbXMtY2VudGVyIGdhcC04XCI+XG4gICAgICAgICAgICAgICAge2NsaWVudHMubWFwKChjbGllbnQsIGluZGV4KSA9PiAoXG4gICAgICAgICAgICAgICAgICA8ZGl2XG4gICAgICAgICAgICAgICAgICAgIGtleT17aW5kZXh9XG4gICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInRleHQtZ3JheS00MDAgZm9udC1zZW1pYm9sZCB0ZXh0LWxnIG9wYWNpdHktNjAgaG92ZXI6b3BhY2l0eS0xMDAgdHJhbnNpdGlvbi1vcGFjaXR5XCJcbiAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAge2NsaWVudH1cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICkpfVxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDwvbW90aW9uLmRpdj5cbiAgICAgICAgICA8L21vdGlvbi5kaXY+XG5cbiAgICAgICAgICB7LyogUmlnaHQgVmlzdWFsICovfVxuICAgICAgICAgIDxtb3Rpb24uZGl2XG4gICAgICAgICAgICBpbml0aWFsPXt7IG9wYWNpdHk6IDAsIHg6IDUwIH19XG4gICAgICAgICAgICBhbmltYXRlPXt7IG9wYWNpdHk6IDEsIHg6IDAgfX1cbiAgICAgICAgICAgIHRyYW5zaXRpb249e3sgZHVyYXRpb246IDAuOCB9fVxuICAgICAgICAgICAgY2xhc3NOYW1lPVwicmVsYXRpdmUgbGc6aC1bNjAwcHhdXCJcbiAgICAgICAgICA+XG4gICAgICAgICAgICB7LyogM0QgQ2FyZCBTdGFjayAqL31cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicmVsYXRpdmUgdy1mdWxsIGgtZnVsbFwiPlxuICAgICAgICAgICAgICB7LyogTWFpbiBDYXJkICovfVxuICAgICAgICAgICAgICA8bW90aW9uLmRpdlxuICAgICAgICAgICAgICAgIHdoaWxlSG92ZXI9e3sgc2NhbGU6IDEuMDIgfX1cbiAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJhYnNvbHV0ZSB0b3AtMCByaWdodC0wIHctZnVsbCBtYXgtdy1tZFwiXG4gICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImNhcmQtbW9kZXJuIHAtOCByZWxhdGl2ZSBvdmVyZmxvdy1oaWRkZW5cIj5cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYWJzb2x1dGUgdG9wLTAgcmlnaHQtMCB3LTMyIGgtMzIgYmctZ3JhZGllbnQtdG8tYnIgZnJvbS1pbmRpZ28tNTAwIHRvLXB1cnBsZS01MDAgcm91bmRlZC1mdWxsIGJsdXItM3hsIG9wYWNpdHktMjBcIj48L2Rpdj5cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicmVsYXRpdmUgei0xMFwiPlxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInctMTYgaC0xNiBiZy1ncmFkaWVudC10by1yIGZyb20taW5kaWdvLTYwMCB0by1wdXJwbGUtNjAwIHJvdW5kZWQtMnhsIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIG1iLTZcIj5cbiAgICAgICAgICAgICAgICAgICAgICA8QnJhaW4gY2xhc3NOYW1lPVwidy04IGgtOCB0ZXh0LXdoaXRlXCIgLz5cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgIDxoMyBjbGFzc05hbWU9XCJ0ZXh0LTJ4bCBmb250LWJvbGQgbWItNFwiPkFJIOeHn+mKt+Wkp+iFpjwvaDM+XG4gICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtZ3JheS02MDAgbWItNlwiPlxuICAgICAgICAgICAgICAgICAgICAgIOaVtOWQiOapn+WZqOWtuOe/kuOAgeiHqueEtuiqnuiogOiZleeQhuWSjOmgkOa4rOWIhuaekO+8jOeCuuaCqOeahOeHn+mKt+etlueVpeaPkOS+m+aZuuiDveaxuuetluaUr+aMgVxuICAgICAgICAgICAgICAgICAgICA8L3A+XG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS0zXCI+XG4gICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTJcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxDaGVja0NpcmNsZSBjbGFzc05hbWU9XCJ3LTUgaC01IHRleHQtZ3JlZW4tNTAwXCIgLz5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtc21cIj7lr6bmmYLmlbjmk5rliIbmnpA8L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTJcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxDaGVja0NpcmNsZSBjbGFzc05hbWU9XCJ3LTUgaC01IHRleHQtZ3JlZW4tNTAwXCIgLz5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtc21cIj7oh6rli5XljJbnh5/pirfmtYHnqIs8L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTJcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxDaGVja0NpcmNsZSBjbGFzc05hbWU9XCJ3LTUgaC01IHRleHQtZ3JlZW4tNTAwXCIgLz5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtc21cIj7lgIvmgKfljJblrqLmiLbpq5TpqZc8L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDwvbW90aW9uLmRpdj5cblxuICAgICAgICAgICAgICB7LyogRmxvYXRpbmcgRWxlbWVudHMgKi99XG4gICAgICAgICAgICAgIDxtb3Rpb24uZGl2XG4gICAgICAgICAgICAgICAgYW5pbWF0ZT17eyB5OiBbMCwgLTIwLCAwXSB9fVxuICAgICAgICAgICAgICAgIHRyYW5zaXRpb249e3sgZHVyYXRpb246IDQsIHJlcGVhdDogSW5maW5pdHkgfX1cbiAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJhYnNvbHV0ZSB0b3AtMjAgbGVmdC0wIGNhcmQtbW9kZXJuIHAtNCBzaGFkb3cteGxcIlxuICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTNcIj5cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidy0xMiBoLTEyIGJnLWdyZWVuLTEwMCByb3VuZGVkLXhsIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyXCI+XG4gICAgICAgICAgICAgICAgICAgIDxUcmVuZGluZ1VwIGNsYXNzTmFtZT1cInctNiBoLTYgdGV4dC1ncmVlbi02MDBcIiAvPlxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtZ3JheS01MDBcIj7ovYnljJbnjofmj5DljYc8L3A+XG4gICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQteGwgZm9udC1ib2xkIHRleHQtZ3JlZW4tNjAwXCI+KzI4NyU8L3A+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgPC9tb3Rpb24uZGl2PlxuXG4gICAgICAgICAgICAgIDxtb3Rpb24uZGl2XG4gICAgICAgICAgICAgICAgYW5pbWF0ZT17eyB5OiBbMCwgMjAsIDBdIH19XG4gICAgICAgICAgICAgICAgdHJhbnNpdGlvbj17eyBkdXJhdGlvbjogNSwgcmVwZWF0OiBJbmZpbml0eSB9fVxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImFic29sdXRlIGJvdHRvbS0yMCBsZWZ0LTEwIGNhcmQtbW9kZXJuIHAtNCBzaGFkb3cteGxcIlxuICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTNcIj5cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidy0xMiBoLTEyIGJnLXB1cnBsZS0xMDAgcm91bmRlZC14bCBmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlclwiPlxuICAgICAgICAgICAgICAgICAgICA8VXNlcnMgY2xhc3NOYW1lPVwidy02IGgtNiB0ZXh0LXB1cnBsZS02MDBcIiAvPlxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtZ3JheS01MDBcIj7mtLvouo3nlKjmiLY8L3A+XG4gICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQteGwgZm9udC1ib2xkIHRleHQtcHVycGxlLTYwMFwiPjIuNU0rPC9wPlxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDwvbW90aW9uLmRpdj5cblxuICAgICAgICAgICAgICB7LyogRGVjb3JhdGl2ZSBDaXJjbGVzICovfVxuICAgICAgICAgICAgICA8bW90aW9uLmRpdlxuICAgICAgICAgICAgICAgIGFuaW1hdGU9e3sgcm90YXRlOiAzNjAgfX1cbiAgICAgICAgICAgICAgICB0cmFuc2l0aW9uPXt7IGR1cmF0aW9uOiAyMCwgcmVwZWF0OiBJbmZpbml0eSwgZWFzZTogXCJsaW5lYXJcIiB9fVxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImFic29sdXRlIC1ib3R0b20tMTAgLXJpZ2h0LTEwIHctNDAgaC00MCBib3JkZXItNCBib3JkZXItaW5kaWdvLTIwMCByb3VuZGVkLWZ1bGxcIlxuICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICA8bW90aW9uLmRpdlxuICAgICAgICAgICAgICAgIGFuaW1hdGU9e3sgcm90YXRlOiAtMzYwIH19XG4gICAgICAgICAgICAgICAgdHJhbnNpdGlvbj17eyBkdXJhdGlvbjogMTUsIHJlcGVhdDogSW5maW5pdHksIGVhc2U6IFwibGluZWFyXCIgfX1cbiAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJhYnNvbHV0ZSAtdG9wLTUgLWxlZnQtNSB3LTMyIGgtMzIgYm9yZGVyLTQgYm9yZGVyLXB1cnBsZS0yMDAgcm91bmRlZC1mdWxsXCJcbiAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvbW90aW9uLmRpdj5cbiAgICAgICAgPC9kaXY+XG4gICAgICA8L2Rpdj5cblxuICAgICAgey8qIFZpZGVvIE1vZGFsICovfVxuICAgICAge2lzVmlkZW9Nb2RhbE9wZW4gJiYgKFxuICAgICAgICA8bW90aW9uLmRpdlxuICAgICAgICAgIGluaXRpYWw9e3sgb3BhY2l0eTogMCB9fVxuICAgICAgICAgIGFuaW1hdGU9e3sgb3BhY2l0eTogMSB9fVxuICAgICAgICAgIGV4aXQ9e3sgb3BhY2l0eTogMCB9fVxuICAgICAgICAgIGNsYXNzTmFtZT1cImZpeGVkIGluc2V0LTAgei01MCBmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlciBiZy1ibGFjay84MCBwLTRcIlxuICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHNldElzVmlkZW9Nb2RhbE9wZW4oZmFsc2UpfVxuICAgICAgICA+XG4gICAgICAgICAgPG1vdGlvbi5kaXZcbiAgICAgICAgICAgIGluaXRpYWw9e3sgc2NhbGU6IDAuOSB9fVxuICAgICAgICAgICAgYW5pbWF0ZT17eyBzY2FsZTogMSB9fVxuICAgICAgICAgICAgZXhpdD17eyBzY2FsZTogMC45IH19XG4gICAgICAgICAgICBjbGFzc05hbWU9XCJyZWxhdGl2ZSB3LWZ1bGwgbWF4LXctNHhsIGFzcGVjdC12aWRlbyBiZy1ncmF5LTkwMCByb3VuZGVkLTJ4bCBvdmVyZmxvdy1oaWRkZW5cIlxuICAgICAgICAgICAgb25DbGljaz17KGUpID0+IGUuc3RvcFByb3BhZ2F0aW9uKCl9XG4gICAgICAgICAgPlxuICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBzZXRJc1ZpZGVvTW9kYWxPcGVuKGZhbHNlKX1cbiAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiYWJzb2x1dGUgdG9wLTQgcmlnaHQtNCBwLTIgYmctd2hpdGUvMTAgaG92ZXI6Ymctd2hpdGUvMjAgcm91bmRlZC1mdWxsIHRleHQtd2hpdGUgdHJhbnNpdGlvbi1jb2xvcnNcIlxuICAgICAgICAgICAgPlxuICAgICAgICAgICAgICA8WCBzaXplPXsyNH0gLz5cbiAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ3LWZ1bGwgaC1mdWxsIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyXCI+XG4gICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtd2hpdGUgdGV4dC14bFwiPuimlumgu+a8lOekuuWFp+WuuTwvcD5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvbW90aW9uLmRpdj5cbiAgICAgICAgPC9tb3Rpb24uZGl2PlxuICAgICAgKX1cbiAgICA8L3NlY3Rpb24+XG4gIClcbn1cblxuZXhwb3J0IGRlZmF1bHQgSGVybyJdLCJuYW1lcyI6WyJtb3Rpb24iLCJMaW5rIiwiQXJyb3dSaWdodCIsIlNwYXJrbGVzIiwiVHJlbmRpbmdVcCIsIlVzZXJzIiwiQnJhaW4iLCJQbGF5IiwiQ2hlY2tDaXJjbGUiLCJ1c2VTdGF0ZSIsIkhlcm8iLCJpc1ZpZGVvTW9kYWxPcGVuIiwic2V0SXNWaWRlb01vZGFsT3BlbiIsImZlYXR1cmVzIiwiaWNvbiIsInRleHQiLCJjbGllbnRzIiwic2VjdGlvbiIsImNsYXNzTmFtZSIsImRpdiIsImFuaW1hdGUiLCJzY2FsZSIsInJvdGF0ZSIsInRyYW5zaXRpb24iLCJkdXJhdGlvbiIsInJlcGVhdCIsIkluZmluaXR5IiwicmVwZWF0VHlwZSIsImluaXRpYWwiLCJvcGFjaXR5IiwieCIsInkiLCJzcGFuIiwiaDEiLCJiciIsInAiLCJtYXAiLCJmZWF0dXJlIiwiaW5kZXgiLCJkZWxheSIsImhyZWYiLCJidXR0b24iLCJvbkNsaWNrIiwiY2xpZW50Iiwid2hpbGVIb3ZlciIsImgzIiwiZWFzZSIsImV4aXQiLCJlIiwic3RvcFByb3BhZ2F0aW9uIiwiWCIsInNpemUiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/Hero.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/play.js":
/*!**********************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/play.js ***!
  \**********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __iconNode: () => (/* binding */ __iconNode),\n/* harmony export */   \"default\": () => (/* binding */ Play)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst __iconNode = [\n    [\n        \"polygon\",\n        {\n            points: \"6 3 20 12 6 21 6 3\",\n            key: \"1oa8hb\"\n        }\n    ]\n];\nconst Play = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"play\", __iconNode);\n //# sourceMappingURL=play.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9sdWNpZGUtcmVhY3QvZGlzdC9lc20vaWNvbnMvcGxheS5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7QUFHYSxtQkFBdUI7SUFBQztRQUFDLFNBQVc7UUFBQTtZQUFFLFFBQVEsb0JBQXNCO1lBQUEsSUFBSyxTQUFTO1FBQUEsQ0FBQztLQUFDO0NBQUE7QUFhM0YsV0FBTyxrRUFBaUIsU0FBUSxDQUFVIiwic291cmNlcyI6WyIvVXNlcnMva3cvc3JjL2ljb25zL3BsYXkudHMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IGNyZWF0ZUx1Y2lkZUljb24gZnJvbSAnLi4vY3JlYXRlTHVjaWRlSWNvbic7XG5pbXBvcnQgeyBJY29uTm9kZSB9IGZyb20gJy4uL3R5cGVzJztcblxuZXhwb3J0IGNvbnN0IF9faWNvbk5vZGU6IEljb25Ob2RlID0gW1sncG9seWdvbicsIHsgcG9pbnRzOiAnNiAzIDIwIDEyIDYgMjEgNiAzJywga2V5OiAnMW9hOGhiJyB9XV07XG5cbi8qKlxuICogQGNvbXBvbmVudCBAbmFtZSBQbGF5XG4gKiBAZGVzY3JpcHRpb24gTHVjaWRlIFNWRyBpY29uIGNvbXBvbmVudCwgcmVuZGVycyBTVkcgRWxlbWVudCB3aXRoIGNoaWxkcmVuLlxuICpcbiAqIEBwcmV2aWV3ICFbaW1nXShkYXRhOmltYWdlL3N2Zyt4bWw7YmFzZTY0LFBITjJaeUFnZUcxc2JuTTlJbWgwZEhBNkx5OTNkM2N1ZHpNdWIzSm5Mekl3TURBdmMzWm5JZ29nSUhkcFpIUm9QU0l5TkNJS0lDQm9aV2xuYUhROUlqSTBJZ29nSUhacFpYZENiM2c5SWpBZ01DQXlOQ0F5TkNJS0lDQm1hV3hzUFNKdWIyNWxJZ29nSUhOMGNtOXJaVDBpSXpBd01DSWdjM1I1YkdVOUltSmhZMnRuY205MWJtUXRZMjlzYjNJNklDTm1abVk3SUdKdmNtUmxjaTF5WVdScGRYTTZJREp3ZUNJS0lDQnpkSEp2YTJVdGQybGtkR2c5SWpJaUNpQWdjM1J5YjJ0bExXeHBibVZqWVhBOUluSnZkVzVrSWdvZ0lITjBjbTlyWlMxc2FXNWxhbTlwYmowaWNtOTFibVFpQ2o0S0lDQThjRzlzZVdkdmJpQndiMmx1ZEhNOUlqWWdNeUF5TUNBeE1pQTJJREl4SURZZ015SWdMejRLUEM5emRtYytDZz09KSAtIGh0dHBzOi8vbHVjaWRlLmRldi9pY29ucy9wbGF5XG4gKiBAc2VlIGh0dHBzOi8vbHVjaWRlLmRldi9ndWlkZS9wYWNrYWdlcy9sdWNpZGUtcmVhY3QgLSBEb2N1bWVudGF0aW9uXG4gKlxuICogQHBhcmFtIHtPYmplY3R9IHByb3BzIC0gTHVjaWRlIGljb25zIHByb3BzIGFuZCBhbnkgdmFsaWQgU1ZHIGF0dHJpYnV0ZVxuICogQHJldHVybnMge0pTWC5FbGVtZW50fSBKU1ggRWxlbWVudFxuICpcbiAqL1xuY29uc3QgUGxheSA9IGNyZWF0ZUx1Y2lkZUljb24oJ3BsYXknLCBfX2ljb25Ob2RlKTtcblxuZXhwb3J0IGRlZmF1bHQgUGxheTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/play.js\n"));

/***/ })

});