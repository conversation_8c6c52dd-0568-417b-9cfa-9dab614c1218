#chat-toggle-button {
    position: fixed;
    bottom: 20px;
    right: 20px;
    background-color: var(--primary-color);
    color: var(--light-text-color);
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 1.5rem;
    font-weight: bold;
    cursor: pointer;
    box-shadow: 0 4px 10px rgba(0,0,0,0.2);
    transition: all 0.3s ease;
}

#chat-toggle-button:hover {
    background-color: var(--primary-hover-color);
    transform: scale(1.1);
}

.chatbot-header {
    background: var(--primary-color);
    color: var(--light-text-color);
    padding: 1rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-top-left-radius: 10px;
    border-top-right-radius: 10px;
}

.chatbot-header h3 {
    margin: 0;
    font-size: 1.1rem;
}

#close-chatbot {
    background: none;
    border: none;
    color: var(--light-text-color);
    font-size: 1.5rem;
    cursor: pointer;
}

.chatbot-messages {
    flex-grow: 1;
    padding: 1rem;
    overflow-y: auto;
    background: #f4f7f9;
}

.message {
    margin-bottom: 1rem;
    padding: 0.75rem;
    border-radius: 10px;
    max-width: 80%;
    line-height: 1.4;
}

.message.bot {
    background-color: #e9e9eb;
    color: #333;
    align-self: flex-start;
    border-bottom-left-radius: 0;
}

.message.user {
    background-color: var(--primary-color);
    color: var(--light-text-color);
    align-self: flex-end;
    margin-left: auto;
    border-bottom-right-radius: 0;
}

.chatbot-input {
    padding: 1rem;
    border-top: 1px solid var(--border-color);
}

#chatbot-form {
    display: flex;
}

#chatbot-text-input {
    flex-grow: 1;
    border: 1px solid var(--border-color);
    border-radius: 20px;
    padding: 0.75rem;
    font-size: 1rem;
}

#chatbot-form button {
    background: var(--primary-color);
    color: var(--light-text-color);
    border: none;
    border-radius: 20px;
    padding: 0.75rem 1rem;
    margin-left: 0.5rem;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

#chatbot-form button:hover {
    background: var(--primary-hover-color);
}
