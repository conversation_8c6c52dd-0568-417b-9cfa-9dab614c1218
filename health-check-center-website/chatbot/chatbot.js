document.addEventListener('DOMContentLoaded', () => {

    // --- Chatbot ---
    const chatbotContainer = document.getElementById('chatbot-container');
    const body = document.querySelector('body');

    // 1. Create and inject the chatbot icon
    const chatToggleButton = document.createElement('div');
    chatToggleButton.id = 'chat-toggle-button';
    chatToggleButton.innerText = 'AI';
    body.appendChild(chatToggleButton);

    // 2. Inject chatbot HTML structure into its container
    if (chatbotContainer) {
        chatbotContainer.innerHTML = `
            <div class="chatbot-header">
                <h3>AI健康助手</h3>
                <button id="close-chatbot">-</button>
            </div>
            <div class="chatbot-messages">
                <div class="message bot">您好！我是您的AI健康助手，有什么可以帮您？</div>
            </div>
            <div class="chatbot-input">
                <form id="chatbot-form">
                    <input type="text" id="chatbot-text-input" placeholder="输入您的问题..." autocomplete="off">
                    <button type="submit">发送</button>
                </form>
            </div>
        `;
    }

    // 3. Add event listeners
    const closeChatbotButton = document.getElementById('close-chatbot');
    const chatbotForm = document.getElementById('chatbot-form');
    const chatbotInput = document.getElementById('chatbot-text-input');
    const messagesContainer = document.querySelector('.chatbot-messages');

    chatToggleButton.addEventListener('click', () => {
        chatbotContainer.style.display = 'flex';
        chatToggleButton.style.display = 'none';
    });

    closeChatbotButton.addEventListener('click', () => {
        chatbotContainer.style.display = 'none';
        chatToggleButton.style.display = 'block';
    });

    chatbotForm.addEventListener('submit', (e) => {
        e.preventDefault();
        const userInput = chatbotInput.value.trim();
        if (userInput === '') return;

        addMessage(userInput, 'user');
        chatbotInput.value = '';
        
        // Simulate bot response
        setTimeout(() => {
            getBotResponse(userInput);
        }, 1000);
    });

    function addMessage(text, sender) {
        const messageElement = document.createElement('div');
        messageElement.className = `message ${sender}`;
        messageElement.innerText = text;
        messagesContainer.appendChild(messageElement);
        messagesContainer.scrollTop = messagesContainer.scrollHeight; // Auto-scroll to bottom
    }

    function getBotResponse(userInput) {
        let botResponse = '';
        const lowerCaseInput = userInput.toLowerCase();

        if (lowerCaseInput.includes('套餐') || lowerCaseInput.includes('package')) {
            botResponse = '我们有三种主要的体检套餐：基础、全面和尊尚。您对哪一种感兴趣？';
        } else if (lowerCaseInput.includes('你好') || lowerCaseInput.includes('hello')) {
            botResponse = '您好！有什么可以为您服务的吗？';
        } else if (lowerCaseInput.includes('预约') || lowerCaseInput.includes('booking')) {
            botResponse = '您可以通过点击页面顶部的"立即预约"按钮进行预约，或者告诉我您的联系方式，稍后我们的客服会联系您。';
        } else {
            botResponse = '感谢您的提问。我现在还是一个简单的机器人，对于复杂问题，建议您直接致电 (852) 1234 5678 联系我们的客服。';
        }
        addMessage(botResponse, 'bot');
    }
});
