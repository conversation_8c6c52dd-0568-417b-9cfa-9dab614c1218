:root {
    --primary-color: #007BFF;
    --primary-hover-color: #0056b3;
    --secondary-color: #6c757d;
    --background-color: #f8f9fa;
    --text-color: #333;
    --light-text-color: #fff;
    --border-color: #dee2e6;
    --font-family: 'Helvetica Neue', Arial, sans-serif;
}

* {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

html {
    scroll-behavior: smooth;
}

body {
    font-family: var(--font-family);
    line-height: 1.6;
    background-color: var(--background-color);
    color: var(--text-color);
}

.container {
    max-width: 1100px;
    margin: auto;
    padding: 0 2rem;
}

header {
    background: var(--light-text-color);
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    position: sticky;
    top: 0;
    z-index: 1000;
}

nav {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 0;
}

.logo {
    color: var(--primary-color);
    font-size: 1.5rem;
    font-weight: bold;
    text-decoration: none;
}

nav ul {
    display: flex;
    list-style: none;
    align-items: center;
}

nav ul li {
    margin-left: 1.5rem;
}

nav a {
    color: var(--text-color);
    text-decoration: none;
    transition: color 0.3s ease;
}

nav a:hover {
    color: var(--primary-color);
}

.btn-primary {
    background-color: var(--primary-color);
    color: var(--light-text-color);
    padding: 0.5rem 1rem;
    border-radius: 5px;
    text-decoration: none;
    transition: background-color 0.3s ease;
}

.btn-primary:hover {
    background-color: var(--primary-hover-color);
    color: var(--light-text-color);
}

#hero {
    background: linear-gradient(rgba(0, 123, 255, 0.1), rgba(0, 123, 255, 0.1)), url('https://images.unsplash.com/photo-1576091160550-2173dba999ab?q=80&w=2070&auto=format&fit=crop') no-repeat center center/cover;
    color: var(--text-color);
    height: 60vh;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    text-align: center;
    padding: 0 1rem;
}

#hero h1 {
    font-size: 3rem;
    margin-bottom: 1rem;
}

#hero p {
    font-size: 1.25rem;
    margin-bottom: 2rem;
}

.btn-secondary {
    background: var(--light-text-color);
    color: var(--primary-color);
    padding: 0.75rem 1.5rem;
    border: 1px solid var(--primary-color);
    border-radius: 5px;
    text-decoration: none;
    transition: all 0.3s ease;
}

.btn-secondary:hover {
    background: var(--primary-color);
    color: var(--light-text-color);
}

section {
    padding: 4rem 0;
}

section h2 {
    text-align: center;
    margin-bottom: 2rem;
    font-size: 2.5rem;
}

#services .service-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1.5rem;
    margin-top: 2rem;
}

.service-card {
    background: var(--light-text-color);
    border: 1px solid var(--border-color);
    border-radius: 10px;
    padding: 2rem;
    text-align: left;
    box-shadow: 0 4px 6px rgba(0,0,0,0.05);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    display: flex;
    flex-direction: column;
}

.service-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 12px rgba(0,0,0,0.1);
}

.service-card h3 {
    color: var(--primary-color);
    font-size: 1.5rem;
    margin-bottom: 0.5rem;
}

.service-card .price {
    font-size: 1.75rem;
    font-weight: bold;
    margin-bottom: 1rem;
}

.service-card p {
    margin-bottom: 1rem;
    flex-grow: 1;
}

.service-card ul {
    list-style: none;
    margin-bottom: 1.5rem;
    flex-grow: 1;
}

.service-card ul li {
    margin-bottom: 0.5rem;
    position: relative;
    padding-left: 1.5em;
}

.service-card ul li::before {
    content: '✓';
    color: var(--primary-color);
    position: absolute;
    left: 0;
}

.service-card .btn-primary {
    display: block;
    width: 100%;
    text-align: center;
    margin-top: auto;
}

#about {
    background: var(--light-text-color);
}

footer {
    background: #333;
    color: var(--light-text-color);
    text-align: center;
    padding: 2rem 0;
}

#chatbot-container {
    position: fixed;
    bottom: 20px;
    right: 20px;
    width: 350px;
    height: 500px;
    background: white;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.2);
    display: none; /* Initially hidden */
    flex-direction: column;
}

@media(max-width: 768px) {
    #hero h1 {
        font-size: 2.5rem;
    }

    nav ul {
        display: none; /* Simple responsive, will improve later */
    }
}
