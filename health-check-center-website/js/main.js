document.addEventListener('DOMContentLoaded', () => {
    const servicePackages = [
        {
            name: '基础体检套餐',
            price: 'HK$ 1,800',
            description: '覆盖常规健康指标，适合年度健康检查。',
            items: ['血常规', '尿常规', '肝功能', '肾功能', '心电图']
        },
        {
            name: '全面体检套餐',
            price: 'HK$ 3,500',
            description: '在基础之上，增加更多专项检查，全面评估健康状况。',
            items: ['基础项目', '肿瘤标志物', '甲状腺功能', '胸部X光']
        },
        {
            name: '尊尚体检套餐',
            price: 'HK$ 6,800',
            description: '专为高端客户设计，提供深度、个性化的健康筛查。',
            items: ['全面项目', '心脏彩超', '头部MRI', '基因检测咨询']
        }
    ];

    const serviceCardsContainer = document.querySelector('#services .service-cards');

    if (serviceCardsContainer) {
        serviceCardsContainer.innerHTML = servicePackages.map(pkg => `
            <div class="service-card">
                <h3>${pkg.name}</h3>
                <p class="price">${pkg.price}</p>
                <p>${pkg.description}</p>
                <ul>
                    ${pkg.items.map(item => `<li>${item}</li>`).join('')}
                </ul>
                <a href="#" class="btn-primary">选择此套餐</a>
            </div>
        `).join('');
    }
});
