#!/bin/bash

# Claude Code 設置腳本

echo "=== Claude Code 設置 ==="
echo ""

# 加載 nvm
export NVM_DIR="$HOME/.nvm"
[ -s "$NVM_DIR/nvm.sh" ] && \. "$NVM_DIR/nvm.sh"

# 檢查 Node.js 安裝
echo "檢查 Node.js 安裝狀態..."
if command -v node >/dev/null 2>&1; then
    echo "✅ Node.js 已安裝 (版本: $(node --version))"
    echo "✅ npm 已安裝 (版本: $(npm --version))"
else
    echo "❌ Node.js 未安裝"
    echo "請先安裝 Node.js"
    exit 1
fi

# 檢查 Claude Code 安裝
echo ""
echo "檢查 Claude Code 安裝狀態..."
if command -v claude >/dev/null 2>&1; then
    echo "✅ Claude Code 已安裝 (版本: $(claude --version))"
else
    echo "❌ Claude Code 未安裝"
    echo "正在安裝..."
    npm install -g @anthropic-ai/claude-code
fi

echo ""
echo "=== API Key 設置 ==="
echo ""
echo "請按照以下步驟設置您的 Claude API Key:"
echo ""
echo "1. 前往 Anthropic Console: https://console.anthropic.com/"
echo "2. 登錄或註冊帳戶"
echo "3. 在 API Keys 頁面創建新的 API key"
echo "4. 複製您的 API key"
echo ""
echo "5. 設置環境變數 (選擇其中一種方法):"
echo ""
echo "   方法 A - 臨時設置 (僅當前終端會話有效):"
echo "   export ANTHROPIC_API_KEY='your-api-key-here'"
echo ""
echo "   方法 B - 永久設置 (添加到 ~/.zshrc):"
echo "   echo 'export ANTHROPIC_API_KEY=\"your-api-key-here\"' >> ~/.zshrc"
echo "   source ~/.zshrc"
echo ""
echo "   方法 C - 使用 Claude Code 內建認證:"
echo "   claude auth"
echo ""

# 檢查當前是否已設置 API key
if [ -n "$ANTHROPIC_API_KEY" ]; then
    echo "✅ ANTHROPIC_API_KEY 環境變數已設置"
else
    echo "⚠️  ANTHROPIC_API_KEY 環境變數未設置"
fi

echo ""
echo "=== 測試安裝 ==="
echo "運行以下命令測試 Claude Code:"
echo "claude"
echo ""
echo "如果遇到問題，請檢查:"
echo "- API key 是否正確設置"
echo "- 網絡連接是否正常"
echo "- API key 是否有足夠的額度"
echo ""
echo "=== 常用命令 ==="
echo "claude                    # 啟動互動模式"
echo "claude auth               # 設置認證"
echo "claude --help             # 查看幫助"
echo "claude --version          # 查看版本"
echo ""
echo "=== 快速啟動 ==="
echo "要開始使用 Claude Code，請運行:"
echo "source ~/.zshrc           # 重新加載環境變數"
echo "claude                    # 啟動 Claude Code"
