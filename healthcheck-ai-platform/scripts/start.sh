#!/bin/bash

echo "🚀 Starting HealthCheck AI Platform..."

# 檢查環境變量文件
if [ ! -f .env ]; then
    echo "❌ .env file not found! Copying from .env.example..."
    cp .env.example .env
    echo "⚠️  Please edit .env file with your configuration"
    exit 1
fi

# 檢查 Python 環境
if [ ! -d "venv" ]; then
    echo "📦 Creating virtual environment..."
    python3 -m venv venv
fi

# 激活虛擬環境
source venv/bin/activate

# 安裝/更新依賴
echo "📥 Installing dependencies..."
pip install -r requirements.txt

# 檢查 Docker
if ! command -v docker &> /dev/null; then
    echo "❌ Docker not found! Please install Docker first."
    exit 1
fi

# 啟動服務
echo "🐳 Starting services with Docker Compose..."
docker-compose up -d postgres redis

# 等待服務就緒
echo "⏳ Waiting for services to be ready..."
sleep 5

# 運行數據庫遷移
echo "🗄️ Running database migrations..."
# alembic upgrade head

# 啟動應用
echo "✅ Starting application..."
python src/main.py