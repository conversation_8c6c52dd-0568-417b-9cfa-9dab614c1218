#!/usr/bin/env python3
"""
啟動客服監察系統 - 生成報告
"""
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.analysis.periodic_report_generator import PeriodicReportGenerator
from datetime import datetime
import argparse

def main():
    parser = argparse.ArgumentParser(description='客服監察系統報告生成器')
    parser.add_argument('--type', choices=['4hour', 'daily', 'both'], default='both',
                        help='報告類型：4hour(4小時監察), daily(每日跟進), both(兩者都生成)')
    parser.add_argument('--format', choices=['text', 'json'], default='text',
                        help='輸出格式：text(文字), json')
    parser.add_argument('--db', default='/Users/<USER>/Desktop/Claude/whatsapp-mcp/whatsapp-bridge/store/messages.db',
                        help='數據庫路徑')
    
    args = parser.parse_args()
    
    # 創建報告生成器
    generator = PeriodicReportGenerator(args.db, "reports")
    
    print(f"客服監察系統")
    print(f"當前時間: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 80)
    
    # 生成報告
    if args.type in ['4hour', 'both']:
        print("\n生成4小時監察報告...")
        hourly_report = generator.generate_4hour_report()
        print("\n" + generator.format_report_for_display(hourly_report, args.format))
        print(f"\n✅ 4小時報告已保存到 reports/hourly_report_latest.json")
    
    if args.type in ['daily', 'both']:
        print("\n\n生成明日跟進清單...")
        daily_report = generator.generate_daily_follow_up_list()
        print("\n" + generator.format_report_for_display(daily_report, args.format))
        print(f"\n✅ 每日跟進清單已保存到 reports/daily_report_latest.json")
    
    print("\n\n提示：")
    print("- 使用 --type 4hour 只生成4小時報告")
    print("- 使用 --type daily 只生成每日跟進清單")
    print("- 使用 --format json 輸出JSON格式")
    print("- 報告保存在 reports/ 目錄")

if __name__ == "__main__":
    main()