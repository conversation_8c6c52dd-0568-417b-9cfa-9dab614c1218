# 價格驗證系統使用指南

## 🎯 系統目的

確保AI回答的價格和套餐內容完全準確，避免AI幻覺造成的錯誤信息，保護公司聲譽和客戶信任。

## 🛡️ 核心功能

### 1. 價格驗證
- 自動檢查AI提及的價格是否正確
- 支持團購、折扣等複雜定價規則
- 實時計算最終價格

### 2. 內容驗證
- 檢查套餐內容描述是否準確
- 驗證檢查項目是否存在
- 確保服務描述一致

### 3. 合規檢查
- 自動識別禁止詞彙（如“最低價”、“醫生建議”）
- 確保符合法律法規要求

### 4. 自動修正
- 發現錯誤後自動修正
- 保留修正日誌
- 不影響AI回應速度

## 🚀 快速開始

### 1. 初始化系統

```python
from src.core.price_validator import PriceValidator, AIResponseMiddleware

# 初始化驗證器
validator = PriceValidator("/path/to/config/price_config.json")

# 初始化中間件
middleware = AIResponseMiddleware(validator)
```

### 2. 集成到ChatGPT

```python
import openai
from src.core.price_validator import AIResponseMiddleware, PriceValidator

class SafeAIChatbot:
    def __init__(self, config_path: str):
        self.validator = PriceValidator(config_path)
        self.middleware = AIResponseMiddleware(self.validator)
        
    async def get_response(self, user_query: str) -> str:
        # 1. 讓AI生成回答
        ai_response = await self._get_ai_response(user_query)
        
        # 2. 驗證和修正
        result = await self.middleware.process_ai_response(
            ai_response=ai_response,
            user_query=user_query,
            ai_provider='chatgpt'
        )
        
        # 3. 返回安全的回答
        return result['response']
```

## 📝 配置文件結構

### price_config.json

```json
{
  "version": "1.0.0",
  "last_updated": "2025-01-11T00:00:00",
  "price_rules": [
    {
      "id": "130_standard",
      "service_name": "130項全面健康檢查計劃",
      "base_price": 2950,
      "currency": "HKD",
      "valid_from": "2025-01-01T00:00:00",
      "valid_until": "2025-12-31T23:59:59",
      "discounts": [
        {
          "name": "個人現金券優惠",
          "type": "cash_voucher",
          "value": 300,
          "condition": {"group_size": 1}
        },
        {
          "name": "2人同行8折",
          "type": "percentage",
          "value": 20,
          "condition": {"min_people": 2}
        },
        {
          "name": "3人同行1人免費",
          "type": "group_buy",
          "min_people": 3,
          "free_people": 1,
          "condition": {"min_people": 3}
        }
      ]
    }
  ],
  "packages": [
    {
      "id": "pkg_130",
      "name": "130項全面健康檢查計劃",
      "category": "comprehensive",
      "items": [
        "身高、體重、BMI",
        "血壓測量",
        "全血球計數",
        "肝功能測試(10項)",
        "..."
      ]
    }
  ]
}
```

## 🔍 使用場景

### 1. 驗證單個價格

```python
# 驗證標準價格
status, message, details = validator.validate_price(
    service_name="130項全面體檢",
    quoted_price=2950,
    conditions={}
)

# 驗證團購價格
status, message, details = validator.validate_price(
    service_name="130項全面體檢",
    quoted_price=2360,
    conditions={"group_size": 2}
)
```

### 2. 驗證AI回應

```python
ai_response = """
我們的130項全面體檢原價是$2,950，
現在有2人同行8折優惠，每人只需$2,360。
這是全港最低價！
"""

validation_result = validator.validate_ai_response(ai_response)

if validation_result['requires_correction']:
    print("需要修正的問題：")
    for v in validation_result['validations']:
        if v['status'] != 'valid':
            print(f"- {v['type']}: {v['message']}")
```

### 3. 生成安全回應

```python
from src.core.price_validator import SafeResponseGenerator

generator = SafeResponseGenerator(validator)

# 生成經過驗證的價格回應
response = generator.generate_price_response(
    service_name="130項全面體檢",
    conditions={"group_size": 2}
)

print(response)
```

## ⚠️ 禁止詞彙列表

系統會自動檢查並替換以下詞彙：

| 禁止詞彙 | 替換為 |
|---------|--------|
| 最低價 | 優惠價 |
| 最便宜 | 超值 |
| 全港最低 | 競爭力價格 |
| 醫生建議 | 專業建議 |
| 醫生說 | 專家認為 |
| 保證治癒 | （移除） |

## 📊 日誌和監控

### 查看驗證日誌

```python
# 獲取最近的驗證結果
recent_logs = validator.validation_log[-10:]

for log in recent_logs:
    print(f"\n時間: {log['timestamp']}")
    print(f"狀態: {log['overall_status']}")
    print(f"需要修正: {log['requires_correction']}")
```

### 修正統計

```python
# 獲取修正統計
correction_stats = middleware.get_correction_statistics()

print(f"總驗證次數: {correction_stats['total_validations']}")
print(f"修正次數: {correction_stats['corrections_made']}")
print(f"修正率: {correction_stats['correction_rate']:.1%}")
```

## 🔄 更新價格配置

### 1. 修改配置文件

編輯 `config/price_config.json` 文件，更新價格或優惠規則。

### 2. 重新載入

```python
# 重新載入配置
validator._load_configurations()
print("✓ 價格配置已更新")
```

### 3. 驗證更新

```python
# 驗證新價格
test_price = validator.get_correct_price_info(
    "130項全面體檢",
    {"group_size": 2}
)
print(f"新2人同行價格: ${test_price['final_price']:.0f}")
```

## 🎯 最佳實踐

### 1. 定期更新配置
- 每次價格變動時立即更新
- 保留歷史版本記錄
- 設定有效期限

### 2. 監控修正率
- 如果修正率過高，可能需要重新訓練AI
- 定期分析常見錯誤
- 優化AI提示詞

### 3. 測試覆蓋
- 測試所有價格組合
- 模擬各種查詢場景
- 驗證特殊優惠

### 4. 錯誤處理
```python
try:
    result = await middleware.process_ai_response(ai_response, user_query)
except Exception as e:
    # 如果驗證失敗，使用安全回應
    return "抱歉，請稍後再試或聯繫客服人員。"
```

## 🤝 與ChatGPT集成

### 完整示例

```python
import asyncio
import openai
from src.core.price_validator import PriceValidator, AIResponseMiddleware

class ValidatedChatGPT:
    def __init__(self, api_key: str, config_path: str):
        openai.api_key = api_key
        self.validator = PriceValidator(config_path)
        self.middleware = AIResponseMiddleware(self.validator)
    
    async def chat(self, user_message: str) -> str:
        # 1. 調用ChatGPT
        response = openai.ChatCompletion.create(
            model="gpt-4",
            messages=[
                {"role": "system", "content": "你是健康檢查公司的客服助理"},
                {"role": "user", "content": user_message}
            ]
        )
        
        ai_response = response.choices[0].message.content
        
        # 2. 驗證和修正
        validated_result = await self.middleware.process_ai_response(
            ai_response=ai_response,
            user_query=user_message,
            ai_provider='chatgpt'
        )
        
        # 3. 返回安全的回應
        return validated_result['response']

# 使用示例
async def main():
    chatbot = ValidatedChatGPT(
        api_key="your-api-key",
        config_path="config/price_config.json"
    )
    
    user_query = "130項體檢多少錢？有2人同行優惠嗎？"
    response = await chatbot.chat(user_query)
    print(response)  # 經過驗證的安全回應

if __name__ == "__main__":
    asyncio.run(main())
```

## 📞 支持

如有問題，請聯繫：
- 技術支持：<EMAIL>
- 文檔：[https://docs.healthcheck-ai.com](https://docs.healthcheck-ai.com)