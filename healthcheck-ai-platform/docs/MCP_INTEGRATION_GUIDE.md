# MCP WhatsApp 整合指南

## 概述

本系統已從 WhatsApp Business API 遷移到 MCP (Message Control Protocol)，並實現了先進的防機器人偵測機制，確保對話數據的安全記錄。

## 主要功能

### 1. MCP 整合
- 使用 MCP 協議進行 WhatsApp 消息收發
- 支援文字、圖片、文件和音頻消息
- 實時消息處理和回應

### 2. 防機器人偵測機制
- **行為分析**：監測回應時間、打字速度等行為模式
- **模式檢測**：識別重複或模板化的對話模式
- **活動追蹤**：分析用戶活動頻率和規律性
- **人性化處理**：自動添加打字錯誤、情感表達等人類特徵

### 3. 數據安全記錄
- 敏感信息自動混淆
- 會話追蹤和分析
- 完整的對話歷史記錄
- 防止被識別為數據挖掘行為

## 快速開始

### 1. 環境設置

```bash
# 安裝依賴
pip install -r requirements.txt

# 設置環境變量
cp .env.example .env
# 編輯 .env 文件，設置數據庫連接等信息
```

### 2. 數據庫初始化

```bash
# 運行數據庫遷移
python src/database/add_anti_bot_columns.py
```

### 3. 啟動服務

```bash
# 啟動 MCP 整合服務
python src/main_mcp.py
```

服務將在 http://localhost:8001 啟動

## API 端點

### 1. 發送消息
```http
POST /api/send_message
Content-Type: application/json

{
    "recipient_id": "+85212345678",
    "content": "您好，感謝您的諮詢"
}
```

### 2. 獲取對話歷史
```http
GET /api/conversations/{customer_phone}
```

### 3. 防機器人分析
```http
GET /api/anti_bot/analysis/{conversation_id}
```

### 4. MCP Webhook
```http
POST /webhook/mcp
```

## 防機器人策略

### 1. 消息發送延遲
系統會自動計算並應用人類般的打字延遲：
- 基於消息長度
- 加入隨機思考時間
- 模擬真實打字速度

### 2. 速率限制
- 每分鐘最多 10 條消息
- 每小時最多 200 條消息
- 突發消息限制：5 條

### 3. 行為多樣性
- 10% 機率出現打字錯誤（並自動更正）
- 20% 機率加入情感表達
- 回應時間隨機變化

## 配置說明

配置文件位於 `config/mcp_config.yaml`，主要設置包括：

### MCP 設置
- 服務器連接信息
- WhatsApp 相關參數

### 防機器人設置
- 風險評分閾值
- 速率限制參數
- 人性化行為參數

### 業務規則
- 工作時間設置
- 自動回覆消息

## 監控和維護

### 1. 日誌查看
```bash
tail -f logs/mcp_integration.log
```

### 2. 性能監控
- 系統自動記錄性能指標
- 每 5 分鐘生成報告

### 3. 風險警報
- 高風險行為自動警報
- 支援 Email 和 Slack 通知

## 最佳實踐

### 1. 對話管理
- 保持自然的對話節奏
- 避免過於頻繁的消息發送
- 適當使用情感表達和個性化內容

### 2. 數據安全
- 定期檢查混淆規則
- 監控異常訪問模式
- 保護客戶隱私信息

### 3. 系統優化
- 根據分析結果調整參數
- 定期更新人性化表達庫
- 持續改進防偵測算法

## 故障排除

### 常見問題

1. **消息發送失敗**
   - 檢查 MCP 服務連接
   - 確認速率限制未超標
   - 查看錯誤日誌

2. **高風險評分警告**
   - 檢查對話模式
   - 調整人性化參數
   - 增加回應時間變化

3. **數據庫連接錯誤**
   - 確認數據庫服務運行
   - 檢查連接字符串
   - 驗證數據庫權限

## 更新日誌

### v2.0.0 (當前版本)
- 從 WhatsApp Business API 遷移到 MCP
- 實現防機器人偵測機制
- 添加人性化行為模擬
- 改進數據安全記錄

### 未來計劃
- 支援更多消息類型
- 增強機器學習模型
- 改進行為分析算法
- 支援多語言對話

## 聯繫支援

如有問題或需要協助，請聯繫技術支援團隊。