# 策略知識庫使用指南

## 🎯 系統目的

策略知識庫是一個智能化的銷售策略管理系統，旨在：

1. **收集和組織** - 將成功的銷售策略系統化地存儲和分類
2. **追蹤和分析** - 持續監測每個策略的效果和轉化率
3. **學習和優化** - 從執行結果中學習，不斷改善策略
4. **智能推薦** - 根據客戶特徵自動推薦最適合的策略

## 📚 系統架構

### 核心組件

1. **StrategyKnowledgeBase** - 策略管理核心
   - 創建、更新、搜索策略
   - 記錄執行結果
   - 管理策略關係

2. **StrategyLearningEngine** - 機器學習引擎
   - 分析成功模式
   - 生成優化建議
   - A/B測試設計

3. **StrategyAnalytics** - 分析和報告
   - 效能指標計算
   - 趨勢分析
   - 視覺化報告

4. **StrategyTemplates** - 預設模板
   - 經過驗證的最佳實踐
   - 快速部署新策略

### 策略分類

- **轉化優化** (conversion_optimization)
- **定價策略** (pricing_strategy)
- **溝通策略** (communication_tactics)
- **跟進方法** (followup_methods)
- **異議處理** (objection_handling)
- **促銷活動** (promotion_campaigns)
- **客戶旅程** (customer_journey)
- **競爭分析** (competitor_analysis)

## 🚀 快速開始

### 1. 初始化知識庫

```python
from src.knowledge_base.strategy_knowledge_base import StrategyKnowledgeBase
from src.knowledge_base.strategy_templates import initialize_strategy_templates

# 創建知識庫
kb = StrategyKnowledgeBase("data/strategy_knowledge.db")

# 初始化預設模板
initialize_strategy_templates("data/strategy_knowledge.db")
```

### 2. 創建新策略

```python
from src.knowledge_base.strategy_knowledge_base import StrategyCategory

strategy = kb.create_strategy(
    name="節日限時優惠",
    category=StrategyCategory.PROMOTION,
    description="針對節日推出的限時優惠活動",
    implementation={
        "discount": "20%",
        "duration": "3_days",
        "target_services": ["130項全面體檢", "158項尊尚體檢"]
    },
    target_metrics={
        "conversion_rate": 0.3,
        "average_order_value": 3500
    },
    created_by="admin",
    tags=["holiday", "limited_time", "discount"]
)
```

### 3. 記錄執行結果

```python
from src.knowledge_base.strategy_knowledge_base import StrategyOutcome
from datetime import datetime

outcome = StrategyOutcome(
    strategy_id=strategy.id,
    execution_date=datetime.now(),
    customer_id="customer_123",
    success=True,
    metrics={
        "conversion_time_hours": 24,
        "revenue": 3500,
        "followup_count": 3
    },
    feedback="客戶對優惠很滿意，快速下單",
    context={
        "channel": "whatsapp",
        "customer_type": "price_sensitive",
        "time_of_day": "evening"
    }
)

kb.record_outcome(outcome)
```

### 4. 搜索和推薦策略

```python
# 搜索高成功率策略
high_performing = kb.search_strategies(
    category=StrategyCategory.CONVERSION,
    status=StrategyStatus.ACTIVE,
    min_success_rate=0.7
)

# 智能推薦
customer_profile = {
    "price_mentioned": 3,
    "health_questions": 1,
    "total_messages": 15,
    "tags": ["price_sensitive", "family_oriented"]
}

recommendations = kb.recommend_strategies(
    customer_profile=customer_profile,
    context={"need_type": "conversion_optimization"},
    limit=3
)
```

## 📊 分析和報告

### 生成效能報告

```python
from src.knowledge_base.strategy_analytics import StrategyAnalytics

analytics = StrategyAnalytics(
    kb_db_path="data/strategy_knowledge.db",
    messages_db_path="data/messages.db"
)

# 分析最近30天的效能
metrics = analytics.analyze_strategy_performance()

# 生成報告
analytics.generate_performance_report(
    metrics_list=metrics,
    output_path="reports/strategy_performance.md"
)

# 生成視覺化
analytics.visualize_performance(
    metrics_list=metrics,
    output_dir="reports/visualizations"
)
```

### 策略學習和優化

```python
from src.knowledge_base.strategy_knowledge_base import StrategyLearningEngine

learning_engine = StrategyLearningEngine(kb)

# 從最近30天的結果學習
learning_results = learning_engine.learn_from_outcomes(days=30)

# 獲取A/B測試建議
ab_tests = learning_engine.suggest_ab_tests()

for test in ab_tests:
    print(f"\n建議測試: {test['strategy_name']}")
    print(f"當前成功率: {test['current_success_rate']:.1%}")
    print(f"測試原因: {test['test_reason']}")
    for variant in test['variants']:
        print(f"  - {variant['name']}: {variant['hypothesis']}")
```

## 🌟 最佳實踐

### 1. 策略命名規範

- 使用清晰、描述性的名稱
- 包含策略的主要特點
- 例如："即時預約獎勵策略"、"階梯式優惠策略"

### 2. 標籤使用

常用標籤：
- `immediate_reward` - 即時獎勵
- `time_pressure` - 時間壓力
- `value_add` - 增值服務
- `personalization` - 個性化
- `education` - 教育內容
- `social_proof` - 社交證明

### 3. 指標設定

設定合理的目標指標：
```python
target_metrics = {
    "conversion_rate": 0.2,        # 20%轉化率
    "decision_time_hours": 48,     # 48小時內決策
    "customer_satisfaction": 4.5,  # 4.5分滿意度
    "repeat_rate": 0.3            # 30%複購率
}
```

### 4. 持續優化流程

1. **部署** - 將策略狀態設為 `TESTING`
2. **監測** - 收集10-20個執行結果
3. **評估** - 分析成功率和指標達成情況
4. **優化** - 根據結果調整參數
5. **推廣** - 成功後設為 `ACTIVE`

### 5. 結果記錄

詳細記錄每次執行：
```python
context = {
    "customer_segment": "price_sensitive",
    "interaction_count": 5,
    "previous_strategies": ["edu_001", "price_002"],
    "time_of_day": "evening",
    "day_of_week": "Saturday",
    "channel": "whatsapp",
    "agent_id": "agent_123"
}
```

## 📡 API 參考

### StrategyKnowledgeBase

```python
# 創建策略
create_strategy(name, category, description, implementation, 
                target_metrics, created_by, tags=None, dependencies=None)

# 更新策略
update_strategy_metrics(strategy_id, metrics)

# 記錄結果
record_outcome(outcome)

# 搜索策略
search_strategies(category=None, status=None, tags=None, min_success_rate=None)

# 智能推薦
recommend_strategies(customer_profile, context, limit=3)

# 分析性能
analyze_strategy_performance(strategy_id, days=30)
```

### StrategyAnalytics

```python
# 分析效能
analyze_strategy_performance(start_date=None, end_date=None)

# 生成報告
generate_performance_report(metrics_list, output_path)

# 視覺化
visualize_performance(metrics_list, output_dir)

# 客戶推薦
get_strategy_recommendations(customer_profile)
```

## 🎯 使用場景

### 1. 新客戶詢問

```python
# 分析客戶特徵
customer_analysis = analyze_customer_conversation(chat_history)

# 獲取推薦策略
recommended = kb.recommend_strategies(
    customer_profile=customer_analysis,
    context={"stage": "initial_inquiry"},
    limit=3
)

# 應用最佳策略
best_strategy = recommended[0][0]
apply_strategy(best_strategy, customer_id)
```

### 2. 長期未轉化客戶

```python
# 搜索跟進策略
followup_strategies = kb.search_strategies(
    category=StrategyCategory.FOLLOWUP,
    tags=["reactivation", "long_term_nurture"]
)

# 選擇適合的策略
for strategy in followup_strategies:
    if strategy.success_rate > 0.5:
        execute_followup(strategy, dormant_customers)
```

### 3. 節日促銷

```python
# 創建節日策略
holiday_strategy = kb.create_strategy(
    name=f"{holiday_name}特別優惠",
    category=StrategyCategory.PROMOTION,
    description=f"針對{holiday_name}的限時優惠",
    implementation={
        "discount_type": "percentage",
        "discount_value": 25,
        "valid_days": 7,
        "bundle_offers": True
    },
    target_metrics={
        "conversion_rate": 0.35,
        "average_order_value": 4000
    },
    created_by="marketing_team",
    tags=["holiday", "limited_time", "seasonal"]
)
```

## 🔄 更新和維護

### 定期評估

每週執行：
```bash
python -m src.knowledge_base.weekly_review
```

該腳本會：
1. 識別表現不佳的策略
2. 建議改善方案
3. 標記需要暫停的策略
4. 提出A/B測試建議

### 數據備份

```bash
# 備份知識庫
python -m src.knowledge_base.backup --output backups/kb_backup_$(date +%Y%m%d).json

# 恢復知識庫
python -m src.knowledge_base.restore --input backups/kb_backup_20250711.json
```

## 🔍 故障排查

### 常見問題

1. **策略成功率低**
   - 檢查目標客群是否正確
   - 評估執行時機和方式
   - 分析失敗案例的共同特徵

2. **推薦不準確**
   - 確保客戶特徵分析正確
   - 檢查策略標籤是否完整
   - 調整相關性計算權重

3. **數據不一致**
   - 確保所有系統使用相同的時區
   - 檢查結果記錄是否完整
   - 驗證指標計算邏輯

### 日誌查看

```bash
# 查看策略執行日誌
tail -f logs/strategy_execution.log

# 查看學習引擎日誌
tail -f logs/learning_engine.log

# 查看錯誤日誌
grep ERROR logs/*.log
```

## 🌐 未來發展

### 計劃中的功能

1. **AI驅動的策略生成**
   - 基於GPT的策略創意生成
   - 自動化策略模板建議

2. **多渠道整合**
   - 支持Email、SMS等渠道
   - 跨渠道策略協同

3. **預測分析**
   - 策略效果預測
   - 客戶行為預測

4. **實時優化**
   - 動態參數調整
   - 實時A/B測試

## 📞 支持和反饋

如有問題或建議，請聯繫：
- Email: <EMAIL>
- 文檔：[https://docs.healthcheck-ai.com](https://docs.healthcheck-ai.com)
- GitHub: [https://github.com/healthcheck-ai/strategy-kb](https://github.com/healthcheck-ai/strategy-kb)