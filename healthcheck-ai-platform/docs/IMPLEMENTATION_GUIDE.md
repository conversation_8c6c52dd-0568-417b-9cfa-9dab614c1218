# AI幻覺防護系統實施指南

## 快速開始

### 1. 安裝依賴

```bash
cd healthcheck-ai-platform
pip install -r requirements.txt
```

### 2. 配置環境變量

```bash
# .env 文件
OPENAI_API_KEY=your_key_here
DATABASE_URL=postgresql://user:pass@localhost/healthcheck
REDIS_URL=redis://localhost:6379/0
LOG_LEVEL=INFO
```

### 3. 初始化系統

```python
from src.core.enhanced_conversation_manager import create_enhanced_conversation_manager
from src.integrations.messaging_interface import MessagingInterface

# 創建消息接口
messaging_client = MessagingInterface()

# 創建增強型對話管理器
manager = create_enhanced_conversation_manager(messaging_client)
```

## 集成步驟

### 步驟1：更新主應用

修改 `src/main.py`：

```python
from src.core.enhanced_conversation_manager import create_enhanced_conversation_manager

# 替換原有的 ConversationManager
# conversation_manager = ConversationManager(messaging_client)
conversation_manager = create_enhanced_conversation_manager(messaging_client)

@app.post("/webhook/whatsapp")
async def handle_whatsapp_webhook(request: Request):
    """處理WhatsApp webhook"""
    data = await request.json()
    
    # 解析消息
    message = parse_whatsapp_message(data)
    
    # 使用增強型管理器處理
    await conversation_manager.handle_message(message)
    
    return {"status": "ok"}
```

### 步驟2：配置知識庫

創建 `config/knowledge_base.yaml`：

```yaml
packages:
  - id: pkg_001
    name: 精選計劃
    price: 1950
    items: 110
    features:
      - 超聲波檢查
      - 癌症指標
      - 血液分析
    
  - id: pkg_002
    name: 全面計劃
    price: 2950
    items: 130
    features:
      - 超聲波+胸部X光
      - 心電圖
      - 肝炎篩查

promotions:
  coupons:
    CPN100: 
      discount: 100
      applicable: 精選計劃
    CPN300:
      discount: 300
      applicable: 全面計劃
```

### 步驟3：更新Agent配置

修改 `config/agents/traffic_reception.yaml`：

```yaml
role: 流量接待專員
goal: 友好接待客戶，了解需求，引導至合適的體檢套餐
backstory: |
  你是一位經驗豐富的健康顧問，專門負責接待諮詢體檢服務的客戶。
  
validation_rules:
  - always_check_knowledge_base: true
  - require_fact_verification: true
  - confidence_threshold: 0.8
```

### 步驟4：設置監控

創建 `src/monitoring/validation_metrics.py`：

```python
from prometheus_client import Counter, Histogram, Gauge

# 定義指標
validation_errors = Counter(
    'validation_errors_total',
    'Total validation errors',
    ['type', 'severity']
)

confidence_score = Histogram(
    'ai_confidence_score',
    'AI response confidence scores'
)

fallback_rate = Gauge(
    'fallback_rate',
    'Rate of fallback to human agents'
)
```

## 測試驗證

### 1. 運行單元測試

```bash
pytest testing/unit-tests/test_validators.py -v
```

### 2. 集成測試

```bash
python testing/test_integration.py
```

### 3. 手動測試場景

測試以下場景確保系統正常工作：

**場景1：正常查詢**
```
用戶：請問全面計劃多少錢？
AI：全面計劃的優惠價格是 $2,950，包含130項檢查。使用優惠券CPN300可再減$300。
```

**場景2：錯誤糾正**
```
用戶：精選計劃是不是3000元？
AI：精選計劃的優惠價格是 $1,950（原價$5,880），包含110項檢查。
```

**場景3：人工介入**
```
用戶：我要投訴你們的服務！
AI：您的問題比較重要，我為您轉接專業客服人員來協助您。
```

## 生產環境部署

### 1. 數據庫遷移

```bash
# 添加必要的表
python src/database/add_validation_tables.py
```

### 2. 配置Redis

```bash
# 確保Redis配置了持久化
redis-cli CONFIG SET save "900 1 300 10 60 10000"
```

### 3. 設置日誌

```python
# src/monitoring/logging_config.py
LOGGING_CONFIG = {
    'version': 1,
    'handlers': {
        'validation': {
            'class': 'logging.handlers.RotatingFileHandler',
            'filename': 'logs/validation.log',
            'maxBytes': 10485760,  # 10MB
            'backupCount': 5
        }
    }
}
```

### 4. 性能優化

- 啟用知識庫緩存
- 配置連接池
- 設置適當的超時時間

```python
# 緩存配置
CACHE_CONFIG = {
    'knowledge_ttl': 7200,  # 2小時
    'validation_ttl': 300,  # 5分鐘
    'max_cache_size': 1000
}
```

## 監控和維護

### 關鍵指標監控

1. **實時監控面板**
   - 平均置信度分數
   - 驗證失敗率
   - 人工介入率
   - 響應時間

2. **告警規則**
   ```yaml
   alerts:
     - name: LowConfidenceScore
       condition: avg(confidence_score) < 0.6
       duration: 5m
       action: notify_team
     
     - name: HighFallbackRate
       condition: fallback_rate > 0.2
       duration: 10m
       action: escalate
   ```

### 日常維護

1. **每日檢查**
   - 查看驗證失敗日誌
   - 檢查人工介入案例
   - 更新知識庫（如有需要）

2. **每週任務**
   - 分析置信度趨勢
   - 優化驗證規則
   - 更新測試用例

3. **每月審查**
   - 評估整體效果
   - 收集改進建議
   - 規劃下一步優化

## 故障排除

### 常見問題

**問題1：置信度持續偏低**
- 檢查知識庫是否過時
- 審查驗證規則是否過嚴
- 分析具體失敗案例

**問題2：響應時間過長**
- 檢查緩存命中率
- 優化知識庫查詢
- 考慮異步處理

**問題3：錯誤的人工介入**
- 調整置信度閾值
- 優化敏感詞列表
- 改進意圖識別

## 最佳實踐

1. **漸進式部署**
   - 先在測試環境驗證
   - 灰度發布到部分用戶
   - 監控指標後全量上線

2. **持續優化**
   - 定期更新知識庫
   - 基於數據調整閾值
   - 收集用戶反饋改進

3. **團隊協作**
   - 客服團隊參與規則制定
   - 定期培訓和知識分享
   - 建立反饋機制

## 總結

實施AI幻覺防護系統需要：
1. 完整的技術部署
2. 持續的監控維護
3. 團隊的積極參與
4. 數據驅動的優化

遵循本指南，您可以建立一個可靠、準確、值得信賴的AI客服系統。