#!/bin/bash

echo "🚀 HealthCheck AI Platform - 項目設置腳本"
echo "========================================"

# 顏色定義
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

# 檢查 Python 版本
echo -e "${YELLOW}檢查 Python 版本...${NC}"
if command -v python3 &> /dev/null; then
    PYTHON_VERSION=$(python3 --version | cut -d " " -f 2 | cut -d "." -f 1,2)
    echo -e "${GREEN}✓ Python $PYTHON_VERSION 已安裝${NC}"
else
    echo -e "${RED}✗ Python 3 未安裝，請先安裝 Python 3.9+${NC}"
    exit 1
fi

# 創建虛擬環境
echo -e "\n${YELLOW}創建 Python 虛擬環境...${NC}"
python3 -m venv venv
echo -e "${GREEN}✓ 虛擬環境創建完成${NC}"

# 激活虛擬環境並安裝依賴
echo -e "\n${YELLOW}安裝項目依賴...${NC}"
source venv/bin/activate
pip install --upgrade pip
pip install -r requirements.txt
echo -e "${GREEN}✓ 依賴安裝完成${NC}"

# 創建環境變量文件
if [ ! -f .env ]; then
    echo -e "\n${YELLOW}創建環境變量文件...${NC}"
    cp .env.example .env
    echo -e "${GREEN}✓ .env 文件已創建${NC}"
    echo -e "${YELLOW}⚠️  請編輯 .env 文件，填入您的 API 密鑰和配置${NC}"
fi

# 檢查 Docker
echo -e "\n${YELLOW}檢查 Docker...${NC}"
if command -v docker &> /dev/null; then
    echo -e "${GREEN}✓ Docker 已安裝${NC}"
    
    # 檢查 Docker Compose
    if command -v docker-compose &> /dev/null; then
        echo -e "${GREEN}✓ Docker Compose 已安裝${NC}"
    else
        echo -e "${YELLOW}⚠️  Docker Compose 未安裝，某些功能可能無法使用${NC}"
    fi
else
    echo -e "${YELLOW}⚠️  Docker 未安裝，將使用本地模式運行${NC}"
fi

# 創建必要的目錄
echo -e "\n${YELLOW}創建項目目錄...${NC}"
mkdir -p logs
mkdir -p data
echo -e "${GREEN}✓ 目錄創建完成${NC}"

# 顯示下一步指示
echo -e "\n${GREEN}========================================${NC}"
echo -e "${GREEN}✅ 項目設置完成！${NC}"
echo -e "${GREEN}========================================${NC}"
echo -e "\n接下來的步驟："
echo -e "1. 編輯 ${YELLOW}.env${NC} 文件，配置必要的環境變量"
echo -e "2. 運行 ${YELLOW}./scripts/start.sh${NC} 啟動應用"
echo -e "3. 訪問 ${YELLOW}http://localhost:8000${NC} 查看 API 文檔"
echo -e "\n測試命令："
echo -e "${YELLOW}curl -X POST http://localhost:8000/test/send_message \\
  -H \"Content-Type: application/json\" \\
  -d '{\"sender_id\": \"test_user\", \"content\": \"我想了解體檢套餐\"}'${NC}"