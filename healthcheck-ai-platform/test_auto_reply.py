#!/usr/bin/env python3
"""
測試自動回覆系統
"""
import asyncio
from src.core.auto_reply_handler import AutoReplyHandler
from src.core.conversation_manager import ConversationManager
from src.integrations.mock_whatsapp import MockWhatsAppClient
from src.integrations.messaging_interface import Message
from datetime import datetime

async def test_auto_reply():
    """測試自動回覆功能"""
    # 初始化自動回覆處理器
    auto_reply = AutoReplyHandler()
    
    # 測試案例
    test_messages = [
        "你好，我想查詢森仁醫健體檢服務/優惠 - 多人同行體檢優惠",
        "了解130項全面健康體檢套餐詳情",
        "查詢森仁醫健體檢服務/優惠 - 尊尚健康檢查計劃",
        "請問全面檢查套餐多少錢？",
        "了解110項特選健康檢查計劃詳細",
        "收費大概係？",
        "我想同朋友一齊做體檢，有優惠嗎？",
        "地址在哪裡？",
        "請問有冇其他時間？",
        "報告幾時有？",
        "女士體檢套餐有什麼？",
    ]
    
    print("=== 測試自動回覆系統 ===\n")
    
    for i, msg in enumerate(test_messages, 1):
        print(f"\n測試 {i}: {msg}")
        print("-" * 50)
        
        # 檢查是否應該自動回覆
        should_reply = auto_reply.should_auto_reply(msg)
        print(f"應該自動回覆: {should_reply}")
        
        if should_reply:
            # 匹配關鍵詞
            result = auto_reply.match_keywords(msg)
            if result:
                template_key, template_content = result
                print(f"匹配模板: {template_key}")
                
                # 格式化回覆
                response = auto_reply.format_response(template_content)
                print(f"\n自動回覆內容:\n{response[:200]}...")  # 只顯示前200字符
            else:
                print("未找到匹配的模板")
        
        print("=" * 80)
        
async def test_conversation_flow():
    """測試完整對話流程"""
    print("\n\n=== 測試完整對話流程 ===\n")
    
    # 初始化模擬客戶端和對話管理器
    mock_client = MockWhatsAppClient()
    conversation_manager = ConversationManager(mock_client)
    
    # 模擬客戶消息
    test_conversations = [
        {
            "customer_id": "85212345678",
            "messages": [
                "查詢多人同行優惠",
                "我們有3個人想一起檢查",
                "130項全面計劃適合嗎？",
            ]
        },
        {
            "customer_id": "85298765432",
            "messages": [
                "收費大概係？",
                "女士體檢有什麼項目？",
                "地址在哪？",
            ]
        }
    ]
    
    for convo in test_conversations:
        customer_id = convo["customer_id"]
        print(f"\n客戶 {customer_id} 的對話:")
        print("-" * 50)
        
        for msg_content in convo["messages"]:
            # 創建消息對象
            message = Message(
                id=f"msg_{datetime.now().timestamp()}",
                sender_id=customer_id,
                content=msg_content,
                timestamp=datetime.now()
            )
            
            print(f"\n客戶: {msg_content}")
            
            # 處理消息
            await conversation_manager.handle_message(message)
            
            # 獲取發送的回覆
            if mock_client.sent_messages:
                last_response = mock_client.sent_messages[-1]
                print(f"系統: {last_response['content'][:200]}...")
            
            await asyncio.sleep(0.5)  # 模擬延遲

if __name__ == "__main__":
    asyncio.run(test_auto_reply())
    asyncio.run(test_conversation_flow())