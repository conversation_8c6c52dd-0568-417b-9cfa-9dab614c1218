#!/usr/bin/env python3
"""
快速測試腳本 - 檢查代碼完整性
不需要完整的依賴安裝
"""

import os
import sys
import importlib.util
from pathlib import Path

# 設置 Python 路徑
sys.path.append(str(Path(__file__).parent))

def test_file_structure():
    """測試文件結構是否完整"""
    print("📁 檢查文件結構...")
    
    required_files = [
        "src/main.py",
        "src/agents/base_agent.py",
        "src/agents/traffic_reception_agent.py",
        "src/agents/pricing_calculator_agent.py",
        "src/agents/sales_progression_agent.py",
        "src/agents/follow_up_strategy_agent.py",
        "src/agents/quality_monitor_agent.py",
        "src/core/conversation_manager.py",
        "src/core/cache.py",
        "src/core/rules_engine.py",
        "src/database/models.py",
        "src/database/connection.py",
        "src/integrations/llm_client.py",
        "src/integrations/messaging_interface.py",
        "src/integrations/mock_whatsapp.py",
        "src/monitoring/metrics.py",
        "src/monitoring/logging_config.py",
        "config/agents/traffic_reception.yaml",
        "config/agents/pricing_calculator.yaml",
        "config/agents/sales_progression.yaml",
        "config/agents/follow_up_strategy.yaml",
        "config/agents/quality_monitor.yaml",
    ]
    
    missing_files = []
    for file_path in required_files:
        if not os.path.exists(file_path):
            missing_files.append(file_path)
            print(f"   ❌ 缺失: {file_path}")
        else:
            print(f"   ✅ 存在: {file_path}")
    
    if missing_files:
        print(f"\n❌ 缺失 {len(missing_files)} 個文件")
        return False
    else:
        print("\n✅ 所有文件結構完整")
        return True

def test_module_imports():
    """測試是否可以成功導入模塊（不執行）"""
    print("\n🔧 檢查模塊導入...")
    
    modules_to_test = [
        ("src.agents.base_agent", "BaseHealthCheckAgent"),
        ("src.core.conversation_manager", "ConversationState"),
        ("src.database.models", "Customer"),
        ("src.integrations.messaging_interface", "MessagingInterface"),
    ]
    
    failed_imports = []
    for module_name, class_name in modules_to_test:
        try:
            spec = importlib.util.spec_from_file_location(
                module_name, 
                module_name.replace(".", "/") + ".py"
            )
            if spec and spec.loader:
                module = importlib.util.module_from_spec(spec)
                print(f"   ✅ 可導入: {module_name}.{class_name}")
            else:
                print(f"   ❌ 無法找到: {module_name}")
                failed_imports.append(module_name)
        except Exception as e:
            print(f"   ❌ 導入失敗: {module_name} - {str(e)}")
            failed_imports.append(module_name)
    
    if failed_imports:
        print(f"\n❌ {len(failed_imports)} 個模塊導入失敗")
        return False
    else:
        print("\n✅ 所有核心模塊可導入")
        return True

def test_config_files():
    """測試配置文件是否可讀"""
    print("\n📋 檢查配置文件...")
    
    import yaml
    
    config_files = [
        "config/agents/traffic_reception.yaml",
        "config/agents/pricing_calculator.yaml",
        "config/agents/sales_progression.yaml",
        "config/agents/follow_up_strategy.yaml",
        "config/agents/quality_monitor.yaml",
    ]
    
    failed_configs = []
    for config_file in config_files:
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)
                if config and 'role' in config:
                    print(f"   ✅ 配置有效: {config_file} - Role: {config['role']}")
                else:
                    print(f"   ❌ 配置無效: {config_file}")
                    failed_configs.append(config_file)
        except Exception as e:
            print(f"   ❌ 讀取失敗: {config_file} - {str(e)}")
            failed_configs.append(config_file)
    
    if failed_configs:
        print(f"\n❌ {len(failed_configs)} 個配置文件有問題")
        return False
    else:
        print("\n✅ 所有配置文件正常")
        return True

def test_main_app():
    """測試主應用是否可以啟動"""
    print("\n🚀 檢查主應用...")
    
    try:
        # 只檢查是否可以導入，不實際運行
        spec = importlib.util.spec_from_file_location("main", "src/main.py")
        if spec and spec.loader:
            print("   ✅ 主應用文件可加載")
            
            # 檢查是否定義了必要的端點
            with open("src/main.py", 'r') as f:
                content = f.read()
                endpoints = [
                    "@app.get",
                    "@app.post",
                    "/webhook",
                    "/test/conversation"
                ]
                
                for endpoint in endpoints:
                    if endpoint in content:
                        print(f"   ✅ 找到端點: {endpoint}")
                    else:
                        print(f"   ❌ 缺少端點: {endpoint}")
            
            return True
        else:
            print("   ❌ 無法加載主應用")
            return False
    except Exception as e:
        print(f"   ❌ 檢查失敗: {str(e)}")
        return False

def main():
    """主測試函數"""
    print("🏥 HealthCheck AI Platform - 代碼完整性測試")
    print("=" * 50)
    
    all_tests_passed = True
    
    # 運行各項測試
    all_tests_passed &= test_file_structure()
    all_tests_passed &= test_module_imports()
    all_tests_passed &= test_config_files()
    all_tests_passed &= test_main_app()
    
    print("\n" + "=" * 50)
    if all_tests_passed:
        print("✅ 所有測試通過！代碼結構完整。")
        print("\n下一步：")
        print("1. 完成完整的依賴安裝: ./setup.sh")
        print("2. 配置環境變量: cp .env.example .env")
        print("3. 啟動應用: ./scripts/start.sh")
    else:
        print("❌ 有測試失敗，請檢查上述錯誤。")
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())