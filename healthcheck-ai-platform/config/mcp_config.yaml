# MCP WhatsApp 整合配置

mcp:
  # MCP 服務器設置
  server:
    host: "localhost"
    port: 8080
    transport: "stdio"
  
  # WhatsApp 相關設置
  whatsapp:
    # 發送消息的延遲設置（秒）
    message_delays:
      min_delay: 1.5
      max_delay: 4.0
      typing_speed: 200  # 每分鐘字符數
    
    # 媒體文件設置
    media:
      max_file_size: 16777216  # 16MB
      allowed_types:
        - "image/jpeg"
        - "image/png"
        - "application/pdf"
        - "audio/mpeg"
        - "video/mp4"

# 防機器人偵測設置
anti_bot:
  # 行為分析閾值
  thresholds:
    risk_score_high: 0.7
    risk_score_medium: 0.5
    risk_score_low: 0.3
  
  # 速率限制
  rate_limiting:
    max_messages_per_minute: 10
    max_messages_per_hour: 200
    burst_limit: 5
    cooldown_seconds: 30
  
  # 人性化設置
  humanization:
    typo_probability: 0.1  # 10% 機會出現錯字
    emotion_probability: 0.2  # 20% 機會加入情感表達
    correction_patterns:
      - "*{correction}"
      - "我是說{correction}"
      - "更正：{correction}"
    
    # 情感表達庫
    emotions:
      positive:
        - "😊"
        - "👍"
        - "很高興能幫助您"
        - "太好了"
      thinking:
        - "嗯..."
        - "讓我看看"
        - "稍等一下"
        - "我查一下"
      acknowledgment:
        - "明白了"
        - "了解"
        - "好的"
        - "收到"

# 對話記錄設置
conversation_logging:
  # 數據混淆
  obfuscation:
    enabled: true
    patterns:
      phone_number: "\\d{8,15}"
      credit_card: "\\d{4}[\\s-]?\\d{4}[\\s-]?\\d{4}[\\s-]?\\d{4}"
      email: "[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}"
  
  # 會話追蹤
  session_tracking:
    enabled: true
    session_timeout_minutes: 30
    track_mouse_movements: false  # 不追蹤鼠標移動以減少數據量
    track_click_patterns: true
    track_scroll_behavior: true

# 業務規則
business_rules:
  # 工作時間（香港時間）
  working_hours:
    start: "09:00"
    end: "18:00"
    timezone: "Asia/Hong_Kong"
    working_days:
      - "Monday"
      - "Tuesday"
      - "Wednesday"
      - "Thursday"
      - "Friday"
  
  # 自動回覆設置
  auto_reply:
    outside_hours_message: |
      感謝您的留言。我們的服務時間是週一至週五 9:00-18:00。
      我們會在下一個工作日盡快回覆您。
    
    busy_message: |
      感謝您的耐心等待。我們的客服正在為其他客戶服務，
      稍後會盡快回覆您。

# 監控和警報
monitoring:
  # 性能指標
  metrics:
    enable_tracking: true
    report_interval_minutes: 5
  
  # 警報設置
  alerts:
    high_risk_score_threshold: 0.8
    suspicious_activity_count: 10
    notification_channels:
      - "email"
      - "slack"