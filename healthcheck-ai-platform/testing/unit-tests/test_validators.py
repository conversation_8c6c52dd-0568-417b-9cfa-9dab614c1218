"""
驗證器單元測試
測試輸入驗證、輸出驗證、知識驗證和回退處理
"""
import pytest
import asyncio
from datetime import datetime
from unittest.mock import Mock, patch

# 導入待測試的模塊
import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent.parent.parent))

from src.core.input_validator import InputValidator, ValidationLevel, ValidationResult
from src.core.output_validator import OutputValidator, OutputValidationResult
from src.core.knowledge_validator import KnowledgeValidator, ValidationReport
from src.core.fallback_handler import FallbackHandler, ConfidenceLevel, EscalationReason

class TestInputValidator:
    """測試輸入驗證器"""
    
    def setup_method(self):
        self.validator = InputValidator()
    
    def test_valid_input(self):
        """測試有效輸入"""
        result = self.validator.validate_input("我想了解體檢套餐的價格")
        assert result.is_valid == True
        assert result.sanitized_input is not None
        
    def test_sql_injection_detection(self):
        """測試SQL注入檢測"""
        malicious_inputs = [
            "'; DROP TABLE users; --",
            "1' OR '1'='1",
            "SELECT * FROM customers WHERE id=1",
            "1; DELETE FROM packages;"
        ]
        
        for input_text in malicious_inputs:
            result = self.validator.validate_input(input_text)
            assert result.is_valid == False
            assert result.level == ValidationLevel.CRITICAL
            assert "SQL injection" in result.message
    
    def test_xss_detection(self):
        """測試XSS攻擊檢測"""
        xss_inputs = [
            "<script>alert('XSS')</script>",
            "<iframe src='evil.com'></iframe>",
            "javascript:alert(1)",
            "<img src=x onerror=alert(1)>"
        ]
        
        for input_text in xss_inputs:
            result = self.validator.validate_input(input_text)
            assert result.is_valid == False
            assert result.level == ValidationLevel.CRITICAL
            assert "XSS" in result.message
    
    def test_sensitive_words_detection(self):
        """測試敏感詞檢測"""
        result = self.validator.validate_input("這個藥物可以治癒所有疾病")
        assert result.is_valid == False
        assert "Sensitive content" in result.message
    
    def test_message_length_validation(self):
        """測試消息長度驗證"""
        # 太長的消息
        long_message = "a" * 1001
        result = self.validator.validate_input(long_message)
        assert result.is_valid == False
        assert "too long" in result.message
        
        # 空消息
        result = self.validator.validate_input("   ")
        assert result.is_valid == False
        assert "too short" in result.message
    
    def test_intent_extraction(self):
        """測試意圖提取"""
        test_cases = [
            ("請問全面計劃的價格是多少？", "price_inquiry"),
            ("我想預約明天下午的體檢", "appointment_booking"),
            ("精選計劃和全面計劃有什麼區別？", "package_comparison"),
            ("你們在銅鑼灣的地址是什麼？", "location_inquiry"),
            ("你好", "general_inquiry")
        ]
        
        for input_text, expected_intent in test_cases:
            intent_info = self.validator.extract_intent(input_text)
            assert intent_info["primary_intent"] == expected_intent
    
    def test_phone_number_validation(self):
        """測試電話號碼驗證"""
        valid_phones = ["98765432", "5123 4567", "+852 6789 0123"]
        invalid_phones = ["123", "abcd1234", "12345678901"]
        
        for phone in valid_phones:
            is_valid, normalized = self.validator.validate_phone_number(phone)
            assert is_valid == True
            assert len(normalized) == 8
            
        for phone in invalid_phones:
            is_valid, normalized = self.validator.validate_phone_number(phone)
            assert is_valid == False

class TestOutputValidator:
    """測試輸出驗證器"""
    
    def setup_method(self):
        self.validator = OutputValidator()
    
    def test_price_accuracy(self):
        """測試價格準確性驗證"""
        # 正確的價格
        response = "精選計劃的價格是 $1,950"
        result = self.validator.validate_output(response)
        assert result.is_valid == True
        
        # 錯誤的價格
        response = "精選計劃的價格是 $2,500"
        result = self.validator.validate_output(response)
        assert result.is_valid == False
        assert len(result.issues) > 0
        assert any("price" in issue.lower() for issue in result.issues)
    
    def test_package_info_accuracy(self):
        """測試套餐信息準確性"""
        # 錯誤的項目數
        response = "全面計劃包含150項檢查"
        result = self.validator.validate_output(response)
        assert result.is_valid == False
        assert any("130 items" in issue for issue in result.issues)
    
    def test_forbidden_promises(self):
        """測試禁止承諾檢測"""
        forbidden_responses = [
            "我們保證能治癒您的疾病",
            "這是絕對最好的選擇",
            "百分百準確的檢查結果"
        ]
        
        for response in forbidden_responses:
            result = self.validator.validate_output(response)
            assert result.is_valid == False
            assert any("forbidden promise" in issue.lower() for issue in result.issues)
            assert result.requires_human_review == True
    
    def test_medical_restrictions(self):
        """測試醫療限制檢測"""
        response = "根據您的症狀，我建議您使用這種治療方案"
        result = self.validator.validate_output(response)
        assert result.is_valid == False
        assert any("medical restriction" in issue.lower() for issue in result.issues)
    
    def test_safe_response_generation(self):
        """測試安全回應生成"""
        original = "精選計劃價格是 $2,500，保證效果最好！"
        validation_result = self.validator.validate_output(original)
        safe_response = self.validator.generate_safe_response(original, validation_result)
        
        assert "$2,500" not in safe_response
        assert "保證" not in safe_response
        assert "以上信息僅供參考" in safe_response

class TestKnowledgeValidator:
    """測試知識驗證器"""
    
    def setup_method(self):
        self.validator = KnowledgeValidator()
    
    def test_accurate_response_validation(self):
        """測試準確回應的驗證"""
        response = "精選計劃的價格是 $1,950，包含110項檢查"
        report = self.validator.validate_response(response, {})
        
        assert report.is_valid == True
        assert report.accuracy_score > 0.8
        assert len(report.matched_items) > 0
    
    def test_inaccurate_response_detection(self):
        """測試不準確回應的檢測"""
        response = "精選計劃的價格是 $2,500，包含150項檢查"
        report = self.validator.validate_response(response, {})
        
        assert report.is_valid == False
        assert len(report.conflicting_info) > 0
        assert report.accuracy_score < 0.8
    
    def test_missing_info_detection(self):
        """測試遺漏信息檢測"""
        response = "我們有體檢套餐"  # 沒有具體信息
        context = {"intent": "price_inquiry"}
        report = self.validator.validate_response(response, context)
        
        assert len(report.missing_info) > 0
        assert any("價格" in info for info in report.missing_info)
    
    def test_knowledge_retrieval(self):
        """測試知識檢索"""
        # 獲取套餐價格
        price = self.validator.get_accurate_info("精選計劃", "price")
        assert price == 1950
        
        # 獲取完整信息
        info = self.validator.get_accurate_info("全面計劃")
        assert info["price"] == 2950
        assert info["items"] == 130

class TestFallbackHandler:
    """測試回退處理器"""
    
    def setup_method(self):
        self.handler = FallbackHandler()
    
    @pytest.mark.asyncio
    async def test_confidence_evaluation(self):
        """測試置信度評估"""
        user_input = "我想了解體檢套餐價格"
        ai_output = "精選計劃的價格是 $1,950"
        context = {"customer_id": "test123", "message_count": 5}
        
        score = await self.handler.evaluate_confidence(user_input, ai_output, context)
        
        assert isinstance(score.overall_score, float)
        assert 0 <= score.overall_score <= 1
        assert score.level in [ConfidenceLevel.HIGH, ConfidenceLevel.MEDIUM, 
                               ConfidenceLevel.LOW, ConfidenceLevel.CRITICAL]
    
    @pytest.mark.asyncio
    async def test_human_request_detection(self):
        """測試人工服務請求檢測"""
        human_requests = [
            "我要找人工客服",
            "請轉接真人",
            "不要機器人回答"
        ]
        
        for request in human_requests:
            decision = await self.handler.make_fallback_decision(
                request, "任何回應", {}
            )
            assert decision.should_fallback == True
            assert decision.escalation_reason == EscalationReason.CUSTOMER_REQUEST
    
    @pytest.mark.asyncio
    async def test_sensitive_topic_detection(self):
        """測試敏感話題檢測"""
        sensitive_input = "我要投訴你們的服務"
        decision = await self.handler.make_fallback_decision(
            sensitive_input, "抱歉給您帶來不便", {}
        )
        
        assert decision.should_fallback == True
        assert decision.escalation_reason == EscalationReason.SENSITIVE_TOPIC
    
    @pytest.mark.asyncio
    async def test_low_confidence_handling(self):
        """測試低置信度處理"""
        # 模擬低置信度場景
        with patch.object(self.handler, 'evaluate_confidence') as mock_eval:
            from src.core.fallback_handler import ConfidenceScore
            mock_score = ConfidenceScore(
                overall_score=0.3,
                input_score=0.5,
                output_score=0.2,
                context_score=0.3,
                history_score=0.2,
                level=ConfidenceLevel.CRITICAL,
                factors={}
            )
            mock_eval.return_value = mock_score
            
            decision = await self.handler.make_fallback_decision(
                "複雜的醫療問題", "不確定的回答", {}
            )
            
            assert decision.should_fallback == True
            assert decision.escalation_reason == EscalationReason.LOW_CONFIDENCE
    
    def test_repeated_failure_detection(self):
        """測試重複失敗檢測"""
        customer_id = "test_customer"
        
        # 記錄多次失敗
        for _ in range(4):
            self.handler.record_failure(customer_id)
        
        # 檢查是否檢測到重複失敗
        is_repeated = self.handler._check_repeated_failures(customer_id)
        assert is_repeated == True

class TestIntegration:
    """集成測試"""
    
    @pytest.mark.asyncio
    async def test_full_validation_pipeline(self):
        """測試完整的驗證流程"""
        input_validator = InputValidator()
        output_validator = OutputValidator()
        knowledge_validator = KnowledgeValidator()
        fallback_handler = FallbackHandler()
        
        # 測試場景1：正常查詢
        user_input = "請問全面計劃的價格是多少？"
        
        # 輸入驗證
        input_result = input_validator.validate_input(user_input)
        assert input_result.is_valid == True
        
        # 模擬AI回應
        ai_response = "全面計劃的優惠價格是 $2,950，包含130項檢查。"
        
        # 輸出驗證
        output_result = output_validator.validate_output(ai_response)
        assert output_result.is_valid == True
        
        # 知識驗證
        knowledge_result = knowledge_validator.validate_response(ai_response, {})
        assert knowledge_result.is_valid == True
        
        # 置信度評估
        confidence = await fallback_handler.evaluate_confidence(
            user_input, ai_response, {}
        )
        assert confidence.overall_score > 0.7
        
    @pytest.mark.asyncio
    async def test_error_scenario(self):
        """測試錯誤場景"""
        input_validator = InputValidator()
        output_validator = OutputValidator()
        fallback_handler = FallbackHandler()
        
        # 測試場景2：錯誤信息
        user_input = "精選計劃多少錢"
        ai_response = "精選計劃的價格是 $3,000，絕對物超所值！"
        
        # 輸出驗證應該失敗
        output_result = output_validator.validate_output(ai_response)
        assert output_result.is_valid == False
        
        # 應該觸發回退
        decision = await fallback_handler.make_fallback_decision(
            user_input, ai_response, {}
        )
        
        # 根據錯誤嚴重程度，可能需要人工介入
        assert decision.confidence_score.overall_score < 0.8

if __name__ == "__main__":
    pytest.main([__file__, "-v"])