import asyncio
import aiohttp
import json
from typing import Dict, Any, List

class ConversationTester:
    """對話流程測試器"""
    
    def __init__(self, base_url: str = "http://localhost:8000"):
        self.base_url = base_url
        self.test_scenarios = self._load_test_scenarios()
    
    def _load_test_scenarios(self) -> Dict[str, Any]:
        """加載測試場景"""
        return {
            "new_customer_basic": {
                "name": "新客戶諮詢基礎套餐",
                "customer_id": "test_new_001",
                "messages": [
                    "你好，我想了解一下體檢套餐",
                    "1",  # 選擇個人體檢
                    "基礎體檢多少錢？",
                    "好的，我想預約"
                ]
            },
            "price_comparison": {
                "name": "價格比較場景",
                "customer_id": "test_price_001",
                "messages": [
                    "我想比較一下不同的體檢套餐",
                    "標準和尊享套餐有什麼區別？",
                    "如果我帶家人一起來有優惠嗎？"
                ]
            },
            "promotion_inquiry": {
                "name": "優惠查詢場景",
                "customer_id": "test_promo_001",
                "messages": [
                    "現在有什麼優惠活動嗎？",
                    "新客戶有特別優惠嗎？",
                    "我想要最划算的套餐"
                ]
            }
        }
    
    async def run_scenario(self, scenario_key: str) -> Dict[str, Any]:
        """運行測試場景"""
        scenario = self.test_scenarios.get(scenario_key)
        if not scenario:
            return {"error": f"場景 {scenario_key} 不存在"}
        
        results = {
            "scenario": scenario["name"],
            "customer_id": scenario["customer_id"],
            "interactions": []
        }
        
        async with aiohttp.ClientSession() as session:
            for i, message in enumerate(scenario["messages"]):
                # 發送消息
                response = await self._send_message(
                    session,
                    scenario["customer_id"],
                    message
                )
                
                results["interactions"].append({
                    "turn": i + 1,
                    "user_message": message,
                    "bot_responses": self._extract_bot_responses(response)
                })
                
                # 模擬用戶思考時間
                await asyncio.sleep(2)
        
        return results
    
    async def _send_message(
        self,
        session: aiohttp.ClientSession,
        customer_id: str,
        content: str
    ) -> Dict[str, Any]:
        """發送測試消息"""
        url = f"{self.base_url}/test/send_message"
        data = {
            "sender_id": customer_id,
            "content": content
        }
        
        async with session.post(url, json=data) as response:
            return await response.json()
    
    def _extract_bot_responses(self, response: Dict[str, Any]) -> List[str]:
        """提取機器人回覆"""
        messages = response.get("conversation", [])
        bot_messages = [
            msg["content"]
            for msg in messages
            if msg["sender_id"] == "business"
        ]
        # 返回最新的回覆
        return bot_messages[-2:] if len(bot_messages) > 1 else bot_messages

async def main():
    """運行測試"""
    tester = ConversationTester()
    
    # 測試所有場景
    for scenario_key in tester.test_scenarios.keys():
        print(f"\n{'='*50}")
        print(f"運行場景: {scenario_key}")
        print(f"{'='*50}")
        
        results = await tester.run_scenario(scenario_key)
        
        # 打印結果
        for interaction in results["interactions"]:
            print(f"\n回合 {interaction['turn']}:")
            print(f"用戶: {interaction['user_message']}")
            for response in interaction['bot_responses']:
                print(f"系統: {response[:100]}...")  # 只顯示前100字符
        
        await asyncio.sleep(3)  # 場景之間暫停

if __name__ == "__main__":
    asyncio.run(main())