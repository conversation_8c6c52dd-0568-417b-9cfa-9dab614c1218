# HealthCheck AI Sales & Service Platform

## 🏥 森仁醫健智能客服系統

一個基於多智能體架構的 WhatsApp 自動化銷售與服務系統，專為體檢行業設計。

### 🎯 核心特性

- **5 個專業 AI Agents**：流量接待、價格計算、銷售推進、跟進策略、質量監控
- **24/7 全天候服務**：零人工成本，毫秒級響應
- **智能定價系統**：實時計算複雜優惠組合，100% 準確
- **個性化服務**：基於客戶畫像提供定制化體驗
- **持續優化**：通過數據分析不斷提升轉化率

### 🚀 快速開始

#### 1. 環境準備

```bash
# 克隆項目
git clone https://github.com/your-org/healthcheck-ai-platform.git
cd healthcheck-ai-platform

# 創建虛擬環境
python3 -m venv venv
source venv/bin/activate  # Windows: venv\Scripts\activate

# 安裝依賴
pip install -r requirements.txt

# 複製環境變量
cp .env.example .env
# 編輯 .env 文件，填入你的配置
```

#### 2. 使用 Docker Compose 啟動

```bash
# 啟動所有服務
docker-compose up -d

# 查看日誌
docker-compose logs -f app
```

#### 3. 本地開發

```bash
# 啟動數據庫和緩存
docker-compose up -d postgres redis

# 運行應用
python src/main.py
```

### 📡 API 端點

- `GET /` - 健康檢查
- `POST /webhook/whatsapp` - WhatsApp webhook
- `POST /test/send_message` - 測試消息發送
- `GET /conversations/{customer_id}` - 獲取對話歷史
- `GET /health` - 詳細健康狀態
- `GET /metrics` - Prometheus 指標

### 🧪 測試

```bash
# 運行對話流程測試
python testing/test_conversation_flow.py

# 運行單元測試
pytest testing/unit-tests/

# 測試單個對話
curl -X POST http://localhost:8000/test/send_message \
  -H "Content-Type: application/json" \
  -d '{"sender_id": "test_user", "content": "我想了解體檢套餐"}'
```

### 📊 監控

- **Prometheus**: http://localhost:9090
- **Grafana**: http://localhost:3000 (admin/admin)
- **健康檢查**: http://localhost:8000/health

### 🏗️ 項目結構

```
healthcheck-ai-platform/
├── src/
│   ├── agents/          # AI Agents 實現
│   ├── core/           # 核心功能
│   ├── database/       # 數據模型
│   ├── integrations/   # 外部集成
│   ├── monitoring/     # 監控系統
│   └── utils/          # 工具類
├── config/             # 配置文件
├── testing/            # 測試用例
├── deployment/         # 部署配置
└── docs/              # 文檔
```

### 🔧 配置說明

主要配置項（.env 文件）：

- `OPENAI_API_KEY` - OpenAI API 密鑰
- `DATABASE_URL` - PostgreSQL 連接字符串
- `REDIS_HOST` - Redis 服務器地址
- `WHATSAPP_API_KEY` - WhatsApp Business API 密鑰（當可用時）

### 📈 性能指標

- **響應時間**: < 2 秒
- **準確率**: > 98%
- **並發處理**: 1000+ 對話/分鐘
- **可用性**: 99.9%

### 🤝 貢獻指南

1. Fork 項目
2. 創建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 開啟 Pull Request

### 📄 許可證

本項目採用 MIT 許可證 - 查看 [LICENSE](LICENSE) 文件了解詳情

### 👥 團隊

- 產品設計：森仁醫健產品團隊
- 技術實現：AI 工程團隊
- 項目管理：數字化轉型團隊

### 📞 聯繫方式

- 技術支持：<EMAIL>
- 商務合作：<EMAIL>