version: '3.8'

services:
  app:
    build: .
    container_name: healthcheck-ai-app
    ports:
      - "8000:8000"
    environment:
      - ENVIRONMENT=development
      - DATABASE_URL=****************************************************/healthcheck_ai
      - REDIS_HOST=redis
      - REDIS_PORT=6379
    depends_on:
      - postgres
      - redis
    volumes:
      - ./src:/app/src
      - ./config:/app/config
    networks:
      - healthcheck-network

  postgres:
    image: postgres:15
    container_name: healthcheck-postgres
    environment:
      POSTGRES_DB: healthcheck_ai
      POSTGRES_USER: healthcheck_user
      POSTGRES_PASSWORD: password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
    networks:
      - healthcheck-network

  redis:
    image: redis:7-alpine
    container_name: healthcheck-redis
    ports:
      - "6379:6379"
    networks:
      - healthcheck-network

  prometheus:
    image: prom/prometheus:latest
    container_name: healthcheck-prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./deployment/monitoring/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    networks:
      - healthcheck-network

  grafana:
    image: grafana/grafana:latest
    container_name: healthcheck-grafana
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin
    volumes:
      - grafana_data:/var/lib/grafana
    networks:
      - healthcheck-network

volumes:
  postgres_data:
  prometheus_data:
  grafana_data:

networks:
  healthcheck-network:
    driver: bridge