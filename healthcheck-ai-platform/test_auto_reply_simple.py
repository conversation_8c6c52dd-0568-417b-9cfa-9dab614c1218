#!/usr/bin/env python3
"""
簡單測試自動回覆系統（無需依賴）
"""
import json
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.core.auto_reply_handler import <PERSON>ReplyHandler

def test_auto_reply():
    """測試自動回覆功能"""
    # 初始化自動回覆處理器
    auto_reply = AutoReplyHandler()
    
    # 測試案例
    test_messages = [
        "你好，我想查詢森仁醫健體檢服務/優惠 - 多人同行體檢優惠",
        "了解130項全面健康體檢套餐詳情",
        "查詢森仁醫健體檢服務/優惠 - 尊尚健康檢查計劃",
        "請問全面檢查套餐多少錢？",
        "了解110項特選健康檢查計劃詳細",
        "收費大概係？",
        "我想同朋友一齊做體檢，有優惠嗎？",
        "地址在哪裡？",
        "請問有冇其他時間？",
        "報告幾時有？",
        "女士體檢套餐有什麼？",
        "好的",  # 測試不應該自動回覆的情況
        "1",     # 測試不應該自動回覆的情況
    ]
    
    print("=== 測試自動回覆系統 ===\n")
    
    success_count = 0
    total_count = len(test_messages)
    
    for i, msg in enumerate(test_messages, 1):
        print(f"\n測試 {i}/{total_count}: {msg}")
        print("-" * 80)
        
        # 檢查是否應該自動回覆
        should_reply = auto_reply.should_auto_reply(msg)
        print(f"應該自動回覆: {'是' if should_reply else '否'}")
        
        if should_reply:
            # 匹配關鍵詞
            result = auto_reply.match_keywords(msg)
            if result:
                template_key, template_content = result
                print(f"✓ 匹配模板: {template_key}")
                
                # 格式化回覆
                response = auto_reply.format_response(template_content)
                print(f"\n自動回覆預覽:")
                print("-" * 40)
                # 顯示前3行
                lines = response.split('\n')
                for j, line in enumerate(lines[:3]):
                    print(f"{line}")
                if len(lines) > 3:
                    print(f"... (共 {len(lines)} 行)")
                print("-" * 40)
                
                success_count += 1
            else:
                print("✗ 未找到匹配的模板")
        else:
            # 對於不應該自動回覆的消息，這是正確的行為
            if msg in ["好的", "1"]:
                print("✓ 正確：簡短回應不需要自動回覆")
                success_count += 1
    
    print("\n" + "=" * 80)
    print(f"\n測試結果: {success_count}/{total_count} 成功")
    print(f"成功率: {success_count/total_count*100:.1f}%")
    
    # 顯示所有可用的模板鍵
    print("\n可用的自動回覆模板:")
    print("-" * 40)
    for pattern in auto_reply.keyword_patterns:
        print(f"- {pattern.template_key} (優先級: {pattern.priority})")
        print(f"  關鍵詞: {', '.join(pattern.keywords[:5])}{'...' if len(pattern.keywords) > 5 else ''}")

if __name__ == "__main__":
    test_auto_reply()