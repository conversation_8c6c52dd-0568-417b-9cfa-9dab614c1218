#!/usr/bin/env python3
"""
WhatsApp 聊天記錄深度分析腳本
使用增強型銷售代理系統分析歷史對話數據
"""

import sqlite3
import asyncio
import json
from datetime import datetime, timedelta
from typing import Dict, Any, List, Tuple
from collections import defaultdict
import pandas as pd

# 假設已設置相關路徑
import sys
sys.path.append('/Users/<USER>/Desktop/Claude/healthcheck-ai-platform')

from src.agents.enhanced_sales_orchestrator import EnhancedSalesOrchestrator


class WhatsAppDataAnalyzer:
    """WhatsApp 數據深度分析器"""
    
    def __init__(self, db_path: str):
        self.db_path = db_path
        self.orchestrator = EnhancedSalesOrchestrator()
        self.conn = sqlite3.connect(db_path)
        self.conn.row_factory = sqlite3.Row
        
    def get_conversation_summary(self) -> Dict[str, Any]:
        """獲取對話總體摘要"""
        cursor = self.conn.cursor()
        
        # 總體統計
        total_messages = cursor.execute("SELECT COUNT(*) FROM messages").fetchone()[0]
        unique_chats = cursor.execute("SELECT COUNT(DISTINCT chat_jid) FROM messages").fetchone()[0]
        
        # 時間範圍
        time_range = cursor.execute("""
            SELECT MIN(timestamp) as start_time, MAX(timestamp) as end_time 
            FROM messages
        """).fetchone()
        
        # 發送/接收比例
        sent_received = cursor.execute("""
            SELECT 
                SUM(CASE WHEN is_from_me = 1 THEN 1 ELSE 0 END) as sent,
                SUM(CASE WHEN is_from_me = 0 THEN 1 ELSE 0 END) as received
            FROM messages
        """).fetchone()
        
        return {
            'total_messages': total_messages,
            'unique_chats': unique_chats,
            'date_range': {
                'start': time_range['start_time'],
                'end': time_range['end_time']
            },
            'message_distribution': {
                'sent': sent_received['sent'],
                'received': sent_received['received']
            }
        }
    
    def get_top_conversations(self, limit: int = 10) -> List[Dict[str, Any]]:
        """獲取最活躍的對話"""
        cursor = self.conn.cursor()
        
        conversations = cursor.execute("""
            SELECT 
                m.chat_jid,
                COUNT(*) as message_count,
                MAX(m.timestamp) as last_message,
                MIN(m.timestamp) as first_message,
                c.name as contact_name
            FROM messages m
            LEFT JOIN chats c ON m.chat_jid = c.jid
            GROUP BY m.chat_jid
            ORDER BY message_count DESC
            LIMIT ?
        """, (limit,)).fetchall()
        
        return [dict(conv) for conv in conversations]
    
    def get_conversation_history(self, chat_jid: str, limit: int = 100) -> List[Dict[str, Any]]:
        """獲取特定對話的歷史記錄"""
        cursor = self.conn.cursor()
        
        messages = cursor.execute("""
            SELECT 
                id, sender, content, timestamp, is_from_me, media_type
            FROM messages
            WHERE chat_jid = ?
            ORDER BY timestamp DESC
            LIMIT ?
        """, (chat_jid, limit)).fetchall()
        
        return [dict(msg) for msg in messages]
    
    def extract_customer_intent_patterns(self) -> Dict[str, Any]:
        """提取客戶意圖模式"""
        cursor = self.conn.cursor()
        
        # 常見問題關鍵詞
        intent_keywords = {
            'price_inquiry': ['價格', '多少錢', '費用', '收費', 'price', 'cost'],
            'appointment': ['預約', '時間', '什麼時候', '幾點', 'book', 'appointment'],
            'service_info': ['檢查', '項目', '包含', '內容', 'include', 'service'],
            'location': ['地址', '位置', '在哪', '怎麼去', 'where', 'location'],
            'health_concern': ['不舒服', '疼痛', '症狀', '健康', 'pain', 'symptom']
        }
        
        intent_counts = defaultdict(int)
        
        # 分析所有客戶消息
        customer_messages = cursor.execute("""
            SELECT content FROM messages 
            WHERE is_from_me = 0 AND content IS NOT NULL
        """).fetchall()
        
        for msg in customer_messages:
            content = msg['content'].lower() if msg['content'] else ''
            for intent, keywords in intent_keywords.items():
                if any(keyword in content for keyword in keywords):
                    intent_counts[intent] += 1
        
        return dict(intent_counts)
    
    def analyze_response_patterns(self) -> Dict[str, Any]:
        """分析回應模式"""
        cursor = self.conn.cursor()
        
        # 分析回應時間
        response_times = []
        
        conversations = cursor.execute("""
            SELECT DISTINCT chat_jid FROM messages
        """).fetchall()
        
        for conv in conversations:
            chat_jid = conv['chat_jid']
            messages = cursor.execute("""
                SELECT timestamp, is_from_me
                FROM messages
                WHERE chat_jid = ?
                ORDER BY timestamp
            """, (chat_jid,)).fetchall()
            
            # 計算回應時間
            for i in range(1, len(messages)):
                if messages[i-1]['is_from_me'] != messages[i]['is_from_me']:
                    # 發現對話切換
                    if messages[i]['is_from_me'] == 1:  # 我們的回應
                        time_diff = self._calculate_time_diff(
                            messages[i-1]['timestamp'],
                            messages[i]['timestamp']
                        )
                        if time_diff and time_diff < 3600:  # 1小時內的回應
                            response_times.append(time_diff)
        
        avg_response_time = sum(response_times) / len(response_times) if response_times else 0
        
        return {
            'average_response_time_seconds': avg_response_time,
            'total_responses_analyzed': len(response_times),
            'response_time_distribution': {
                'under_1_min': sum(1 for t in response_times if t < 60),
                'under_5_min': sum(1 for t in response_times if 60 <= t < 300),
                'under_15_min': sum(1 for t in response_times if 300 <= t < 900),
                'over_15_min': sum(1 for t in response_times if t >= 900)
            }
        }
    
    def _calculate_time_diff(self, time1: str, time2: str) -> float:
        """計算時間差（秒）"""
        try:
            t1 = datetime.fromisoformat(time1.replace('Z', '+00:00'))
            t2 = datetime.fromisoformat(time2.replace('Z', '+00:00'))
            return abs((t2 - t1).total_seconds())
        except:
            return None
    
    async def analyze_sales_conversations(self, sample_size: int = 5) -> List[Dict[str, Any]]:
        """使用增強型銷售代理分析對話"""
        # 獲取最活躍的對話進行分析
        top_conversations = self.get_top_conversations(sample_size)
        analysis_results = []
        
        for conv in top_conversations:
            chat_jid = conv['chat_jid']
            contact_name = conv.get('contact_name', 'Unknown')
            
            # 獲取對話歷史
            messages = self.get_conversation_history(chat_jid, limit=50)
            
            if len(messages) < 5:  # 對話太短，跳過
                continue
            
            # 構建對話上下文
            conversation_history = []
            for msg in reversed(messages):  # 按時間順序
                role = 'agent' if msg['is_from_me'] else 'customer'
                conversation_history.append({
                    'role': role,
                    'content': msg['content'],
                    'timestamp': msg['timestamp']
                })
            
            # 嘗試提取客戶信息
            customer_profile = self._extract_customer_profile(messages, contact_name)
            
            # 構建分析上下文
            context = {
                'conversation_id': f'WHATSAPP-{chat_jid}',
                'customer_id': chat_jid,
                'conversation_history': conversation_history[-20:],  # 最近20條
                'customer_profile': customer_profile,
                'conversation_stage': self._determine_stage(conversation_history),
                'current_sentiment': self._analyze_sentiment(conversation_history)
            }
            
            # 執行深度分析
            try:
                analysis = await self.orchestrator.analyze_conversation(
                    context,
                    analysis_depth='comprehensive'
                )
                
                analysis_results.append({
                    'chat_jid': chat_jid,
                    'contact_name': contact_name,
                    'message_count': conv['message_count'],
                    'analysis': analysis,
                    'key_insights': {
                        'success_probability': analysis['success_probability'],
                        'customer_type': analysis['synthesis']['customer_archetype']['name'],
                        'main_barriers': analysis['synthesis']['identified_risks'][:2],
                        'recommended_actions': analysis['action_plan']['immediate_actions'][:2]
                    }
                })
            except Exception as e:
                print(f"分析 {chat_jid} 時出錯: {str(e)}")
                continue
        
        return analysis_results
    
    def _extract_customer_profile(self, messages: List[Dict], contact_name: str) -> Dict[str, Any]:
        """從消息中提取客戶資料"""
        profile = {
            'name': contact_name or 'Unknown',
            'first_time_buyer': True,  # 預設
            'message_frequency': 'medium'
        }
        
        # 分析消息頻率
        if messages:
            first_msg_time = datetime.fromisoformat(messages[-1]['timestamp'].replace('Z', '+00:00'))
            last_msg_time = datetime.fromisoformat(messages[0]['timestamp'].replace('Z', '+00:00'))
            days_active = (last_msg_time - first_msg_time).days
            
            if days_active > 0:
                msg_per_day = len(messages) / days_active
                if msg_per_day > 10:
                    profile['message_frequency'] = 'high'
                elif msg_per_day < 2:
                    profile['message_frequency'] = 'low'
        
        # 嘗試識別健康關注點
        health_keywords = {
            '心臟': 'heart',
            '血壓': 'blood_pressure',
            '糖尿': 'diabetes',
            '癌': 'cancer',
            '體檢': 'general_checkup'
        }
        
        concerns = []
        all_content = ' '.join([m['content'] for m in messages if m['content'] and not m['is_from_me']])
        
        for keyword, concern in health_keywords.items():
            if keyword in all_content:
                concerns.append(concern)
        
        if concerns:
            profile['health_concerns'] = concerns[:3]  # 最多3個
        
        return profile
    
    def _determine_stage(self, history: List[Dict]) -> str:
        """判斷對話階段"""
        if len(history) < 3:
            return 'initial'
        
        recent_messages = ' '.join([m['content'] for m in history[-5:] if m['content']])
        
        if any(word in recent_messages for word in ['預約', '時間', '什麼時候', 'book']):
            return 'closing'
        elif any(word in recent_messages for word in ['價格', '多少錢', 'price']):
            return 'evaluation'
        elif any(word in recent_messages for word in ['包含', '項目', '檢查什麼']):
            return 'consideration'
        else:
            return 'discovery'
    
    def _analyze_sentiment(self, history: List[Dict]) -> str:
        """分析客戶情緒"""
        if not history:
            return 'neutral'
        
        # 獲取最近的客戶消息
        recent_customer_msgs = [
            m['content'] for m in history[-10:] 
            if m['role'] == 'customer' and m['content']
        ]
        
        if not recent_customer_msgs:
            return 'neutral'
        
        all_content = ' '.join(recent_customer_msgs)
        
        # 簡單的情緒分析
        positive_indicators = ['好', '謝謝', '可以', '明白', 'ok', 'thanks']
        negative_indicators = ['不', '貴', '算了', '再說', 'no', 'expensive']
        
        positive_count = sum(1 for ind in positive_indicators if ind in all_content.lower())
        negative_count = sum(1 for ind in negative_indicators if ind in all_content.lower())
        
        if positive_count > negative_count:
            return 'positive'
        elif negative_count > positive_count:
            return 'negative'
        else:
            return 'neutral'
    
    def generate_insights_report(self, analysis_results: List[Dict]) -> Dict[str, Any]:
        """生成洞察報告"""
        if not analysis_results:
            return {'error': 'No analysis results available'}
        
        # 彙總成功率
        success_rates = [r['key_insights']['success_probability'] for r in analysis_results]
        avg_success_rate = sum(success_rates) / len(success_rates)
        
        # 客戶類型分佈
        customer_types = defaultdict(int)
        for r in analysis_results:
            customer_types[r['key_insights']['customer_type']] += 1
        
        # 主要障礙統計
        all_barriers = []
        for r in analysis_results:
            barriers = r['key_insights']['main_barriers']
            all_barriers.extend([b['description'] for b in barriers])
        
        barrier_counts = defaultdict(int)
        for barrier in all_barriers:
            barrier_counts[barrier] += 1
        
        # 建議行動統計
        action_types = defaultdict(int)
        for r in analysis_results:
            actions = r['key_insights']['recommended_actions']
            for action in actions:
                action_types[action['action']] += 1
        
        return {
            'summary_statistics': {
                'conversations_analyzed': len(analysis_results),
                'average_success_probability': f"{avg_success_rate:.2%}",
                'high_potential_conversations': sum(1 for r in success_rates if r > 0.7)
            },
            'customer_insights': {
                'type_distribution': dict(customer_types),
                'most_common_type': max(customer_types, key=customer_types.get) if customer_types else 'Unknown'
            },
            'barrier_analysis': {
                'top_barriers': dict(sorted(barrier_counts.items(), key=lambda x: x[1], reverse=True)[:5]),
                'barrier_frequency': f"{len(all_barriers) / len(analysis_results):.1f} per conversation"
            },
            'recommended_strategies': {
                'top_actions': dict(sorted(action_types.items(), key=lambda x: x[1], reverse=True)[:5]),
                'action_diversity': len(action_types)
            },
            'optimization_opportunities': self._identify_optimization_opportunities(analysis_results)
        }
    
    def _identify_optimization_opportunities(self, results: List[Dict]) -> List[str]:
        """識別優化機會"""
        opportunities = []
        
        # 分析低成功率模式
        low_success = [r for r in results if r['key_insights']['success_probability'] < 0.5]
        if len(low_success) > len(results) * 0.3:
            opportunities.append("超過30%的對話成功率偏低，建議加強初期信任建立")
        
        # 分析客戶類型
        cautious_customers = sum(1 for r in results 
                               if 'Careful' in r['key_insights']['customer_type'] or 
                                  'Researcher' in r['key_insights']['customer_type'])
        if cautious_customers > len(results) * 0.4:
            opportunities.append("謹慎型客戶比例高，建議提供更多數據支持和保證")
        
        # 分析常見障礙
        psychological_barriers = sum(1 for r in results 
                                   for b in r['key_insights']['main_barriers']
                                   if 'psychological' in b.get('type', ''))
        if psychological_barriers > len(results) * 0.5:
            opportunities.append("心理障礙普遍存在，建議培訓團隊心理疏導技巧")
        
        return opportunities
    
    def close(self):
        """關閉數據庫連接"""
        self.conn.close()


async def main():
    """主程序"""
    print("🔍 WhatsApp 聊天記錄深度分析")
    print("=" * 80)
    
    # 初始化分析器
    db_path = "/Users/<USER>/Desktop/Claude/whatsapp-mcp/whatsapp-bridge/store/messages.db"
    analyzer = WhatsAppDataAnalyzer(db_path)
    
    try:
        # 1. 總體摘要
        print("\n📊 數據總覽")
        print("-" * 40)
        summary = analyzer.get_conversation_summary()
        print(f"總消息數: {summary['total_messages']:,}")
        print(f"獨立對話: {summary['unique_chats']}")
        print(f"時間範圍: {summary['date_range']['start']} 至 {summary['date_range']['end']}")
        print(f"發送/接收: {summary['message_distribution']['sent']:,} / {summary['message_distribution']['received']:,}")
        
        # 2. 客戶意圖分析
        print("\n🎯 客戶意圖模式")
        print("-" * 40)
        intents = analyzer.extract_customer_intent_patterns()
        for intent, count in sorted(intents.items(), key=lambda x: x[1], reverse=True):
            print(f"{intent}: {count:,} 次")
        
        # 3. 回應模式分析
        print("\n⏱️ 回應時間分析")
        print("-" * 40)
        response_patterns = analyzer.analyze_response_patterns()
        print(f"平均回應時間: {response_patterns['average_response_time_seconds']:.1f} 秒")
        print("回應時間分佈:")
        for time_range, count in response_patterns['response_time_distribution'].items():
            print(f"  {time_range}: {count}")
        
        # 4. 銷售對話深度分析
        print("\n🤖 使用增強型銷售代理進行深度分析...")
        print("-" * 40)
        
        # 分析前5個最活躍的對話
        analysis_results = await analyzer.analyze_sales_conversations(sample_size=5)
        
        print(f"\n分析了 {len(analysis_results)} 個對話")
        
        # 顯示每個對話的關鍵洞察
        for i, result in enumerate(analysis_results, 1):
            print(f"\n對話 {i}: {result['contact_name']} ({result['message_count']} 條消息)")
            insights = result['key_insights']
            print(f"  - 成功概率: {insights['success_probability']:.2%}")
            print(f"  - 客戶類型: {insights['customer_type']}")
            if insights['main_barriers']:
                print(f"  - 主要障礙: {insights['main_barriers'][0]['description']}")
            if insights['recommended_actions']:
                print(f"  - 建議行動: {insights['recommended_actions'][0]['description']}")
        
        # 5. 生成綜合洞察報告
        print("\n📈 綜合洞察報告")
        print("=" * 80)
        
        insights_report = analyzer.generate_insights_report(analysis_results)
        
        # 摘要統計
        stats = insights_report['summary_statistics']
        print(f"分析對話數: {stats['conversations_analyzed']}")
        print(f"平均成功率: {stats['average_success_probability']}")
        print(f"高潛力對話: {stats['high_potential_conversations']}")
        
        # 客戶洞察
        print("\n👥 客戶類型分佈:")
        for ctype, count in insights_report['customer_insights']['type_distribution'].items():
            print(f"  - {ctype}: {count}")
        
        # 障礙分析
        print("\n🚧 主要銷售障礙:")
        for barrier, count in insights_report['barrier_analysis']['top_barriers'].items():
            print(f"  - {barrier}: {count} 次")
        
        # 優化建議
        print("\n💡 優化機會:")
        for opportunity in insights_report['optimization_opportunities']:
            print(f"  ✓ {opportunity}")
        
        # 保存詳細報告
        report_path = "/Users/<USER>/Desktop/Claude/healthcheck-ai-platform/whatsapp_analysis_report.json"
        with open(report_path, 'w', encoding='utf-8') as f:
            json.dump({
                'analysis_date': datetime.now().isoformat(),
                'summary': summary,
                'intent_patterns': intents,
                'response_patterns': response_patterns,
                'detailed_analysis': [
                    {
                        'chat_jid': r['chat_jid'],
                        'contact_name': r['contact_name'],
                        'message_count': r['message_count'],
                        'success_probability': r['key_insights']['success_probability'],
                        'customer_type': r['key_insights']['customer_type']
                    }
                    for r in analysis_results
                ],
                'insights_report': insights_report
            }, f, ensure_ascii=False, indent=2)
        
        print(f"\n📄 詳細報告已保存至: {report_path}")
        
    finally:
        analyzer.close()


if __name__ == "__main__":
    print("🚀 啟動 WhatsApp 數據深度分析...")
    asyncio.run(main())