#!/usr/bin/env python3
"""
WhatsApp 聊天記錄分析 - 獨立版本
不依賴 CrewAI，直接分析數據並生成洞察
"""

import sqlite3
import json
from datetime import datetime, timedelta
from typing import Dict, Any, List, Tuple
from collections import defaultdict, Counter
import re


class WhatsAppAnalyzer:
    """WhatsApp 數據分析器"""
    
    def __init__(self, db_path: str):
        self.db_path = db_path
        self.conn = sqlite3.connect(db_path)
        self.conn.row_factory = sqlite3.Row
        
        # 銷售分析關鍵詞定義
        self.intent_keywords = {
            'price_inquiry': ['價格', '多少錢', '費用', '收費', '優惠', '折扣', 'price', 'cost', 'discount'],
            'appointment': ['預約', '時間', '什麼時候', '幾點', '安排', 'book', 'appointment', 'when'],
            'service_info': ['檢查', '項目', '包含', '內容', '套餐', 'include', 'service', 'package'],
            'location': ['地址', '位置', '在哪', '怎麼去', '交通', 'where', 'location', 'address'],
            'health_concern': ['不舒服', '疼痛', '症狀', '健康', '擔心', 'pain', 'symptom', 'worry'],
            'comparison': ['比較', '區別', '不同', '其他', '別家', 'compare', 'difference', 'other'],
            'objection': ['貴', '考慮', '想想', '算了', '不需要', 'expensive', 'think', 'no need']
        }
        
        # 心理特徵關鍵詞
        self.psychological_keywords = {
            'analytical': ['數據', '證明', '研究', '比較', '分析', 'data', 'proof', 'research'],
            'emotional': ['感覺', '擔心', '害怕', '希望', '相信', 'feel', 'worry', 'hope'],
            'practical': ['方便', '快速', '簡單', '實際', '效率', 'convenient', 'fast', 'simple'],
            'social': ['朋友', '家人', '推薦', '別人', '大家', 'friend', 'family', 'recommend']
        }
        
        # 購買階段關鍵詞
        self.stage_keywords = {
            'awareness': ['聽說', '了解', '什麼是', '介紹', 'heard', 'what is', 'introduce'],
            'interest': ['想要', '有興趣', '詳細', '更多', 'want', 'interested', 'more'],
            'consideration': ['比較', '考慮', '怎麼樣', '值得', 'compare', 'consider', 'worth'],
            'intent': ['預約', '什麼時候', '怎麼付', '確定', 'book', 'when', 'payment', 'confirm'],
            'evaluation': ['價格', '優惠', '便宜', '划算', 'price', 'discount', 'cheap', 'value']
        }
    
    def analyze_all_conversations(self) -> Dict[str, Any]:
        """全面分析所有對話"""
        print("📊 開始分析 WhatsApp 對話數據...")
        
        # 1. 基礎統計
        basic_stats = self._get_basic_statistics()
        print(f"✓ 總消息數: {basic_stats['total_messages']:,}")
        print(f"✓ 獨立對話: {basic_stats['unique_chats']}")
        
        # 2. 對話活躍度分析
        activity_analysis = self._analyze_conversation_activity()
        print(f"✓ 活躍對話: {activity_analysis['active_conversations']} (最近30天)")
        
        # 3. 客戶意圖分析
        intent_analysis = self._analyze_customer_intents()
        print(f"✓ 分析了 {intent_analysis['messages_analyzed']:,} 條客戶消息")
        
        # 4. 心理特徵分析
        psychological_analysis = self._analyze_psychological_patterns()
        print(f"✓ 識別了 {len(psychological_analysis['customer_profiles'])} 個客戶畫像")
        
        # 5. 銷售漏斗分析
        funnel_analysis = self._analyze_sales_funnel()
        print(f"✓ 追蹤了 {funnel_analysis['total_journeys']} 個客戶旅程")
        
        # 6. 關鍵詞和話題分析
        keyword_analysis = self._analyze_keywords_and_topics()
        
        # 7. 回應效果分析
        response_analysis = self._analyze_response_effectiveness()
        
        # 8. 生成綜合洞察
        insights = self._generate_comprehensive_insights(
            basic_stats, activity_analysis, intent_analysis,
            psychological_analysis, funnel_analysis, keyword_analysis,
            response_analysis
        )
        
        return {
            'analysis_timestamp': datetime.now().isoformat(),
            'basic_statistics': basic_stats,
            'activity_analysis': activity_analysis,
            'intent_analysis': intent_analysis,
            'psychological_analysis': psychological_analysis,
            'sales_funnel': funnel_analysis,
            'keyword_analysis': keyword_analysis,
            'response_effectiveness': response_analysis,
            'insights_and_recommendations': insights
        }
    
    def _get_basic_statistics(self) -> Dict[str, Any]:
        """獲取基礎統計數據"""
        cursor = self.conn.cursor()
        
        stats = {}
        
        # 總消息數
        stats['total_messages'] = cursor.execute("SELECT COUNT(*) FROM messages").fetchone()[0]
        
        # 獨立對話數
        stats['unique_chats'] = cursor.execute("SELECT COUNT(DISTINCT chat_jid) FROM messages").fetchone()[0]
        
        # 時間範圍
        time_range = cursor.execute("""
            SELECT MIN(timestamp) as start_time, MAX(timestamp) as end_time 
            FROM messages WHERE timestamp IS NOT NULL
        """).fetchone()
        
        stats['date_range'] = {
            'start': time_range['start_time'],
            'end': time_range['end_time']
        }
        
        # 消息類型分佈
        msg_types = cursor.execute("""
            SELECT 
                SUM(CASE WHEN is_from_me = 1 THEN 1 ELSE 0 END) as sent,
                SUM(CASE WHEN is_from_me = 0 THEN 1 ELSE 0 END) as received,
                SUM(CASE WHEN media_type IS NOT NULL THEN 1 ELSE 0 END) as media
            FROM messages
        """).fetchone()
        
        stats['message_distribution'] = dict(msg_types)
        
        # 平均對話長度
        avg_conv_length = cursor.execute("""
            SELECT AVG(msg_count) as avg_length FROM (
                SELECT COUNT(*) as msg_count 
                FROM messages 
                GROUP BY chat_jid
            )
        """).fetchone()
        
        stats['average_conversation_length'] = round(avg_conv_length['avg_length'], 1)
        
        return stats
    
    def _analyze_conversation_activity(self) -> Dict[str, Any]:
        """分析對話活躍度"""
        cursor = self.conn.cursor()
        
        # 最近30天的活躍對話
        thirty_days_ago = (datetime.now() - timedelta(days=30)).isoformat()
        
        active_recent = cursor.execute("""
            SELECT COUNT(DISTINCT chat_jid) as active_chats
            FROM messages 
            WHERE timestamp > ?
        """, (thirty_days_ago,)).fetchone()
        
        # 對話頻率分佈
        conversation_frequencies = cursor.execute("""
            SELECT 
                chat_jid,
                COUNT(*) as message_count,
                MAX(timestamp) as last_message,
                MIN(timestamp) as first_message
            FROM messages
            GROUP BY chat_jid
            ORDER BY message_count DESC
        """).fetchall()
        
        # 分類對話活躍度
        high_activity = 0
        medium_activity = 0
        low_activity = 0
        inactive = 0
        
        for conv in conversation_frequencies:
            if conv['message_count'] > 100:
                high_activity += 1
            elif conv['message_count'] > 20:
                medium_activity += 1
            elif conv['message_count'] > 5:
                low_activity += 1
            else:
                inactive += 1
        
        # 識別最活躍的對話
        top_conversations = []
        for conv in conversation_frequencies[:10]:
            # 嘗試獲取聯繫人名稱
            contact_info = cursor.execute("""
                SELECT name FROM chats WHERE jid = ?
            """, (conv['chat_jid'],)).fetchone()
            
            top_conversations.append({
                'chat_jid': conv['chat_jid'],
                'contact_name': contact_info['name'] if contact_info else 'Unknown',
                'message_count': conv['message_count'],
                'last_message': conv['last_message']
            })
        
        return {
            'active_conversations': active_recent['active_chats'],
            'activity_distribution': {
                'high_activity': high_activity,
                'medium_activity': medium_activity,
                'low_activity': low_activity,
                'inactive': inactive
            },
            'top_conversations': top_conversations,
            'engagement_metrics': self._calculate_engagement_metrics(conversation_frequencies)
        }
    
    def _calculate_engagement_metrics(self, conversations: List) -> Dict[str, Any]:
        """計算參與度指標"""
        total_conversations = len(conversations)
        if total_conversations == 0:
            return {}
        
        # 計算對話持續時間
        conversation_durations = []
        for conv in conversations:
            if conv['first_message'] and conv['last_message']:
                try:
                    first = datetime.fromisoformat(conv['first_message'].replace('Z', '+00:00'))
                    last = datetime.fromisoformat(conv['last_message'].replace('Z', '+00:00'))
                    duration_days = (last - first).days
                    if duration_days > 0:
                        conversation_durations.append(duration_days)
                except:
                    pass
        
        avg_duration = sum(conversation_durations) / len(conversation_durations) if conversation_durations else 0
        
        return {
            'average_conversation_duration_days': round(avg_duration, 1),
            'conversations_over_30_days': sum(1 for d in conversation_durations if d > 30),
            'conversations_single_day': sum(1 for d in conversation_durations if d == 0)
        }
    
    def _analyze_customer_intents(self) -> Dict[str, Any]:
        """分析客戶意圖"""
        cursor = self.conn.cursor()
        
        # 獲取所有客戶消息
        customer_messages = cursor.execute("""
            SELECT content, chat_jid, timestamp
            FROM messages 
            WHERE is_from_me = 0 AND content IS NOT NULL AND content != ''
        """).fetchall()
        
        intent_counts = defaultdict(int)
        intent_by_conversation = defaultdict(lambda: defaultdict(int))
        intent_timeline = defaultdict(list)
        
        messages_analyzed = 0
        
        for msg in customer_messages:
            content = msg['content'].lower()
            messages_analyzed += 1
            
            # 檢查每種意圖
            for intent, keywords in self.intent_keywords.items():
                if any(keyword in content for keyword in keywords):
                    intent_counts[intent] += 1
                    intent_by_conversation[msg['chat_jid']][intent] += 1
                    
                    # 記錄時間線
                    if msg['timestamp']:
                        intent_timeline[intent].append(msg['timestamp'])
        
        # 計算意圖轉化率（從詢問到預約的比例）
        conversations_with_price_inquiry = sum(1 for conv in intent_by_conversation.values() 
                                             if conv.get('price_inquiry', 0) > 0)
        conversations_with_appointment = sum(1 for conv in intent_by_conversation.values() 
                                           if conv.get('appointment', 0) > 0)
        
        conversion_rate = (conversations_with_appointment / conversations_with_price_inquiry * 100 
                          if conversations_with_price_inquiry > 0 else 0)
        
        # 意圖組合分析
        intent_combinations = []
        for conv_intents in intent_by_conversation.values():
            if len(conv_intents) > 1:
                intents_list = list(conv_intents.keys())
                intent_combinations.append(tuple(sorted(intents_list)))
        
        common_combinations = Counter(intent_combinations).most_common(5)
        
        return {
            'messages_analyzed': messages_analyzed,
            'intent_distribution': dict(intent_counts),
            'conversations_by_intent': {
                intent: sum(1 for conv in intent_by_conversation.values() if intent in conv)
                for intent in self.intent_keywords.keys()
            },
            'price_to_appointment_conversion': f"{conversion_rate:.1f}%",
            'common_intent_combinations': [
                {'intents': list(combo[0]), 'count': combo[1]}
                for combo in common_combinations
            ],
            'objection_rate': f"{(intent_counts['objection'] / messages_analyzed * 100):.1f}%" if messages_analyzed > 0 else "0%"
        }
    
    def _analyze_psychological_patterns(self) -> Dict[str, Any]:
        """分析客戶心理特徵"""
        cursor = self.conn.cursor()
        
        # 獲取每個對話的所有消息
        conversations = cursor.execute("""
            SELECT DISTINCT chat_jid FROM messages
        """).fetchall()
        
        customer_profiles = []
        psychological_distribution = defaultdict(int)
        
        for conv in conversations[:50]:  # 分析前50個對話
            chat_jid = conv['chat_jid']
            
            # 獲取該對話的客戶消息
            messages = cursor.execute("""
                SELECT content FROM messages 
                WHERE chat_jid = ? AND is_from_me = 0 AND content IS NOT NULL
                ORDER BY timestamp
            """, (chat_jid,)).fetchall()
            
            if len(messages) < 5:  # 消息太少，跳過
                continue
            
            # 分析心理特徵
            all_content = ' '.join([m['content'].lower() for m in messages])
            
            psychological_scores = {}
            for trait, keywords in self.psychological_keywords.items():
                score = sum(1 for keyword in keywords if keyword in all_content)
                psychological_scores[trait] = score
            
            # 確定主要特徵
            if max(psychological_scores.values()) > 0:
                primary_trait = max(psychological_scores, key=psychological_scores.get)
                psychological_distribution[primary_trait] += 1
                
                # 分析決策速度
                first_msg = messages[0]
                last_msg = messages[-1]
                
                decision_speed = 'unknown'
                if any(word in last_msg['content'].lower() for word in ['預約', '確定', 'book', 'yes']):
                    if len(messages) < 10:
                        decision_speed = 'fast'
                    elif len(messages) < 20:
                        decision_speed = 'moderate'
                    else:
                        decision_speed = 'slow'
                
                customer_profiles.append({
                    'chat_jid': chat_jid,
                    'primary_trait': primary_trait,
                    'decision_speed': decision_speed,
                    'message_count': len(messages),
                    'trait_scores': psychological_scores
                })
        
        # 根據心理特徵推薦銷售策略
        trait_strategies = {
            'analytical': '提供詳細數據對比、專業報告、科學依據',
            'emotional': '建立情感連接、分享成功故事、強調安心感',
            'practical': '突出便利性、節省時間、簡化流程',
            'social': '展示客戶見證、群體優惠、推薦獎勵'
        }
        
        return {
            'customer_profiles': customer_profiles[:20],  # 返回前20個
            'psychological_distribution': dict(psychological_distribution),
            'trait_strategies': trait_strategies,
            'insights': {
                'dominant_trait': max(psychological_distribution, key=psychological_distribution.get) if psychological_distribution else 'unknown',
                'profiles_analyzed': len(customer_profiles)
            }
        }
    
    def _analyze_sales_funnel(self) -> Dict[str, Any]:
        """分析銷售漏斗"""
        cursor = self.conn.cursor()
        
        # 分析每個對話的階段進展
        conversations = cursor.execute("""
            SELECT chat_jid, COUNT(*) as msg_count
            FROM messages
            GROUP BY chat_jid
            HAVING msg_count > 5
        """).fetchall()
        
        funnel_stages = {
            'awareness': 0,
            'interest': 0,
            'consideration': 0,
            'intent': 0,
            'evaluation': 0,
            'purchase': 0
        }
        
        stage_transitions = []
        
        for conv in conversations:
            chat_jid = conv['chat_jid']
            
            # 獲取對話消息
            messages = cursor.execute("""
                SELECT content, is_from_me, timestamp
                FROM messages
                WHERE chat_jid = ? AND content IS NOT NULL
                ORDER BY timestamp
            """, (chat_jid,)).fetchall()
            
            # 追蹤階段進展
            stages_reached = []
            
            for msg in messages:
                if msg['is_from_me'] == 0:  # 客戶消息
                    content = msg['content'].lower()
                    
                    for stage, keywords in self.stage_keywords.items():
                        if any(keyword in content for keyword in keywords):
                            if stage not in stages_reached:
                                stages_reached.append(stage)
                                funnel_stages[stage] += 1
            
            # 檢查是否完成購買
            final_messages = ' '.join([m['content'].lower() for m in messages[-5:]])
            if any(word in final_messages for word in ['確定預約', '已付款', 'confirmed', 'paid']):
                funnel_stages['purchase'] += 1
            
            if len(stages_reached) > 1:
                stage_transitions.append({
                    'chat_jid': chat_jid,
                    'stages': stages_reached,
                    'stage_count': len(stages_reached)
                })
        
        # 計算轉化率
        total_journeys = len(conversations)
        conversion_rates = {}
        
        stage_order = ['awareness', 'interest', 'consideration', 'intent', 'evaluation', 'purchase']
        for i, stage in enumerate(stage_order):
            if i == 0:
                conversion_rates[stage] = (funnel_stages[stage] / total_journeys * 100) if total_journeys > 0 else 0
            else:
                prev_stage = stage_order[i-1]
                if funnel_stages[prev_stage] > 0:
                    conversion_rates[f"{prev_stage}_to_{stage}"] = (funnel_stages[stage] / funnel_stages[prev_stage] * 100)
                else:
                    conversion_rates[f"{prev_stage}_to_{stage}"] = 0
        
        # 識別流失點
        dropout_points = []
        for i in range(len(stage_order) - 1):
            current = stage_order[i]
            next_stage = stage_order[i + 1]
            dropout_rate = 100 - conversion_rates.get(f"{current}_to_{next_stage}", 0)
            if dropout_rate > 50:  # 流失率超過50%
                dropout_points.append({
                    'from_stage': current,
                    'to_stage': next_stage,
                    'dropout_rate': f"{dropout_rate:.1f}%"
                })
        
        return {
            'total_journeys': total_journeys,
            'stage_counts': funnel_stages,
            'conversion_rates': {k: f"{v:.1f}%" for k, v in conversion_rates.items()},
            'average_stages_per_journey': round(sum(t['stage_count'] for t in stage_transitions) / len(stage_transitions), 1) if stage_transitions else 0,
            'dropout_points': dropout_points,
            'complete_journeys': funnel_stages['purchase'],
            'overall_conversion_rate': f"{(funnel_stages['purchase'] / total_journeys * 100):.1f}%" if total_journeys > 0 else "0%"
        }
    
    def _analyze_keywords_and_topics(self) -> Dict[str, Any]:
        """分析關鍵詞和話題"""
        cursor = self.conn.cursor()
        
        # 獲取所有消息內容
        all_messages = cursor.execute("""
            SELECT content, is_from_me
            FROM messages
            WHERE content IS NOT NULL AND content != ''
        """).fetchall()
        
        # 分別統計客戶和客服的關鍵詞
        customer_words = []
        agent_words = []
        
        # 常見問題模式
        question_patterns = {
            'price_related': ['多少錢', '價格', '費用', '優惠', '折扣'],
            'time_related': ['什麼時候', '幾點', '多久', '需要多長時間'],
            'service_related': ['包含什麼', '有哪些項目', '檢查什麼'],
            'process_related': ['怎麼預約', '流程是什麼', '需要準備什麼']
        }
        
        question_counts = defaultdict(int)
        
        for msg in all_messages:
            content = msg['content']
            
            # 基本文本清理
            words = re.findall(r'[\u4e00-\u9fa5]+|[a-zA-Z]+', content.lower())
            
            if msg['is_from_me'] == 0:  # 客戶消息
                customer_words.extend(words)
                
                # 檢查問題類型
                for q_type, patterns in question_patterns.items():
                    if any(pattern in content for pattern in patterns):
                        question_counts[q_type] += 1
            else:  # 客服消息
                agent_words.extend(words)
        
        # 統計高頻詞（排除常見停用詞）
        stop_words = {'的', '了', '是', '我', '你', '在', '和', '有', '不', '這', '個', '們', 'the', 'is', 'and', 'to', 'a', 'in', 'for', 'on'}
        
        customer_word_freq = Counter(w for w in customer_words if w not in stop_words and len(w) > 1)
        agent_word_freq = Counter(w for w in agent_words if w not in stop_words and len(w) > 1)
        
        # 話題聚類（簡化版）
        health_topics = {
            'cardiovascular': ['心臟', '血壓', '血管', '心電圖'],
            'diabetes': ['血糖', '糖尿病', '胰島素'],
            'cancer': ['癌症', '腫瘤', '癌', '標記物'],
            'general': ['體檢', '全面', '常規', '基礎']
        }
        
        topic_mentions = defaultdict(int)
        for topic, keywords in health_topics.items():
            for word in customer_words:
                if word in keywords:
                    topic_mentions[topic] += 1
        
        return {
            'customer_keywords': {
                'top_20_words': dict(customer_word_freq.most_common(20)),
                'total_unique_words': len(set(customer_words))
            },
            'agent_keywords': {
                'top_20_words': dict(agent_word_freq.most_common(20)),
                'total_unique_words': len(set(agent_words))
            },
            'common_questions': dict(question_counts),
            'health_topics_interest': dict(topic_mentions),
            'communication_stats': {
                'avg_customer_message_length': len(customer_words) / sum(1 for m in all_messages if m['is_from_me'] == 0) if customer_words else 0,
                'avg_agent_message_length': len(agent_words) / sum(1 for m in all_messages if m['is_from_me'] == 1) if agent_words else 0
            }
        }
    
    def _analyze_response_effectiveness(self) -> Dict[str, Any]:
        """分析回應效果"""
        cursor = self.conn.cursor()
        
        # 獲取對話對
        conversations = cursor.execute("""
            SELECT DISTINCT chat_jid FROM messages
        """).fetchall()
        
        response_metrics = {
            'positive_responses': 0,
            'negative_responses': 0,
            'neutral_responses': 0,
            'total_exchanges': 0,
            'conversation_continuations': 0,
            'conversation_drops': 0
        }
        
        response_times = []
        
        for conv in conversations[:100]:  # 分析前100個對話
            chat_jid = conv['chat_jid']
            
            messages = cursor.execute("""
                SELECT content, is_from_me, timestamp
                FROM messages
                WHERE chat_jid = ?
                ORDER BY timestamp
            """, (chat_jid,)).fetchall()
            
            # 分析對話交互
            for i in range(1, len(messages)):
                prev_msg = messages[i-1]
                curr_msg = messages[i]
                
                # 如果是客戶消息後的客服回應
                if prev_msg['is_from_me'] == 0 and curr_msg['is_from_me'] == 1:
                    response_metrics['total_exchanges'] += 1
                    
                    # 計算回應時間
                    if prev_msg['timestamp'] and curr_msg['timestamp']:
                        try:
                            t1 = datetime.fromisoformat(prev_msg['timestamp'].replace('Z', '+00:00'))
                            t2 = datetime.fromisoformat(curr_msg['timestamp'].replace('Z', '+00:00'))
                            response_time = (t2 - t1).total_seconds()
                            if 0 < response_time < 3600:  # 1小時內的合理回應
                                response_times.append(response_time)
                        except:
                            pass
                
                # 如果是客服回應後的客戶反應
                elif prev_msg['is_from_me'] == 1 and curr_msg['is_from_me'] == 0:
                    customer_response = curr_msg['content'].lower() if curr_msg['content'] else ''
                    
                    # 分析客戶反應
                    positive_indicators = ['好', '謝謝', '明白', '可以', 'ok', 'thanks', 'yes']
                    negative_indicators = ['不', '算了', '再說', '貴', 'no', 'expensive']
                    
                    if any(ind in customer_response for ind in positive_indicators):
                        response_metrics['positive_responses'] += 1
                        response_metrics['conversation_continuations'] += 1
                    elif any(ind in customer_response for ind in negative_indicators):
                        response_metrics['negative_responses'] += 1
                        # 檢查是否對話結束
                        if i == len(messages) - 1 or (i < len(messages) - 1 and messages[i+1]['is_from_me'] == 1):
                            response_metrics['conversation_drops'] += 1
                    else:
                        response_metrics['neutral_responses'] += 1
        
        # 計算平均回應時間
        avg_response_time = sum(response_times) / len(response_times) if response_times else 0
        
        # 計算回應滿意度
        total_responses = (response_metrics['positive_responses'] + 
                          response_metrics['negative_responses'] + 
                          response_metrics['neutral_responses'])
        
        satisfaction_rate = (response_metrics['positive_responses'] / total_responses * 100) if total_responses > 0 else 0
        
        return {
            'response_time_analysis': {
                'average_seconds': round(avg_response_time, 1),
                'under_1_min': sum(1 for t in response_times if t < 60),
                'under_5_min': sum(1 for t in response_times if 60 <= t < 300),
                'over_5_min': sum(1 for t in response_times if t >= 300)
            },
            'response_effectiveness': {
                'positive_rate': f"{(response_metrics['positive_responses'] / total_responses * 100):.1f}%" if total_responses > 0 else "0%",
                'negative_rate': f"{(response_metrics['negative_responses'] / total_responses * 100):.1f}%" if total_responses > 0 else "0%",
                'continuation_rate': f"{(response_metrics['conversation_continuations'] / response_metrics['total_exchanges'] * 100):.1f}%" if response_metrics['total_exchanges'] > 0 else "0%"
            },
            'metrics': response_metrics,
            'satisfaction_score': f"{satisfaction_rate:.1f}%"
        }
    
    def _generate_comprehensive_insights(self, *analyses) -> Dict[str, Any]:
        """生成綜合洞察和建議"""
        basic_stats, activity, intents, psychological, funnel, keywords, responses = analyses
        
        insights = {
            'executive_summary': {},
            'key_findings': [],
            'opportunities': [],
            'action_recommendations': [],
            'risk_areas': []
        }
        
        # 執行摘要
        insights['executive_summary'] = {
            'total_conversations': basic_stats['unique_chats'],
            'active_customers': activity['active_conversations'],
            'overall_conversion_rate': funnel['overall_conversion_rate'],
            'customer_satisfaction': responses['satisfaction_score'],
            'average_response_time': f"{responses['response_time_analysis']['average_seconds']:.0f}秒",
            'dominant_customer_type': psychological['insights']['dominant_trait']
        }
        
        # 關鍵發現
        # 1. 轉化率分析
        conversion_rate = float(funnel['overall_conversion_rate'].rstrip('%'))
        if conversion_rate < 10:
            insights['key_findings'].append({
                'finding': '低轉化率警報',
                'detail': f'整體轉化率僅為 {funnel["overall_conversion_rate"]}，遠低於行業平均水平',
                'impact': 'high',
                'category': 'conversion'
            })
        
        # 2. 客戶意圖分析
        price_inquiry_rate = intents['intent_distribution'].get('price_inquiry', 0) / intents['messages_analyzed'] * 100 if intents['messages_analyzed'] > 0 else 0
        if price_inquiry_rate > 30:
            insights['key_findings'].append({
                'finding': '價格敏感度高',
                'detail': f'{price_inquiry_rate:.1f}% 的客戶消息涉及價格詢問',
                'impact': 'medium',
                'category': 'pricing'
            })
        
        # 3. 回應時間分析
        if responses['response_time_analysis']['average_seconds'] > 300:
            insights['key_findings'].append({
                'finding': '回應時間過長',
                'detail': f'平均回應時間為 {responses["response_time_analysis"]["average_seconds"]:.0f} 秒，可能影響客戶體驗',
                'impact': 'high',
                'category': 'service'
            })
        
        # 4. 心理特徵洞察
        dominant_trait = psychological['insights']['dominant_trait']
        insights['key_findings'].append({
            'finding': f'客戶群體以{dominant_trait}型為主',
            'detail': f'建議採用 {psychological["trait_strategies"].get(dominant_trait, "個性化")} 的銷售策略',
            'impact': 'medium',
            'category': 'strategy'
        })
        
        # 機會識別
        # 1. 未轉化的高意向客戶
        high_intent_convs = sum(1 for conv in intents['conversations_by_intent'].items() 
                               if conv[0] in ['appointment', 'intent'] and conv[1] > 0)
        if high_intent_convs > 10:
            insights['opportunities'].append({
                'opportunity': '高意向客戶挖掘',
                'description': f'發現 {high_intent_convs} 個表達預約意向但未完成轉化的對話',
                'potential_value': 'high',
                'effort': 'low'
            })
        
        # 2. 交叉銷售機會
        health_interests = keywords['health_topics_interest']
        if len([v for v in health_interests.values() if v > 5]) > 2:
            insights['opportunities'].append({
                'opportunity': '健康套餐升級',
                'description': '客戶對多個健康領域表現出興趣，可推廣綜合體檢套餐',
                'potential_value': 'medium',
                'effort': 'medium'
            })
        
        # 行動建議
        # 基於發現生成具體建議
        if conversion_rate < 10:
            insights['action_recommendations'].append({
                'priority': 'high',
                'action': '優化銷售流程',
                'specific_steps': [
                    '簡化預約流程，減少步驟',
                    '在價格詢問後立即提供優惠方案',
                    '加強價值塑造，而非價格競爭'
                ],
                'expected_impact': '預計可提升轉化率至15-20%',
                'timeline': '2週內實施'
            })
        
        if responses['response_time_analysis']['average_seconds'] > 300:
            insights['action_recommendations'].append({
                'priority': 'high',
                'action': '改善回應速度',
                'specific_steps': [
                    '設置自動回應系統',
                    '增加客服人員排班',
                    '使用快捷回覆模板'
                ],
                'expected_impact': '提升客戶滿意度和對話延續率',
                'timeline': '1週內實施'
            })
        
        if dominant_trait == 'analytical':
            insights['action_recommendations'].append({
                'priority': 'medium',
                'action': '強化數據支撐',
                'specific_steps': [
                    '準備詳細的檢查項目對比表',
                    '提供科學研究和統計數據',
                    '展示資質認證和專業背書'
                ],
                'expected_impact': '提高分析型客戶的信任度和轉化率',
                'timeline': '2週內準備'
            })
        
        # 風險識別
        dropout_points = funnel.get('dropout_points', [])
        if dropout_points:
            insights['risk_areas'].append({
                'risk': '銷售漏斗斷點',
                'description': f'在 {dropout_points[0]["from_stage"]} 到 {dropout_points[0]["to_stage"]} 階段流失率達 {dropout_points[0]["dropout_rate"]}',
                'mitigation': '加強該階段的客戶引導和價值傳遞'
            })
        
        objection_rate = float(intents.get('objection_rate', '0%').rstrip('%'))
        if objection_rate > 20:
            insights['risk_areas'].append({
                'risk': '高異議率',
                'description': f'{objection_rate:.1f}% 的客戶消息包含異議或拒絕',
                'mitigation': '培訓團隊異議處理技巧，優化產品定位'
            })
        
        return insights
    
    def save_analysis_report(self, analysis_results: Dict[str, Any], output_path: str):
        """保存分析報告"""
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(analysis_results, f, ensure_ascii=False, indent=2)
        print(f"\n📄 詳細分析報告已保存至: {output_path}")
    
    def generate_executive_report(self, analysis_results: Dict[str, Any]) -> str:
        """生成管理層報告"""
        insights = analysis_results['insights_and_recommendations']
        
        report = f"""
# WhatsApp 客戶對話深度分析報告

**分析日期**: {analysis_results['analysis_timestamp']}

## 執行摘要

- **總對話數**: {insights['executive_summary']['total_conversations']:,}
- **活躍客戶**: {insights['executive_summary']['active_customers']} (最近30天)
- **整體轉化率**: {insights['executive_summary']['overall_conversion_rate']}
- **客戶滿意度**: {insights['executive_summary']['customer_satisfaction']}
- **平均回應時間**: {insights['executive_summary']['average_response_time']}

## 關鍵發現

"""
        for finding in insights['key_findings'][:5]:
            report += f"### {finding['finding']}\n"
            report += f"- {finding['detail']}\n"
            report += f"- 影響程度: {finding['impact']}\n\n"
        
        report += "## 機會與建議\n\n"
        
        for i, rec in enumerate(insights['action_recommendations'][:3], 1):
            report += f"### {i}. {rec['action']} (優先級: {rec['priority']})\n"
            report += "**具體步驟**:\n"
            for step in rec['specific_steps']:
                report += f"- {step}\n"
            report += f"\n**預期效果**: {rec['expected_impact']}\n"
            report += f"**實施時間**: {rec['timeline']}\n\n"
        
        report += "## 風險提示\n\n"
        
        for risk in insights['risk_areas']:
            report += f"- **{risk['risk']}**: {risk['description']}\n"
            report += f"  - 建議措施: {risk['mitigation']}\n\n"
        
        return report
    
    def close(self):
        """關閉數據庫連接"""
        self.conn.close()


def main():
    """主程序"""
    print("🚀 啟動 WhatsApp 聊天記錄深度分析...")
    print("=" * 80)
    
    # 數據庫路徑
    db_path = "/Users/<USER>/Desktop/Claude/whatsapp-mcp/whatsapp-bridge/store/messages.db"
    
    # 初始化分析器
    analyzer = WhatsAppAnalyzer(db_path)
    
    try:
        # 執行全面分析
        analysis_results = analyzer.analyze_all_conversations()
        
        # 保存詳細報告
        output_path = "/Users/<USER>/Desktop/Claude/healthcheck-ai-platform/whatsapp_analysis_report.json"
        analyzer.save_analysis_report(analysis_results, output_path)
        
        # 生成並顯示管理層報告
        executive_report = analyzer.generate_executive_report(analysis_results)
        print(executive_report)
        
        # 保存管理層報告
        exec_report_path = "/Users/<USER>/Desktop/Claude/healthcheck-ai-platform/whatsapp_executive_report.md"
        with open(exec_report_path, 'w', encoding='utf-8') as f:
            f.write(executive_report)
        print(f"📊 管理層報告已保存至: {exec_report_path}")
        
    finally:
        analyzer.close()
    
    print("\n✅ 分析完成！")


if __name__ == "__main__":
    main()