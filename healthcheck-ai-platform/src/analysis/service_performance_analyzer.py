"""
客服績效分析器 - 評估回覆質量、轉化率和客戶滿意度
"""
import sqlite3
from datetime import datetime, timedelta
from typing import Dict, List, Tuple, Optional
from dataclasses import dataclass
import json
import re
from collections import defaultdict

@dataclass
class ConversationMetrics:
    """對話指標"""
    chat_jid: str
    customer_name: str
    first_response_time: Optional[timedelta]
    total_messages: int
    agent_messages: int
    customer_messages: int
    conversation_duration: timedelta
    last_message_time: datetime
    conversion_status: str  # 'converted', 'pending', 'lost'
    follow_up_needed: bool
    quality_score: float
    key_topics: List[str]

@dataclass 
class AgentPerformance:
    """客服人員表現"""
    agent_id: str
    total_conversations: int
    avg_response_time: timedelta
    conversion_rate: float
    customer_satisfaction: float
    follow_up_completion_rate: float
    best_practices: List[str]
    areas_for_improvement: List[str]

class ServicePerformanceAnalyzer:
    """客服績效分析器"""
    
    def __init__(self, db_path: str = "store/messages.db"):
        self.db_path = db_path
        self.quality_keywords = {
            'positive': ['謝謝', '多謝', '好的', '明白', '清楚', '專業', '詳細', '幫到', '滿意'],
            'negative': ['唔明', '不清楚', '太貴', '考慮', '問題', '投訴', '不滿'],
            'conversion': ['預約', 'book', '確認', '安排', '幾時來', '什麼時間'],
            'interest': ['想了解', '查詢', '詢問', '想知道', '有興趣'],
            'objection': ['貴', '預算', '考慮一下', '比較', '其他']
        }
        
    def analyze_conversation(self, chat_jid: str, time_window_hours: int = 168) -> ConversationMetrics:
        """分析單個對話的指標"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # 獲取對話消息
        cursor.execute("""
            SELECT sender, content, timestamp, is_from_me
            FROM messages 
            WHERE chat_jid = ? 
                AND timestamp > datetime('now', '-{} hours')
            ORDER BY timestamp
        """.format(time_window_hours), (chat_jid,))
        
        messages = cursor.fetchall()
        
        # 獲取客戶名稱
        cursor.execute("SELECT name FROM chats WHERE jid = ?", (chat_jid,))
        name_result = cursor.fetchone()
        customer_name = name_result[0] if name_result else chat_jid
        
        conn.close()
        
        if not messages:
            return None
            
        # 計算指標
        first_customer_msg_time = None
        first_agent_response_time = None
        agent_messages = 0
        customer_messages = 0
        all_content = []
        
        for sender, content, timestamp, is_from_me in messages:
            if content:
                all_content.append(content)
                
            if not is_from_me and not first_customer_msg_time:
                first_customer_msg_time = datetime.fromisoformat(timestamp.replace('+08:00', ''))
            elif is_from_me:
                agent_messages += 1
                if first_customer_msg_time and not first_agent_response_time:
                    first_agent_response_time = datetime.fromisoformat(timestamp.replace('+08:00', ''))
            else:
                customer_messages += 1
        
        # 計算首次響應時間
        first_response_time = None
        if first_customer_msg_time and first_agent_response_time:
            first_response_time = first_agent_response_time - first_customer_msg_time
            
        # 計算對話持續時間
        first_msg_time = datetime.fromisoformat(messages[0][2].replace('+08:00', ''))
        last_msg_time = datetime.fromisoformat(messages[-1][2].replace('+08:00', ''))
        conversation_duration = last_msg_time - first_msg_time
        
        # 分析對話質量和狀態
        combined_content = ' '.join(all_content)
        quality_score = self._calculate_quality_score(combined_content)
        conversion_status = self._determine_conversion_status(combined_content)
        follow_up_needed = self._check_follow_up_needed(messages)
        key_topics = self._extract_key_topics(combined_content)
        
        return ConversationMetrics(
            chat_jid=chat_jid,
            customer_name=customer_name,
            first_response_time=first_response_time,
            total_messages=len(messages),
            agent_messages=agent_messages,
            customer_messages=customer_messages,
            conversation_duration=conversation_duration,
            last_message_time=last_msg_time,
            conversion_status=conversion_status,
            follow_up_needed=follow_up_needed,
            quality_score=quality_score,
            key_topics=key_topics
        )
    
    def _calculate_quality_score(self, content: str) -> float:
        """計算對話質量分數 (0-100)"""
        score = 50  # 基礎分
        
        # 正面關鍵詞加分
        for keyword in self.quality_keywords['positive']:
            score += content.count(keyword) * 2
            
        # 負面關鍵詞扣分
        for keyword in self.quality_keywords['negative']:
            score -= content.count(keyword) * 3
            
        # 轉化相關加分
        for keyword in self.quality_keywords['conversion']:
            score += content.count(keyword) * 5
            
        return min(100, max(0, score))
    
    def _determine_conversion_status(self, content: str) -> str:
        """判斷轉化狀態"""
        content_lower = content.lower()
        
        # 檢查是否已預約
        if any(word in content_lower for word in ['已預約', '已安排', 'book左', '確認預約']):
            return 'converted'
        
        # 檢查是否有異議
        objection_count = sum(1 for word in self.quality_keywords['objection'] if word in content)
        if objection_count >= 2:
            return 'lost'
            
        return 'pending'
    
    def _check_follow_up_needed(self, messages: List) -> bool:
        """檢查是否需要跟進"""
        if not messages:
            return False
            
        # 最後一條消息是客戶發的，需要跟進
        last_msg = messages[-1]
        if not last_msg[3]:  # is_from_me = False
            return True
            
        # 對話中有未解決的問題
        last_content = last_msg[1] or ''
        if any(word in last_content for word in ['考慮', '想想', '之後', '遲些']):
            return True
            
        return False
    
    def _extract_key_topics(self, content: str) -> List[str]:
        """提取關鍵話題"""
        topics = []
        
        # 檢查提及的套餐
        packages = re.findall(r'\d{3}項|女士|全面|尊尚|特選', content)
        topics.extend(packages)
        
        # 檢查關注點
        if '價' in content or '錢' in content:
            topics.append('價格諮詢')
        if '時間' in content or '預約' in content:
            topics.append('預約安排')
        if '地址' in content or '位置' in content:
            topics.append('地點查詢')
            
        return list(set(topics))
    
    def generate_performance_report(self, time_window_hours: int = 4) -> Dict:
        """生成績效報告"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # 獲取活躍對話
        cursor.execute("""
            SELECT DISTINCT chat_jid 
            FROM messages 
            WHERE timestamp > datetime('now', '-{} hours')
        """.format(time_window_hours))
        
        active_chats = [row[0] for row in cursor.fetchall()]
        conn.close()
        
        # 分析每個對話
        conversations = []
        for chat_jid in active_chats:
            metrics = self.analyze_conversation(chat_jid, time_window_hours)
            if metrics:
                conversations.append(metrics)
        
        # 生成報告
        report = {
            'period': f'最近{time_window_hours}小時',
            'generated_at': datetime.now().isoformat(),
            'summary': {
                'total_conversations': len(conversations),
                'need_follow_up': sum(1 for c in conversations if c.follow_up_needed),
                'converted': sum(1 for c in conversations if c.conversion_status == 'converted'),
                'pending': sum(1 for c in conversations if c.conversion_status == 'pending'),
                'lost': sum(1 for c in conversations if c.conversion_status == 'lost'),
                'avg_response_time': self._calculate_avg_response_time(conversations),
                'avg_quality_score': sum(c.quality_score for c in conversations) / len(conversations) if conversations else 0
            },
            'follow_up_list': self._generate_follow_up_list(conversations),
            'best_conversations': self._identify_best_conversations(conversations),
            'areas_for_improvement': self._identify_improvement_areas(conversations)
        }
        
        return report
    
    def _calculate_avg_response_time(self, conversations: List[ConversationMetrics]) -> str:
        """計算平均響應時間"""
        valid_times = [c.first_response_time for c in conversations if c.first_response_time]
        if not valid_times:
            return "N/A"
            
        avg_seconds = sum(t.total_seconds() for t in valid_times) / len(valid_times)
        return f"{int(avg_seconds // 60)}分{int(avg_seconds % 60)}秒"
    
    def _generate_follow_up_list(self, conversations: List[ConversationMetrics]) -> List[Dict]:
        """生成需要跟進的客戶清單"""
        follow_ups = []
        
        for conv in conversations:
            if conv.follow_up_needed:
                priority = 'high' if conv.conversion_status == 'pending' else 'medium'
                
                follow_ups.append({
                    'customer': conv.customer_name,
                    'chat_id': conv.chat_jid,
                    'last_contact': conv.last_message_time.isoformat(),
                    'topics': conv.key_topics,
                    'priority': priority,
                    'suggested_action': self._suggest_follow_up_action(conv)
                })
                
        return sorted(follow_ups, key=lambda x: x['priority'], reverse=True)
    
    def _suggest_follow_up_action(self, conv: ConversationMetrics) -> str:
        """建議跟進行動"""
        if conv.conversion_status == 'pending':
            if '價格諮詢' in conv.key_topics:
                return "提供限時優惠，創造緊迫感"
            elif '預約安排' in conv.key_topics:
                return "主動提供可預約時段"
            else:
                return "了解具體需求，提供個性化建議"
        else:
            return "發送關懷信息，維持關係"
    
    def _identify_best_conversations(self, conversations: List[ConversationMetrics]) -> List[Dict]:
        """識別優秀對話案例"""
        # 找出高質量且已轉化的對話
        best_convs = [c for c in conversations 
                      if c.quality_score > 70 and c.conversion_status == 'converted']
        
        return [{
            'customer': conv.customer_name,
            'quality_score': conv.quality_score,
            'response_time': str(conv.first_response_time) if conv.first_response_time else 'N/A',
            'key_success_factors': self._extract_success_factors(conv)
        } for conv in sorted(best_convs, key=lambda x: x.quality_score, reverse=True)[:5]]
    
    def _extract_success_factors(self, conv: ConversationMetrics) -> List[str]:
        """提取成功因素"""
        factors = []
        
        if conv.first_response_time and conv.first_response_time.total_seconds() < 300:
            factors.append("快速響應")
        if conv.quality_score > 80:
            factors.append("專業親切")
        if conv.agent_messages > 3:
            factors.append("積極跟進")
            
        return factors
    
    def _identify_improvement_areas(self, conversations: List[ConversationMetrics]) -> List[str]:
        """識別需要改進的地方"""
        areas = []
        
        # 檢查響應時間
        slow_responses = [c for c in conversations 
                         if c.first_response_time and c.first_response_time.total_seconds() > 1800]
        if len(slow_responses) > len(conversations) * 0.2:
            areas.append("提高首次響應速度")
            
        # 檢查轉化率
        conversion_rate = sum(1 for c in conversations if c.conversion_status == 'converted') / len(conversations) if conversations else 0
        if conversion_rate < 0.3:
            areas.append("提升轉化率")
            
        # 檢查跟進率
        follow_up_rate = sum(1 for c in conversations if c.follow_up_needed) / len(conversations) if conversations else 0
        if follow_up_rate > 0.5:
            areas.append("減少未完成對話")
            
        return areas
    
    def generate_daily_follow_up_list(self) -> Dict:
        """生成第二天的跟進清單"""
        # 分析過去7天的對話
        report_7d = self.generate_performance_report(168)
        
        # 分析過去24小時的對話
        report_24h = self.generate_performance_report(24)
        
        follow_up_list = {
            'date': (datetime.now() + timedelta(days=1)).strftime('%Y-%m-%d'),
            'high_priority': [],
            'medium_priority': [],
            'low_priority': []
        }
        
        # 合併兩個報告的跟進清單
        all_follow_ups = report_7d['follow_up_list'] + report_24h['follow_up_list']
        
        # 去重並分類
        seen = set()
        for item in all_follow_ups:
            if item['chat_id'] not in seen:
                seen.add(item['chat_id'])
                
                # 根據最後聯繫時間分配優先級
                last_contact = datetime.fromisoformat(item['last_contact'])
                hours_since_contact = (datetime.now() - last_contact).total_seconds() / 3600
                
                if hours_since_contact < 24 and item['priority'] == 'high':
                    follow_up_list['high_priority'].append(item)
                elif hours_since_contact < 72:
                    follow_up_list['medium_priority'].append(item)
                else:
                    follow_up_list['low_priority'].append(item)
        
        return follow_up_list