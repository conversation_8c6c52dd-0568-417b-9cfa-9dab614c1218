#!/usr/bin/env python3
"""
WhatsApp 真實客戶對話深度分析
排除內部群組、合作夥伴等非客戶對話
"""

import sqlite3
import json
from datetime import datetime, timedelta
from typing import Dict, Any, List, Tuple, Set
from collections import defaultdict, Counter
import re


class RealCustomerAnalyzer:
    """真實客戶數據分析器"""
    
    def __init__(self, db_path: str):
        self.db_path = db_path
        self.conn = sqlite3.connect(db_path)
        self.conn.row_factory = sqlite3.Row
        
        # 非客戶識別規則
        self.non_customer_patterns = {
            'groups': '@g.us',  # 群組
            'keywords': [
                '醫學化驗室', '化驗所', '收血台', 'operation', 
                'supplement', '森仁', 'staff', 'team', '內部',
                'admin', 'test', 'bot', 'system'
            ]
        }
        
        # 真實客戶特徵
        self.customer_indicators = {
            'inquiries': ['價格', '體檢', '預約', '地址', '時間', 'how much', 'booking'],
            'concerns': ['健康', '檢查', '報告', '結果', 'health', 'check'],
            'responses': ['謝謝', '好的', '明白', '考慮', 'thanks', 'ok']
        }
    
    def identify_real_customers(self) -> Set[str]:
        """識別真實客戶對話"""
        cursor = self.conn.cursor()
        
        # 獲取所有對話
        all_chats = cursor.execute("""
            SELECT DISTINCT m.chat_jid, c.name, COUNT(*) as msg_count
            FROM messages m
            LEFT JOIN chats c ON m.chat_jid = c.jid
            GROUP BY m.chat_jid
        """).fetchall()
        
        real_customers = set()
        excluded_chats = []
        
        for chat in all_chats:
            chat_jid = chat['chat_jid']
            name = chat['name'] or ''
            
            # 排除群組
            if self.non_customer_patterns['groups'] in chat_jid:
                excluded_chats.append({'jid': chat_jid, 'name': name, 'reason': 'group'})
                continue
            
            # 排除包含特定關鍵詞的
            is_non_customer = False
            for keyword in self.non_customer_patterns['keywords']:
                if keyword.lower() in name.lower():
                    excluded_chats.append({'jid': chat_jid, 'name': name, 'reason': f'keyword: {keyword}'})
                    is_non_customer = True
                    break
            
            if is_non_customer:
                continue
            
            # 檢查對話內容是否像客戶
            sample_messages = cursor.execute("""
                SELECT content, is_from_me 
                FROM messages 
                WHERE chat_jid = ? AND content IS NOT NULL
                LIMIT 20
            """, (chat_jid,)).fetchall()
            
            customer_score = 0
            for msg in sample_messages:
                if msg['is_from_me'] == 0:  # 客戶消息
                    content = msg['content'].lower()
                    # 檢查客戶特徵
                    for indicator_list in self.customer_indicators.values():
                        if any(ind in content for ind in indicator_list):
                            customer_score += 1
                            break
            
            # 如果有客戶特徵，認為是真實客戶
            if customer_score >= 2 or (customer_score > 0 and chat['msg_count'] > 10):
                real_customers.add(chat_jid)
            elif chat['msg_count'] < 5:
                excluded_chats.append({'jid': chat_jid, 'name': name, 'reason': 'too_few_messages'})
        
        print(f"\n📊 識別結果:")
        print(f"- 總對話數: {len(all_chats)}")
        print(f"- 真實客戶: {len(real_customers)}")
        print(f"- 排除對話: {len(excluded_chats)}")
        
        print(f"\n🚫 排除的對話示例:")
        for exc in excluded_chats[:10]:
            print(f"  - {exc['name'] or exc['jid'][:20]}: {exc['reason']}")
        
        return real_customers
    
    def analyze_customer_conversations(self, real_customers: Set[str]) -> Dict[str, Any]:
        """分析真實客戶對話"""
        cursor = self.conn.cursor()
        
        analysis = {
            'basic_stats': {},
            'customer_journey': {},
            'conversation_quality': {},
            'sales_effectiveness': {},
            'detailed_insights': []
        }
        
        # 基礎統計
        total_messages = cursor.execute("""
            SELECT COUNT(*) FROM messages 
            WHERE chat_jid IN ({})
        """.format(','.join(['?'] * len(real_customers))), list(real_customers)).fetchone()[0]
        
        customer_messages = cursor.execute("""
            SELECT COUNT(*) FROM messages 
            WHERE chat_jid IN ({}) AND is_from_me = 0
        """.format(','.join(['?'] * len(real_customers))), list(real_customers)).fetchone()[0]
        
        analysis['basic_stats'] = {
            'real_customer_count': len(real_customers),
            'total_messages': total_messages,
            'customer_messages': customer_messages,
            'agent_messages': total_messages - customer_messages,
            'avg_messages_per_customer': total_messages / len(real_customers) if real_customers else 0
        }
        
        # 分析每個客戶的詳細對話
        high_value_customers = []
        completed_purchases = []
        abandoned_carts = []
        
        for customer_jid in list(real_customers)[:100]:  # 分析前100個客戶
            customer_analysis = self._analyze_single_customer(customer_jid)
            
            if customer_analysis['conversion_stage'] == 'completed':
                completed_purchases.append(customer_analysis)
            elif customer_analysis['conversion_stage'] in ['pricing_discussed', 'appointment_intent']:
                abandoned_carts.append(customer_analysis)
            
            if customer_analysis['engagement_score'] > 0.7:
                high_value_customers.append(customer_analysis)
            
            analysis['detailed_insights'].append(customer_analysis)
        
        # 客戶旅程分析
        analysis['customer_journey'] = {
            'completed_purchases': len(completed_purchases),
            'abandoned_carts': len(abandoned_carts),
            'high_value_customers': len(high_value_customers),
            'conversion_rate': f"{(len(completed_purchases) / len(analysis['detailed_insights']) * 100):.1f}%",
            'abandonment_rate': f"{(len(abandoned_carts) / len(analysis['detailed_insights']) * 100):.1f}%"
        }
        
        # 對話質量分析
        response_times = []
        satisfaction_scores = []
        
        for insight in analysis['detailed_insights']:
            if insight['avg_response_time'] > 0:
                response_times.append(insight['avg_response_time'])
            satisfaction_scores.append(insight['satisfaction_indicator'])
        
        analysis['conversation_quality'] = {
            'avg_response_time_seconds': sum(response_times) / len(response_times) if response_times else 0,
            'avg_satisfaction_score': sum(satisfaction_scores) / len(satisfaction_scores) if satisfaction_scores else 0,
            'conversations_with_good_response': sum(1 for t in response_times if t < 300)  # 5分鐘內
        }
        
        # 銷售效果分析
        analysis['sales_effectiveness'] = self._analyze_sales_patterns(analysis['detailed_insights'])
        
        return analysis
    
    def _analyze_single_customer(self, chat_jid: str) -> Dict[str, Any]:
        """分析單個客戶的完整對話"""
        cursor = self.conn.cursor()
        
        # 獲取所有消息
        messages = cursor.execute("""
            SELECT content, is_from_me, timestamp
            FROM messages
            WHERE chat_jid = ? AND content IS NOT NULL
            ORDER BY timestamp
        """, (chat_jid,)).fetchall()
        
        if not messages:
            return {}
        
        # 基本信息
        contact_info = cursor.execute("""
            SELECT name FROM chats WHERE jid = ?
        """, (chat_jid,)).fetchone()
        
        analysis = {
            'chat_jid': chat_jid,
            'contact_name': contact_info['name'] if contact_info else 'Unknown',
            'total_messages': len(messages),
            'conversation_start': messages[0]['timestamp'],
            'last_interaction': messages[-1]['timestamp']
        }
        
        # 分析對話內容
        customer_messages = []
        agent_messages = []
        response_times = []
        
        for i, msg in enumerate(messages):
            if msg['is_from_me'] == 0:
                customer_messages.append(msg['content'])
            else:
                agent_messages.append(msg['content'])
            
            # 計算回應時間
            if i > 0 and messages[i-1]['is_from_me'] != msg['is_from_me']:
                if msg['is_from_me'] == 1:  # 客服回應
                    time_diff = self._calculate_time_diff(messages[i-1]['timestamp'], msg['timestamp'])
                    if time_diff and 0 < time_diff < 3600:
                        response_times.append(time_diff)
        
        # 識別對話階段
        all_customer_text = ' '.join(customer_messages).lower()
        
        conversion_stage = 'initial_inquiry'
        if any(word in all_customer_text for word in ['預約了', '已付款', 'confirmed', '確認']):
            conversion_stage = 'completed'
        elif any(word in all_customer_text for word in ['什麼時候', '幾點', '預約', 'book']):
            conversion_stage = 'appointment_intent'
        elif any(word in all_customer_text for word in ['多少錢', '價格', 'price']):
            conversion_stage = 'pricing_discussed'
        elif any(word in all_customer_text for word in ['什麼項目', '包含', '檢查']):
            conversion_stage = 'service_inquiry'
        
        # 計算參與度分數
        engagement_score = 0
        if len(customer_messages) > 5:
            engagement_score += 0.3
        if len(customer_messages) > 10:
            engagement_score += 0.2
        if conversion_stage in ['appointment_intent', 'completed']:
            engagement_score += 0.3
        if any(word in all_customer_text for word in ['謝謝', '好的', '明白']):
            engagement_score += 0.2
        
        # 滿意度指標
        positive_words = sum(1 for word in ['謝謝', '好', '明白', '可以'] if word in all_customer_text)
        negative_words = sum(1 for word in ['不', '貴', '算了', '再說'] if word in all_customer_text)
        satisfaction_indicator = (positive_words - negative_words) / max(len(customer_messages), 1)
        
        analysis.update({
            'customer_messages_count': len(customer_messages),
            'agent_messages_count': len(agent_messages),
            'avg_response_time': sum(response_times) / len(response_times) if response_times else 0,
            'conversion_stage': conversion_stage,
            'engagement_score': min(engagement_score, 1.0),
            'satisfaction_indicator': satisfaction_indicator,
            'key_topics': self._extract_key_topics(all_customer_text),
            'last_customer_message': customer_messages[-1] if customer_messages else '',
            'conversation_outcome': self._determine_outcome(messages)
        })
        
        return analysis
    
    def _calculate_time_diff(self, time1: str, time2: str) -> float:
        """計算時間差（秒）"""
        try:
            t1 = datetime.fromisoformat(time1.replace('Z', '+00:00'))
            t2 = datetime.fromisoformat(time2.replace('Z', '+00:00'))
            return abs((t2 - t1).total_seconds())
        except:
            return None
    
    def _extract_key_topics(self, text: str) -> List[str]:
        """提取關鍵話題"""
        topics = []
        
        topic_keywords = {
            'pricing': ['價格', '多少錢', '費用', '優惠'],
            'appointment': ['預約', '時間', '什麼時候'],
            'health_checkup': ['體檢', '檢查', '項目'],
            'location': ['地址', '位置', '在哪'],
            'health_concern': ['不舒服', '疼痛', '擔心']
        }
        
        for topic, keywords in topic_keywords.items():
            if any(kw in text for kw in keywords):
                topics.append(topic)
        
        return topics
    
    def _determine_outcome(self, messages: List[Dict]) -> str:
        """判斷對話結果"""
        if len(messages) < 3:
            return 'too_short'
        
        # 檢查最後幾條消息
        last_messages = ' '.join([m['content'] for m in messages[-5:]])
        
        if any(word in last_messages for word in ['預約成功', '確認', '已安排']):
            return 'successful_booking'
        elif any(word in last_messages for word in ['謝謝', '再見', '拜拜']):
            return 'polite_end'
        elif any(word in last_messages for word in ['算了', '不用了', '再說']):
            return 'customer_declined'
        elif messages[-1]['is_from_me'] == 1:
            return 'awaiting_customer_response'
        else:
            return 'awaiting_agent_response'
    
    def _analyze_sales_patterns(self, customer_insights: List[Dict]) -> Dict[str, Any]:
        """分析銷售模式"""
        # 轉化漏斗
        stage_counts = defaultdict(int)
        for insight in customer_insights:
            stage_counts[insight['conversion_stage']] += 1
        
        # 常見失敗原因
        failed_conversations = [c for c in customer_insights 
                               if c['conversation_outcome'] in ['customer_declined', 'awaiting_customer_response']]
        
        failure_patterns = defaultdict(int)
        for conv in failed_conversations:
            if 'pricing' in conv['key_topics']:
                failure_patterns['price_sensitivity'] += 1
            if conv['avg_response_time'] > 600:
                failure_patterns['slow_response'] += 1
            if conv['engagement_score'] < 0.3:
                failure_patterns['low_engagement'] += 1
        
        # 成功因素
        successful_conversations = [c for c in customer_insights 
                                   if c['conversion_stage'] == 'completed']
        
        success_factors = {
            'avg_messages_to_conversion': sum(c['total_messages'] for c in successful_conversations) / len(successful_conversations) if successful_conversations else 0,
            'avg_response_time': sum(c['avg_response_time'] for c in successful_conversations) / len(successful_conversations) if successful_conversations else 0,
            'common_topics': Counter([topic for c in successful_conversations for topic in c['key_topics']]).most_common(3)
        }
        
        return {
            'conversion_funnel': dict(stage_counts),
            'failure_patterns': dict(failure_patterns),
            'success_factors': success_factors,
            'recommendations': self._generate_recommendations(stage_counts, failure_patterns, success_factors)
        }
    
    def _generate_recommendations(self, stage_counts: Dict, failure_patterns: Dict, success_factors: Dict) -> List[str]:
        """生成改進建議"""
        recommendations = []
        
        # 基於失敗模式的建議
        if failure_patterns.get('price_sensitivity', 0) > 10:
            recommendations.append("價格是主要障礙 - 建議準備更多價值說明和分期方案")
        
        if failure_patterns.get('slow_response', 0) > 5:
            recommendations.append("回應速度影響轉化 - 建議設置快速回覆模板和自動應答")
        
        if failure_patterns.get('low_engagement', 0) > 10:
            recommendations.append("客戶參與度低 - 需要更吸引人的開場白和互動方式")
        
        # 基於轉化漏斗的建議
        total = sum(stage_counts.values())
        if total > 0:
            if stage_counts.get('pricing_discussed', 0) / total > 0.3:
                recommendations.append("大量客戶止步於價格討論 - 需要優化價格展示策略")
            
            if stage_counts.get('completed', 0) / total < 0.1:
                recommendations.append("轉化率偏低 - 建議加強成交話術培訓")
        
        # 基於成功因素的建議
        if success_factors['avg_response_time'] < 120:
            recommendations.append("快速回應是成功關鍵 - 保持2分鐘內回覆")
        
        return recommendations
    
    def generate_detailed_report(self, analysis: Dict[str, Any]) -> str:
        """生成詳細報告"""
        report = f"""
# WhatsApp 真實客戶對話分析報告

**分析時間**: {datetime.now().strftime('%Y-%m-%d %H:%M')}

## 📊 核心數據

### 客戶概況
- **真實客戶數**: {analysis['basic_stats']['real_customer_count']}
- **總消息數**: {analysis['basic_stats']['total_messages']:,}
- **客戶消息**: {analysis['basic_stats']['customer_messages']:,}
- **平均每客戶消息數**: {analysis['basic_stats']['avg_messages_per_customer']:.1f}

### 💰 銷售轉化
- **完成購買**: {analysis['customer_journey']['completed_purchases']} 位客戶
- **購買意向但未完成**: {analysis['customer_journey']['abandoned_carts']} 位客戶
- **高價值客戶**: {analysis['customer_journey']['high_value_customers']} 位
- **整體轉化率**: {analysis['customer_journey']['conversion_rate']}
- **流失率**: {analysis['customer_journey']['abandonment_rate']}

### ⏱️ 服務質量
- **平均回應時間**: {analysis['conversation_quality']['avg_response_time_seconds']:.0f} 秒
- **5分鐘內回應**: {analysis['conversation_quality']['conversations_with_good_response']} 個對話
- **客戶滿意度指標**: {analysis['conversation_quality']['avg_satisfaction_score']:.2f}

## 🔍 深度洞察

### 銷售漏斗分析
"""
        # 添加漏斗數據
        funnel = analysis['sales_effectiveness']['conversion_funnel']
        for stage, count in sorted(funnel.items(), key=lambda x: x[1], reverse=True):
            report += f"- {stage}: {count} 位客戶\n"
        
        report += "\n### 失敗原因分析\n"
        for pattern, count in analysis['sales_effectiveness']['failure_patterns'].items():
            report += f"- {pattern}: {count} 次\n"
        
        report += "\n## 💡 改進建議\n\n"
        for i, rec in enumerate(analysis['sales_effectiveness']['recommendations'], 1):
            report += f"{i}. {rec}\n"
        
        # 添加具體客戶案例
        report += "\n## 📋 客戶案例分析\n\n"
        
        # 成功案例
        successful = [c for c in analysis['detailed_insights'] if c['conversion_stage'] == 'completed']
        if successful:
            report += "### ✅ 成功案例\n"
            for case in successful[:3]:
                report += f"\n**客戶**: {case['contact_name']}\n"
                report += f"- 總消息數: {case['total_messages']}\n"
                report += f"- 平均回應時間: {case['avg_response_time']:.0f}秒\n"
                report += f"- 關鍵話題: {', '.join(case['key_topics'])}\n"
        
        # 流失案例
        abandoned = [c for c in analysis['detailed_insights'] 
                    if c['conversion_stage'] in ['pricing_discussed', 'appointment_intent'] 
                    and c['conversation_outcome'] == 'customer_declined']
        if abandoned:
            report += "\n### ❌ 流失案例\n"
            for case in abandoned[:3]:
                report += f"\n**客戶**: {case['contact_name']}\n"
                report += f"- 停留階段: {case['conversion_stage']}\n"
                report += f"- 最後消息: {case['last_customer_message'][:50]}...\n"
                report += f"- 可能原因: 回應時間 {case['avg_response_time']:.0f}秒\n"
        
        return report
    
    def export_customer_list(self, analysis: Dict[str, Any], output_path: str):
        """導出客戶清單"""
        customer_data = []
        
        for insight in analysis['detailed_insights']:
            customer_data.append({
                'contact': insight['contact_name'],
                'chat_id': insight['chat_jid'],
                'messages': insight['total_messages'],
                'stage': insight['conversion_stage'],
                'engagement': f"{insight['engagement_score']:.2f}",
                'last_interaction': insight['last_interaction'],
                'outcome': insight['conversation_outcome'],
                'topics': ', '.join(insight['key_topics'])
            })
        
        # 按參與度排序
        customer_data.sort(key=lambda x: float(x['engagement']), reverse=True)
        
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(customer_data, f, ensure_ascii=False, indent=2)
        
        print(f"\n📋 客戶清單已導出至: {output_path}")


def main():
    """主程序"""
    print("🔍 WhatsApp 真實客戶對話深度分析")
    print("=" * 80)
    
    db_path = "/Users/<USER>/Desktop/Claude/whatsapp-mcp/whatsapp-bridge/store/messages.db"
    analyzer = RealCustomerAnalyzer(db_path)
    
    # 1. 識別真實客戶
    print("\n步驟1: 識別真實客戶...")
    real_customers = analyzer.identify_real_customers()
    
    if not real_customers:
        print("❌ 未找到真實客戶對話")
        return
    
    # 2. 分析客戶對話
    print("\n步驟2: 分析客戶對話...")
    analysis = analyzer.analyze_customer_conversations(real_customers)
    
    # 3. 生成報告
    print("\n步驟3: 生成分析報告...")
    report = analyzer.generate_detailed_report(analysis)
    print(report)
    
    # 4. 保存報告
    report_path = "/Users/<USER>/Desktop/Claude/healthcheck-ai-platform/real_customer_analysis_report.md"
    with open(report_path, 'w', encoding='utf-8') as f:
        f.write(report)
    print(f"\n📄 報告已保存至: {report_path}")
    
    # 5. 導出客戶清單
    customer_list_path = "/Users/<USER>/Desktop/Claude/healthcheck-ai-platform/customer_list.json"
    analyzer.export_customer_list(analysis, customer_list_path)
    
    # 6. 保存原始分析數據
    raw_data_path = "/Users/<USER>/Desktop/Claude/healthcheck-ai-platform/customer_analysis_raw.json"
    with open(raw_data_path, 'w', encoding='utf-8') as f:
        json.dump(analysis, f, ensure_ascii=False, indent=2)
    print(f"📊 原始數據已保存至: {raw_data_path}")


if __name__ == "__main__":
    main()