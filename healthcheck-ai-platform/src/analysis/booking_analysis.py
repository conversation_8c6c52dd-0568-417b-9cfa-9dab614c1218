#!/usr/bin/env python3
"""
預約數據深度分析
基於實際預約記錄建立完整的轉化分析
"""

import sqlite3
import json
from datetime import datetime, timedelta
from typing import Dict, Any, List, Tuple
from collections import defaultdict, Counter


class BookingAnalyzer:
    """預約數據分析器"""
    
    def __init__(self, db_path: str):
        self.db_path = db_path
        self.conn = sqlite3.connect(db_path)
        self.conn.row_factory = sqlite3.Row
    
    def analyze_booking_data(self, days: int = 30) -> Dict[str, Any]:
        """分析最近N天的預約數據"""
        cursor = self.conn.cursor()
        
        # 1. 統計預約提醒（已確認預約）
        end_date = datetime.now()
        start_date = end_date - timedelta(days=days)
        
        print(f"\n📊 分析 {start_date.date()} 至 {end_date.date()} 的預約數據...")
        
        # 查找所有預約提醒
        booking_reminders = cursor.execute("""
            SELECT 
                chat_jid,
                content,
                timestamp
            FROM messages 
            WHERE is_from_me = 1 
            AND content LIKE '%體檢提示%'
            AND timestamp >= ?
            ORDER BY timestamp DESC
        """, (start_date.isoformat(),)).fetchall()
        
        print(f"✓ 找到 {len(booking_reminders)} 個預約提醒")
        
        # 解析預約詳情
        bookings = []
        for reminder in booking_reminders:
            booking_info = self._parse_booking_reminder(reminder)
            if booking_info:
                bookings.append(booking_info)
        
        # 2. 分析預約轉化路徑
        conversion_paths = []
        for booking in bookings:
            # 查找該客戶的完整對話歷史
            chat_history = cursor.execute("""
                SELECT content, is_from_me, timestamp
                FROM messages
                WHERE chat_jid = ?
                AND timestamp <= ?
                ORDER BY timestamp
            """, (booking['chat_jid'], booking['reminder_sent'])).fetchall()
            
            path_analysis = self._analyze_conversion_path(chat_history)
            conversion_paths.append({
                **booking,
                **path_analysis
            })
        
        # 3. 統計分析
        analysis_results = {
            'booking_summary': self._generate_booking_summary(bookings),
            'service_analysis': self._analyze_services(bookings),
            'time_analysis': self._analyze_booking_times(bookings),
            'conversion_analysis': self._analyze_conversions(conversion_paths),
            'customer_journey': self._analyze_customer_journeys(conversion_paths)
        }
        
        return analysis_results
    
    def _parse_booking_reminder(self, reminder: sqlite3.Row) -> Dict[str, Any]:
        """解析預約提醒消息"""
        content = reminder['content']
        
        # 提取預約信息
        booking_info = {
            'chat_jid': reminder['chat_jid'],
            'reminder_sent': reminder['timestamp']
        }
        
        # 提取日期時間
        import re
        date_match = re.search(r'時間：(\d+/\d+/\d+)\s+(\d+:\d+)', content)
        if date_match:
            booking_info['appointment_date'] = date_match.group(1)
            booking_info['appointment_time'] = date_match.group(2)
        
        # 提取服務內容
        service_match = re.search(r'內容：(.+?)(?:\s+記得注意|$)', content, re.MULTILINE)
        if service_match:
            booking_info['service'] = service_match.group(1).strip()
        
        # 提取地點
        if '佐敦' in content:
            booking_info['location'] = '佐敦'
        elif '銅鑼灣' in content:
            booking_info['location'] = '銅鑼灣'
        else:
            booking_info['location'] = '未知'
        
        return booking_info
    
    def _analyze_conversion_path(self, chat_history: List[sqlite3.Row]) -> Dict[str, Any]:
        """分析轉化路徑"""
        if not chat_history:
            return {}
        
        # 統計基本信息
        total_messages = len(chat_history)
        customer_messages = sum(1 for m in chat_history if m['is_from_me'] == 0)
        agent_messages = sum(1 for m in chat_history if m['is_from_me'] == 1)
        
        # 計算對話持續時間
        first_msg = chat_history[0]['timestamp']
        last_msg = chat_history[-1]['timestamp']
        
        try:
            start_time = datetime.fromisoformat(first_msg.replace('Z', '+00:00'))
            end_time = datetime.fromisoformat(last_msg.replace('Z', '+00:00'))
            duration_hours = (end_time - start_time).total_seconds() / 3600
        except:
            duration_hours = 0
        
        # 識別關鍵節點
        inquiry_time = None
        price_discussed = False
        appointment_intent = False
        
        for msg in chat_history:
            if msg['is_from_me'] == 0:  # 客戶消息
                content = msg['content'].lower() if msg['content'] else ''
                
                if not inquiry_time and any(word in content for word in ['體檢', '檢查', 'check']):
                    inquiry_time = msg['timestamp']
                
                if any(word in content for word in ['價格', '多少錢', 'price']):
                    price_discussed = True
                
                if any(word in content for word in ['預約', '時間', 'book']):
                    appointment_intent = True
        
        # 計算轉化時間
        conversion_time = None
        if inquiry_time:
            try:
                inquiry_dt = datetime.fromisoformat(inquiry_time.replace('Z', '+00:00'))
                booking_dt = datetime.fromisoformat(last_msg.replace('Z', '+00:00'))
                conversion_time = (booking_dt - inquiry_dt).total_seconds() / 3600
            except:
                pass
        
        return {
            'total_messages': total_messages,
            'customer_messages': customer_messages,
            'agent_messages': agent_messages,
            'conversation_duration_hours': round(duration_hours, 1),
            'conversion_time_hours': round(conversion_time, 1) if conversion_time else None,
            'price_discussed': price_discussed,
            'appointment_intent_shown': appointment_intent,
            'touchpoints': self._count_touchpoints(chat_history)
        }
    
    def _count_touchpoints(self, chat_history: List[sqlite3.Row]) -> int:
        """計算接觸點數量（對話回合數）"""
        touchpoints = 0
        last_sender = None
        
        for msg in chat_history:
            if msg['is_from_me'] != last_sender:
                touchpoints += 1
                last_sender = msg['is_from_me']
        
        return touchpoints // 2  # 一個回合算一個接觸點
    
    def _generate_booking_summary(self, bookings: List[Dict]) -> Dict[str, Any]:
        """生成預約摘要"""
        if not bookings:
            return {'total_bookings': 0}
        
        # 按日期統計
        bookings_by_date = defaultdict(int)
        for booking in bookings:
            date = booking.get('appointment_date', 'Unknown')
            bookings_by_date[date] += 1
        
        # 按地點統計
        bookings_by_location = defaultdict(int)
        for booking in bookings:
            location = booking.get('location', 'Unknown')
            bookings_by_location[location] += 1
        
        return {
            'total_bookings': len(bookings),
            'bookings_by_date': dict(bookings_by_date),
            'bookings_by_location': dict(bookings_by_location),
            'date_range': {
                'earliest': min(b.get('appointment_date', '') for b in bookings),
                'latest': max(b.get('appointment_date', '') for b in bookings)
            }
        }
    
    def _analyze_services(self, bookings: List[Dict]) -> Dict[str, Any]:
        """分析服務類型"""
        service_counts = defaultdict(int)
        service_categories = defaultdict(int)
        
        for booking in bookings:
            service = booking.get('service', 'Unknown')
            service_counts[service] += 1
            
            # 分類服務
            if '130項' in service:
                service_categories['130項全面體檢'] += 1
            elif '158項' in service:
                service_categories['158項尊尚體檢'] += 1
            elif '110項' in service:
                service_categories['110項特選體檢'] += 1
            elif '女士' in service:
                service_categories['女士專項'] += 1
            elif '血' in service or '甲狀腺' in service:
                service_categories['單項檢查'] += 1
            else:
                service_categories['其他'] += 1
        
        # 識別促銷活動
        promotions = defaultdict(int)
        for booking in bookings:
            service = booking.get('service', '')
            if '2人同行' in service:
                promotions['2人同行優惠'] += 1
            elif '3人' in service:
                promotions['3人同行優惠'] += 1
        
        return {
            'service_distribution': dict(sorted(service_counts.items(), key=lambda x: x[1], reverse=True)[:10]),
            'service_categories': dict(service_categories),
            'promotion_usage': dict(promotions),
            'most_popular_service': max(service_counts, key=service_counts.get) if service_counts else None
        }
    
    def _analyze_booking_times(self, bookings: List[Dict]) -> Dict[str, Any]:
        """分析預約時間偏好"""
        time_slots = defaultdict(int)
        weekday_distribution = defaultdict(int)
        
        for booking in bookings:
            # 分析時段
            time_str = booking.get('appointment_time', '')
            if time_str:
                try:
                    hour = int(time_str.split(':')[0])
                    if 9 <= hour < 12:
                        time_slots['上午(9-12)'] += 1
                    elif 12 <= hour < 14:
                        time_slots['中午(12-14)'] += 1
                    elif 14 <= hour < 18:
                        time_slots['下午(14-18)'] += 1
                    else:
                        time_slots['其他時段'] += 1
                except:
                    pass
            
            # 分析星期
            date_str = booking.get('appointment_date', '')
            if date_str:
                try:
                    # 假設格式為 DD/MM/YYYY
                    parts = date_str.split('/')
                    if len(parts) == 3:
                        date_obj = datetime(int(parts[2]), int(parts[1]), int(parts[0]))
                        weekday = date_obj.strftime('%A')
                        weekday_distribution[weekday] += 1
                except:
                    pass
        
        return {
            'time_slot_preference': dict(time_slots),
            'weekday_distribution': dict(weekday_distribution),
            'peak_time_slot': max(time_slots, key=time_slots.get) if time_slots else None
        }
    
    def _analyze_conversions(self, conversion_paths: List[Dict]) -> Dict[str, Any]:
        """分析轉化效率"""
        if not conversion_paths:
            return {}
        
        # 平均轉化指標
        total_messages = [p['total_messages'] for p in conversion_paths if 'total_messages' in p]
        conversion_times = [p['conversion_time_hours'] for p in conversion_paths if p.get('conversion_time_hours')]
        touchpoints = [p['touchpoints'] for p in conversion_paths if 'touchpoints' in p]
        
        # 轉化特徵
        price_discussed_count = sum(1 for p in conversion_paths if p.get('price_discussed'))
        quick_conversions = sum(1 for t in conversion_times if t < 24)  # 24小時內轉化
        
        return {
            'average_messages_to_conversion': round(sum(total_messages) / len(total_messages), 1) if total_messages else 0,
            'average_conversion_time_hours': round(sum(conversion_times) / len(conversion_times), 1) if conversion_times else 0,
            'average_touchpoints': round(sum(touchpoints) / len(touchpoints), 1) if touchpoints else 0,
            'price_discussion_rate': f"{(price_discussed_count / len(conversion_paths) * 100):.1f}%",
            'quick_conversion_rate': f"{(quick_conversions / len(conversion_times) * 100):.1f}%" if conversion_times else "0%",
            'conversion_efficiency': {
                'under_1_hour': sum(1 for t in conversion_times if t < 1),
                'under_24_hours': sum(1 for t in conversion_times if 1 <= t < 24),
                'over_24_hours': sum(1 for t in conversion_times if t >= 24)
            }
        }
    
    def _analyze_customer_journeys(self, conversion_paths: List[Dict]) -> Dict[str, Any]:
        """分析客戶旅程"""
        journey_types = {
            'fast_decision': 0,  # 少於5條消息
            'normal_decision': 0,  # 5-15條消息
            'extended_decision': 0,  # 超過15條消息
            'price_sensitive': 0,  # 討論過價格
            'direct_booking': 0  # 直接預約意向
        }
        
        for path in conversion_paths:
            msgs = path.get('total_messages', 0)
            
            if msgs < 5:
                journey_types['fast_decision'] += 1
            elif msgs <= 15:
                journey_types['normal_decision'] += 1
            else:
                journey_types['extended_decision'] += 1
            
            if path.get('price_discussed'):
                journey_types['price_sensitive'] += 1
            
            if path.get('appointment_intent_shown') and msgs < 10:
                journey_types['direct_booking'] += 1
        
        return {
            'journey_distribution': journey_types,
            'insights': self._generate_journey_insights(journey_types, len(conversion_paths))
        }
    
    def _generate_journey_insights(self, journey_types: Dict, total: int) -> List[str]:
        """生成客戶旅程洞察"""
        insights = []
        
        if total == 0:
            return insights
        
        fast_pct = journey_types['fast_decision'] / total * 100
        if fast_pct > 30:
            insights.append(f"{fast_pct:.0f}%客戶快速決策，顯示品牌信任度高")
        
        price_pct = journey_types['price_sensitive'] / total * 100
        if price_pct > 50:
            insights.append(f"{price_pct:.0f}%客戶關注價格，建議優化價值傳遞")
        
        extended_pct = journey_types['extended_decision'] / total * 100
        if extended_pct > 30:
            insights.append(f"{extended_pct:.0f}%客戶需要長時間考慮，可能需要更好的跟進策略")
        
        return insights
    
    def generate_comprehensive_report(self, analysis: Dict[str, Any]) -> str:
        """生成綜合報告"""
        report = f"""
# 預約數據深度分析報告

**生成時間**: {datetime.now().strftime('%Y-%m-%d %H:%M')}

## 📊 預約概況

### 總體數據
- **總預約數**: {analysis['booking_summary']['total_bookings']}
- **日期範圍**: {analysis['booking_summary']['date_range']['earliest']} 至 {analysis['booking_summary']['date_range']['latest']}

### 地點分佈
"""
        for location, count in analysis['booking_summary']['bookings_by_location'].items():
            report += f"- {location}: {count} ({count/analysis['booking_summary']['total_bookings']*100:.1f}%)\n"
        
        report += "\n## 🏥 服務分析\n\n"
        report += "### 服務類別分佈\n"
        for category, count in sorted(analysis['service_analysis']['service_categories'].items(), 
                                    key=lambda x: x[1], reverse=True):
            report += f"- {category}: {count}\n"
        
        report += "\n### 促銷活動使用\n"
        for promo, count in analysis['service_analysis']['promotion_usage'].items():
            report += f"- {promo}: {count}\n"
        
        report += f"\n**最受歡迎服務**: {analysis['service_analysis']['most_popular_service']}\n"
        
        report += "\n## ⏰ 時間偏好分析\n\n"
        report += "### 時段分佈\n"
        for slot, count in analysis['time_analysis']['time_slot_preference'].items():
            report += f"- {slot}: {count}\n"
        
        report += f"\n**高峰時段**: {analysis['time_analysis']['peak_time_slot']}\n"
        
        report += "\n## 🎯 轉化效率分析\n\n"
        conv = analysis['conversion_analysis']
        report += f"- **平均對話消息數**: {conv.get('average_messages_to_conversion', 'N/A')}\n"
        report += f"- **平均轉化時間**: {conv.get('average_conversion_time_hours', 'N/A')} 小時\n"
        report += f"- **平均接觸點**: {conv.get('average_touchpoints', 'N/A')} 次\n"
        report += f"- **價格討論率**: {conv.get('price_discussion_rate', 'N/A')}\n"
        report += f"- **快速轉化率**: {conv.get('quick_conversion_rate', 'N/A')}\n"
        
        report += "\n### 轉化時間分佈\n"
        if 'conversion_efficiency' in conv:
            for time_range, count in conv['conversion_efficiency'].items():
                report += f"- {time_range}: {count}\n"
        
        report += "\n## 🛤️ 客戶旅程分析\n\n"
        journey = analysis['customer_journey']
        for journey_type, count in journey['journey_distribution'].items():
            report += f"- {journey_type}: {count}\n"
        
        if journey['insights']:
            report += "\n### 關鍵洞察\n"
            for insight in journey['insights']:
                report += f"- {insight}\n"
        
        return report
    
    def export_booking_details(self, analysis: Dict[str, Any], bookings: List[Dict], output_path: str):
        """導出詳細預約數據"""
        export_data = {
            'summary': analysis,
            'bookings': bookings,
            'export_time': datetime.now().isoformat()
        }
        
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(export_data, f, ensure_ascii=False, indent=2)
        
        print(f"\n📄 詳細數據已導出至: {output_path}")


def main():
    """主程序"""
    print("🔍 預約數據深度分析")
    print("=" * 80)
    
    db_path = "/Users/<USER>/Desktop/Claude/whatsapp-mcp/whatsapp-bridge/store/messages.db"
    analyzer = BookingAnalyzer(db_path)
    
    # 分析最近30天的數據
    analysis = analyzer.analyze_booking_data(days=30)
    
    # 生成報告
    report = analyzer.generate_comprehensive_report(analysis)
    print(report)
    
    # 保存報告
    report_path = "/Users/<USER>/Desktop/Claude/healthcheck-ai-platform/booking_analysis_report.md"
    with open(report_path, 'w', encoding='utf-8') as f:
        f.write(report)
    print(f"\n📊 分析報告已保存至: {report_path}")
    
    # 導出詳細數據
    # 需要重新獲取bookings數據用於導出
    cursor = analyzer.conn.cursor()
    bookings_data = cursor.execute("""
        SELECT chat_jid, content, timestamp 
        FROM messages 
        WHERE is_from_me = 1 
        AND content LIKE '%體檢提示%' 
        AND timestamp >= datetime('now', '-30 days')
    """).fetchall()
    
    bookings = []
    for b in bookings_data:
        parsed = analyzer._parse_booking_reminder(b)
        if parsed:
            bookings.append(parsed)
    
    details_path = "/Users/<USER>/Desktop/Claude/healthcheck-ai-platform/booking_details.json"
    analyzer.export_booking_details(analysis, bookings, details_path)
    
    print("\n✅ 分析完成！")


if __name__ == "__main__":
    main()