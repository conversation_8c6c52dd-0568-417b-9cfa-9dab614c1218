"""
客戶跟進追蹤系統 - 智能識別需要跟進的客戶並提供策略建議
"""
import sqlite3
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass
from enum import Enum
import json

class FollowUpPriority(Enum):
    """跟進優先級"""
    URGENT = "urgent"      # 緊急 - 24小時內
    HIGH = "high"          # 高 - 48小時內
    MEDIUM = "medium"      # 中 - 72小時內
    LOW = "low"           # 低 - 一週內

@dataclass
class CustomerFollowUp:
    """客戶跟進信息"""
    chat_jid: str
    customer_name: str
    phone_number: str
    last_contact: datetime
    conversation_stage: str
    interest_level: str
    follow_up_reason: str
    suggested_message: str
    priority: FollowUpPriority
    historical_value: float
    tags: List[str]

class FollowUpTracker:
    """客戶跟進追蹤器"""
    
    def __init__(self, db_path: str = "store/messages.db"):
        self.db_path = db_path
        self.follow_up_triggers = {
            'no_response': {
                'keywords': ['?', '嗎', '呢', '想了解', '查詢'],
                'wait_time': 24,  # 小時
                'template': "您好{name}，之前您查詢的{topic}，還有什麼需要了解的嗎？我們有專人為您詳細解答。"
            },
            'price_inquiry': {
                'keywords': ['價錢', '收費', '多少錢', '優惠'],
                'wait_time': 48,
                'template': "您好{name}，關於您詢問的體檢價格，現在有限時優惠，{offer}。想為您預留名額嗎？"
            },
            'appointment_pending': {
                'keywords': ['預約', '時間', '什麼時候', '安排'],
                'wait_time': 24,
                'template': "您好{name}，您想預約的{date}還有位置，需要幫您確認嗎？"
            },
            'comparison_shopping': {
                'keywords': ['比較', '考慮', '其他', '想想'],
                'wait_time': 72,
                'template': "您好{name}，明白您需要時間考慮。想告訴您我們的{package}包含獨家的{feature}，是其他地方沒有的。"
            }
        }
    
    def identify_follow_up_customers(self, hours_lookback: int = 168) -> List[CustomerFollowUp]:
        """識別需要跟進的客戶"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # 獲取最近有互動但未完成轉化的客戶
        cursor.execute("""
            SELECT DISTINCT 
                m.chat_jid,
                c.name,
                m.sender,
                MAX(m.timestamp) as last_message_time,
                COUNT(m.id) as message_count
            FROM messages m
            JOIN chats c ON m.chat_jid = c.jid
            WHERE m.timestamp > datetime('now', '-{} hours')
            GROUP BY m.chat_jid
            HAVING message_count > 1
        """.format(hours_lookback))
        
        conversations = cursor.fetchall()
        follow_ups = []
        
        for chat_jid, name, sender, last_msg_time, msg_count in conversations:
            # 分析對話內容
            analysis = self._analyze_conversation_for_follow_up(cursor, chat_jid)
            
            if analysis['needs_follow_up']:
                priority = self._determine_priority(analysis, last_msg_time)
                suggested_msg = self._generate_follow_up_message(analysis, name)
                
                # 處理時區問題
                try:
                    last_contact_time = datetime.fromisoformat(last_msg_time.replace('+08:00', ''))
                except:
                    last_contact_time = datetime.now()
                
                follow_up = CustomerFollowUp(
                    chat_jid=chat_jid,
                    customer_name=name or sender,
                    phone_number=chat_jid.split('@')[0],
                    last_contact=last_contact_time,
                    conversation_stage=analysis['stage'],
                    interest_level=analysis['interest_level'],
                    follow_up_reason=analysis['reason'],
                    suggested_message=suggested_msg,
                    priority=priority,
                    historical_value=self._calculate_customer_value(cursor, chat_jid),
                    tags=analysis['tags']
                )
                follow_ups.append(follow_up)
        
        conn.close()
        
        # 按優先級排序
        return sorted(follow_ups, key=lambda x: (x.priority.value, x.historical_value), reverse=True)
    
    def _analyze_conversation_for_follow_up(self, cursor, chat_jid: str) -> Dict:
        """分析對話以確定是否需要跟進"""
        # 獲取最近的消息
        cursor.execute("""
            SELECT content, is_from_me, timestamp
            FROM messages
            WHERE chat_jid = ?
            ORDER BY timestamp DESC
            LIMIT 20
        """, (chat_jid,))
        
        messages = cursor.fetchall()
        
        if not messages:
            return {'needs_follow_up': False}
        
        # 分析對話內容
        last_msg_from_customer = None
        has_unanswered_question = False
        mentioned_topics = []
        interest_signals = 0
        objection_signals = 0
        
        for content, is_from_me, timestamp in messages:
            if not content:
                continue
                
            if not is_from_me:
                last_msg_from_customer = content
                # 檢查是否有未回答的問題
                if any(q in content for q in ['?', '嗎', '呢', '如何', '怎樣']):
                    has_unanswered_question = True
                    
            # 收集提及的話題
            for trigger_type, trigger_info in self.follow_up_triggers.items():
                if any(keyword in content for keyword in trigger_info['keywords']):
                    mentioned_topics.append(trigger_type)
                    
            # 計算興趣信號
            if any(word in content for word in ['想了解', '有興趣', '想知道', '查詢']):
                interest_signals += 1
            if any(word in content for word in ['考慮', '想想', '比較', '太貴']):
                objection_signals += 1
        
        # 判斷對話階段
        stage = self._determine_conversation_stage(messages)
        
        # 判斷是否需要跟進
        needs_follow_up = False
        reason = ""
        
        # 最後一條消息是客戶發的且超過一定時間
        if not messages[0][1]:  # 最新消息不是我們發的
            try:
                last_msg_time = datetime.fromisoformat(messages[0][2].replace('+08:00', ''))
                hours_since = (datetime.now() - last_msg_time).total_seconds() / 3600
                if hours_since > 4:
                    needs_follow_up = True
                    reason = "客戶消息未回覆"
            except:
                needs_follow_up = True
                reason = "客戶消息未回覆"
        
        # 有未回答的問題
        elif has_unanswered_question:
            needs_follow_up = True
            reason = "有待解答的問題"
            
        # 表達了興趣但未預約
        elif interest_signals > objection_signals and stage != 'converted':
            needs_follow_up = True
            reason = "潛在客戶需要推進"
            
        # 有異議需要處理
        elif objection_signals > 0:
            needs_follow_up = True
            reason = "需要處理客戶顧慮"
        
        return {
            'needs_follow_up': needs_follow_up,
            'reason': reason,
            'stage': stage,
            'interest_level': 'high' if interest_signals > 1 else 'medium' if interest_signals > 0 else 'low',
            'topics': list(set(mentioned_topics)),
            'last_customer_message': last_msg_from_customer,
            'tags': self._generate_tags(mentioned_topics, stage)
        }
    
    def _determine_conversation_stage(self, messages: List) -> str:
        """判斷對話階段"""
        all_content = ' '.join([m[0] for m in messages if m[0]])
        
        if any(word in all_content for word in ['已預約', '確認預約', '已安排']):
            return 'converted'
        elif any(word in all_content for word in ['預約', '什麼時間', '幾時']):
            return 'booking'
        elif any(word in all_content for word in ['價錢', '收費', '優惠']):
            return 'pricing'
        elif any(word in all_content for word in ['了解', '查詢', '想知道']):
            return 'inquiry'
        else:
            return 'initial'
    
    def _determine_priority(self, analysis: Dict, last_msg_time: str) -> FollowUpPriority:
        """確定跟進優先級"""
        try:
            last_time = datetime.fromisoformat(last_msg_time.replace('+08:00', ''))
            hours_since = (datetime.now() - last_time).total_seconds() / 3600
        except:
            hours_since = 24  # 默認24小時
        
        # 根據時間和興趣程度確定優先級
        if analysis['interest_level'] == 'high' and hours_since < 24:
            return FollowUpPriority.URGENT
        elif analysis['stage'] in ['booking', 'pricing'] and hours_since < 48:
            return FollowUpPriority.HIGH
        elif hours_since < 72:
            return FollowUpPriority.MEDIUM
        else:
            return FollowUpPriority.LOW
    
    def _generate_follow_up_message(self, analysis: Dict, customer_name: str) -> str:
        """生成跟進消息建議"""
        name = customer_name.split('@')[0] if '@' in customer_name else customer_name
        
        # 根據對話階段生成不同的跟進消息
        if analysis['stage'] == 'booking':
            return f"您好{name}，之前您想預約體檢，我幫您查看了一下，本週六上午還有空位，方便嗎？"
        elif analysis['stage'] == 'pricing':
            return f"您好{name}，關於您詢問的體檢價格，現在預約可享受早鳥優惠，原價$2,950，現在只需$2,360，優惠期有限啊！"
        elif analysis['interest_level'] == 'high':
            return f"您好{name}，看到您對我們的體檢計劃很感興趣，有什麼可以幫您進一步了解的嗎？我們的健康顧問隨時為您服務。"
        else:
            return f"您好{name}，之前的查詢還有什麼需要幫忙的嗎？很樂意為您解答。"
    
    def _calculate_customer_value(self, cursor, chat_jid: str) -> float:
        """計算客戶歷史價值"""
        # 查詢客戶歷史互動
        cursor.execute("""
            SELECT COUNT(*) as total_messages,
                   MIN(timestamp) as first_contact
            FROM messages
            WHERE chat_jid = ?
        """, (chat_jid,))
        
        result = cursor.fetchone()
        if not result:
            return 0.0
            
        total_messages, first_contact = result
        
        # 計算關係時長（天）
        if first_contact:
            days_since_first_contact = (datetime.now() - datetime.fromisoformat(first_contact.replace('+08:00', ''))).days
        else:
            days_since_first_contact = 0
            
        # 簡單的價值計算：消息數量 + 關係時長權重
        value = total_messages * 0.1 + days_since_first_contact * 0.5
        
        return min(100.0, value)  # 最高100分
    
    def _generate_tags(self, topics: List[str], stage: str) -> List[str]:
        """生成標籤"""
        tags = []
        
        # 話題標籤
        topic_tag_map = {
            'price_inquiry': '價格敏感',
            'appointment_pending': '預約意向',
            'comparison_shopping': '比較中',
            'no_response': '需回覆'
        }
        
        for topic in topics:
            if topic in topic_tag_map:
                tags.append(topic_tag_map[topic])
                
        # 階段標籤
        if stage == 'booking':
            tags.append('即將成交')
        elif stage == 'inquiry':
            tags.append('初步了解')
            
        return tags
    
    def export_follow_up_list(self, follow_ups: List[CustomerFollowUp], format: str = 'json') -> str:
        """導出跟進清單"""
        if format == 'json':
            data = []
            for f in follow_ups:
                data.append({
                    'customer_name': f.customer_name,
                    'phone': f.phone_number,
                    'last_contact': f.last_contact.strftime('%Y-%m-%d %H:%M'),
                    'priority': f.priority.value,
                    'reason': f.follow_up_reason,
                    'suggested_message': f.suggested_message,
                    'tags': f.tags
                })
            return json.dumps(data, ensure_ascii=False, indent=2)
            
        elif format == 'text':
            lines = ["=== 客戶跟進清單 ===\n"]
            
            for priority in FollowUpPriority:
                priority_items = [f for f in follow_ups if f.priority == priority]
                if priority_items:
                    lines.append(f"\n【{priority.value.upper()} 優先級】")
                    for item in priority_items:
                        lines.append(f"\n客戶：{item.customer_name} ({item.phone_number})")
                        lines.append(f"最後聯繫：{item.last_contact.strftime('%m/%d %H:%M')}")
                        lines.append(f"跟進原因：{item.follow_up_reason}")
                        lines.append(f"建議消息：{item.suggested_message}")
                        lines.append(f"標籤：{', '.join(item.tags)}")
                        lines.append("-" * 50)
                        
            return '\n'.join(lines)