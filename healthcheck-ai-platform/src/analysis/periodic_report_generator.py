"""
定期報告生成器 - 每4小時監察報告和每日跟進清單
"""
import sqlite3
from datetime import datetime, timedelta
from typing import Dict, List, Optional
import json
import os
from dataclasses import dataclass, asdict
# import schedule  # 可選依賴，用於定時任務
import time

from .service_performance_analyzer import ServicePerformanceAnalyzer
from .follow_up_tracker import FollowUpTracker

@dataclass
class ReportSection:
    """報告段落"""
    title: str
    content: Dict
    priority: str

class PeriodicReportGenerator:
    """定期報告生成器"""
    
    def __init__(self, db_path: str = "store/messages.db", 
                 report_dir: str = "reports"):
        self.db_path = db_path
        self.report_dir = report_dir
        self.performance_analyzer = ServicePerformanceAnalyzer(db_path)
        self.follow_up_tracker = FollowUpTracker(db_path)
        
        # 創建報告目錄
        os.makedirs(report_dir, exist_ok=True)
        
    def generate_4hour_report(self) -> Dict:
        """生成4小時監察報告"""
        report_time = datetime.now()
        report = {
            'report_type': '4小時監察報告',
            'generated_at': report_time.isoformat(),
            'period': f"{(report_time - timedelta(hours=4)).strftime('%H:%M')} - {report_time.strftime('%H:%M')}",
            'sections': []
        }
        
        # 1. 概覽統計
        performance = self.performance_analyzer.generate_performance_report(4)
        overview = ReportSection(
            title="📊 整體表現概覽",
            content={
                'active_conversations': performance['summary']['total_conversations'],
                'converted': performance['summary']['converted'],
                'pending': performance['summary']['pending'],
                'need_follow_up': performance['summary']['need_follow_up'],
                'avg_response_time': performance['summary']['avg_response_time'],
                'avg_quality_score': f"{performance['summary']['avg_quality_score']:.1f}/100"
            },
            priority='high'
        )
        report['sections'].append(asdict(overview))
        
        # 2. 需要即時處理的客戶
        urgent_follow_ups = [f for f in self.follow_up_tracker.identify_follow_up_customers(4) 
                            if f.priority.value in ['urgent', 'high']]
        
        if urgent_follow_ups:
            urgent_section = ReportSection(
                title="🚨 需要即時跟進",
                content={
                    'count': len(urgent_follow_ups),
                    'customers': [{
                        'name': f.customer_name,
                        'phone': f.phone_number,
                        'reason': f.follow_up_reason,
                        'last_contact': f.last_contact.strftime('%H:%M'),
                        'suggested_action': f.suggested_message
                    } for f in urgent_follow_ups[:5]]  # 只顯示前5個
                },
                priority='urgent'
            )
            report['sections'].append(asdict(urgent_section))
        
        # 3. 優秀對話案例
        best_conversations = performance.get('best_conversations', [])
        if best_conversations:
            best_cases = ReportSection(
                title="⭐ 優秀服務案例",
                content={
                    'count': len(best_conversations),
                    'examples': best_conversations[:3]  # 前3個案例
                },
                priority='medium'
            )
            report['sections'].append(asdict(best_cases))
        
        # 4. 關鍵指標警示
        alerts = self._generate_alerts(performance)
        if alerts:
            alert_section = ReportSection(
                title="⚠️ 關鍵指標警示",
                content={
                    'alerts': alerts
                },
                priority='high'
            )
            report['sections'].append(asdict(alert_section))
        
        # 5. 建議改進事項
        improvements = performance.get('areas_for_improvement', [])
        if improvements:
            improvement_section = ReportSection(
                title="💡 改進建議",
                content={
                    'suggestions': improvements
                },
                priority='medium'
            )
            report['sections'].append(asdict(improvement_section))
        
        # 保存報告
        self._save_report(report, 'hourly')
        
        return report
    
    def generate_daily_follow_up_list(self) -> Dict:
        """生成第二天跟進清單"""
        tomorrow = datetime.now() + timedelta(days=1)
        report = {
            'report_type': '每日跟進清單',
            'generated_at': datetime.now().isoformat(),
            'target_date': tomorrow.strftime('%Y-%m-%d'),
            'sections': []
        }
        
        # 獲取所有需要跟進的客戶
        all_follow_ups = self.follow_up_tracker.identify_follow_up_customers(168)  # 過去7天
        
        # 按優先級分組
        priority_groups = {
            'urgent': [],
            'high': [],
            'medium': [],
            'low': []
        }
        
        for follow_up in all_follow_ups:
            priority_groups[follow_up.priority.value].append(follow_up)
        
        # 生成各優先級的跟進計劃
        for priority, customers in priority_groups.items():
            if customers:
                # 根據優先級設定跟進時間
                follow_up_times = self._suggest_follow_up_times(priority, len(customers))
                
                section = ReportSection(
                    title=f"📅 {priority.upper()} 優先級跟進 ({len(customers)}位)",
                    content={
                        'customers': [{
                            'name': c.customer_name,
                            'phone': c.phone_number,
                            'suggested_time': follow_up_times[i],
                            'last_contact': c.last_contact.strftime('%m/%d %H:%M'),
                            'stage': c.conversation_stage,
                            'interest': c.interest_level,
                            'message': c.suggested_message,
                            'tags': c.tags
                        } for i, c in enumerate(customers[:10])]  # 每個優先級最多顯示10個
                    },
                    priority=priority
                )
                report['sections'].append(asdict(section))
        
        # 添加執行建議
        execution_tips = ReportSection(
            title="📝 執行建議",
            content={
                'tips': [
                    "早上9-10點：聯繫URGENT級別客戶，把握黃金時段",
                    "上午10-12點：處理HIGH級別客戶，推進即將成交的案子",
                    "下午2-4點：跟進MEDIUM級別客戶，培育潛在機會",
                    "下午4-6點：聯繫LOW級別客戶，維護長期關係"
                ],
                'best_practices': [
                    "開場白要親切自然，提及上次對話內容",
                    "根據客戶興趣點切入，不要硬推銷",
                    "設定明確的下一步行動",
                    "記錄每次跟進結果，方便下次參考"
                ]
            },
            priority='info'
        )
        report['sections'].append(asdict(execution_tips))
        
        # 保存報告
        self._save_report(report, 'daily')
        
        return report
    
    def _generate_alerts(self, performance: Dict) -> List[Dict]:
        """生成警示信息"""
        alerts = []
        summary = performance['summary']
        
        # 轉化率警示
        if summary['total_conversations'] > 0:
            conversion_rate = summary['converted'] / summary['total_conversations']
            if conversion_rate < 0.2:
                alerts.append({
                    'type': '低轉化率',
                    'message': f'轉化率僅{conversion_rate*100:.1f}%，建議檢查話術和跟進策略',
                    'severity': 'high'
                })
        
        # 響應時間警示
        if summary['avg_response_time'] != 'N/A':
            # 解析平均響應時間
            time_parts = summary['avg_response_time'].split('分')
            if len(time_parts) > 0:
                minutes = int(time_parts[0])
                if minutes > 30:
                    alerts.append({
                        'type': '響應緩慢',
                        'message': f'平均響應時間{summary["avg_response_time"]}，超過30分鐘',
                        'severity': 'medium'
                    })
        
        # 待跟進數量警示
        if summary['need_follow_up'] > 10:
            alerts.append({
                'type': '積壓跟進',
                'message': f'有{summary["need_follow_up"]}位客戶待跟進，請及時處理',
                'severity': 'high'
            })
        
        return alerts
    
    def _suggest_follow_up_times(self, priority: str, count: int) -> List[str]:
        """建議跟進時間"""
        times = []
        
        if priority == 'urgent':
            # 緊急客戶：上午9-10點
            base_time = 9
            interval = 60 // max(count, 1)  # 分鐘
            for i in range(count):
                hour = base_time + (i * interval) // 60
                minute = (i * interval) % 60
                times.append(f"{hour:02d}:{minute:02d}")
                
        elif priority == 'high':
            # 高優先級：上午10-12點
            base_time = 10
            interval = 120 // max(count, 1)
            for i in range(count):
                hour = base_time + (i * interval) // 60
                minute = (i * interval) % 60
                times.append(f"{hour:02d}:{minute:02d}")
                
        elif priority == 'medium':
            # 中優先級：下午2-4點
            base_time = 14
            interval = 120 // max(count, 1)
            for i in range(count):
                hour = base_time + (i * interval) // 60
                minute = (i * interval) % 60
                times.append(f"{hour:02d}:{minute:02d}")
                
        else:  # low
            # 低優先級：下午4-6點
            base_time = 16
            interval = 120 // max(count, 1)
            for i in range(count):
                hour = base_time + (i * interval) // 60
                minute = (i * interval) % 60
                times.append(f"{hour:02d}:{minute:02d}")
        
        return times
    
    def _save_report(self, report: Dict, report_type: str):
        """保存報告到文件"""
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        filename = f"{report_type}_report_{timestamp}.json"
        filepath = os.path.join(self.report_dir, filename)
        
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
            
        # 同時保存一份latest版本，方便查看
        latest_filename = f"{report_type}_report_latest.json"
        latest_filepath = os.path.join(self.report_dir, latest_filename)
        with open(latest_filepath, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
    
    def format_report_for_display(self, report: Dict, format: str = 'text') -> str:
        """格式化報告用於顯示"""
        if format == 'text':
            lines = []
            lines.append(f"=== {report['report_type']} ===")
            lines.append(f"生成時間：{report['generated_at']}")
            
            if 'period' in report:
                lines.append(f"統計週期：{report['period']}")
            elif 'target_date' in report:
                lines.append(f"目標日期：{report['target_date']}")
                
            lines.append("")
            
            for section in report['sections']:
                lines.append(f"\n{section['title']}")
                lines.append("-" * 40)
                
                content = section['content']
                if isinstance(content, dict):
                    for key, value in content.items():
                        if isinstance(value, list):
                            lines.append(f"{key}:")
                            for item in value:
                                if isinstance(item, dict):
                                    for k, v in item.items():
                                        lines.append(f"  - {k}: {v}")
                                    lines.append("")
                                else:
                                    lines.append(f"  - {item}")
                        else:
                            lines.append(f"{key}: {value}")
                            
            return '\n'.join(lines)
            
        else:  # JSON格式
            return json.dumps(report, ensure_ascii=False, indent=2)
    
    def start_scheduled_reports(self):
        """啟動定時報告任務（需要安裝 schedule 模組）"""
        try:
            import schedule
            # 每4小時生成監察報告
            schedule.every(4).hours.do(self.generate_4hour_report)
            
            # 每天晚上8點生成第二天跟進清單
            schedule.every().day.at("20:00").do(self.generate_daily_follow_up_list)
            
            print("定時報告服務已啟動...")
            print("- 每4小時生成監察報告")
            print("- 每晚8點生成次日跟進清單")
            
            while True:
                schedule.run_pending()
                time.sleep(60)  # 每分鐘檢查一次
        except ImportError:
            print("需要安裝 schedule 模組才能使用定時功能：pip install schedule")
            print("您可以手動調用 generate_4hour_report() 和 generate_daily_follow_up_list()")