import logging
import logging.config
import json
import sys
import os
from datetime import datetime
from typing import Dict, Any
from pythonjsonlogger import jsonlogger

class CustomJsonFormatter(jsonlogger.JsonFormatter):
    """自定義 JSON 日誌格式化器"""
    
    def add_fields(self, log_record, record, message_dict):
        super(CustomJsonFormatter, self).add_fields(log_record, record, message_dict)
        
        # 添加時間戳
        log_record['timestamp'] = datetime.utcnow().isoformat()
        
        # 添加服務信息
        log_record['service'] = 'healthcheck-ai-platform'
        log_record['environment'] = os.getenv('ENVIRONMENT', 'development')
        
        # 添加請求 ID（如果存在）
        if hasattr(record, 'request_id'):
            log_record['request_id'] = record.request_id
        
        # 添加用戶 ID（如果存在）
        if hasattr(record, 'user_id'):
            log_record['user_id'] = record.user_id

class ContextFilter(logging.Filter):
    """添加上下文信息的過濾器"""
    
    def filter(self, record):
        # 從線程本地存儲獲取上下文
        import threading
        context = getattr(threading.current_thread(), 'log_context', {})
        
        for key, value in context.items():
            setattr(record, key, value)
        
        return True

def setup_logging(log_level: str = None):
    """設置日誌配置"""
    
    log_level = log_level or os.getenv('LOG_LEVEL', 'INFO')
    log_format = os.getenv('LOG_FORMAT', 'json')  # json or text
    
    # 基本配置
    config = {
        'version': 1,
        'disable_existing_loggers': False,
        'filters': {
            'context_filter': {
                '()': ContextFilter,
            }
        },
        'formatters': {
            'json': {
                '()': CustomJsonFormatter,
                'format': '%(timestamp)s %(level)s %(name)s %(message)s'
            },
            'text': {
                'format': '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
                'datefmt': '%Y-%m-%d %H:%M:%S'
            },
            'detailed': {
                'format': '%(asctime)s - %(name)s - %(levelname)s - %(funcName)s:%(lineno)d - %(message)s',
                'datefmt': '%Y-%m-%d %H:%M:%S'
            }
        },
        'handlers': {
            'console': {
                'class': 'logging.StreamHandler',
                'level': log_level,
                'formatter': log_format,
                'stream': 'ext://sys.stdout',
                'filters': ['context_filter']
            },
            'file': {
                'class': 'logging.handlers.RotatingFileHandler',
                'level': log_level,
                'formatter': 'json',
                'filename': 'logs/healthcheck-ai.log',
                'maxBytes': 104857600,  # 100MB
                'backupCount': 10,
                'filters': ['context_filter']
            },
            'error_file': {
                'class': 'logging.handlers.RotatingFileHandler',
                'level': 'ERROR',
                'formatter': 'json',
                'filename': 'logs/healthcheck-ai-errors.log',
                'maxBytes': 104857600,  # 100MB
                'backupCount': 10,
                'filters': ['context_filter']
            }
        },
        'loggers': {
            '': {  # Root logger
                'handlers': ['console', 'file', 'error_file'],
                'level': log_level,
                'propagate': False
            },
            'healthcheck': {
                'handlers': ['console', 'file', 'error_file'],
                'level': log_level,
                'propagate': False
            },
            'uvicorn': {
                'handlers': ['console'],
                'level': 'INFO',
                'propagate': False
            },
            'sqlalchemy': {
                'handlers': ['console'],
                'level': 'WARNING',
                'propagate': False
            },
            'httpx': {
                'handlers': ['console'],
                'level': 'WARNING',
                'propagate': False
            }
        }
    }
    
    # 確保日誌目錄存在
    os.makedirs('logs', exist_ok=True)
    
    # 應用配置
    logging.config.dictConfig(config)
    
    # 設置第三方庫的日誌級別
    logging.getLogger('asyncio').setLevel(logging.WARNING)
    logging.getLogger('aiohttp').setLevel(logging.WARNING)
    
    logger = logging.getLogger(__name__)
    logger.info(f"Logging configured with level: {log_level}, format: {log_format}")

class LogContext:
    """日誌上下文管理器"""
    
    def __init__(self, **kwargs):
        self.context = kwargs
        self._old_context = None
    
    def __enter__(self):
        import threading
        thread = threading.current_thread()
        self._old_context = getattr(thread, 'log_context', {})
        new_context = self._old_context.copy()
        new_context.update(self.context)
        thread.log_context = new_context
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        import threading
        thread = threading.current_thread()
        thread.log_context = self._old_context

def log_function_call(logger_name: str = None):
    """記錄函數調用的裝飾器"""
    def decorator(func):
        from functools import wraps
        
        @wraps(func)
        async def async_wrapper(*args, **kwargs):
            logger = logging.getLogger(logger_name or func.__module__)
            
            # 記錄函數調用
            logger.debug(f"Calling {func.__name__} with args={args}, kwargs={kwargs}")
            
            start_time = datetime.now()
            try:
                result = await func(*args, **kwargs)
                duration = (datetime.now() - start_time).total_seconds()
                
                logger.debug(f"{func.__name__} completed in {duration:.3f}s")
                return result
                
            except Exception as e:
                duration = (datetime.now() - start_time).total_seconds()
                logger.error(
                    f"{func.__name__} failed after {duration:.3f}s",
                    exc_info=True,
                    extra={'function': func.__name__, 'error': str(e)}
                )
                raise
        
        @wraps(func)
        def sync_wrapper(*args, **kwargs):
            logger = logging.getLogger(logger_name or func.__module__)
            
            # 記錄函數調用
            logger.debug(f"Calling {func.__name__} with args={args}, kwargs={kwargs}")
            
            start_time = datetime.now()
            try:
                result = func(*args, **kwargs)
                duration = (datetime.now() - start_time).total_seconds()
                
                logger.debug(f"{func.__name__} completed in {duration:.3f}s")
                return result
                
            except Exception as e:
                duration = (datetime.now() - start_time).total_seconds()
                logger.error(
                    f"{func.__name__} failed after {duration:.3f}s",
                    exc_info=True,
                    extra={'function': func.__name__, 'error': str(e)}
                )
                raise
        
        import asyncio
        if asyncio.iscoroutinefunction(func):
            return async_wrapper
        else:
            return sync_wrapper
    
    return decorator

class StructuredLogger:
    """結構化日誌記錄器"""
    
    def __init__(self, name: str):
        self.logger = logging.getLogger(name)
    
    def log_event(self, event_type: str, level: str = "INFO", **kwargs):
        """記錄結構化事件"""
        log_data = {
            'event_type': event_type,
            'event_data': kwargs
        }
        
        level_map = {
            'DEBUG': self.logger.debug,
            'INFO': self.logger.info,
            'WARNING': self.logger.warning,
            'ERROR': self.logger.error,
            'CRITICAL': self.logger.critical
        }
        
        log_func = level_map.get(level.upper(), self.logger.info)
        log_func(json.dumps(log_data))
    
    def log_api_request(self, method: str, path: str, status_code: int, 
                       duration: float, **kwargs):
        """記錄 API 請求"""
        self.log_event(
            'api_request',
            level='INFO',
            method=method,
            path=path,
            status_code=status_code,
            duration=duration,
            **kwargs
        )
    
    def log_whatsapp_message(self, direction: str, sender: str, 
                           message_id: str, **kwargs):
        """記錄 WhatsApp 消息"""
        self.log_event(
            'whatsapp_message',
            level='INFO',
            direction=direction,
            sender=sender,
            message_id=message_id,
            **kwargs
        )
    
    def log_agent_action(self, agent_name: str, action: str, 
                        duration: float, **kwargs):
        """記錄 Agent 動作"""
        self.log_event(
            'agent_action',
            level='INFO',
            agent_name=agent_name,
            action=action,
            duration=duration,
            **kwargs
        )
    
    def log_conversion(self, customer_id: str, package_type: str, 
                      revenue: float, **kwargs):
        """記錄轉化事件"""
        self.log_event(
            'conversion',
            level='INFO',
            customer_id=customer_id,
            package_type=package_type,
            revenue=revenue,
            **kwargs
        )
    
    def log_error(self, error_type: str, error_message: str, **kwargs):
        """記錄錯誤"""
        self.log_event(
            'error',
            level='ERROR',
            error_type=error_type,
            error_message=error_message,
            **kwargs
        )
    
    def log_performance(self, operation: str, duration: float, **kwargs):
        """記錄性能數據"""
        self.log_event(
            'performance',
            level='DEBUG',
            operation=operation,
            duration=duration,
            **kwargs
        )

# 創建全局結構化日誌記錄器
structured_logger = StructuredLogger('healthcheck.structured')

# 審計日誌
class AuditLogger:
    """審計日誌記錄器"""
    
    def __init__(self):
        self.logger = logging.getLogger('healthcheck.audit')
        # 設置專門的審計日誌處理器
        handler = logging.handlers.RotatingFileHandler(
            'logs/audit.log',
            maxBytes=104857600,  # 100MB
            backupCount=30  # 保留30個備份
        )
        handler.setFormatter(CustomJsonFormatter())
        self.logger.addHandler(handler)
        self.logger.setLevel(logging.INFO)
    
    def log_access(self, user_id: str, resource: str, action: str, 
                  result: str, **kwargs):
        """記錄訪問日誌"""
        self.logger.info(json.dumps({
            'audit_type': 'access',
            'user_id': user_id,
            'resource': resource,
            'action': action,
            'result': result,
            'timestamp': datetime.utcnow().isoformat(),
            **kwargs
        }))
    
    def log_data_change(self, user_id: str, entity_type: str, 
                       entity_id: str, changes: Dict[str, Any], **kwargs):
        """記錄數據變更"""
        self.logger.info(json.dumps({
            'audit_type': 'data_change',
            'user_id': user_id,
            'entity_type': entity_type,
            'entity_id': entity_id,
            'changes': changes,
            'timestamp': datetime.utcnow().isoformat(),
            **kwargs
        }))
    
    def log_security_event(self, event_type: str, severity: str, 
                         details: Dict[str, Any], **kwargs):
        """記錄安全事件"""
        self.logger.warning(json.dumps({
            'audit_type': 'security',
            'event_type': event_type,
            'severity': severity,
            'details': details,
            'timestamp': datetime.utcnow().isoformat(),
            **kwargs
        }))

# 創建全局審計日誌記錄器
audit_logger = AuditLogger()