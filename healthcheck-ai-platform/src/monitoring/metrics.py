from typing import Dict, Any, Optional, List, Union
import time
import asyncio
from datetime import datetime, timedelta
from functools import wraps
import logging
import os
from collections import defaultdict, deque

try:
    from prometheus_client import Counter, Histogram, Gauge, Summary, Info
    from prometheus_client import generate_latest, CONTENT_TYPE_LATEST
    PROMETHEUS_AVAILABLE = True
except ImportError:
    PROMETHEUS_AVAILABLE = False

try:
    from opentelemetry import trace
    from opentelemetry.trace import Status, StatusCode
    from opentelemetry.sdk.trace import TracerProvider
    from opentelemetry.sdk.trace.export import BatchSpanProcessor
    from opentelemetry.exporter.otlp.proto.grpc.trace_exporter import OTLPSpanExporter
    from opentelemetry.sdk.resources import Resource
    from opentelemetry.instrumentation.fastapi import FastAPIInstrumentor
    OPENTELEMETRY_AVAILABLE = True
except ImportError:
    OPENTELEMETRY_AVAILABLE = False

logger = logging.getLogger(__name__)

class MetricsCollector:
    """系統指標收集器"""
    
    def __init__(self):
        self.metrics_enabled = os.getenv("METRICS_ENABLED", "true").lower() == "true"
        self.custom_metrics = {}
        self.metric_history = defaultdict(lambda: deque(maxlen=1000))
        
        if PROMETHEUS_AVAILABLE and self.metrics_enabled:
            self._init_prometheus_metrics()
        else:
            logger.info("Prometheus metrics disabled or unavailable")
    
    def _init_prometheus_metrics(self):
        """初始化 Prometheus 指標"""
        # 請求指標
        self.request_count = Counter(
            'healthcheck_requests_total',
            'Total number of requests',
            ['method', 'endpoint', 'status']
        )
        
        self.request_duration = Histogram(
            'healthcheck_request_duration_seconds',
            'Request duration in seconds',
            ['method', 'endpoint']
        )
        
        # WhatsApp 消息指標
        self.message_count = Counter(
            'healthcheck_whatsapp_messages_total',
            'Total WhatsApp messages processed',
            ['direction', 'status']  # direction: inbound/outbound, status: success/failed
        )
        
        self.message_processing_time = Histogram(
            'healthcheck_message_processing_seconds',
            'Message processing time in seconds',
            ['agent_type']
        )
        
        # 對話指標
        self.active_conversations = Gauge(
            'healthcheck_active_conversations',
            'Number of active conversations'
        )
        
        self.conversation_duration = Histogram(
            'healthcheck_conversation_duration_seconds',
            'Conversation duration in seconds',
            ['outcome']  # outcome: completed/abandoned
        )
        
        # 業務指標
        self.conversion_count = Counter(
            'healthcheck_conversions_total',
            'Total number of conversions',
            ['package_type', 'channel']
        )
        
        self.revenue_total = Counter(
            'healthcheck_revenue_total',
            'Total revenue generated',
            ['package_type', 'payment_method']
        )
        
        # AI Agent 指標
        self.agent_invocations = Counter(
            'healthcheck_agent_invocations_total',
            'Total agent invocations',
            ['agent_name', 'status']
        )
        
        self.agent_response_time = Histogram(
            'healthcheck_agent_response_seconds',
            'Agent response time in seconds',
            ['agent_name']
        )
        
        self.agent_accuracy = Gauge(
            'healthcheck_agent_accuracy',
            'Agent accuracy score',
            ['agent_name']
        )
        
        # LLM 指標
        self.llm_requests = Counter(
            'healthcheck_llm_requests_total',
            'Total LLM API requests',
            ['provider', 'model', 'status']
        )
        
        self.llm_tokens = Counter(
            'healthcheck_llm_tokens_total',
            'Total LLM tokens used',
            ['provider', 'model', 'token_type']  # token_type: prompt/completion
        )
        
        self.llm_cost = Counter(
            'healthcheck_llm_cost_dollars',
            'Total LLM API cost in dollars',
            ['provider', 'model']
        )
        
        # 系統指標
        self.error_count = Counter(
            'healthcheck_errors_total',
            'Total number of errors',
            ['error_type', 'severity']
        )
        
        self.cache_operations = Counter(
            'healthcheck_cache_operations_total',
            'Cache operations',
            ['operation', 'status']  # operation: get/set/delete, status: hit/miss/error
        )
        
        self.database_queries = Counter(
            'healthcheck_database_queries_total',
            'Database queries',
            ['query_type', 'status']
        )
        
        self.database_query_duration = Histogram(
            'healthcheck_database_query_seconds',
            'Database query duration',
            ['query_type']
        )
        
        # 服務健康指標
        self.service_health = Gauge(
            'healthcheck_service_health',
            'Service health status (1=healthy, 0=unhealthy)',
            ['component']
        )
        
        # 質量指標
        self.quality_score = Gauge(
            'healthcheck_quality_score',
            'Conversation quality score',
            ['dimension']  # dimension: accuracy/relevance/satisfaction
        )
        
        # 合規指標
        self.compliance_violations = Counter(
            'healthcheck_compliance_violations_total',
            'Compliance violations detected',
            ['violation_type', 'severity']
        )
    
    def record_request(self, method: str, endpoint: str, status: int, duration: float):
        """記錄 HTTP 請求"""
        if PROMETHEUS_AVAILABLE and self.metrics_enabled:
            self.request_count.labels(method=method, endpoint=endpoint, status=str(status)).inc()
            self.request_duration.labels(method=method, endpoint=endpoint).observe(duration)
        
        # 同時記錄到內存
        self._record_custom_metric("http_requests", {
            "method": method,
            "endpoint": endpoint,
            "status": status,
            "duration": duration,
            "timestamp": datetime.now().isoformat()
        })
    
    def record_message(self, direction: str, status: str, processing_time: float = None, agent: str = None):
        """記錄 WhatsApp 消息"""
        if PROMETHEUS_AVAILABLE and self.metrics_enabled:
            self.message_count.labels(direction=direction, status=status).inc()
            if processing_time and agent:
                self.message_processing_time.labels(agent_type=agent).observe(processing_time)
        
        self._record_custom_metric("whatsapp_messages", {
            "direction": direction,
            "status": status,
            "processing_time": processing_time,
            "agent": agent,
            "timestamp": datetime.now().isoformat()
        })
    
    def update_active_conversations(self, count: int):
        """更新活躍對話數"""
        if PROMETHEUS_AVAILABLE and self.metrics_enabled:
            self.active_conversations.set(count)
        
        self._record_custom_metric("active_conversations", {
            "count": count,
            "timestamp": datetime.now().isoformat()
        })
    
    def record_conversion(self, package_type: str, channel: str, revenue: float, payment_method: str):
        """記錄轉化"""
        if PROMETHEUS_AVAILABLE and self.metrics_enabled:
            self.conversion_count.labels(package_type=package_type, channel=channel).inc()
            self.revenue_total.labels(package_type=package_type, payment_method=payment_method).inc(revenue)
        
        self._record_custom_metric("conversions", {
            "package_type": package_type,
            "channel": channel,
            "revenue": revenue,
            "payment_method": payment_method,
            "timestamp": datetime.now().isoformat()
        })
    
    def record_agent_invocation(self, agent_name: str, status: str, response_time: float):
        """記錄 Agent 調用"""
        if PROMETHEUS_AVAILABLE and self.metrics_enabled:
            self.agent_invocations.labels(agent_name=agent_name, status=status).inc()
            self.agent_response_time.labels(agent_name=agent_name).observe(response_time)
        
        self._record_custom_metric("agent_invocations", {
            "agent_name": agent_name,
            "status": status,
            "response_time": response_time,
            "timestamp": datetime.now().isoformat()
        })
    
    def update_agent_accuracy(self, agent_name: str, accuracy: float):
        """更新 Agent 準確度"""
        if PROMETHEUS_AVAILABLE and self.metrics_enabled:
            self.agent_accuracy.labels(agent_name=agent_name).set(accuracy)
        
        self._record_custom_metric("agent_accuracy", {
            "agent_name": agent_name,
            "accuracy": accuracy,
            "timestamp": datetime.now().isoformat()
        })
    
    def record_llm_request(self, provider: str, model: str, status: str, 
                          prompt_tokens: int, completion_tokens: int, cost: float):
        """記錄 LLM 請求"""
        if PROMETHEUS_AVAILABLE and self.metrics_enabled:
            self.llm_requests.labels(provider=provider, model=model, status=status).inc()
            self.llm_tokens.labels(provider=provider, model=model, token_type="prompt").inc(prompt_tokens)
            self.llm_tokens.labels(provider=provider, model=model, token_type="completion").inc(completion_tokens)
            self.llm_cost.labels(provider=provider, model=model).inc(cost)
        
        self._record_custom_metric("llm_requests", {
            "provider": provider,
            "model": model,
            "status": status,
            "prompt_tokens": prompt_tokens,
            "completion_tokens": completion_tokens,
            "cost": cost,
            "timestamp": datetime.now().isoformat()
        })
    
    def record_error(self, error_type: str, severity: str = "medium"):
        """記錄錯誤"""
        if PROMETHEUS_AVAILABLE and self.metrics_enabled:
            self.error_count.labels(error_type=error_type, severity=severity).inc()
        
        self._record_custom_metric("errors", {
            "error_type": error_type,
            "severity": severity,
            "timestamp": datetime.now().isoformat()
        })
    
    def record_cache_operation(self, operation: str, status: str):
        """記錄緩存操作"""
        if PROMETHEUS_AVAILABLE and self.metrics_enabled:
            self.cache_operations.labels(operation=operation, status=status).inc()
        
        self._record_custom_metric("cache_operations", {
            "operation": operation,
            "status": status,
            "timestamp": datetime.now().isoformat()
        })
    
    def record_database_query(self, query_type: str, status: str, duration: float):
        """記錄數據庫查詢"""
        if PROMETHEUS_AVAILABLE and self.metrics_enabled:
            self.database_queries.labels(query_type=query_type, status=status).inc()
            self.database_query_duration.labels(query_type=query_type).observe(duration)
        
        self._record_custom_metric("database_queries", {
            "query_type": query_type,
            "status": status,
            "duration": duration,
            "timestamp": datetime.now().isoformat()
        })
    
    def update_service_health(self, component: str, healthy: bool):
        """更新服務健康狀態"""
        if PROMETHEUS_AVAILABLE and self.metrics_enabled:
            self.service_health.labels(component=component).set(1 if healthy else 0)
        
        self._record_custom_metric("service_health", {
            "component": component,
            "healthy": healthy,
            "timestamp": datetime.now().isoformat()
        })
    
    def update_quality_score(self, dimension: str, score: float):
        """更新質量分數"""
        if PROMETHEUS_AVAILABLE and self.metrics_enabled:
            self.quality_score.labels(dimension=dimension).set(score)
        
        self._record_custom_metric("quality_scores", {
            "dimension": dimension,
            "score": score,
            "timestamp": datetime.now().isoformat()
        })
    
    def record_compliance_violation(self, violation_type: str, severity: str):
        """記錄合規違規"""
        if PROMETHEUS_AVAILABLE and self.metrics_enabled:
            self.compliance_violations.labels(violation_type=violation_type, severity=severity).inc()
        
        self._record_custom_metric("compliance_violations", {
            "violation_type": violation_type,
            "severity": severity,
            "timestamp": datetime.now().isoformat()
        })
    
    def _record_custom_metric(self, metric_name: str, data: Dict[str, Any]):
        """記錄自定義指標到內存"""
        self.metric_history[metric_name].append(data)
    
    def get_metrics_summary(self, metric_name: Optional[str] = None, 
                           time_range: Optional[int] = 3600) -> Dict[str, Any]:
        """獲取指標摘要"""
        cutoff_time = datetime.now() - timedelta(seconds=time_range)
        
        if metric_name:
            metrics = list(self.metric_history.get(metric_name, []))
            # 過濾時間範圍
            metrics = [m for m in metrics 
                      if datetime.fromisoformat(m['timestamp']) > cutoff_time]
            return {metric_name: metrics}
        else:
            # 返回所有指標
            summary = {}
            for name, history in self.metric_history.items():
                metrics = [m for m in history 
                          if datetime.fromisoformat(m['timestamp']) > cutoff_time]
                summary[name] = metrics
            return summary
    
    def export_prometheus_metrics(self) -> bytes:
        """導出 Prometheus 格式的指標"""
        if PROMETHEUS_AVAILABLE and self.metrics_enabled:
            return generate_latest()
        return b""

# 性能監控裝飾器
def monitor_performance(metric_name: str = None):
    """監控函數性能的裝飾器"""
    def decorator(func):
        @wraps(func)
        async def async_wrapper(*args, **kwargs):
            start_time = time.time()
            try:
                result = await func(*args, **kwargs)
                duration = time.time() - start_time
                
                # 記錄性能指標
                if hasattr(args[0], 'metrics_collector'):
                    args[0].metrics_collector._record_custom_metric(
                        metric_name or f"function_{func.__name__}",
                        {
                            "duration": duration,
                            "status": "success",
                            "timestamp": datetime.now().isoformat()
                        }
                    )
                
                return result
            except Exception as e:
                duration = time.time() - start_time
                
                # 記錄錯誤
                if hasattr(args[0], 'metrics_collector'):
                    args[0].metrics_collector._record_custom_metric(
                        metric_name or f"function_{func.__name__}",
                        {
                            "duration": duration,
                            "status": "error",
                            "error": str(e),
                            "timestamp": datetime.now().isoformat()
                        }
                    )
                raise
        
        @wraps(func)
        def sync_wrapper(*args, **kwargs):
            start_time = time.time()
            try:
                result = func(*args, **kwargs)
                duration = time.time() - start_time
                
                # 記錄性能指標
                if hasattr(args[0], 'metrics_collector'):
                    args[0].metrics_collector._record_custom_metric(
                        metric_name or f"function_{func.__name__}",
                        {
                            "duration": duration,
                            "status": "success",
                            "timestamp": datetime.now().isoformat()
                        }
                    )
                
                return result
            except Exception as e:
                duration = time.time() - start_time
                
                # 記錄錯誤
                if hasattr(args[0], 'metrics_collector'):
                    args[0].metrics_collector._record_custom_metric(
                        metric_name or f"function_{func.__name__}",
                        {
                            "duration": duration,
                            "status": "error",
                            "error": str(e),
                            "timestamp": datetime.now().isoformat()
                        }
                    )
                raise
        
        if asyncio.iscoroutinefunction(func):
            return async_wrapper
        else:
            return sync_wrapper
    
    return decorator

# OpenTelemetry Tracing
class TracingManager:
    """分布式追蹤管理器"""
    
    def __init__(self):
        self.tracing_enabled = os.getenv("TRACING_ENABLED", "false").lower() == "true"
        self.tracer = None
        
        if OPENTELEMETRY_AVAILABLE and self.tracing_enabled:
            self._init_tracing()
    
    def _init_tracing(self):
        """初始化 OpenTelemetry 追蹤"""
        try:
            # 設置資源信息
            resource = Resource.create({
                "service.name": "healthcheck-ai-platform",
                "service.version": "1.0.0",
                "deployment.environment": os.getenv("ENVIRONMENT", "development")
            })
            
            # 設置追蹤提供者
            provider = TracerProvider(resource=resource)
            trace.set_tracer_provider(provider)
            
            # 設置導出器
            otlp_exporter = OTLPSpanExporter(
                endpoint=os.getenv("OTLP_ENDPOINT", "localhost:4317"),
                insecure=True
            )
            
            # 添加批處理器
            span_processor = BatchSpanProcessor(otlp_exporter)
            provider.add_span_processor(span_processor)
            
            # 獲取追蹤器
            self.tracer = trace.get_tracer("healthcheck-ai-platform")
            
            logger.info("OpenTelemetry tracing initialized")
        except Exception as e:
            logger.error(f"Failed to initialize tracing: {e}")
            self.tracing_enabled = False
    
    def create_span(self, name: str, attributes: Dict[str, Any] = None):
        """創建追蹤 span"""
        if self.tracer:
            span = self.tracer.start_span(name)
            if attributes:
                for key, value in attributes.items():
                    span.set_attribute(key, str(value))
            return span
        return None
    
    def instrument_fastapi(self, app):
        """為 FastAPI 應用添加自動追蹤"""
        if OPENTELEMETRY_AVAILABLE and self.tracing_enabled:
            try:
                FastAPIInstrumentor.instrument_app(app)
                logger.info("FastAPI instrumentation enabled")
            except Exception as e:
                logger.error(f"Failed to instrument FastAPI: {e}")

# 追蹤裝飾器
def trace_operation(operation_name: str = None):
    """追蹤操作的裝飾器"""
    def decorator(func):
        @wraps(func)
        async def async_wrapper(*args, **kwargs):
            # 獲取追蹤管理器
            tracing_manager = getattr(args[0], 'tracing_manager', None) if args else None
            
            if tracing_manager and tracing_manager.tracer:
                with tracing_manager.tracer.start_as_current_span(
                    operation_name or func.__name__
                ) as span:
                    try:
                        # 添加函數參數作為屬性
                        span.set_attribute("function.name", func.__name__)
                        span.set_attribute("function.module", func.__module__)
                        
                        result = await func(*args, **kwargs)
                        span.set_status(Status(StatusCode.OK))
                        return result
                    except Exception as e:
                        span.set_status(Status(StatusCode.ERROR, str(e)))
                        span.record_exception(e)
                        raise
            else:
                return await func(*args, **kwargs)
        
        @wraps(func)
        def sync_wrapper(*args, **kwargs):
            # 獲取追蹤管理器
            tracing_manager = getattr(args[0], 'tracing_manager', None) if args else None
            
            if tracing_manager and tracing_manager.tracer:
                with tracing_manager.tracer.start_as_current_span(
                    operation_name or func.__name__
                ) as span:
                    try:
                        # 添加函數參數作為屬性
                        span.set_attribute("function.name", func.__name__)
                        span.set_attribute("function.module", func.__module__)
                        
                        result = func(*args, **kwargs)
                        span.set_status(Status(StatusCode.OK))
                        return result
                    except Exception as e:
                        span.set_status(Status(StatusCode.ERROR, str(e)))
                        span.record_exception(e)
                        raise
            else:
                return func(*args, **kwargs)
        
        if asyncio.iscoroutinefunction(func):
            return async_wrapper
        else:
            return sync_wrapper
    
    return decorator

# 全局實例
metrics_collector = MetricsCollector()
tracing_manager = TracingManager()