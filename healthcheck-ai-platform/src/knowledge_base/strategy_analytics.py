#!/usr/bin/env python3
"""
策略分析和報告系統
追蹤策略效果並提供改善建議
"""

import sqlite3
import json
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple
import pandas as pd
import numpy as np
from collections import defaultdict, Counter
from dataclasses import dataclass
import matplotlib.pyplot as plt
import seaborn as sns
from matplotlib import rcParams

# 設定中文顯示
rcParams['font.sans-serif'] = ['PingFang HK', 'Arial Unicode MS', 'sans-serif']
rcParams['axes.unicode_minus'] = False


@dataclass
class StrategyPerformanceMetrics:
    """策略效能指標"""
    strategy_id: str
    strategy_name: str
    category: str
    total_executions: int
    success_count: int
    success_rate: float
    avg_conversion_time: float  # 小時
    revenue_impact: float
    customer_satisfaction: float
    trend: str  # improving, stable, declining
    roi: float
    
    @property
    def failure_count(self) -> int:
        return self.total_executions - self.success_count


class StrategyAnalytics:
    """策略分析引擎"""
    
    def __init__(self, kb_db_path: str, messages_db_path: str):
        self.kb_db_path = kb_db_path
        self.messages_db_path = messages_db_path
    
    def analyze_strategy_performance(self, 
                                   start_date: Optional[datetime] = None,
                                   end_date: Optional[datetime] = None) -> List[StrategyPerformanceMetrics]:
        """分析所有策略的效能"""
        if not end_date:
            end_date = datetime.now()
        if not start_date:
            start_date = end_date - timedelta(days=30)
        
        conn = sqlite3.connect(self.kb_db_path)
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()
        
        # 獲取所有策略及其執行結果
        strategies = cursor.execute("""
            SELECT 
                s.id,
                s.name,
                s.category,
                COUNT(so.id) as total_executions,
                SUM(CASE WHEN so.success THEN 1 ELSE 0 END) as success_count,
                AVG(CASE WHEN so.success THEN 1.0 ELSE 0.0 END) as success_rate
            FROM strategies s
            LEFT JOIN strategy_outcomes so ON s.id = so.strategy_id
            WHERE so.execution_date BETWEEN ? AND ?
            GROUP BY s.id, s.name, s.category
            HAVING total_executions > 0
        """, (start_date.isoformat(), end_date.isoformat())).fetchall()
        
        metrics_list = []
        
        for strategy in strategies:
            # 計算額外指標
            additional_metrics = self._calculate_additional_metrics(
                strategy['id'], start_date, end_date, cursor
            )
            
            metrics = StrategyPerformanceMetrics(
                strategy_id=strategy['id'],
                strategy_name=strategy['name'],
                category=strategy['category'],
                total_executions=strategy['total_executions'],
                success_count=strategy['success_count'],
                success_rate=strategy['success_rate'] or 0,
                avg_conversion_time=additional_metrics['avg_conversion_time'],
                revenue_impact=additional_metrics['revenue_impact'],
                customer_satisfaction=additional_metrics['customer_satisfaction'],
                trend=additional_metrics['trend'],
                roi=additional_metrics['roi']
            )
            
            metrics_list.append(metrics)
        
        conn.close()
        
        # 按成功率排序
        metrics_list.sort(key=lambda x: x.success_rate, reverse=True)
        
        return metrics_list
    
    def _calculate_additional_metrics(self, 
                                    strategy_id: str,
                                    start_date: datetime,
                                    end_date: datetime,
                                    cursor: sqlite3.Cursor) -> Dict[str, Any]:
        """計算額外的效能指標"""
        # 獲取詳細的執行結果
        outcomes = cursor.execute("""
            SELECT * FROM strategy_outcomes
            WHERE strategy_id = ? AND execution_date BETWEEN ? AND ?
            ORDER BY execution_date
        """, (strategy_id, start_date.isoformat(), end_date.isoformat())).fetchall()
        
        if not outcomes:
            return {
                'avg_conversion_time': 0,
                'revenue_impact': 0,
                'customer_satisfaction': 0,
                'trend': 'no_data',
                'roi': 0
            }
        
        # 計算平均轉化時間
        conversion_times = []
        revenue_impacts = []
        satisfaction_scores = []
        
        for outcome in outcomes:
            metrics = json.loads(outcome['metrics']) if outcome['metrics'] else {}
            
            if 'conversion_time_hours' in metrics:
                conversion_times.append(metrics['conversion_time_hours'])
            
            if 'revenue' in metrics:
                revenue_impacts.append(metrics['revenue'])
            
            if outcome['feedback']:
                # 假設反饋中包含滿意度評分
                try:
                    feedback = json.loads(outcome['feedback'])
                    if 'satisfaction_score' in feedback:
                        satisfaction_scores.append(feedback['satisfaction_score'])
                except:
                    pass
        
        # 計算趨勢
        trend = self._calculate_trend(outcomes)
        
        # 計算ROI
        total_revenue = sum(revenue_impacts) if revenue_impacts else 0
        strategy_cost = 100 * len(outcomes)  # 假設每次執行成本100元
        roi = (total_revenue - strategy_cost) / strategy_cost if strategy_cost > 0 else 0
        
        return {
            'avg_conversion_time': np.mean(conversion_times) if conversion_times else 0,
            'revenue_impact': total_revenue,
            'customer_satisfaction': np.mean(satisfaction_scores) if satisfaction_scores else 0,
            'trend': trend,
            'roi': roi
        }
    
    def _calculate_trend(self, outcomes: List[sqlite3.Row]) -> str:
        """計算策略效能趨勢"""
        if len(outcomes) < 10:
            return 'insufficient_data'
        
        # 將結果分為前半和後半
        mid_point = len(outcomes) // 2
        first_half = outcomes[:mid_point]
        second_half = outcomes[mid_point:]
        
        # 計算兩半的成功率
        first_success_rate = sum(1 for o in first_half if o['success']) / len(first_half)
        second_success_rate = sum(1 for o in second_half if o['success']) / len(second_half)
        
        # 判斷趨勢
        if second_success_rate > first_success_rate * 1.1:
            return 'improving'
        elif second_success_rate < first_success_rate * 0.9:
            return 'declining'
        else:
            return 'stable'
    
    def generate_performance_report(self, 
                                  metrics_list: List[StrategyPerformanceMetrics],
                                  output_path: str):
        """生成效能報告"""
        report = f"""
# 策略效能分析報告

**生成時間**: {datetime.now().strftime('%Y-%m-%d %H:%M')}

## 📈 整體概覽

- **分析策略數**: {len(metrics_list)}
- **總執行次數**: {sum(m.total_executions for m in metrics_list)}
- **平均成功率**: {np.mean([m.success_rate for m in metrics_list]):.1%}
- **總收入影響**: ${sum(m.revenue_impact for m in metrics_list):,.0f}

## 🏆 Top 5 最佳表現策略

| 排名 | 策略名稱 | 類別 | 成功率 | 執行次數 | 趨勢 | ROI |
|------|----------|------|---------|-----------|------|-----|
"""
        
        # 添加Top 5策略
        for i, metric in enumerate(metrics_list[:5], 1):
            trend_emoji = {
                'improving': '📈',
                'stable': '↔️',
                'declining': '📉',
                'insufficient_data': '❓',
                'no_data': '⚠️'
            }.get(metric.trend, '❓')
            
            report += f"| {i} | {metric.strategy_name} | {metric.category} | "
            report += f"{metric.success_rate:.1%} | {metric.total_executions} | "
            report += f"{trend_emoji} {metric.trend} | {metric.roi:.1%} |\n"
        
        # 按類別分析
        report += "\n## 📋 按類別分析\n\n"
        
        category_metrics = defaultdict(lambda: {
            'strategies': [],
            'total_executions': 0,
            'success_count': 0
        })
        
        for metric in metrics_list:
            cat = category_metrics[metric.category]
            cat['strategies'].append(metric)
            cat['total_executions'] += metric.total_executions
            cat['success_count'] += metric.success_count
        
        for category, data in category_metrics.items():
            avg_success_rate = data['success_count'] / data['total_executions'] if data['total_executions'] > 0 else 0
            report += f"\n### {category}\n"
            report += f"- 策略數: {len(data['strategies'])}\n"
            report += f"- 總執行次數: {data['total_executions']}\n"
            report += f"- 平均成功率: {avg_success_rate:.1%}\n"
        
        # 改善建議
        report += "\n## 💡 改善建議\n\n"
        
        # 找出需要改善的策略
        low_performers = [m for m in metrics_list if m.success_rate < 0.5 and m.total_executions >= 10]
        declining_strategies = [m for m in metrics_list if m.trend == 'declining']
        
        if low_performers:
            report += "### 需要優化的策略\n"
            for metric in low_performers[:3]:
                report += f"- **{metric.strategy_name}**: 成功率僅{metric.success_rate:.1%}\n"
                report += f"  - 建議：重新評估目標客群和執行方式\n"
        
        if declining_strategies:
            report += "\n### 表現下滑的策略\n"
            for metric in declining_strategies[:3]:
                report += f"- **{metric.strategy_name}**: 趨勢下滑\n"
                report += f"  - 建議：分析近期失敗案例，調整策略參數\n"
        
        # 成功案例
        report += "\n## 🌟 成功案例\n\n"
        
        high_roi_strategies = sorted([m for m in metrics_list if m.roi > 2], 
                                   key=lambda x: x.roi, reverse=True)[:3]
        
        if high_roi_strategies:
            report += "高ROI策略：\n"
            for metric in high_roi_strategies:
                report += f"- **{metric.strategy_name}**: ROI {metric.roi:.0%}\n"
                report += f"  - 成功因素：高成功率({metric.success_rate:.1%})和低成本\n"
        
        # 保存報告
        with open(output_path, 'w', encoding='utf-8') as f:
            f.write(report)
        
        return report
    
    def visualize_performance(self, 
                            metrics_list: List[StrategyPerformanceMetrics],
                            output_dir: str):
        """視覺化策略效能"""
        # 設定風格
        plt.style.use('seaborn-v0_8-darkgrid')
        
        # 1. 成功率分佈圖
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))
        
        # 成功率直方圖
        success_rates = [m.success_rate for m in metrics_list]
        ax1.hist(success_rates, bins=10, color='skyblue', edgecolor='black', alpha=0.7)
        ax1.set_xlabel('成功率')
        ax1.set_ylabel('策略數量')
        ax1.set_title('策略成功率分佈')
        ax1.axvline(np.mean(success_rates), color='red', linestyle='--', 
                   label=f'平均: {np.mean(success_rates):.1%}')
        ax1.legend()
        
        # 按類別的平均成功率
        category_success = defaultdict(list)
        for m in metrics_list:
            category_success[m.category].append(m.success_rate)
        
        categories = list(category_success.keys())
        avg_success = [np.mean(category_success[cat]) for cat in categories]
        
        bars = ax2.bar(categories, avg_success, color=plt.cm.Set3(range(len(categories))))
        ax2.set_xlabel('策略類別')
        ax2.set_ylabel('平均成功率')
        ax2.set_title('各類別策略平均成功率')
        ax2.set_ylim(0, 1)
        
        # 添加數值標籤
        for bar, rate in zip(bars, avg_success):
            height = bar.get_height()
            ax2.text(bar.get_x() + bar.get_width()/2., height,
                    f'{rate:.1%}', ha='center', va='bottom')
        
        plt.tight_layout()
        plt.savefig(f"{output_dir}/strategy_performance_overview.png", dpi=300, bbox_inches='tight')
        plt.close()
        
        # 2. 趨勢分析圖
        fig, ax = plt.subplots(figsize=(10, 6))
        
        trend_counts = Counter([m.trend for m in metrics_list])
        trends = list(trend_counts.keys())
        counts = list(trend_counts.values())
        
        colors = {
            'improving': 'green',
            'stable': 'yellow',
            'declining': 'red',
            'insufficient_data': 'gray',
            'no_data': 'lightgray'
        }
        
        bar_colors = [colors.get(t, 'blue') for t in trends]
        bars = ax.bar(trends, counts, color=bar_colors, alpha=0.7)
        
        ax.set_xlabel('趨勢')
        ax.set_ylabel('策略數量')
        ax.set_title('策略效能趨勢分析')
        
        # 添加數值標籤
        for bar, count in zip(bars, counts):
            height = bar.get_height()
            ax.text(bar.get_x() + bar.get_width()/2., height,
                   f'{count}', ha='center', va='bottom')
        
        plt.tight_layout()
        plt.savefig(f"{output_dir}/strategy_trend_analysis.png", dpi=300, bbox_inches='tight')
        plt.close()
        
        # 3. ROI vs 成功率散點圖
        fig, ax = plt.subplots(figsize=(10, 8))
        
        # 過濾掉異常值
        filtered_metrics = [m for m in metrics_list if -5 < m.roi < 10]
        
        x = [m.success_rate for m in filtered_metrics]
        y = [m.roi for m in filtered_metrics]
        sizes = [m.total_executions * 10 for m in filtered_metrics]
        
        # 按類別著色
        category_colors = {cat: plt.cm.Set3(i) for i, cat in enumerate(set(m.category for m in filtered_metrics))}
        colors = [category_colors[m.category] for m in filtered_metrics]
        
        scatter = ax.scatter(x, y, s=sizes, c=colors, alpha=0.6, edgecolors='black')
        
        ax.set_xlabel('成功率')
        ax.set_ylabel('ROI')
        ax.set_title('ROI vs 成功率分析')
        ax.axhline(y=0, color='gray', linestyle='--', alpha=0.5)
        ax.axvline(x=0.5, color='gray', linestyle='--', alpha=0.5)
        
        # 添加象限標籤
        ax.text(0.75, 3, '高成功率\n高ROI', ha='center', fontsize=12, alpha=0.7)
        ax.text(0.25, 3, '低成功率\n高ROI', ha='center', fontsize=12, alpha=0.7)
        ax.text(0.75, -2, '高成功率\n低ROI', ha='center', fontsize=12, alpha=0.7)
        ax.text(0.25, -2, '低成功率\n低ROI', ha='center', fontsize=12, alpha=0.7)
        
        # 添加圖例
        legend_elements = [plt.scatter([], [], c=color, s=100, label=cat) 
                          for cat, color in category_colors.items()]
        ax.legend(handles=legend_elements, title='策略類別', loc='upper left')
        
        plt.tight_layout()
        plt.savefig(f"{output_dir}/roi_vs_success_rate.png", dpi=300, bbox_inches='tight')
        plt.close()
    
    def get_strategy_recommendations(self, 
                                   customer_profile: Dict[str, Any]) -> List[Dict[str, Any]]:
        """為特定客戶推薦策略"""
        # 分析客戶特徵
        customer_type = self._classify_customer(customer_profile)
        
        # 獲取相關策略的效能數據
        conn = sqlite3.connect(self.kb_db_path)
        cursor = conn.cursor()
        
        # 查找適合該客戶類型的成功策略
        recommendations = []
        
        # 基於客戶類型推薦
        if customer_type == 'price_sensitive':
            # 推薦價值堆疊和團購策略
            strategies = cursor.execute("""
                SELECT s.*, 
                       AVG(CASE WHEN so.success THEN 1.0 ELSE 0.0 END) as success_rate
                FROM strategies s
                JOIN strategy_outcomes so ON s.id = so.strategy_id
                WHERE s.category IN ('pricing_strategy', 'promotion_campaigns')
                  AND s.status = 'active'
                GROUP BY s.id
                HAVING success_rate > 0.6
                ORDER BY success_rate DESC
                LIMIT 3
            """).fetchall()
            
        elif customer_type == 'health_conscious':
            # 推薦教育式和情感連結策略
            strategies = cursor.execute("""
                SELECT s.*, 
                       AVG(CASE WHEN so.success THEN 1.0 ELSE 0.0 END) as success_rate
                FROM strategies s
                JOIN strategy_outcomes so ON s.id = so.strategy_id
                WHERE s.category = 'communication_tactics'
                  AND s.tags LIKE '%education%'
                  AND s.status = 'active'
                GROUP BY s.id
                HAVING success_rate > 0.5
                ORDER BY success_rate DESC
                LIMIT 3
            """).fetchall()
            
        else:  # quick_decision 或其他
            # 推薦即時獎勵和快速轉化策略
            strategies = cursor.execute("""
                SELECT s.*, 
                       AVG(CASE WHEN so.success THEN 1.0 ELSE 0.0 END) as success_rate
                FROM strategies s
                JOIN strategy_outcomes so ON s.id = so.strategy_id
                WHERE s.category = 'conversion_optimization'
                  AND s.tags LIKE '%immediate%'
                  AND s.status = 'active'
                GROUP BY s.id
                HAVING success_rate > 0.5
                ORDER BY success_rate DESC
                LIMIT 3
            """).fetchall()
        
        # 構建推薦結果
        for strategy in strategies:
            recommendation = {
                'strategy_id': strategy['id'],
                'strategy_name': strategy['name'],
                'category': strategy['category'],
                'success_rate': strategy['success_rate'],
                'reason': self._generate_recommendation_reason(strategy, customer_type),
                'implementation_tips': self._get_implementation_tips(strategy, customer_profile)
            }
            recommendations.append(recommendation)
        
        conn.close()
        
        return recommendations
    
    def _classify_customer(self, customer_profile: Dict[str, Any]) -> str:
        """分類客戶類型"""
        # 基於客戶特徵判斷
        if customer_profile.get('price_mentioned', 0) > 2:
            return 'price_sensitive'
        elif customer_profile.get('health_questions', 0) > 3:
            return 'health_conscious'
        elif customer_profile.get('total_messages', 0) < 10:
            return 'quick_decision'
        else:
            return 'standard'
    
    def _generate_recommendation_reason(self, strategy: sqlite3.Row, customer_type: str) -> str:
        """生成推薦理由"""
        reasons = {
            'price_sensitive': f"這個策略在價格敏感客戶中有{strategy['success_rate']:.0%}的成功率",
            'health_conscious': f"通過教育內容建立信任，成功率達{strategy['success_rate']:.0%}",
            'quick_decision': f"快速決策客戶的理想選擇，成功率{strategy['success_rate']:.0%}",
            'standard': f"經過驗證的有效策略，平均成功率{strategy['success_rate']:.0%}"
        }
        return reasons.get(customer_type, f"成功率{strategy['success_rate']:.0%}的優質策略")
    
    def _get_implementation_tips(self, strategy: sqlite3.Row, customer_profile: Dict[str, Any]) -> List[str]:
        """獲取實施建議"""
        tips = []
        
        # 基於策略類型給出具體建議
        if 'immediate' in strategy['tags']:
            tips.append("在首次詢問後2小時內跟進")
            tips.append("強調優惠的時限性")
        
        if 'value' in strategy['tags']:
            tips.append("清楚展示總價值和優惠價格")
            tips.append("使用對比表格展示價值")
        
        if 'education' in strategy['tags']:
            tips.append("分享相關健康知識")
            tips.append("避免過度銷售")
        
        # 基於客戶特徵微調
        if customer_profile.get('preferred_time'):
            tips.append(f"在{customer_profile['preferred_time']}聯繫客戶")
        
        return tips


def main():
    """測試分析功能"""
    kb_db_path = "/Users/<USER>/Desktop/Claude/healthcheck-ai-platform/data/strategy_knowledge.db"
    messages_db_path = "/Users/<USER>/Desktop/Claude/whatsapp-mcp/whatsapp-bridge/store/messages.db"
    
    analytics = StrategyAnalytics(kb_db_path, messages_db_path)
    
    # 分析最近30天的策略效能
    metrics = analytics.analyze_strategy_performance()
    
    # 生成報告
    report_path = "/Users/<USER>/Desktop/Claude/healthcheck-ai-platform/reports/strategy_performance_report.md"
    analytics.generate_performance_report(metrics, report_path)
    
    # 生成視覺化
    viz_dir = "/Users/<USER>/Desktop/Claude/healthcheck-ai-platform/reports/visualizations"
    analytics.visualize_performance(metrics, viz_dir)
    
    print(f"📊 分析完成！")
    print(f"📄 報告保存在: {report_path}")
    print(f"🎨 視覺化保存在: {viz_dir}")


if __name__ == "__main__":
    main()