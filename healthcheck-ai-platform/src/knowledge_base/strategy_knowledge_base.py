#!/usr/bin/env python3
"""
策略知識庫系統 - HealthCheck AI Platform
用於收集、組織和優化銷售策略的智能知識管理系統
"""

import json
import sqlite3
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, asdict
from enum import Enum
import hashlib
from collections import defaultdict


class StrategyCategory(Enum):
    """策略分類"""
    CONVERSION = "conversion_optimization"  # 轉化優化
    PRICING = "pricing_strategy"  # 定價策略
    COMMUNICATION = "communication_tactics"  # 溝通策略
    FOLLOWUP = "followup_methods"  # 跟進方法
    OBJECTION = "objection_handling"  # 異議處理
    PROMOTION = "promotion_campaigns"  # 促銷活動
    CUSTOMER_JOURNEY = "customer_journey"  # 客戶旅程
    COMPETITOR = "competitor_analysis"  # 競爭分析


class StrategyStatus(Enum):
    """策略狀態"""
    DRAFT = "draft"  # 草稿
    TESTING = "testing"  # 測試中
    ACTIVE = "active"  # 活躍使用
    PAUSED = "paused"  # 暫停
    ARCHIVED = "archived"  # 已歸檔


@dataclass
class Strategy:
    """策略實體"""
    id: str
    name: str
    category: StrategyCategory
    description: str
    implementation: Dict[str, Any]
    target_metrics: Dict[str, float]
    actual_metrics: Dict[str, float]
    status: StrategyStatus
    created_at: datetime
    updated_at: datetime
    created_by: str
    tags: List[str]
    dependencies: List[str]  # 依賴的其他策略
    success_rate: float = 0.0
    usage_count: int = 0
    feedback_score: float = 0.0
    
    def to_dict(self) -> Dict[str, Any]:
        """轉換為字典"""
        data = asdict(self)
        data['category'] = self.category.value
        data['status'] = self.status.value
        data['created_at'] = self.created_at.isoformat()
        data['updated_at'] = self.updated_at.isoformat()
        return data


@dataclass
class StrategyOutcome:
    """策略執行結果"""
    strategy_id: str
    execution_date: datetime
    customer_id: str
    success: bool
    metrics: Dict[str, Any]
    feedback: Optional[str]
    context: Dict[str, Any]


class StrategyKnowledgeBase:
    """策略知識庫核心系統"""
    
    def __init__(self, db_path: str):
        self.db_path = db_path
        self._init_database()
    
    def _init_database(self):
        """初始化數據庫"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # 策略表
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS strategies (
                id TEXT PRIMARY KEY,
                name TEXT NOT NULL,
                category TEXT NOT NULL,
                description TEXT,
                implementation TEXT,
                target_metrics TEXT,
                actual_metrics TEXT,
                status TEXT NOT NULL,
                created_at TIMESTAMP,
                updated_at TIMESTAMP,
                created_by TEXT,
                tags TEXT,
                dependencies TEXT,
                success_rate REAL DEFAULT 0.0,
                usage_count INTEGER DEFAULT 0,
                feedback_score REAL DEFAULT 0.0
            )
        """)
        
        # 策略執行記錄表
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS strategy_outcomes (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                strategy_id TEXT NOT NULL,
                execution_date TIMESTAMP,
                customer_id TEXT,
                success BOOLEAN,
                metrics TEXT,
                feedback TEXT,
                context TEXT,
                FOREIGN KEY (strategy_id) REFERENCES strategies(id)
            )
        """)
        
        # 策略關聯表（用於追踪策略之間的關係）
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS strategy_relations (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                strategy_a TEXT NOT NULL,
                strategy_b TEXT NOT NULL,
                relation_type TEXT,
                strength REAL DEFAULT 0.0,
                FOREIGN KEY (strategy_a) REFERENCES strategies(id),
                FOREIGN KEY (strategy_b) REFERENCES strategies(id)
            )
        """)
        
        # 策略版本歷史表
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS strategy_versions (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                strategy_id TEXT NOT NULL,
                version INTEGER NOT NULL,
                changes TEXT,
                created_at TIMESTAMP,
                created_by TEXT,
                FOREIGN KEY (strategy_id) REFERENCES strategies(id)
            )
        """)
        
        conn.commit()
        conn.close()
    
    def create_strategy(self, 
                       name: str,
                       category: StrategyCategory,
                       description: str,
                       implementation: Dict[str, Any],
                       target_metrics: Dict[str, float],
                       created_by: str,
                       tags: List[str] = None,
                       dependencies: List[str] = None) -> Strategy:
        """創建新策略"""
        strategy_id = self._generate_strategy_id(name, category)
        now = datetime.now()
        
        strategy = Strategy(
            id=strategy_id,
            name=name,
            category=category,
            description=description,
            implementation=implementation,
            target_metrics=target_metrics,
            actual_metrics={},
            status=StrategyStatus.DRAFT,
            created_at=now,
            updated_at=now,
            created_by=created_by,
            tags=tags or [],
            dependencies=dependencies or []
        )
        
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute("""
            INSERT INTO strategies VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        """, (
            strategy.id,
            strategy.name,
            strategy.category.value,
            strategy.description,
            json.dumps(strategy.implementation),
            json.dumps(strategy.target_metrics),
            json.dumps(strategy.actual_metrics),
            strategy.status.value,
            strategy.created_at.isoformat(),
            strategy.updated_at.isoformat(),
            strategy.created_by,
            json.dumps(strategy.tags),
            json.dumps(strategy.dependencies),
            strategy.success_rate,
            strategy.usage_count,
            strategy.feedback_score
        ))
        
        conn.commit()
        conn.close()
        
        return strategy
    
    def _generate_strategy_id(self, name: str, category: StrategyCategory) -> str:
        """生成策略ID"""
        content = f"{name}_{category.value}_{datetime.now().isoformat()}"
        return hashlib.md5(content.encode()).hexdigest()[:12]
    
    def update_strategy_metrics(self, strategy_id: str, metrics: Dict[str, float]):
        """更新策略實際指標"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute("""
            UPDATE strategies 
            SET actual_metrics = ?, updated_at = ?
            WHERE id = ?
        """, (json.dumps(metrics), datetime.now().isoformat(), strategy_id))
        
        conn.commit()
        conn.close()
    
    def record_outcome(self, outcome: StrategyOutcome):
        """記錄策略執行結果"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute("""
            INSERT INTO strategy_outcomes 
            (strategy_id, execution_date, customer_id, success, metrics, feedback, context)
            VALUES (?, ?, ?, ?, ?, ?, ?)
        """, (
            outcome.strategy_id,
            outcome.execution_date.isoformat(),
            outcome.customer_id,
            outcome.success,
            json.dumps(outcome.metrics),
            outcome.feedback,
            json.dumps(outcome.context)
        ))
        
        # 更新策略使用次數和成功率
        cursor.execute("""
            UPDATE strategies
            SET usage_count = usage_count + 1,
                success_rate = (
                    SELECT AVG(CASE WHEN success THEN 1.0 ELSE 0.0 END)
                    FROM strategy_outcomes
                    WHERE strategy_id = ?
                )
            WHERE id = ?
        """, (outcome.strategy_id, outcome.strategy_id))
        
        conn.commit()
        conn.close()
    
    def get_strategy(self, strategy_id: str) -> Optional[Strategy]:
        """獲取單個策略"""
        conn = sqlite3.connect(self.db_path)
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()
        
        row = cursor.execute("""
            SELECT * FROM strategies WHERE id = ?
        """, (strategy_id,)).fetchone()
        
        conn.close()
        
        if row:
            return self._row_to_strategy(row)
        return None
    
    def _row_to_strategy(self, row: sqlite3.Row) -> Strategy:
        """將數據庫行轉換為Strategy對象"""
        return Strategy(
            id=row['id'],
            name=row['name'],
            category=StrategyCategory(row['category']),
            description=row['description'],
            implementation=json.loads(row['implementation']),
            target_metrics=json.loads(row['target_metrics']),
            actual_metrics=json.loads(row['actual_metrics']),
            status=StrategyStatus(row['status']),
            created_at=datetime.fromisoformat(row['created_at']),
            updated_at=datetime.fromisoformat(row['updated_at']),
            created_by=row['created_by'],
            tags=json.loads(row['tags']),
            dependencies=json.loads(row['dependencies']),
            success_rate=row['success_rate'],
            usage_count=row['usage_count'],
            feedback_score=row['feedback_score']
        )
    
    def search_strategies(self,
                         category: Optional[StrategyCategory] = None,
                         status: Optional[StrategyStatus] = None,
                         tags: Optional[List[str]] = None,
                         min_success_rate: Optional[float] = None) -> List[Strategy]:
        """搜索策略"""
        conn = sqlite3.connect(self.db_path)
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()
        
        query = "SELECT * FROM strategies WHERE 1=1"
        params = []
        
        if category:
            query += " AND category = ?"
            params.append(category.value)
        
        if status:
            query += " AND status = ?"
            params.append(status.value)
        
        if min_success_rate is not None:
            query += " AND success_rate >= ?"
            params.append(min_success_rate)
        
        if tags:
            # 使用JSON搜索標籤
            tag_conditions = []
            for tag in tags:
                tag_conditions.append("tags LIKE ?")
                params.append(f'%"{tag}"%')
            query += f" AND ({' OR '.join(tag_conditions)})"
        
        query += " ORDER BY success_rate DESC, usage_count DESC"
        
        rows = cursor.execute(query, params).fetchall()
        conn.close()
        
        return [self._row_to_strategy(row) for row in rows]
    
    def get_related_strategies(self, strategy_id: str, limit: int = 5) -> List[Tuple[Strategy, float]]:
        """獲取相關策略"""
        conn = sqlite3.connect(self.db_path)
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()
        
        # 查找直接關聯的策略
        related = cursor.execute("""
            SELECT s.*, r.strength
            FROM strategies s
            JOIN strategy_relations r ON (s.id = r.strategy_b OR s.id = r.strategy_a)
            WHERE (r.strategy_a = ? OR r.strategy_b = ?) AND s.id != ?
            ORDER BY r.strength DESC
            LIMIT ?
        """, (strategy_id, strategy_id, strategy_id, limit)).fetchall()
        
        conn.close()
        
        return [(self._row_to_strategy(row), row['strength']) for row in related]
    
    def analyze_strategy_performance(self, strategy_id: str, days: int = 30) -> Dict[str, Any]:
        """分析策略性能"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        end_date = datetime.now()
        start_date = end_date - timedelta(days=days)
        
        # 獲取指定時間範圍內的執行結果
        outcomes = cursor.execute("""
            SELECT * FROM strategy_outcomes
            WHERE strategy_id = ? AND execution_date >= ?
            ORDER BY execution_date
        """, (strategy_id, start_date.isoformat())).fetchall()
        
        if not outcomes:
            return {
                'total_executions': 0,
                'success_rate': 0,
                'trend': 'no_data'
            }
        
        # 計算性能指標
        total = len(outcomes)
        successes = sum(1 for o in outcomes if o['success'])
        
        # 計算趨勢（比較前半段和後半段）
        mid_point = total // 2
        first_half_success = sum(1 for o in outcomes[:mid_point] if o['success'])
        second_half_success = sum(1 for o in outcomes[mid_point:] if o['success'])
        
        first_rate = first_half_success / mid_point if mid_point > 0 else 0
        second_rate = second_half_success / (total - mid_point) if (total - mid_point) > 0 else 0
        
        if second_rate > first_rate * 1.1:
            trend = 'improving'
        elif second_rate < first_rate * 0.9:
            trend = 'declining'
        else:
            trend = 'stable'
        
        # 分析指標達成情況
        strategy = self.get_strategy(strategy_id)
        metric_achievement = {}
        
        if strategy and strategy.target_metrics:
            for metric, target in strategy.target_metrics.items():
                actual = strategy.actual_metrics.get(metric, 0)
                achievement = (actual / target * 100) if target > 0 else 0
                metric_achievement[metric] = {
                    'target': target,
                    'actual': actual,
                    'achievement_rate': achievement
                }
        
        conn.close()
        
        return {
            'total_executions': total,
            'success_rate': successes / total if total > 0 else 0,
            'trend': trend,
            'metric_achievement': metric_achievement,
            'period_days': days
        }
    
    def recommend_strategies(self, 
                           customer_profile: Dict[str, Any],
                           context: Dict[str, Any],
                           limit: int = 3) -> List[Tuple[Strategy, float]]:
        """基於客戶資料和上下文推薦策略"""
        # 獲取所有活躍策略
        active_strategies = self.search_strategies(status=StrategyStatus.ACTIVE)
        
        # 計算每個策略的相關性分數
        scored_strategies = []
        
        for strategy in active_strategies:
            score = self._calculate_relevance_score(strategy, customer_profile, context)
            scored_strategies.append((strategy, score))
        
        # 按分數排序並返回前N個
        scored_strategies.sort(key=lambda x: x[1], reverse=True)
        return scored_strategies[:limit]
    
    def _calculate_relevance_score(self,
                                 strategy: Strategy,
                                 customer_profile: Dict[str, Any],
                                 context: Dict[str, Any]) -> float:
        """計算策略相關性分數"""
        score = 0.0
        
        # 基礎成功率分數
        score += strategy.success_rate * 0.3
        
        # 類別匹配分數
        if context.get('need_type'):
            if strategy.category.value == context['need_type']:
                score += 0.2
        
        # 標籤匹配分數
        customer_tags = set(customer_profile.get('tags', []))
        strategy_tags = set(strategy.tags)
        tag_overlap = len(customer_tags & strategy_tags)
        if customer_tags:
            score += (tag_overlap / len(customer_tags)) * 0.2
        
        # 使用頻率分數（熱門策略）
        if strategy.usage_count > 100:
            score += 0.1
        elif strategy.usage_count > 50:
            score += 0.05
        
        # 反饋分數
        if strategy.feedback_score > 4.0:
            score += 0.1
        
        # 時效性分數（最近更新的策略）
        days_since_update = (datetime.now() - strategy.updated_at).days
        if days_since_update < 7:
            score += 0.1
        elif days_since_update < 30:
            score += 0.05
        
        return min(score, 1.0)  # 確保分數不超過1.0
    
    def export_knowledge_base(self, output_path: str):
        """導出知識庫"""
        conn = sqlite3.connect(self.db_path)
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()
        
        # 導出所有策略
        strategies = cursor.execute("SELECT * FROM strategies").fetchall()
        
        export_data = {
            'export_date': datetime.now().isoformat(),
            'total_strategies': len(strategies),
            'strategies': []
        }
        
        for row in strategies:
            strategy = self._row_to_strategy(row)
            
            # 獲取該策略的性能分析
            performance = self.analyze_strategy_performance(strategy.id)
            
            strategy_data = strategy.to_dict()
            strategy_data['performance'] = performance
            
            export_data['strategies'].append(strategy_data)
        
        # 按類別統計
        category_stats = defaultdict(int)
        for s in export_data['strategies']:
            category_stats[s['category']] += 1
        
        export_data['category_distribution'] = dict(category_stats)
        
        # 保存到文件
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(export_data, f, ensure_ascii=False, indent=2)
        
        conn.close()
        
        return export_data


class StrategyLearningEngine:
    """策略學習引擎 - 從執行結果中學習和優化"""
    
    def __init__(self, knowledge_base: StrategyKnowledgeBase):
        self.kb = knowledge_base
    
    def learn_from_outcomes(self, days: int = 30):
        """從最近的執行結果中學習"""
        conn = sqlite3.connect(self.kb.db_path)
        cursor = conn.cursor()
        
        end_date = datetime.now()
        start_date = end_date - timedelta(days=days)
        
        # 分析成功和失敗的模式
        outcomes = cursor.execute("""
            SELECT 
                so.*,
                s.category,
                s.tags
            FROM strategy_outcomes so
            JOIN strategies s ON so.strategy_id = s.id
            WHERE so.execution_date >= ?
        """, (start_date.isoformat(),)).fetchall()
        
        # 分析成功因素
        success_patterns = self._analyze_success_patterns(outcomes)
        
        # 生成優化建議
        recommendations = self._generate_optimization_recommendations(success_patterns)
        
        conn.close()
        
        return {
            'learning_period': days,
            'total_outcomes_analyzed': len(outcomes),
            'success_patterns': success_patterns,
            'recommendations': recommendations
        }
    
    def _analyze_success_patterns(self, outcomes: List[sqlite3.Row]) -> Dict[str, Any]:
        """分析成功模式"""
        success_factors = defaultdict(lambda: {'success': 0, 'total': 0})
        
        for outcome in outcomes:
            context = json.loads(outcome['context']) if outcome['context'] else {}
            
            # 分析各種因素
            factors = [
                ('category', outcome['category']),
                ('time_of_day', context.get('time_of_day')),
                ('customer_type', context.get('customer_type')),
                ('channel', context.get('channel'))
            ]
            
            for factor_name, factor_value in factors:
                if factor_value:
                    key = f"{factor_name}:{factor_value}"
                    success_factors[key]['total'] += 1
                    if outcome['success']:
                        success_factors[key]['success'] += 1
        
        # 計算成功率
        patterns = {}
        for factor, stats in success_factors.items():
            if stats['total'] >= 5:  # 至少5次執行才有統計意義
                success_rate = stats['success'] / stats['total']
                patterns[factor] = {
                    'success_rate': success_rate,
                    'sample_size': stats['total'],
                    'confidence': min(stats['total'] / 100, 1.0)  # 置信度
                }
        
        return patterns
    
    def _generate_optimization_recommendations(self, patterns: Dict[str, Any]) -> List[Dict[str, Any]]:
        """生成優化建議"""
        recommendations = []
        
        # 找出高成功率的模式
        high_success_patterns = [
            (pattern, data) for pattern, data in patterns.items()
            if data['success_rate'] > 0.7 and data['confidence'] > 0.5
        ]
        
        for pattern, data in high_success_patterns:
            factor_type, factor_value = pattern.split(':', 1)
            
            recommendation = {
                'type': 'leverage_success',
                'factor': factor_type,
                'value': factor_value,
                'success_rate': data['success_rate'],
                'confidence': data['confidence'],
                'action': f"增加在{factor_type}={factor_value}情況下的策略使用"
            }
            recommendations.append(recommendation)
        
        # 找出低成功率的模式
        low_success_patterns = [
            (pattern, data) for pattern, data in patterns.items()
            if data['success_rate'] < 0.3 and data['confidence'] > 0.5
        ]
        
        for pattern, data in low_success_patterns:
            factor_type, factor_value = pattern.split(':', 1)
            
            recommendation = {
                'type': 'improve_weakness',
                'factor': factor_type,
                'value': factor_value,
                'success_rate': data['success_rate'],
                'confidence': data['confidence'],
                'action': f"優化或避免在{factor_type}={factor_value}情況下的策略"
            }
            recommendations.append(recommendation)
        
        return recommendations
    
    def suggest_ab_tests(self) -> List[Dict[str, Any]]:
        """建議A/B測試"""
        # 獲取需要測試的策略
        strategies_to_test = self.kb.search_strategies(
            status=StrategyStatus.ACTIVE,
            min_success_rate=0.4  # 成功率在40%-70%之間的策略最適合測試
        )
        
        test_suggestions = []
        
        for strategy in strategies_to_test[:5]:  # 最多建議5個測試
            if 0.4 <= strategy.success_rate <= 0.7:
                # 為每個策略生成測試變體
                variants = self._generate_test_variants(strategy)
                
                test_suggestions.append({
                    'strategy_id': strategy.id,
                    'strategy_name': strategy.name,
                    'current_success_rate': strategy.success_rate,
                    'test_reason': '成功率有提升空間',
                    'variants': variants
                })
        
        return test_suggestions
    
    def _generate_test_variants(self, strategy: Strategy) -> List[Dict[str, Any]]:
        """生成測試變體"""
        variants = []
        
        # 基於策略類型生成不同的變體
        if strategy.category == StrategyCategory.PRICING:
            variants.extend([
                {
                    'name': '更激進的折扣',
                    'changes': {'discount_rate': 1.2},
                    'hypothesis': '更大的折扣可能提高轉化率'
                },
                {
                    'name': '階梯式定價',
                    'changes': {'pricing_model': 'tiered'},
                    'hypothesis': '給予選擇可能提高客戶滿意度'
                }
            ])
        elif strategy.category == StrategyCategory.COMMUNICATION:
            variants.extend([
                {
                    'name': '更個性化的溝通',
                    'changes': {'personalization_level': 'high'},
                    'hypothesis': '個性化可能提高參與度'
                },
                {
                    'name': '簡化信息',
                    'changes': {'message_complexity': 'simple'},
                    'hypothesis': '簡單直接可能更有效'
                }
            ])
        
        return variants