#!/usr/bin/env python3
"""
預設策略模板 - 基於實際銷售經驗的最佳實踐
"""

from datetime import datetime
from typing import Dict, List, Any
from strategy_knowledge_base import (
    StrategyKnowledgeBase, 
    StrategyCategory, 
    Strategy,
    StrategyStatus
)


class StrategyTemplates:
    """策略模板管理器"""
    
    @staticmethod
    def get_conversion_strategies() -> List[Dict[str, Any]]:
        """轉化優化策略模板"""
        return [
            {
                'name': '即時預約獎勵策略',
                'category': StrategyCategory.CONVERSION,
                'description': '通過即時獎勵機制促進快速決策',
                'implementation': {
                    'trigger': 'first_inquiry',
                    'window': '24_hours',
                    'reward': {
                        'type': 'additional_service',
                        'value': 300,
                        'description': '贈送價值300元額外檢查項目'
                    },
                    'communication': {
                        'message_template': '現在預約可獲得價值{value}元的{service}檢查，優惠僅限24小時！',
                        'followup_intervals': [2, 6, 20]  # 小時
                    }
                },
                'target_metrics': {
                    'conversion_rate': 0.25,
                    'decision_time_hours': 12,
                    'reward_redemption_rate': 0.8
                },
                'tags': ['immediate_reward', 'time_pressure', 'value_add']
            },
            {
                'name': '階梯式優惠策略',
                'category': StrategyCategory.CONVERSION,
                'description': '根據客戶決策時間提供不同級別優惠',
                'implementation': {
                    'tiers': [
                        {
                            'name': '黃金24小時',
                            'window': '0-24_hours',
                            'benefits': [
                                '免預約費',
                                '贈送300元檢查項目',
                                '優先預約時段'
                            ]
                        },
                        {
                            'name': '銀牌優惠',
                            'window': '24-72_hours',
                            'benefits': [
                                '預約費25元抵100元',
                                '贈送150元檢查項目'
                            ]
                        },
                        {
                            'name': '標準優惠',
                            'window': '72+_hours',
                            'benefits': [
                                '預約費50元抵100元'
                            ]
                        }
                    ],
                    'display_format': 'countdown_timer'
                },
                'target_metrics': {
                    'tier1_conversion': 0.3,
                    'tier2_conversion': 0.2,
                    'overall_conversion': 0.2
                },
                'tags': ['tiered_pricing', 'urgency', 'flexibility']
            },
            {
                'name': '智能跟進策略',
                'category': StrategyCategory.CONVERSION,
                'description': '基於客戶行為的個性化跟進',
                'implementation': {
                    'segments': [
                        {
                            'type': 'high_engagement',
                            'criteria': {
                                'messages': '>10',
                                'questions_asked': '>3'
                            },
                            'approach': {
                                'tone': 'consultative',
                                'focus': 'address_specific_concerns',
                                'offer': 'personalized_package'
                            }
                        },
                        {
                            'type': 'price_sensitive',
                            'criteria': {
                                'price_mentioned': True,
                                'comparison_asked': True
                            },
                            'approach': {
                                'tone': 'value_focused',
                                'focus': 'roi_demonstration',
                                'offer': 'price_match_guarantee'
                            }
                        },
                        {
                            'type': 'quick_decision',
                            'criteria': {
                                'messages': '<5',
                                'appointment_intent': True
                            },
                            'approach': {
                                'tone': 'efficient',
                                'focus': 'easy_booking',
                                'offer': 'fast_track_service'
                            }
                        }
                    ],
                    'followup_matrix': {
                        'high_engagement': [6, 24, 72],  # 小時
                        'price_sensitive': [12, 48, 96],
                        'quick_decision': [2, 12, 24]
                    }
                },
                'target_metrics': {
                    'segment_identification_accuracy': 0.85,
                    'personalized_conversion_rate': 0.25,
                    'customer_satisfaction': 4.5
                },
                'tags': ['personalization', 'behavioral_targeting', 'adaptive']
            }
        ]
    
    @staticmethod
    def get_pricing_strategies() -> List[Dict[str, Any]]:
        """定價策略模板"""
        return [
            {
                'name': '價值堆疊策略',
                'category': StrategyCategory.PRICING,
                'description': '通過堆疊價值而非降價來提升吸引力',
                'implementation': {
                    'base_package': {
                        'name': '130項全面體檢',
                        'price': 2950,
                        'original_value': 5000
                    },
                    'value_adds': [
                        {
                            'condition': 'immediate_booking',
                            'add': '甲狀腺超聲波',
                            'value': 800
                        },
                        {
                            'condition': '2_person_booking',
                            'add': '營養師諮詢',
                            'value': 500
                        },
                        {
                            'condition': 'online_payment',
                            'add': '身體組成分析',
                            'value': 300
                        }
                    ],
                    'presentation': {
                        'format': 'total_value_display',
                        'example': '總價值{total_value}元，現在只需{price}元'
                    }
                },
                'target_metrics': {
                    'perceived_value_score': 4.5,
                    'price_objection_rate': 0.1,
                    'upsell_rate': 0.3
                },
                'tags': ['value_stacking', 'psychological_pricing', 'no_discount']
            },
            {
                'name': '團購優惠策略',
                'category': StrategyCategory.PRICING,
                'description': '鼓勵團體預約以提高單次交易價值',
                'implementation': {
                    'tiers': [
                        {
                            'people': 2,
                            'discount': '10%',
                            'message': '2人同行享9折'
                        },
                        {
                            'people': 3,
                            'discount': '1人免費',
                            'message': '3人同行1人免費，平均每人省33%'
                        },
                        {
                            'people': 5,
                            'discount': '40%',
                            'message': '5人團購享6折超值優惠'
                        }
                    ],
                    'referral_bonus': {
                        'referrer': 100,  # 元優惠券
                        'referee': 50
                    },
                    'viral_mechanism': 'share_to_unlock_bonus'
                },
                'target_metrics': {
                    'group_booking_rate': 0.35,
                    'average_group_size': 2.5,
                    'viral_coefficient': 1.2
                },
                'tags': ['group_buying', 'social_proof', 'viral_growth']
            }
        ]
    
    @staticmethod
    def get_communication_strategies() -> List[Dict[str, Any]]:
        """溝通策略模板"""
        return [
            {
                'name': '教育式銷售策略',
                'category': StrategyCategory.COMMUNICATION,
                'description': '通過健康教育建立信任和需求認知',
                'implementation': {
                    'content_types': [
                        {
                            'type': 'health_tips',
                            'frequency': 'weekly',
                            'topics': [
                                '定期體檢的重要性',
                                '常見健康指標解讀',
                                '預防勝於治療的案例'
                            ]
                        },
                        {
                            'type': 'customer_stories',
                            'frequency': 'bi-weekly',
                            'format': 'anonymized_testimonials'
                        },
                        {
                            'type': 'seasonal_reminders',
                            'triggers': [
                                '換季時節',
                                '節日前後',
                                '流感季節'
                            ]
                        }
                    ],
                    'engagement_ladder': [
                        'awareness',
                        'interest',
                        'consideration',
                        'decision'
                    ],
                    'cta_placement': 'soft_sell_approach'
                },
                'target_metrics': {
                    'content_engagement_rate': 0.25,
                    'education_to_inquiry_rate': 0.15,
                    'trust_score': 4.2
                },
                'tags': ['content_marketing', 'trust_building', 'soft_sell']
            },
            {
                'name': '情感連結策略',
                'category': StrategyCategory.COMMUNICATION,
                'description': '建立情感連結促進決策',
                'implementation': {
                    'emotional_triggers': [
                        {
                            'trigger': 'family_care',
                            'message_angle': '為家人的安心，從自己的健康開始',
                            'target_segment': 'parents'
                        },
                        {
                            'trigger': 'career_success',
                            'message_angle': '健康是事業成功的基石',
                            'target_segment': 'professionals'
                        },
                        {
                            'trigger': 'peace_of_mind',
                            'message_angle': '定期檢查，讓您安心生活每一天',
                            'target_segment': 'health_conscious'
                        }
                    ],
                    'storytelling_framework': {
                        'structure': 'problem_agitation_solution',
                        'personalization': 'segment_based',
                        'proof_elements': 'statistics_and_testimonials'
                    },
                    'response_handling': {
                        'empathy_first': True,
                        'solution_second': True
                    }
                },
                'target_metrics': {
                    'emotional_resonance_score': 4.3,
                    'message_recall_rate': 0.7,
                    'referral_generation_rate': 0.2
                },
                'tags': ['emotional_marketing', 'storytelling', 'personalization']
            }
        ]
    
    @staticmethod
    def get_objection_handling_strategies() -> List[Dict[str, Any]]:
        """異議處理策略模板"""
        return [
            {
                'name': '價格異議處理策略',
                'category': StrategyCategory.OBJECTION,
                'description': '有效處理價格相關異議',
                'implementation': {
                    'objection_responses': {
                        '太貴了': {
                            'acknowledge': '我理解您對價格的考慮',
                            'reframe': '讓我幫您計算一下，這個體檢套餐包含130項檢查，平均每項只需{avg_price}元',
                            'value_prop': '而且我們的設備和醫療團隊都是頂級的，確保結果準確可靠',
                            'close': '您覺得哪個檢查項目對您最重要呢？'
                        },
                        '需要考慮一下': {
                            'acknowledge': '當然，這是個重要決定',
                            'probe': '請問是價格方面還是檢查項目方面需要再考慮呢？',
                            'offer': '我可以為您保留今天的優惠價格到{date}',
                            'close': '需要我為您預留時間嗎？'
                        },
                        '其他地方更便宜': {
                            'acknowledge': '價格確實是重要考慮因素',
                            'differentiate': '不過體檢最重要的是準確性和專業性',
                            'proof': '我們的設備都是最新的，報告由資深醫師解讀',
                            'close': '您最關心檢查結果的哪個方面呢？'
                        }
                    },
                    'prevention_tactics': [
                        'proactive_value_communication',
                        'comparison_chart_ready',
                        'roi_calculation_tool'
                    ]
                },
                'target_metrics': {
                    'objection_conversion_rate': 0.6,
                    'price_objection_reduction': 0.3,
                    'customer_confidence_score': 4.0
                },
                'tags': ['objection_handling', 'value_selling', 'consultative_approach']
            },
            {
                'name': '信任建立策略',
                'category': StrategyCategory.OBJECTION,
                'description': '解決信任相關的異議',
                'implementation': {
                    'trust_builders': [
                        {
                            'type': 'credentials',
                            'elements': [
                                '醫療機構認證',
                                '醫師資歷展示',
                                '設備認證證書'
                            ]
                        },
                        {
                            'type': 'social_proof',
                            'elements': [
                                '客戶見證（匿名）',
                                '每月服務人數',
                                '滿意度評分'
                            ]
                        },
                        {
                            'type': 'transparency',
                            'elements': [
                                '清晰的價格表',
                                '檢查流程說明',
                                '報告樣本展示'
                            ]
                        }
                    ],
                    'trust_reinforcement_sequence': [
                        'initial_credibility',
                        'ongoing_proof',
                        'personal_guarantee'
                    ]
                },
                'target_metrics': {
                    'trust_score_improvement': 0.5,
                    'credibility_objection_rate': 0.05,
                    'referral_rate': 0.25
                },
                'tags': ['trust_building', 'social_proof', 'transparency']
            }
        ]
    
    @staticmethod
    def get_followup_strategies() -> List[Dict[str, Any]]:
        """跟進策略模板"""
        return [
            {
                'name': '智能跟進序列策略',
                'category': StrategyCategory.FOLLOWUP,
                'description': '基於客戶互動程度的自適應跟進',
                'implementation': {
                    'sequences': {
                        'high_interest': {
                            'indicators': ['多次詢問', '查看詳情', '詢問時間'],
                            'timeline': [2, 24, 72],  # 小時
                            'messages': [
                                {
                                    'timing': 2,
                                    'content': '剛才的檢查方案還有什麼疑問嗎？',
                                    'offer': 'quick_consultation'
                                },
                                {
                                    'timing': 24,
                                    'content': '為您整理了個性化體檢建議',
                                    'offer': 'customized_package'
                                },
                                {
                                    'timing': 72,
                                    'content': '本週還有少量優質時段',
                                    'offer': 'priority_booking'
                                }
                            ]
                        },
                        'medium_interest': {
                            'indicators': ['一般詢問', '價格關注'],
                            'timeline': [24, 72, 168],  # 小時
                            'messages': [
                                {
                                    'timing': 24,
                                    'content': '為您準備了詳細的體檢指南',
                                    'offer': 'educational_content'
                                },
                                {
                                    'timing': 72,
                                    'content': '本月特別優惠即將結束',
                                    'offer': 'limited_time_discount'
                                },
                                {
                                    'timing': 168,
                                    'content': '定期體檢小提醒',
                                    'offer': 'health_tips'
                                }
                            ]
                        },
                        'low_interest': {
                            'indicators': ['簡單詢問', '無回應'],
                            'timeline': [72, 168, 336],  # 小時
                            'messages': [
                                {
                                    'timing': 72,
                                    'content': '體檢的最佳時機',
                                    'offer': 'seasonal_reminder'
                                },
                                {
                                    'timing': 168,
                                    'content': '健康資訊分享',
                                    'offer': 'valuable_content'
                                },
                                {
                                    'timing': 336,
                                    'content': '特別優惠預告',
                                    'offer': 'future_promotion'
                                }
                            ]
                        }
                    },
                    'personalization_elements': [
                        'name_usage',
                        'previous_concerns',
                        'preferred_time',
                        'communication_style'
                    ],
                    'stop_triggers': [
                        'explicit_no_interest',
                        'request_to_stop',
                        'booking_completed'
                    ]
                },
                'target_metrics': {
                    'followup_response_rate': 0.35,
                    'followup_conversion_rate': 0.2,
                    'opt_out_rate': 0.05
                },
                'tags': ['automated_followup', 'behavioral_triggers', 'respectful_persistence']
            },
            {
                'name': '休眠客戶激活策略',
                'category': StrategyCategory.FOLLOWUP,
                'description': '重新激活長期未響應的潛在客戶',
                'implementation': {
                    'reactivation_campaigns': [
                        {
                            'dormancy_period': '30-60_days',
                            'approach': 'gentle_reminder',
                            'message': '好久不見，最近身體還好嗎？',
                            'offer': 'free_health_consultation'
                        },
                        {
                            'dormancy_period': '60-90_days',
                            'approach': 'value_update',
                            'message': '我們推出了新的體檢項目',
                            'offer': 'exclusive_preview'
                        },
                        {
                            'dormancy_period': '90+_days',
                            'approach': 'win_back',
                            'message': '特別為您準備的專屬優惠',
                            'offer': 'vip_discount'
                        }
                    ],
                    'reactivation_triggers': [
                        'seasonal_health_events',
                        'birthday_month',
                        'anniversary_of_first_inquiry'
                    ],
                    'success_indicators': [
                        'message_opened',
                        'link_clicked',
                        'response_received'
                    ]
                },
                'target_metrics': {
                    'reactivation_rate': 0.15,
                    'dormant_to_booking_rate': 0.08,
                    'lifetime_value_increase': 0.2
                },
                'tags': ['win_back', 'reactivation', 'long_term_nurture']
            }
        ]


def initialize_strategy_templates(kb_path: str):
    """初始化策略模板到知識庫"""
    kb = StrategyKnowledgeBase(kb_path)
    
    # 獲取所有模板
    all_templates = []
    all_templates.extend(StrategyTemplates.get_conversion_strategies())
    all_templates.extend(StrategyTemplates.get_pricing_strategies())
    all_templates.extend(StrategyTemplates.get_communication_strategies())
    all_templates.extend(StrategyTemplates.get_objection_handling_strategies())
    all_templates.extend(StrategyTemplates.get_followup_strategies())
    
    # 創建策略
    created_strategies = []
    for template in all_templates:
        strategy = kb.create_strategy(
            name=template['name'],
            category=template['category'],
            description=template['description'],
            implementation=template['implementation'],
            target_metrics=template['target_metrics'],
            created_by='system',
            tags=template['tags']
        )
        created_strategies.append(strategy)
        print(f"✓ 創建策略: {strategy.name}")
    
    print(f"\n總共創建了 {len(created_strategies)} 個策略模板")
    return created_strategies


if __name__ == "__main__":
    # 測試初始化
    kb_path = "/Users/<USER>/Desktop/Claude/healthcheck-ai-platform/data/strategy_knowledge.db"
    initialize_strategy_templates(kb_path)