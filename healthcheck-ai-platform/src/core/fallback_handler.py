"""
回退處理器
實現置信度評分和人工介入機制
"""
import asyncio
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime, timedelta
from enum import Enum
import logging
from dataclasses import dataclass
import json

from .input_validator import InputValidator, input_validator
from .output_validator import OutputValidator, output_validator

logger = logging.getLogger(__name__)

class ConfidenceLevel(Enum):
    """置信度級別"""
    HIGH = "high"          # > 0.8
    MEDIUM = "medium"      # 0.6 - 0.8
    LOW = "low"           # 0.4 - 0.6
    CRITICAL = "critical"  # < 0.4

class EscalationReason(Enum):
    """升級原因"""
    LOW_CONFIDENCE = "low_confidence"
    CUSTOMER_REQUEST = "customer_request"
    SENSITIVE_TOPIC = "sensitive_topic"
    TECHNICAL_ERROR = "technical_error"
    COMPLEX_QUERY = "complex_query"
    REPEATED_FAILURE = "repeated_failure"
    MEDICAL_ADVICE = "medical_advice"
    COMPLAINT = "complaint"

@dataclass
class ConfidenceScore:
    """置信度評分"""
    overall_score: float
    input_score: float
    output_score: float
    context_score: float
    history_score: float
    level: ConfidenceLevel
    factors: Dict[str, float]
    
@dataclass
class FallbackDecision:
    """回退決策"""
    should_fallback: bool
    confidence_score: ConfidenceScore
    escalation_reason: Optional[EscalationReason]
    suggested_action: str
    human_agent_message: Optional[str]
    customer_message: str

class FallbackHandler:
    """回退處理器"""
    
    def __init__(self):
        self.input_validator = input_validator
        self.output_validator = output_validator
        
        # 置信度閾值
        self.thresholds = {
            "critical": 0.4,
            "low": 0.6,
            "medium": 0.8,
            "high": 1.0
        }
        
        # 複雜查詢模式
        self.complex_patterns = [
            r"比較.*?套餐.*?區別",
            r"(慢性|急性).*?疾病",
            r"體檢.*?異常.*?怎麼辦",
            r"(投訴|不滿|差評)",
            r"退款|賠償",
            r"醫療.*?建議",
            r"症狀.*?檢查"
        ]
        
        # 敏感主題
        self.sensitive_topics = [
            "投訴", "退款", "賠償", "法律", "糾紛",
            "醫療事故", "誤診", "副作用", "死亡"
        ]
        
        # 客戶請求人工服務的表達
        self.human_request_patterns = [
            r"(找|要|想要|需要).*?人工",
            r"真人.*?客服",
            r"不要.*?機器人",
            r"轉接.*?客服",
            r"人工.*?服務"
        ]
        
        # 失敗歷史記錄（用於檢測重複失敗）
        self.failure_history: Dict[str, List[datetime]] = {}
        
    async def evaluate_confidence(
        self,
        user_input: str,
        ai_output: str,
        context: Dict[str, Any]
    ) -> ConfidenceScore:
        """
        評估整體置信度
        
        Args:
            user_input: 用戶輸入
            ai_output: AI輸出
            context: 對話上下文
            
        Returns:
            ConfidenceScore: 置信度評分
        """
        factors = {}
        
        # 1. 輸入置信度
        input_validation = self.input_validator.validate_input(user_input, context)
        input_score = 1.0 if input_validation.is_valid else 0.5
        
        # 檢查是否為複雜查詢
        if self._is_complex_query(user_input):
            input_score *= 0.7
            factors["complex_query"] = True
            
        factors["input_validation"] = input_score
        
        # 2. 輸出置信度
        output_validation = self.output_validator.validate_output(ai_output, context)
        output_score = output_validation.confidence_score
        factors["output_validation"] = output_score
        
        # 3. 上下文置信度
        context_score = self._evaluate_context_confidence(context)
        factors["context_confidence"] = context_score
        
        # 4. 歷史置信度
        history_score = self._evaluate_history_confidence(context.get("customer_id", "unknown"))
        factors["history_confidence"] = history_score
        
        # 計算總體置信度（加權平均）
        weights = {
            "input": 0.2,
            "output": 0.4,
            "context": 0.2,
            "history": 0.2
        }
        
        overall_score = (
            input_score * weights["input"] +
            output_score * weights["output"] +
            context_score * weights["context"] +
            history_score * weights["history"]
        )
        
        # 確定置信度級別
        if overall_score >= self.thresholds["medium"]:
            level = ConfidenceLevel.HIGH
        elif overall_score >= self.thresholds["low"]:
            level = ConfidenceLevel.MEDIUM
        elif overall_score >= self.thresholds["critical"]:
            level = ConfidenceLevel.LOW
        else:
            level = ConfidenceLevel.CRITICAL
            
        return ConfidenceScore(
            overall_score=overall_score,
            input_score=input_score,
            output_score=output_score,
            context_score=context_score,
            history_score=history_score,
            level=level,
            factors=factors
        )
    
    async def make_fallback_decision(
        self,
        user_input: str,
        ai_output: str,
        context: Dict[str, Any],
        confidence_score: Optional[ConfidenceScore] = None
    ) -> FallbackDecision:
        """
        做出回退決策
        
        Args:
            user_input: 用戶輸入
            ai_output: AI輸出
            context: 對話上下文
            confidence_score: 置信度評分（如果已計算）
            
        Returns:
            FallbackDecision: 回退決策
        """
        # 如果沒有提供置信度評分，則計算
        if confidence_score is None:
            confidence_score = await self.evaluate_confidence(user_input, ai_output, context)
            
        # 檢查是否有客戶主動請求人工服務
        if self._check_human_request(user_input):
            return FallbackDecision(
                should_fallback=True,
                confidence_score=confidence_score,
                escalation_reason=EscalationReason.CUSTOMER_REQUEST,
                suggested_action="immediate_transfer",
                human_agent_message="客戶主動請求人工服務",
                customer_message="好的，我現在為您轉接人工客服，請稍等片刻..."
            )
        
        # 檢查是否涉及敏感話題
        sensitive_topic = self._check_sensitive_topic(user_input)
        if sensitive_topic:
            return FallbackDecision(
                should_fallback=True,
                confidence_score=confidence_score,
                escalation_reason=EscalationReason.SENSITIVE_TOPIC,
                suggested_action="immediate_transfer",
                human_agent_message=f"客戶諮詢涉及敏感話題：{sensitive_topic}",
                customer_message="您的問題比較重要，我為您轉接專業客服人員來協助您。"
            )
        
        # 基於置信度級別決定
        if confidence_score.level == ConfidenceLevel.CRITICAL:
            return FallbackDecision(
                should_fallback=True,
                confidence_score=confidence_score,
                escalation_reason=EscalationReason.LOW_CONFIDENCE,
                suggested_action="immediate_transfer",
                human_agent_message=f"AI置信度過低：{confidence_score.overall_score:.2f}",
                customer_message="抱歉，為了更好地幫助您，我需要請專業客服來協助。"
            )
        
        # 檢查是否有重複失敗
        if self._check_repeated_failures(context.get("customer_id", "unknown")):
            return FallbackDecision(
                should_fallback=True,
                confidence_score=confidence_score,
                escalation_reason=EscalationReason.REPEATED_FAILURE,
                suggested_action="immediate_transfer",
                human_agent_message="多次交互失敗，需要人工介入",
                customer_message="我注意到可能沒有很好地理解您的需求，讓我為您轉接專業客服。"
            )
        
        # 低置信度但不至於立即轉接
        if confidence_score.level == ConfidenceLevel.LOW:
            return FallbackDecision(
                should_fallback=False,
                confidence_score=confidence_score,
                escalation_reason=None,
                suggested_action="add_disclaimer",
                human_agent_message=None,
                customer_message=self._add_disclaimer(ai_output)
            )
        
        # 中等置信度
        if confidence_score.level == ConfidenceLevel.MEDIUM:
            return FallbackDecision(
                should_fallback=False,
                confidence_score=confidence_score,
                escalation_reason=None,
                suggested_action="monitor_closely",
                human_agent_message=None,
                customer_message=ai_output
            )
        
        # 高置信度
        return FallbackDecision(
            should_fallback=False,
            confidence_score=confidence_score,
            escalation_reason=None,
            suggested_action="proceed_normally",
            human_agent_message=None,
            customer_message=ai_output
        )
    
    def _is_complex_query(self, user_input: str) -> bool:
        """檢查是否為複雜查詢"""
        import re
        
        for pattern in self.complex_patterns:
            if re.search(pattern, user_input, re.IGNORECASE):
                return True
                
        # 檢查問題長度
        if len(user_input) > 200:
            return True
            
        # 檢查是否包含多個問題
        question_marks = user_input.count("？") + user_input.count("?")
        if question_marks > 2:
            return True
            
        return False
    
    def _evaluate_context_confidence(self, context: Dict[str, Any]) -> float:
        """評估上下文置信度"""
        score = 1.0
        
        # 對話輪次過多
        if context.get("message_count", 0) > 20:
            score *= 0.8
            
        # 客戶情緒
        if context.get("customer_sentiment") == "negative":
            score *= 0.7
            
        # 對話主題變化
        if context.get("topic_changes", 0) > 3:
            score *= 0.8
            
        return score
    
    def _evaluate_history_confidence(self, customer_id: str) -> float:
        """評估歷史交互置信度"""
        # 檢查最近的失敗記錄
        if customer_id in self.failure_history:
            recent_failures = [
                f for f in self.failure_history[customer_id]
                if f > datetime.now() - timedelta(hours=1)
            ]
            
            if len(recent_failures) > 3:
                return 0.5
            elif len(recent_failures) > 1:
                return 0.7
                
        return 1.0
    
    def _check_human_request(self, user_input: str) -> bool:
        """檢查是否有人工服務請求"""
        import re
        
        for pattern in self.human_request_patterns:
            if re.search(pattern, user_input, re.IGNORECASE):
                return True
        return False
    
    def _check_sensitive_topic(self, user_input: str) -> Optional[str]:
        """檢查是否涉及敏感話題"""
        for topic in self.sensitive_topics:
            if topic in user_input:
                return topic
        return None
    
    def _check_repeated_failures(self, customer_id: str) -> bool:
        """檢查是否有重複失敗"""
        if customer_id in self.failure_history:
            recent_failures = [
                f for f in self.failure_history[customer_id]
                if f > datetime.now() - timedelta(minutes=30)
            ]
            return len(recent_failures) >= 3
        return False
    
    def _add_disclaimer(self, ai_output: str) -> str:
        """添加免責聲明"""
        disclaimer = "\n\n如需更詳細的信息或有其他問題，歡迎隨時告訴我，或撥打客服熱線 3614 5393。"
        return ai_output + disclaimer
    
    def record_failure(self, customer_id: str):
        """記錄失敗"""
        if customer_id not in self.failure_history:
            self.failure_history[customer_id] = []
            
        self.failure_history[customer_id].append(datetime.now())
        
        # 清理舊記錄
        self.failure_history[customer_id] = [
            f for f in self.failure_history[customer_id]
            if f > datetime.now() - timedelta(hours=24)
        ]
    
    async def handle_escalation(
        self,
        decision: FallbackDecision,
        context: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        處理升級到人工客服
        
        Args:
            decision: 回退決策
            context: 對話上下文
            
        Returns:
            Dict[str, Any]: 處理結果
        """
        escalation_data = {
            "timestamp": datetime.now().isoformat(),
            "customer_id": context.get("customer_id"),
            "conversation_id": context.get("conversation_id"),
            "escalation_reason": decision.escalation_reason.value if decision.escalation_reason else None,
            "confidence_score": decision.confidence_score.overall_score,
            "last_customer_message": context.get("last_customer_message"),
            "suggested_ai_response": context.get("suggested_ai_response"),
            "conversation_summary": self._generate_conversation_summary(context)
        }
        
        # 在實際實現中，這裡應該：
        # 1. 通知人工客服系統
        # 2. 創建工單
        # 3. 將對話轉移到人工隊列
        
        logger.info(f"Escalating to human agent: {json.dumps(escalation_data, ensure_ascii=False)}")
        
        return {
            "status": "escalated",
            "escalation_data": escalation_data,
            "customer_message": decision.customer_message,
            "agent_notified": True
        }
    
    def _generate_conversation_summary(self, context: Dict[str, Any]) -> str:
        """生成對話摘要供人工客服參考"""
        summary_parts = []
        
        # 客戶基本信息
        if context.get("customer_name"):
            summary_parts.append(f"客戶：{context['customer_name']}")
            
        # 主要諮詢內容
        if context.get("identified_needs"):
            summary_parts.append(f"需求：{', '.join(context['identified_needs'])}")
            
        # 已報價的套餐
        if context.get("quoted_packages"):
            packages = [p['package'] for p in context['quoted_packages']]
            summary_parts.append(f"已報價：{', '.join(packages)}")
            
        # 客戶關注點
        if context.get("customer_concerns"):
            summary_parts.append(f"關注：{', '.join(context['customer_concerns'])}")
            
        return " | ".join(summary_parts) if summary_parts else "無摘要信息"

# 全局回退處理器實例
fallback_handler = FallbackHandler()