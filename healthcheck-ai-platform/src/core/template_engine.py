#!/usr/bin/env python3
"""
模板引擎 - 基於歷史學習的回覆生成系統
確保AI回覆風格與現有客服保持一致
"""

import json
import random
from typing import Dict, List, Any, Optional
from datetime import datetime
import re


class TemplateEngine:
    """模板引擎核心"""
    
    def __init__(self, template_path: str):
        self.template_path = template_path
        self.templates = self._load_templates()
        
    def _load_templates(self) -> Dict[str, Any]:
        """載入模板配置"""
        with open(self.template_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    
    def generate_greeting(self, context: Dict[str, Any] = None) -> str:
        """生成問候語"""
        if context and context.get('is_followup'):
            return self.templates['templates']['greeting']['followup']
        elif context and context.get('is_simple'):
            return self.templates['templates']['greeting']['simple']
        else:
            service_name = context.get('service_name', '健康體檢服務')
            return self.templates['templates']['greeting']['standard'].format(
                service_name=service_name
            )
    
    def generate_product_intro(self, package_type: str = '130') -> str:
        """生成產品介紹"""
        package_key = f"{package_type}_package"
        if package_key not in self.templates['templates']['product_intro']:
            package_key = '130_package'  # 默認
        
        template = self.templates['templates']['product_intro'][package_key]
        
        # 組合各部分
        parts = []
        for key in ['header', 'target', 'promotion', 'content', 'location', 'cta']:
            if key in template:
                parts.append(template[key])
        
        return '\n\n'.join(parts)
    
    def generate_price_quote(self, 
                           service: str,
                           price: float,
                           discount_info: Optional[Dict[str, Any]] = None) -> str:
        """生成價格報價"""
        if discount_info and 'group_size' in discount_info:
            # 團購價格
            original = price
            if discount_info['group_size'] == 2:
                final = price * 0.8  # 8折
                discount_desc = "2人同行8折"
            elif discount_info['group_size'] == 3:
                final = price * 2 / 3  # 買2送1
                discount_desc = "3人同行1人免費"
            else:
                final = price
                discount_desc = ""
            
            return self.templates['templates']['price_quote']['with_discount'].format(
                original=f"{original:,.0f}",
                discount_desc=discount_desc,
                final=f"{final:,.0f}"
            )
        else:
            # 標準價格
            gift_amount = 300 if '130' in service else 500
            return self.templates['templates']['price_quote']['simple'].format(
                service=service,
                price=f"{price:,.0f}",
                gift=gift_amount
            )
    
    def generate_promotion(self, promo_type: str = 'group') -> str:
        """生成優惠信息"""
        if promo_type == 'group':
            return self.templates['templates']['promotion']['group_discount'].format(
                discount1="8折",
                discount2="1人免費"
            )
        elif promo_type == 'flash':
            return self.templates['templates']['promotion']['flash_sale'].format(
                service="130項全面體檢"
            )
        return ""
    
    def generate_booking_confirmation(self, style: str = 'simple') -> str:
        """生成預約確認"""
        confirmations = self.templates['templates']['booking_confirmation']
        if style in confirmations:
            return confirmations[style]
        return confirmations['simple']
    
    def generate_reminder(self, 
                         reminder_type: str,
                         details: Dict[str, Any]) -> str:
        """生成提醒消息"""
        if reminder_type == 'appointment':
            return self.templates['templates']['reminder']['appointment'].format(
                date=details.get('date', ''),
                time=details.get('time', ''),
                service=details.get('service', '')
            )
        elif reminder_type == 'sample':
            return self.templates['templates']['reminder']['sample'].format(
                name=details.get('name', '客戶')
            )
        return ""
    
    def add_cantonese_particles(self, text: str) -> str:
        """添加粵語語氣詞"""
        particles = self.templates['templates']['common_phrases']['cantonese_particles']
        
        # 句尾添加語氣詞的規則
        if text.endswith('？'):
            text = text[:-1] + random.choice(['呢？', '呀？', '？'])
        elif text.endswith('。'):
            text = text[:-1] + random.choice(['啦。', '囉。', '呀。', '。'])
        elif text.endswith('！'):
            text = text[:-1] + random.choice(['呀！', '啦！', '！'])
        
        return text
    
    def format_price(self, amount: float) -> str:
        """格式化價格"""
        return f"${amount:,.0f}"
    
    def get_emoji(self, emoji_type: str) -> str:
        """獲取表情符號"""
        emojis = self.templates['templates']['common_phrases']['emojis']
        return emojis.get(emoji_type, '')
    
    def apply_style_guide(self, text: str) -> str:
        """應用風格指南"""
        # 確保使用正確的表情符號
        text = text.replace('- ', '✅ ')  # 列表項目
        text = text.replace('地址：', '📍 ')  # 地點
        text = text.replace('優惠：', '🎁 ')  # 優惠
        
        # 添加適當的語氣詞
        if random.random() < 0.3:  # 30%機率添加語氣詞
            text = self.add_cantonese_particles(text)
        
        return text


class ResponseBuilder:
    """回覆構建器 - 組合模板生成完整回覆"""
    
    def __init__(self, template_engine: TemplateEngine):
        self.engine = template_engine
    
    def build_initial_inquiry_response(self, 
                                     service_type: str = '130',
                                     include_promotion: bool = True) -> str:
        """構建初次詢問回覆"""
        response_parts = []
        
        # 1. 產品介紹
        response_parts.append(self.engine.generate_product_intro(service_type))
        
        # 2. 額外優惠信息（如果需要）
        if include_promotion:
            response_parts.append(self.engine.generate_promotion('group'))
        
        return '\n\n'.join(response_parts)
    
    def build_price_inquiry_response(self,
                                   service: str,
                                   base_price: float,
                                   show_discounts: bool = True) -> str:
        """構建價格詢問回覆"""
        response_parts = []
        
        # 1. 問候
        response_parts.append(self.engine.generate_greeting({'is_simple': True}))
        
        # 2. 標準價格
        response_parts.append(self.engine.generate_price_quote(service, base_price))
        
        # 3. 團購優惠
        if show_discounts:
            response_parts.append("同行優惠：")
            response_parts.append(self.engine.generate_price_quote(
                service, base_price, {'group_size': 2}
            ))
            response_parts.append(self.engine.generate_price_quote(
                service, base_price, {'group_size': 3}
            ))
        
        # 4. 行動呼籲
        response_parts.append("\n📆 想預約？回覆 日期＋人數，我哋幫你即刻 book！")
        
        return '\n'.join(response_parts)
    
    def build_booking_confirmation_response(self,
                                          booking_details: Dict[str, Any],
                                          style: str = 'friendly') -> str:
        """構建預約確認回覆"""
        # 簡單確認
        confirmation = self.engine.generate_booking_confirmation(style)
        
        # 如果需要詳細信息
        if booking_details.get('need_reminder'):
            reminder = self.engine.generate_reminder('appointment', booking_details)
            return f"{confirmation}\n\n{reminder}"
        
        return confirmation
    
    def build_followup_response(self,
                              days_since_last_contact: int,
                              customer_name: Optional[str] = None) -> str:
        """構建跟進回覆"""
        response_parts = []
        
        # 1. 問候
        greeting = self.engine.generate_greeting({'is_followup': True})
        response_parts.append(greeting)
        
        # 2. 如果超過3天沒回覆，加入限時優惠
        if days_since_last_contact > 3:
            response_parts.append(self.engine.generate_promotion('flash'))
        
        return '\n\n'.join(response_parts)


class SmartResponseGenerator:
    """智能回覆生成器 - 結合模板和AI"""
    
    def __init__(self, template_engine: TemplateEngine):
        self.engine = template_engine
        self.builder = ResponseBuilder(template_engine)
    
    def generate_response(self,
                        intent: str,
                        context: Dict[str, Any]) -> str:
        """根據意圖生成回覆"""
        
        # 根據不同意圖選擇模板
        if intent == 'price_inquiry':
            return self.builder.build_price_inquiry_response(
                service=context.get('service', '130項全面體檢'),
                base_price=context.get('base_price', 2950),
                show_discounts=context.get('show_discounts', True)
            )
        
        elif intent == 'service_inquiry':
            return self.builder.build_initial_inquiry_response(
                service_type=context.get('service_type', '130'),
                include_promotion=True
            )
        
        elif intent == 'booking_request':
            return self.builder.build_booking_confirmation_response(
                booking_details=context,
                style='friendly'
            )
        
        elif intent == 'followup':
            return self.builder.build_followup_response(
                days_since_last_contact=context.get('days_inactive', 0),
                customer_name=context.get('customer_name')
            )
        
        else:
            # 默認回覆
            return self.engine.generate_greeting({'is_simple': True})
    
    def ensure_consistency(self, ai_response: str) -> str:
        """確保AI回覆符合風格指南"""
        # 應用風格指南
        styled_response = self.engine.apply_style_guide(ai_response)
        
        # 確保價格格式正確
        price_pattern = r'(\d+)'
        def format_price_match(match):
            amount = int(match.group(1))
            if amount > 100:  # 假設超過100的是價格
                return self.engine.format_price(amount)
            return match.group(0)
        
        styled_response = re.sub(price_pattern + r'(?=\s*(?:元|蚊|$))', 
                                format_price_match, styled_response)
        
        return styled_response


def create_sample_responses():
    """創建示例回覆"""
    engine = TemplateEngine("/Users/<USER>/Desktop/Claude/healthcheck-ai-platform/config/response_templates.json")
    generator = SmartResponseGenerator(engine)
    
    print("=== 價格詢問回覆 ===")
    print(generator.generate_response('price_inquiry', {
        'service': '130項全面體檢',
        'base_price': 2950
    }))
    
    print("\n=== 服務介紹回覆 ===")
    print(generator.generate_response('service_inquiry', {
        'service_type': '130'
    }))
    
    print("\n=== 預約確認回覆 ===")
    print(generator.generate_response('booking_request', {
        'date': '15/01/2025',
        'time': '10:00',
        'service': '130項全面體檢',
        'location': '佐敦'
    }))
    
    print("\n=== 跟進回覆 ===")
    print(generator.generate_response('followup', {
        'days_inactive': 5,
        'customer_name': '陳小姐'
    }))


if __name__ == "__main__":
    create_sample_responses()