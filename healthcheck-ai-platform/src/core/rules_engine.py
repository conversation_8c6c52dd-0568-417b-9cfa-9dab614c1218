from typing import Dict, Any, List, Optional, Callable, Union
from datetime import datetime, date, time
from dataclasses import dataclass
from enum import Enum
import json
import logging
from abc import ABC, abstractmethod

logger = logging.getLogger(__name__)

class RuleOperator(Enum):
    """支援的規則運算符"""
    EQUALS = "equals"
    NOT_EQUALS = "not_equals"
    GREATER_THAN = "greater_than"
    LESS_THAN = "less_than"
    GREATER_EQUAL = "greater_equal"
    LESS_EQUAL = "less_equal"
    CONTAINS = "contains"
    NOT_CONTAINS = "not_contains"
    IN = "in"
    NOT_IN = "not_in"
    BETWEEN = "between"
    REGEX = "regex"
    
@dataclass
class RuleCondition:
    """規則條件"""
    field: str
    operator: RuleOperator
    value: Any
    
    def evaluate(self, context: Dict[str, Any]) -> bool:
        """評估條件是否滿足"""
        field_value = self._get_field_value(context, self.field)
        
        if field_value is None and self.operator != RuleOperator.EQUALS:
            return False
            
        try:
            if self.operator == RuleOperator.EQUALS:
                return field_value == self.value
            elif self.operator == RuleOperator.NOT_EQUALS:
                return field_value != self.value
            elif self.operator == RuleOperator.GREATER_THAN:
                return float(field_value) > float(self.value)
            elif self.operator == RuleOperator.LESS_THAN:
                return float(field_value) < float(self.value)
            elif self.operator == RuleOperator.GREATER_EQUAL:
                return float(field_value) >= float(self.value)
            elif self.operator == RuleOperator.LESS_EQUAL:
                return float(field_value) <= float(self.value)
            elif self.operator == RuleOperator.CONTAINS:
                return str(self.value) in str(field_value)
            elif self.operator == RuleOperator.NOT_CONTAINS:
                return str(self.value) not in str(field_value)
            elif self.operator == RuleOperator.IN:
                return field_value in self.value
            elif self.operator == RuleOperator.NOT_IN:
                return field_value not in self.value
            elif self.operator == RuleOperator.BETWEEN:
                if isinstance(self.value, (list, tuple)) and len(self.value) == 2:
                    return self.value[0] <= field_value <= self.value[1]
                return False
            elif self.operator == RuleOperator.REGEX:
                import re
                return bool(re.match(self.value, str(field_value)))
            else:
                return False
                
        except Exception as e:
            logger.error(f"Error evaluating condition: {e}")
            return False
    
    def _get_field_value(self, context: Dict[str, Any], field_path: str) -> Any:
        """從上下文中獲取欄位值，支援嵌套路徑"""
        parts = field_path.split('.')
        value = context
        
        for part in parts:
            if isinstance(value, dict):
                value = value.get(part)
            else:
                return None
                
        return value

@dataclass
class Rule:
    """業務規則"""
    id: str
    name: str
    description: str
    conditions: List[RuleCondition]
    actions: List[Dict[str, Any]]
    priority: int = 0
    enabled: bool = True
    logic: str = "AND"  # AND or OR
    
    def evaluate(self, context: Dict[str, Any]) -> bool:
        """評估規則是否滿足"""
        if not self.enabled:
            return False
            
        if not self.conditions:
            return True
            
        results = [condition.evaluate(context) for condition in self.conditions]
        
        if self.logic == "AND":
            return all(results)
        elif self.logic == "OR":
            return any(results)
        else:
            return False

class RuleAction(ABC):
    """規則動作基類"""
    
    @abstractmethod
    async def execute(self, context: Dict[str, Any], params: Dict[str, Any]) -> Any:
        """執行動作"""
        pass

class DiscountAction(RuleAction):
    """折扣動作"""
    
    async def execute(self, context: Dict[str, Any], params: Dict[str, Any]) -> Any:
        discount_type = params.get("type", "percentage")
        value = params.get("value", 0)
        
        if discount_type == "percentage":
            return {
                "type": "percentage_discount",
                "value": value,
                "description": f"{value}%優惠"
            }
        elif discount_type == "fixed":
            return {
                "type": "fixed_discount",
                "value": value,
                "description": f"減${value}"
            }
        else:
            return None

class FreeItemAction(RuleAction):
    """贈送項目動作"""
    
    async def execute(self, context: Dict[str, Any], params: Dict[str, Any]) -> Any:
        return {
            "type": "free_item",
            "items": params.get("items", []),
            "description": f"贈送: {', '.join(params.get('items', []))}"
        }

class PriorityAction(RuleAction):
    """優先級調整動作"""
    
    async def execute(self, context: Dict[str, Any], params: Dict[str, Any]) -> Any:
        return {
            "type": "priority_adjustment",
            "priority": params.get("priority", "normal"),
            "description": f"優先級: {params.get('priority', 'normal')}"
        }

class NotificationAction(RuleAction):
    """通知動作"""
    
    async def execute(self, context: Dict[str, Any], params: Dict[str, Any]) -> Any:
        return {
            "type": "notification",
            "channel": params.get("channel", "system"),
            "message": params.get("message", ""),
            "recipients": params.get("recipients", [])
        }

class RulesEngine:
    """業務規則引擎"""
    
    def __init__(self):
        self.rules: Dict[str, Rule] = {}
        self.actions: Dict[str, RuleAction] = {
            "discount": DiscountAction(),
            "free_item": FreeItemAction(),
            "priority": PriorityAction(),
            "notification": NotificationAction()
        }
        self._load_default_rules()
    
    def _load_default_rules(self):
        """加載默認規則"""
        # 早鳥優惠
        self.add_rule(Rule(
            id="early_bird_discount",
            name="早鳥優惠",
            description="早上預約享受額外折扣",
            conditions=[
                RuleCondition("appointment.time_slot", RuleOperator.EQUALS, "morning"),
                RuleCondition("appointment.advance_days", RuleOperator.GREATER_EQUAL, 3)
            ],
            actions=[
                {"type": "discount", "params": {"type": "percentage", "value": 5}}
            ],
            priority=10
        ))
        
        # VIP客戶優惠
        self.add_rule(Rule(
            id="vip_customer_benefits",
            name="VIP客戶優惠",
            description="VIP客戶專屬優惠",
            conditions=[
                RuleCondition("customer.total_purchases", RuleOperator.GREATER_EQUAL, 3),
                RuleCondition("customer.total_spent", RuleOperator.GREATER_EQUAL, 20000)
            ],
            actions=[
                {"type": "discount", "params": {"type": "percentage", "value": 10}},
                {"type": "free_item", "params": {"items": ["營養諮詢", "健康報告解讀"]}},
                {"type": "priority", "params": {"priority": "high"}}
            ],
            priority=20
        ))
        
        # 團體優惠
        self.add_rule(Rule(
            id="group_discount",
            name="團體優惠",
            description="3人以上團體優惠",
            conditions=[
                RuleCondition("order.group_size", RuleOperator.GREATER_EQUAL, 3)
            ],
            actions=[
                {"type": "discount", "params": {"type": "percentage", "value": 15}}
            ],
            priority=15
        ))
        
        # 生日月優惠
        self.add_rule(Rule(
            id="birthday_month_special",
            name="生日月優惠",
            description="生日月份特別優惠",
            conditions=[
                RuleCondition("customer.is_birthday_month", RuleOperator.EQUALS, True)
            ],
            actions=[
                {"type": "discount", "params": {"type": "fixed", "value": 500}},
                {"type": "free_item", "params": {"items": ["生日禮品"]}}
            ],
            priority=12
        ))
        
        # 首次客戶優惠
        self.add_rule(Rule(
            id="first_time_customer",
            name="首次客戶優惠",
            description="首次使用服務優惠",
            conditions=[
                RuleCondition("customer.total_purchases", RuleOperator.EQUALS, 0)
            ],
            actions=[
                {"type": "discount", "params": {"type": "percentage", "value": 20}}
            ],
            priority=18
        ))
        
        # 季節性促銷
        self.add_rule(Rule(
            id="seasonal_promotion",
            name="季節性促銷",
            description="特定季節的促銷活動",
            conditions=[
                RuleCondition("promotion.season", RuleOperator.EQUALS, "summer"),
                RuleCondition("package.category", RuleOperator.IN, ["全面檢查", "心血管檢查"])
            ],
            actions=[
                {"type": "discount", "params": {"type": "percentage", "value": 12}},
                {"type": "free_item", "params": {"items": ["防曬乳", "夏季健康指南"]}}
            ],
            priority=8
        ))
        
        # 緊急預約加價
        self.add_rule(Rule(
            id="urgent_appointment_surcharge",
            name="緊急預約加價",
            description="24小時內預約需要加價",
            conditions=[
                RuleCondition("appointment.advance_hours", RuleOperator.LESS_THAN, 24)
            ],
            actions=[
                {"type": "discount", "params": {"type": "percentage", "value": -10}}  # 負數表示加價
            ],
            priority=5
        ))
        
        # 長者優惠
        self.add_rule(Rule(
            id="senior_discount",
            name="長者優惠",
            description="65歲以上長者優惠",
            conditions=[
                RuleCondition("customer.age", RuleOperator.GREATER_EQUAL, 65)
            ],
            actions=[
                {"type": "discount", "params": {"type": "percentage", "value": 10}},
                {"type": "free_item", "params": {"items": ["輪椅服務", "陪同服務"]}}
            ],
            priority=14
        ))
    
    def add_rule(self, rule: Rule):
        """添加規則"""
        self.rules[rule.id] = rule
        logger.info(f"Added rule: {rule.name}")
    
    def remove_rule(self, rule_id: str):
        """移除規則"""
        if rule_id in self.rules:
            del self.rules[rule_id]
            logger.info(f"Removed rule: {rule_id}")
    
    def get_rule(self, rule_id: str) -> Optional[Rule]:
        """獲取規則"""
        return self.rules.get(rule_id)
    
    async def evaluate(self, context: Dict[str, Any]) -> List[Dict[str, Any]]:
        """評估所有規則並返回觸發的動作"""
        triggered_actions = []
        
        # 按優先級排序規則
        sorted_rules = sorted(self.rules.values(), key=lambda r: r.priority, reverse=True)
        
        for rule in sorted_rules:
            try:
                if rule.evaluate(context):
                    logger.info(f"Rule triggered: {rule.name}")
                    
                    # 執行規則動作
                    for action_config in rule.actions:
                        action_type = action_config.get("type")
                        action_params = action_config.get("params", {})
                        
                        if action_type in self.actions:
                            action_result = await self.actions[action_type].execute(context, action_params)
                            if action_result:
                                action_result["rule_id"] = rule.id
                                action_result["rule_name"] = rule.name
                                triggered_actions.append(action_result)
                        else:
                            logger.warning(f"Unknown action type: {action_type}")
                            
            except Exception as e:
                logger.error(f"Error evaluating rule {rule.id}: {e}")
        
        return triggered_actions
    
    def add_custom_action(self, name: str, action: RuleAction):
        """添加自定義動作"""
        self.actions[name] = action
        logger.info(f"Added custom action: {name}")

# 價格計算規則引擎
class PricingRulesEngine(RulesEngine):
    """專門用於價格計算的規則引擎"""
    
    def __init__(self):
        super().__init__()
        self._load_pricing_rules()
    
    def _load_pricing_rules(self):
        """加載價格相關規則"""
        # 套餐組合優惠
        self.add_rule(Rule(
            id="package_bundle_discount",
            name="套餐組合優惠",
            description="同時購買多個套餐的優惠",
            conditions=[
                RuleCondition("order.package_count", RuleOperator.GREATER_EQUAL, 2)
            ],
            actions=[
                {"type": "discount", "params": {"type": "percentage", "value": 8}}
            ],
            priority=16
        ))
        
        # 推薦獎勵
        self.add_rule(Rule(
            id="referral_reward",
            name="推薦獎勵",
            description="通過推薦碼購買的優惠",
            conditions=[
                RuleCondition("order.has_referral_code", RuleOperator.EQUALS, True)
            ],
            actions=[
                {"type": "discount", "params": {"type": "fixed", "value": 300}}
            ],
            priority=11
        ))
        
        # 付款方式優惠
        self.add_rule(Rule(
            id="payment_method_discount",
            name="付款方式優惠",
            description="特定付款方式的優惠",
            conditions=[
                RuleCondition("payment.method", RuleOperator.IN, ["cash", "fps"])
            ],
            actions=[
                {"type": "discount", "params": {"type": "percentage", "value": 2}}
            ],
            priority=6
        ))
    
    async def calculate_final_price(
        self, 
        base_price: float, 
        context: Dict[str, Any]
    ) -> Dict[str, Any]:
        """計算最終價格"""
        # 評估所有規則
        triggered_actions = await self.evaluate(context)
        
        # 計算折扣
        total_percentage_discount = 0
        total_fixed_discount = 0
        free_items = []
        applied_discounts = []
        
        for action in triggered_actions:
            if action["type"] == "percentage_discount":
                total_percentage_discount += action["value"]
                applied_discounts.append({
                    "rule": action["rule_name"],
                    "type": "percentage",
                    "value": action["value"],
                    "description": action["description"]
                })
            elif action["type"] == "fixed_discount":
                total_fixed_discount += action["value"]
                applied_discounts.append({
                    "rule": action["rule_name"],
                    "type": "fixed",
                    "value": action["value"],
                    "description": action["description"]
                })
            elif action["type"] == "free_item":
                free_items.extend(action["items"])
        
        # 應用折扣
        price_after_percentage = base_price * (1 - total_percentage_discount / 100)
        final_price = max(0, price_after_percentage - total_fixed_discount)
        
        # 計算節省金額
        total_savings = base_price - final_price
        savings_percentage = (total_savings / base_price * 100) if base_price > 0 else 0
        
        return {
            "base_price": base_price,
            "final_price": round(final_price, 2),
            "total_savings": round(total_savings, 2),
            "savings_percentage": round(savings_percentage, 2),
            "applied_discounts": applied_discounts,
            "free_items": list(set(free_items)),  # 去重
            "calculation_time": datetime.now().isoformat()
        }

# 全局規則引擎實例
rules_engine = RulesEngine()
pricing_rules_engine = PricingRulesEngine()