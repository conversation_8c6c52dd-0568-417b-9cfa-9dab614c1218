"""
知識庫管理系統
實現動態更新、版本控制和知識檢索
"""
import json
import hashlib
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime, timedelta
from enum import Enum
import logging
from dataclasses import dataclass, asdict
import asyncio
from pathlib import Path
import yaml

from .cache import cache_manager, <PERSON><PERSON><PERSON><PERSON>s
from ..database.models import KnowledgeBaseItem, KnowledgeVersion
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, desc

logger = logging.getLogger(__name__)

class KnowledgeCategory(Enum):
    """知識類別"""
    PACKAGES = "packages"
    PRICING = "pricing"
    LOCATIONS = "locations"
    PROCEDURES = "procedures"
    PROMOTIONS = "promotions"
    POLICIES = "policies"
    MEDICAL_INFO = "medical_info"
    FAQ = "faq"

@dataclass
class KnowledgeUpdate:
    """知識更新記錄"""
    item_id: str
    category: KnowledgeCategory
    action: str  # create, update, delete
    old_value: Optional[Dict[str, Any]]
    new_value: Optional[Dict[str, Any]]
    reason: str
    updated_by: str
    timestamp: datetime

class KnowledgeBaseManager:
    """知識庫管理器"""
    
    def __init__(self, db_session: Session, config_path: str = "config/knowledge"):
        self.db_session = db_session
        self.config_path = Path(config_path)
        self.cache_manager = cache_manager
        
        # 版本控制
        self.current_version = None
        self.version_history: List[KnowledgeVersion] = []
        
        # 知識索引（用於快速查找）
        self.knowledge_index: Dict[str, Dict[str, Any]] = {}
        
        # 變更追踪
        self.pending_updates: List[KnowledgeUpdate] = []
        
        # 初始化
        self._initialize()
        
    def _initialize(self):
        """初始化知識庫"""
        # 加載當前版本
        self.current_version = self._get_current_version()
        
        # 加載知識數據
        self._load_knowledge_data()
        
        # 建立索引
        self._build_index()
        
        logger.info(f"Knowledge base initialized with version: {self.current_version}")
    
    def _get_current_version(self) -> str:
        """獲取當前版本號"""
        latest_version = self.db_session.query(KnowledgeVersion)\
            .order_by(desc(KnowledgeVersion.created_at))\
            .first()
            
        if latest_version:
            return latest_version.version
        else:
            # 創建初始版本
            return self._create_initial_version()
    
    def _create_initial_version(self) -> str:
        """創建初始版本"""
        version = f"v1.0.0-{datetime.now().strftime('%Y%m%d')}"
        
        knowledge_version = KnowledgeVersion(
            version=version,
            description="Initial knowledge base version",
            created_by="system",
            is_active=True
        )
        
        self.db_session.add(knowledge_version)
        self.db_session.commit()
        
        return version
    
    def _load_knowledge_data(self):
        """從數據庫和配置文件加載知識數據"""
        # 從數據庫加載
        db_items = self.db_session.query(KnowledgeBaseItem)\
            .filter(KnowledgeBaseItem.is_active == True)\
            .all()
            
        for item in db_items:
            self.knowledge_index[item.item_id] = {
                "category": item.category,
                "topic": item.topic,
                "content": json.loads(item.content),
                "metadata": json.loads(item.metadata) if item.metadata else {},
                "confidence": item.confidence,
                "version": item.version,
                "last_updated": item.updated_at
            }
        
        # 從配置文件加載（用於初始化或更新）
        self._load_from_config_files()
    
    def _load_from_config_files(self):
        """從配置文件加載知識"""
        if not self.config_path.exists():
            logger.warning(f"Config path {self.config_path} does not exist")
            return
            
        for config_file in self.config_path.glob("*.yaml"):
            try:
                with open(config_file, 'r', encoding='utf-8') as f:
                    data = yaml.safe_load(f)
                    
                category = config_file.stem  # 文件名作為類別
                self._import_config_data(category, data)
                
            except Exception as e:
                logger.error(f"Error loading config file {config_file}: {e}")
    
    def _import_config_data(self, category: str, data: Dict[str, Any]):
        """導入配置數據"""
        for item_key, item_data in data.items():
            item_id = f"{category}_{item_key}"
            
            # 檢查是否已存在
            existing = self.knowledge_index.get(item_id)
            
            if not existing or self._should_update(existing, item_data):
                self.add_or_update_knowledge(
                    item_id=item_id,
                    category=KnowledgeCategory(category),
                    topic=item_key,
                    content=item_data,
                    reason="Imported from config file",
                    updated_by="system"
                )
    
    def _should_update(self, existing: Dict[str, Any], new_data: Dict[str, Any]) -> bool:
        """判斷是否需要更新"""
        # 比較內容哈希
        existing_hash = self._calculate_content_hash(existing["content"])
        new_hash = self._calculate_content_hash(new_data)
        
        return existing_hash != new_hash
    
    def _calculate_content_hash(self, content: Dict[str, Any]) -> str:
        """計算內容哈希值"""
        content_str = json.dumps(content, sort_keys=True, ensure_ascii=False)
        return hashlib.md5(content_str.encode()).hexdigest()
    
    def _build_index(self):
        """建立知識索引"""
        # 建立多維索引
        self.category_index: Dict[str, List[str]] = {}
        self.topic_index: Dict[str, List[str]] = {}
        self.tag_index: Dict[str, List[str]] = {}
        
        for item_id, item_data in self.knowledge_index.items():
            # 按類別索引
            category = item_data["category"]
            if category not in self.category_index:
                self.category_index[category] = []
            self.category_index[category].append(item_id)
            
            # 按主題索引
            topic = item_data["topic"]
            if topic not in self.topic_index:
                self.topic_index[topic] = []
            self.topic_index[topic].append(item_id)
            
            # 按標籤索引
            tags = item_data.get("metadata", {}).get("tags", [])
            for tag in tags:
                if tag not in self.tag_index:
                    self.tag_index[tag] = []
                self.tag_index[tag].append(item_id)
    
    async def add_or_update_knowledge(
        self,
        item_id: str,
        category: KnowledgeCategory,
        topic: str,
        content: Dict[str, Any],
        metadata: Optional[Dict[str, Any]] = None,
        confidence: float = 1.0,
        reason: str = "",
        updated_by: str = "system"
    ) -> bool:
        """添加或更新知識項"""
        try:
            # 檢查是否存在
            existing_item = self.db_session.query(KnowledgeBaseItem)\
                .filter(KnowledgeBaseItem.item_id == item_id)\
                .first()
            
            # 記錄變更
            if existing_item:
                old_value = {
                    "content": json.loads(existing_item.content),
                    "metadata": json.loads(existing_item.metadata) if existing_item.metadata else {}
                }
                action = "update"
            else:
                old_value = None
                action = "create"
            
            # 創建更新記錄
            update_record = KnowledgeUpdate(
                item_id=item_id,
                category=category,
                action=action,
                old_value=old_value,
                new_value={"content": content, "metadata": metadata},
                reason=reason,
                updated_by=updated_by,
                timestamp=datetime.now()
            )
            
            self.pending_updates.append(update_record)
            
            # 更新或創建數據庫記錄
            if existing_item:
                existing_item.content = json.dumps(content, ensure_ascii=False)
                existing_item.metadata = json.dumps(metadata, ensure_ascii=False) if metadata else None
                existing_item.confidence = confidence
                existing_item.version = self.current_version
                existing_item.updated_at = datetime.now()
                existing_item.updated_by = updated_by
            else:
                new_item = KnowledgeBaseItem(
                    item_id=item_id,
                    category=category.value,
                    topic=topic,
                    content=json.dumps(content, ensure_ascii=False),
                    metadata=json.dumps(metadata, ensure_ascii=False) if metadata else None,
                    confidence=confidence,
                    version=self.current_version,
                    created_by=updated_by,
                    is_active=True
                )
                self.db_session.add(new_item)
            
            # 提交到數據庫
            self.db_session.commit()
            
            # 更新內存索引
            self.knowledge_index[item_id] = {
                "category": category.value,
                "topic": topic,
                "content": content,
                "metadata": metadata or {},
                "confidence": confidence,
                "version": self.current_version,
                "last_updated": datetime.now()
            }
            
            # 重建索引
            self._build_index()
            
            # 清除緩存
            await self._invalidate_cache(item_id)
            
            logger.info(f"Knowledge item {item_id} {action}d successfully")
            return True
            
        except Exception as e:
            logger.error(f"Error updating knowledge item {item_id}: {e}")
            self.db_session.rollback()
            return False
    
    async def get_knowledge(
        self,
        item_id: Optional[str] = None,
        category: Optional[KnowledgeCategory] = None,
        topic: Optional[str] = None,
        tags: Optional[List[str]] = None
    ) -> List[Dict[str, Any]]:
        """獲取知識項"""
        results = []
        
        # 按ID查詢
        if item_id:
            item = self.knowledge_index.get(item_id)
            if item:
                results.append({"item_id": item_id, **item})
            return results
        
        # 按類別查詢
        if category:
            item_ids = self.category_index.get(category.value, [])
            for id in item_ids:
                results.append({"item_id": id, **self.knowledge_index[id]})
        
        # 按主題查詢
        if topic:
            item_ids = self.topic_index.get(topic, [])
            for id in item_ids:
                if id not in [r["item_id"] for r in results]:
                    results.append({"item_id": id, **self.knowledge_index[id]})
        
        # 按標籤查詢
        if tags:
            for tag in tags:
                item_ids = self.tag_index.get(tag, [])
                for id in item_ids:
                    if id not in [r["item_id"] for r in results]:
                        results.append({"item_id": id, **self.knowledge_index[id]})
        
        return results
    
    async def search_knowledge(
        self,
        query: str,
        category: Optional[KnowledgeCategory] = None,
        limit: int = 10
    ) -> List[Dict[str, Any]]:
        """搜索知識庫"""
        results = []
        query_lower = query.lower()
        
        for item_id, item_data in self.knowledge_index.items():
            # 類別過濾
            if category and item_data["category"] != category.value:
                continue
            
            # 計算相關性分數
            score = 0.0
            
            # 主題匹配
            if query_lower in item_data["topic"].lower():
                score += 0.5
            
            # 內容匹配
            content_str = json.dumps(item_data["content"], ensure_ascii=False).lower()
            if query_lower in content_str:
                score += 0.3
            
            # 標籤匹配
            tags = item_data.get("metadata", {}).get("tags", [])
            for tag in tags:
                if query_lower in tag.lower():
                    score += 0.2
                    break
            
            if score > 0:
                results.append({
                    "item_id": item_id,
                    "score": score,
                    **item_data
                })
        
        # 按分數排序
        results.sort(key=lambda x: x["score"], reverse=True)
        
        return results[:limit]
    
    async def create_version(
        self,
        description: str,
        created_by: str = "system"
    ) -> str:
        """創建新版本"""
        # 生成新版本號
        current_v = self.current_version.split('-')[0]  # 例如 v1.0.0
        major, minor, patch = current_v[1:].split('.')
        
        # 簡單的版本遞增邏輯
        new_patch = int(patch) + 1
        new_version = f"v{major}.{minor}.{new_patch}-{datetime.now().strftime('%Y%m%d')}"
        
        # 創建版本記錄
        knowledge_version = KnowledgeVersion(
            version=new_version,
            description=description,
            created_by=created_by,
            changes=json.dumps([asdict(u) for u in self.pending_updates], ensure_ascii=False),
            is_active=True
        )
        
        # 停用舊版本
        self.db_session.query(KnowledgeVersion)\
            .filter(KnowledgeVersion.version == self.current_version)\
            .update({"is_active": False})
        
        self.db_session.add(knowledge_version)
        self.db_session.commit()
        
        # 更新當前版本
        self.current_version = new_version
        self.pending_updates = []
        
        logger.info(f"Created new knowledge base version: {new_version}")
        return new_version
    
    async def rollback_version(self, target_version: str) -> bool:
        """回滾到指定版本"""
        try:
            # 獲取目標版本
            target = self.db_session.query(KnowledgeVersion)\
                .filter(KnowledgeVersion.version == target_version)\
                .first()
            
            if not target:
                logger.error(f"Target version {target_version} not found")
                return False
            
            # 獲取該版本的所有知識項
            items = self.db_session.query(KnowledgeBaseItem)\
                .filter(KnowledgeBaseItem.version == target_version)\
                .all()
            
            # 重新激活這些項目
            for item in items:
                item.is_active = True
                
            # 停用當前版本的項目
            self.db_session.query(KnowledgeBaseItem)\
                .filter(KnowledgeBaseItem.version == self.current_version)\
                .update({"is_active": False})
            
            # 更新版本狀態
            self.db_session.query(KnowledgeVersion)\
                .filter(KnowledgeVersion.version == self.current_version)\
                .update({"is_active": False})
                
            target.is_active = True
            
            self.db_session.commit()
            
            # 更新內存狀態
            self.current_version = target_version
            self._load_knowledge_data()
            self._build_index()
            
            # 清除所有緩存
            await self._invalidate_all_cache()
            
            logger.info(f"Rolled back to version: {target_version}")
            return True
            
        except Exception as e:
            logger.error(f"Error rolling back to version {target_version}: {e}")
            self.db_session.rollback()
            return False
    
    async def export_knowledge(
        self,
        output_path: str,
        category: Optional[KnowledgeCategory] = None
    ) -> bool:
        """導出知識庫"""
        try:
            export_data = {
                "version": self.current_version,
                "exported_at": datetime.now().isoformat(),
                "items": []
            }
            
            # 獲取要導出的項目
            if category:
                items = await self.get_knowledge(category=category)
            else:
                items = [{"item_id": k, **v} for k, v in self.knowledge_index.items()]
            
            export_data["items"] = items
            
            # 寫入文件
            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(export_data, f, ensure_ascii=False, indent=2)
            
            logger.info(f"Exported {len(items)} knowledge items to {output_path}")
            return True
            
        except Exception as e:
            logger.error(f"Error exporting knowledge: {e}")
            return False
    
    async def import_knowledge(
        self,
        input_path: str,
        updated_by: str = "system"
    ) -> Tuple[int, int]:
        """導入知識庫"""
        imported = 0
        failed = 0
        
        try:
            with open(input_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            for item in data.get("items", []):
                success = await self.add_or_update_knowledge(
                    item_id=item["item_id"],
                    category=KnowledgeCategory(item["category"]),
                    topic=item["topic"],
                    content=item["content"],
                    metadata=item.get("metadata"),
                    confidence=item.get("confidence", 1.0),
                    reason=f"Imported from {input_path}",
                    updated_by=updated_by
                )
                
                if success:
                    imported += 1
                else:
                    failed += 1
            
            logger.info(f"Imported {imported} items, {failed} failed")
            
        except Exception as e:
            logger.error(f"Error importing knowledge: {e}")
            
        return imported, failed
    
    async def validate_knowledge(self) -> Dict[str, Any]:
        """驗證知識庫完整性"""
        issues = []
        
        # 檢查必需的類別
        required_categories = [
            KnowledgeCategory.PACKAGES,
            KnowledgeCategory.PRICING,
            KnowledgeCategory.LOCATIONS
        ]
        
        for category in required_categories:
            if category.value not in self.category_index:
                issues.append(f"Missing required category: {category.value}")
            elif len(self.category_index[category.value]) == 0:
                issues.append(f"No items in required category: {category.value}")
        
        # 檢查知識項完整性
        for item_id, item_data in self.knowledge_index.items():
            # 檢查必需字段
            if not item_data.get("content"):
                issues.append(f"Item {item_id} has no content")
                
            # 檢查置信度
            if item_data.get("confidence", 0) < 0.5:
                issues.append(f"Item {item_id} has low confidence: {item_data.get('confidence')}")
                
            # 檢查更新時間
            last_updated = item_data.get("last_updated")
            if last_updated and (datetime.now() - last_updated).days > 90:
                issues.append(f"Item {item_id} not updated for over 90 days")
        
        return {
            "is_valid": len(issues) == 0,
            "total_items": len(self.knowledge_index),
            "categories": list(self.category_index.keys()),
            "issues": issues,
            "validation_time": datetime.now().isoformat()
        }
    
    async def _invalidate_cache(self, item_id: str):
        """使緩存失效"""
        cache_keys = [
            f"knowledge:{item_id}",
            f"knowledge:category:{self.knowledge_index[item_id]['category']}",
            f"knowledge:topic:{self.knowledge_index[item_id]['topic']}"
        ]
        
        for key in cache_keys:
            await self.cache_manager.delete(key)
    
    async def _invalidate_all_cache(self):
        """使所有緩存失效"""
        await self.cache_manager.clear_pattern("knowledge:*")
    
    def get_statistics(self) -> Dict[str, Any]:
        """獲取統計信息"""
        stats = {
            "total_items": len(self.knowledge_index),
            "categories": {},
            "confidence_distribution": {
                "high": 0,  # >= 0.8
                "medium": 0,  # 0.5 - 0.8
                "low": 0  # < 0.5
            },
            "recent_updates": len(self.pending_updates),
            "version": self.current_version
        }
        
        # 統計各類別項目數
        for category, items in self.category_index.items():
            stats["categories"][category] = len(items)
        
        # 統計置信度分布
        for item_data in self.knowledge_index.values():
            confidence = item_data.get("confidence", 0)
            if confidence >= 0.8:
                stats["confidence_distribution"]["high"] += 1
            elif confidence >= 0.5:
                stats["confidence_distribution"]["medium"] += 1
            else:
                stats["confidence_distribution"]["low"] += 1
        
        return stats

# 創建全局知識庫管理器實例的工廠函數
def create_knowledge_base_manager(db_session: Session) -> KnowledgeBaseManager:
    """創建知識庫管理器實例"""
    return KnowledgeBaseManager(db_session)