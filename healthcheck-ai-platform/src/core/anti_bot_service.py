"""
防機器人偵測服務
實現多層次的行為分析以避免被WhatsApp標記為機器人
"""
import asyncio
from typing import Dict, List, Optional, Tuple
from datetime import datetime, timedelta
import random
import hashlib
import json
from collections import deque, defaultdict
import numpy as np
from sqlalchemy.orm import Session
from ..database.models import Customer, Conversation, Message, MessageType
import logging

logger = logging.getLogger(__name__)

class AntiBotService:
    """防機器人偵測服務"""
    
    def __init__(self, db_session: Session):
        self.db_session = db_session
        self.behavior_analyzer = BehaviorAnalyzer()
        self.pattern_detector = PatternDetector()
        self.activity_tracker = ActivityTracker()
        
    async def analyze_conversation(self, conversation_id: str) -> Dict[str, Any]:
        """分析對話以檢測機器人行為"""
        conversation = self.db_session.query(Conversation).filter_by(id=conversation_id).first()
        if not conversation:
            return {"risk_score": 0.0, "is_bot": False}
        
        # 獲取對話中的所有消息
        messages = self.db_session.query(Message).filter_by(
            conversation_id=conversation_id
        ).order_by(Message.timestamp).all()
        
        # 分析行為模式
        behavior_score = self.behavior_analyzer.analyze_messages(messages)
        
        # 檢測重複模式
        pattern_score = self.pattern_detector.detect_patterns(messages)
        
        # 追蹤活動頻率
        activity_score = self.activity_tracker.analyze_activity(conversation.customer_id)
        
        # 綜合風險評分
        risk_score = self._calculate_risk_score(behavior_score, pattern_score, activity_score)
        
        # 更新對話記錄
        conversation.behavior_score = 1.0 - risk_score  # 越高越像人類
        self.db_session.commit()
        
        return {
            "risk_score": risk_score,
            "is_bot": risk_score > 0.7,
            "behavior_score": behavior_score,
            "pattern_score": pattern_score,
            "activity_score": activity_score,
            "recommendations": self._get_recommendations(risk_score)
        }
    
    def _calculate_risk_score(self, behavior: float, pattern: float, activity: float) -> float:
        """計算綜合風險評分"""
        # 加權平均
        weights = {
            "behavior": 0.4,
            "pattern": 0.3,
            "activity": 0.3
        }
        
        score = (
            behavior * weights["behavior"] +
            pattern * weights["pattern"] +
            activity * weights["activity"]
        )
        
        return min(1.0, max(0.0, score))
    
    def _get_recommendations(self, risk_score: float) -> List[str]:
        """根據風險評分提供建議"""
        recommendations = []
        
        if risk_score > 0.7:
            recommendations.extend([
                "增加隨機延遲時間",
                "減少消息發送頻率",
                "增加打字錯誤和修正",
                "加入更多人性化的表達"
            ])
        elif risk_score > 0.5:
            recommendations.extend([
                "適當增加回應時間變化",
                "避免過於規律的發送模式",
                "增加對話中的個性化內容"
            ])
        elif risk_score > 0.3:
            recommendations.extend([
                "保持當前的人性化水平",
                "繼續監控行為指標"
            ])
        
        return recommendations


class BehaviorAnalyzer:
    """行為分析器"""
    
    def analyze_messages(self, messages: List[Message]) -> float:
        """分析消息行為模式"""
        if len(messages) < 2:
            return 0.0
        
        bot_indicators = 0
        total_checks = 0
        
        # 檢查響應時間一致性
        response_times = self._get_response_times(messages)
        if response_times:
            variance = np.var(response_times)
            if variance < 100:  # 響應時間過於一致
                bot_indicators += 1
            total_checks += 1
        
        # 檢查消息長度分布
        message_lengths = [len(m.content) for m in messages if m.message_type == MessageType.AGENT]
        if message_lengths:
            length_variance = np.var(message_lengths)
            if length_variance < 50:  # 消息長度過於一致
                bot_indicators += 1
            total_checks += 1
        
        # 檢查打字速度
        typing_speeds = self._calculate_typing_speeds(messages)
        if typing_speeds:
            avg_speed = np.mean(typing_speeds)
            if avg_speed > 300:  # 打字速度過快（字符/分鐘）
                bot_indicators += 1
            total_checks += 1
        
        # 檢查是否有錯字或修正
        has_corrections = self._check_for_corrections(messages)
        if not has_corrections:
            bot_indicators += 0.5  # 從不出錯也是可疑的
        total_checks += 1
        
        return bot_indicators / total_checks if total_checks > 0 else 0.0
    
    def _get_response_times(self, messages: List[Message]) -> List[float]:
        """計算響應時間"""
        response_times = []
        
        for i in range(1, len(messages)):
            if (messages[i].message_type == MessageType.AGENT and 
                messages[i-1].message_type == MessageType.CUSTOMER):
                
                time_diff = (messages[i].timestamp - messages[i-1].timestamp).total_seconds()
                response_times.append(time_diff)
        
        return response_times
    
    def _calculate_typing_speeds(self, messages: List[Message]) -> List[float]:
        """計算打字速度"""
        speeds = []
        
        for msg in messages:
            if msg.message_type == MessageType.AGENT and msg.response_time_ms:
                char_count = len(msg.content)
                time_seconds = msg.response_time_ms / 1000
                if time_seconds > 0:
                    speed = (char_count / time_seconds) * 60  # 字符/分鐘
                    speeds.append(speed)
        
        return speeds
    
    def _check_for_corrections(self, messages: List[Message]) -> bool:
        """檢查是否有錯字修正的跡象"""
        correction_patterns = ["*", "我是說", "更正", "抱歉，應該是"]
        
        for msg in messages:
            if msg.message_type == MessageType.AGENT:
                for pattern in correction_patterns:
                    if pattern in msg.content:
                        return True
        
        return False


class PatternDetector:
    """模式檢測器"""
    
    def __init__(self):
        self.template_cache = defaultdict(int)
        
    def detect_patterns(self, messages: List[Message]) -> float:
        """檢測重複或模板化的模式"""
        agent_messages = [m for m in messages if m.message_type == MessageType.AGENT]
        
        if len(agent_messages) < 3:
            return 0.0
        
        pattern_score = 0.0
        
        # 檢查相似度
        similarity_scores = self._calculate_similarity_scores(agent_messages)
        if similarity_scores:
            avg_similarity = np.mean(similarity_scores)
            if avg_similarity > 0.8:  # 高度相似
                pattern_score += 0.5
        
        # 檢查模板使用
        template_usage = self._detect_template_usage(agent_messages)
        if template_usage > 0.7:  # 大量使用模板
            pattern_score += 0.5
        
        return min(1.0, pattern_score)
    
    def _calculate_similarity_scores(self, messages: List[Message]) -> List[float]:
        """計算消息之間的相似度"""
        scores = []
        
        for i in range(len(messages) - 1):
            for j in range(i + 1, len(messages)):
                score = self._text_similarity(messages[i].content, messages[j].content)
                scores.append(score)
        
        return scores
    
    def _text_similarity(self, text1: str, text2: str) -> float:
        """計算兩個文本的相似度"""
        # 簡單的詞彙重疊計算
        words1 = set(text1.split())
        words2 = set(text2.split())
        
        if not words1 or not words2:
            return 0.0
        
        intersection = words1.intersection(words2)
        union = words1.union(words2)
        
        return len(intersection) / len(union)
    
    def _detect_template_usage(self, messages: List[Message]) -> float:
        """檢測模板使用率"""
        template_count = 0
        
        template_keywords = [
            "感謝您的諮詢",
            "我們的營業時間",
            "請問有什麼可以幫助您",
            "感謝您選擇",
            "如有其他問題"
        ]
        
        for msg in messages:
            for keyword in template_keywords:
                if keyword in msg.content:
                    template_count += 1
                    break
        
        return template_count / len(messages) if messages else 0.0


class ActivityTracker:
    """活動追蹤器"""
    
    def __init__(self):
        self.activity_log = defaultdict(deque)
        self.max_log_size = 100
        
    def analyze_activity(self, customer_id: str) -> float:
        """分析客戶活動模式"""
        # 這裡應該從資料庫獲取歷史活動記錄
        # 簡化實現
        
        activity_score = 0.0
        
        # 檢查活動時間分布
        if self._is_activity_too_regular(customer_id):
            activity_score += 0.5
        
        # 檢查活動頻率
        if self._is_activity_too_frequent(customer_id):
            activity_score += 0.5
        
        return activity_score
    
    def _is_activity_too_regular(self, customer_id: str) -> bool:
        """檢查活動是否過於規律"""
        # 簡化實現
        return False
    
    def _is_activity_too_frequent(self, customer_id: str) -> bool:
        """檢查活動是否過於頻繁"""
        # 簡化實現
        return False


class HumanizationService:
    """人性化服務，增加消息的人性化特徵"""
    
    def __init__(self):
        self.typo_generator = TypoGenerator()
        self.emotion_injector = EmotionInjector()
        
    async def humanize_message(self, content: str) -> str:
        """使消息更加人性化"""
        # 偶爾加入打字錯誤
        if random.random() < 0.1:  # 10% 機率
            content = self.typo_generator.add_typo(content)
        
        # 加入情感表達
        if random.random() < 0.2:  # 20% 機率
            content = self.emotion_injector.add_emotion(content)
        
        return content


class TypoGenerator:
    """錯字生成器"""
    
    def add_typo(self, text: str) -> str:
        """添加錯字"""
        words = text.split()
        if not words:
            return text
        
        # 隨機選擇一個詞
        word_index = random.randint(0, len(words) - 1)
        word = words[word_index]
        
        if len(word) > 3:
            # 交換兩個相鄰字符
            char_index = random.randint(0, len(word) - 2)
            word = (word[:char_index] + 
                   word[char_index + 1] + 
                   word[char_index] + 
                   word[char_index + 2:])
            
            words[word_index] = word
            
            # 加入修正
            words.append(f"*{word[:char_index]}{word[char_index]}{word[char_index + 1]}{word[char_index + 2:]}")
        
        return " ".join(words)


class EmotionInjector:
    """情感注入器"""
    
    def __init__(self):
        self.positive_expressions = ["😊", "👍", "很高興", "太好了"]
        self.thinking_expressions = ["嗯...", "讓我看看", "稍等"]
        
    def add_emotion(self, text: str) -> str:
        """添加情感表達"""
        if random.random() < 0.5:
            # 在開頭加入思考表達
            expression = random.choice(self.thinking_expressions)
            return f"{expression} {text}"
        else:
            # 在結尾加入積極表達
            expression = random.choice(self.positive_expressions)
            return f"{text} {expression}"