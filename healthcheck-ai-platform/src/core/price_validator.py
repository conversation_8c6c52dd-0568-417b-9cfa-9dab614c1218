#!/usr/bin/env python3
"""
價格和套餐內容驗證系統
確保AI回答的準確性，避免幻覺
"""

import json
import hashlib
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, asdict
from enum import Enum
import re


class ValidationStatus(Enum):
    """驗證狀態"""
    VALID = "valid"
    INVALID = "invalid"
    WARNING = "warning"
    EXPIRED = "expired"


@dataclass
class PriceRule:
    """價格規則"""
    id: str
    service_name: str
    base_price: float
    currency: str = "HKD"
    valid_from: datetime = None
    valid_until: datetime = None
    conditions: Dict[str, Any] = None
    discounts: List[Dict[str, Any]] = None
    
    def is_valid(self) -> bool:
        """檢查價格是否在有效期內"""
        now = datetime.now()
        if self.valid_from and now < self.valid_from:
            return False
        if self.valid_until and now > self.valid_until:
            return False
        return True


@dataclass
class ServicePackage:
    """服務套餐"""
    id: str
    name: str
    category: str
    items: List[str]  # 檢查項目列表
    features: List[str]  # 特色服務
    restrictions: List[str]  # 限制條件
    version: str
    last_updated: datetime


class PriceValidator:
    """價格驗證器核心"""
    
    def __init__(self, config_path: str):
        self.config_path = config_path
        self.price_rules: Dict[str, PriceRule] = {}
        self.packages: Dict[str, ServicePackage] = {}
        self.validation_log: List[Dict[str, Any]] = []
        self._load_configurations()
    
    def _load_configurations(self):
        """載入價格和套餐配置"""
        with open(self.config_path, 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        # 載入價格規則
        for rule_data in config.get('price_rules', []):
            rule = PriceRule(
                id=rule_data['id'],
                service_name=rule_data['service_name'],
                base_price=rule_data['base_price'],
                currency=rule_data.get('currency', 'HKD'),
                valid_from=datetime.fromisoformat(rule_data['valid_from']) if rule_data.get('valid_from') else None,
                valid_until=datetime.fromisoformat(rule_data['valid_until']) if rule_data.get('valid_until') else None,
                conditions=rule_data.get('conditions', {}),
                discounts=rule_data.get('discounts', [])
            )
            self.price_rules[rule.id] = rule
        
        # 載入套餐資訊
        for pkg_data in config.get('packages', []):
            package = ServicePackage(
                id=pkg_data['id'],
                name=pkg_data['name'],
                category=pkg_data['category'],
                items=pkg_data['items'],
                features=pkg_data.get('features', []),
                restrictions=pkg_data.get('restrictions', []),
                version=pkg_data['version'],
                last_updated=datetime.fromisoformat(pkg_data['last_updated'])
            )
            self.packages[package.id] = package
    
    def validate_price(self, 
                      service_name: str, 
                      quoted_price: float,
                      conditions: Dict[str, Any] = None) -> Tuple[ValidationStatus, str, Dict[str, Any]]:
        """驗證價格是否正確"""
        # 查找對應的價格規則
        matching_rules = [
            rule for rule in self.price_rules.values()
            if rule.service_name.lower() in service_name.lower() or 
               service_name.lower() in rule.service_name.lower()
        ]
        
        if not matching_rules:
            return (ValidationStatus.INVALID, 
                   f"找不到服務'{service_name}'的價格規則", 
                   {})
        
        # 使用最新的有效規則
        valid_rules = [rule for rule in matching_rules if rule.is_valid()]
        if not valid_rules:
            return (ValidationStatus.EXPIRED, 
                   f"服務'{service_name}'的價格規則已過期", 
                   {})
        
        rule = valid_rules[0]  # 使用第一個有效規則
        
        # 計算實際價格
        actual_price = self._calculate_price(rule, conditions)
        
        # 驗證價格
        price_diff = abs(quoted_price - actual_price)
        if price_diff < 0.01:  # 允許微小誤差
            return (ValidationStatus.VALID, 
                   "價格正確", 
                   {
                       'rule_id': rule.id,
                       'base_price': rule.base_price,
                       'actual_price': actual_price,
                       'applied_discounts': self._get_applied_discounts(rule, conditions)
                   })
        else:
            return (ValidationStatus.INVALID, 
                   f"價格不正確。正確價格應為${actual_price:.0f}，而非${quoted_price:.0f}", 
                   {
                       'rule_id': rule.id,
                       'correct_price': actual_price,
                       'quoted_price': quoted_price,
                       'difference': price_diff
                   })
    
    def _calculate_price(self, rule: PriceRule, conditions: Dict[str, Any] = None) -> float:
        """計算實際價格"""
        price = rule.base_price
        
        if not conditions:
            return price
        
        # 應用折扣
        for discount in rule.discounts or []:
            if self._check_discount_condition(discount, conditions):
                if discount['type'] == 'percentage':
                    price *= (1 - discount['value'] / 100)
                elif discount['type'] == 'fixed':
                    price -= discount['value']
                elif discount['type'] == 'group_buy':
                    # 團購優惠
                    group_size = conditions.get('group_size', 1)
                    if group_size >= discount['min_people']:
                        if discount.get('free_people'):
                            # 買X送Y
                            paid_people = group_size - discount['free_people']
                            price = (rule.base_price * paid_people) / group_size
                        else:
                            # 折扣
                            price *= (1 - discount['value'] / 100)
        
        return max(price, 0)  # 確保價格不為負
    
    def _check_discount_condition(self, discount: Dict[str, Any], conditions: Dict[str, Any]) -> bool:
        """檢查折扣條件是否滿足"""
        if 'condition' not in discount:
            return True
        
        condition = discount['condition']
        
        # 檢查團體大小
        if 'min_people' in condition:
            if conditions.get('group_size', 1) < condition['min_people']:
                return False
        
        # 檢查時間限制
        if 'time_limit' in condition:
            if not conditions.get('within_time_limit', False):
                return False
        
        # 檢查特定日期
        if 'specific_dates' in condition:
            current_date = conditions.get('booking_date', datetime.now().date())
            if str(current_date) not in condition['specific_dates']:
                return False
        
        return True
    
    def _get_applied_discounts(self, rule: PriceRule, conditions: Dict[str, Any] = None) -> List[str]:
        """獲取應用的折扣列表"""
        if not conditions:
            return []
        
        applied = []
        for discount in rule.discounts or []:
            if self._check_discount_condition(discount, conditions):
                applied.append(discount['name'])
        
        return applied
    
    def validate_package_content(self, 
                               package_name: str,
                               mentioned_items: List[str]) -> Tuple[ValidationStatus, str, Dict[str, Any]]:
        """驗證套餐內容是否正確"""
        # 查找套餐
        matching_packages = [
            pkg for pkg in self.packages.values()
            if package_name.lower() in pkg.name.lower() or 
               pkg.name.lower() in package_name.lower()
        ]
        
        if not matching_packages:
            return (ValidationStatus.INVALID,
                   f"找不到套餐'{package_name}'",
                   {})
        
        package = matching_packages[0]
        
        # 驗證提及的項目
        invalid_items = []
        valid_items = []
        
        for item in mentioned_items:
            if any(item.lower() in pkg_item.lower() or pkg_item.lower() in item.lower() 
                  for pkg_item in package.items):
                valid_items.append(item)
            else:
                invalid_items.append(item)
        
        if invalid_items:
            return (ValidationStatus.WARNING,
                   f"套餐'{package.name}'不包含以下項目: {', '.join(invalid_items)}",
                   {
                       'package_id': package.id,
                       'valid_items': valid_items,
                       'invalid_items': invalid_items,
                       'actual_items': package.items[:10]  # 顯示前10項
                   })
        
        return (ValidationStatus.VALID,
               "套餐內容正確",
               {
                   'package_id': package.id,
                   'total_items': len(package.items),
                   'verified_items': len(valid_items)
               })
    
    def validate_ai_response(self, ai_response: str) -> Dict[str, Any]:
        """全面驗證AI回應內容"""
        validation_results = {
            'timestamp': datetime.now().isoformat(),
            'response_length': len(ai_response),
            'validations': []
        }
        
        # 1. 提取價格信息
        price_patterns = [
            r'\$([0-9,]+)',
            r'HKD\s*([0-9,]+)',
            r'每人.*?([0-9,]+).*?元',
            r'原價.*?([0-9,]+)',
            r'優惠價.*?([0-9,]+)'
        ]
        
        mentioned_prices = []
        for pattern in price_patterns:
            matches = re.findall(pattern, ai_response)
            for match in matches:
                price = float(match.replace(',', ''))
                mentioned_prices.append(price)
        
        # 2. 提取服務名稱
        service_keywords = ['130項', '158項', '女士尊尚', '男士至尊', '全面體檢', '尊尚體檢']
        mentioned_services = []
        for keyword in service_keywords:
            if keyword in ai_response:
                mentioned_services.append(keyword)
        
        # 3. 提取折扣信息
        discount_patterns = [
            r'([0-9]+)\s*人同行',
            r'([0-9]+)\s*折',
            r'([0-9]+)%\s*off',
            r'買([0-9]+)送([0-9]+)'
        ]
        
        group_conditions = {}
        for pattern in discount_patterns[:1]:  # 只查找團體人數
            matches = re.findall(pattern, ai_response)
            if matches:
                group_conditions['group_size'] = int(matches[0])
        
        # 4. 驗證每個價格
        for i, (service, price) in enumerate(zip(mentioned_services, mentioned_prices)):
            status, message, details = self.validate_price(service, price, group_conditions)
            validation_results['validations'].append({
                'type': 'price',
                'service': service,
                'quoted_price': price,
                'status': status.value,
                'message': message,
                'details': details
            })
        
        # 5. 檢查禁止詞彙
        forbidden_phrases = [
            '最低價',
            '最便宜',
            '全港最低',
            '醫生建議',
            '醫生說',
            '保證治癒'
        ]
        
        found_forbidden = [phrase for phrase in forbidden_phrases if phrase in ai_response]
        if found_forbidden:
            validation_results['validations'].append({
                'type': 'compliance',
                'status': 'warning',
                'message': f"發現禁止使用的詞彙: {', '.join(found_forbidden)}",
                'details': {'forbidden_phrases': found_forbidden}
            })
        
        # 6. 計算整體驗證結果
        all_valid = all(v.get('status') == 'valid' for v in validation_results['validations'])
        has_warnings = any(v.get('status') == 'warning' for v in validation_results['validations'])
        
        validation_results['overall_status'] = 'valid' if all_valid else ('warning' if has_warnings else 'invalid')
        validation_results['requires_correction'] = not all_valid
        
        # 7. 記錄日誌
        self.validation_log.append(validation_results)
        
        return validation_results
    
    def get_correct_price_info(self, service_name: str, conditions: Dict[str, Any] = None) -> Dict[str, Any]:
        """獲取正確的價格信息"""
        matching_rules = [
            rule for rule in self.price_rules.values()
            if service_name.lower() in rule.service_name.lower() or 
               rule.service_name.lower() in service_name.lower()
        ]
        
        if not matching_rules:
            return {'error': f'找不到{service_name}的價格信息'}
        
        rule = matching_rules[0]
        actual_price = self._calculate_price(rule, conditions)
        
        return {
            'service_name': rule.service_name,
            'base_price': rule.base_price,
            'final_price': actual_price,
            'currency': rule.currency,
            'applicable_discounts': self._get_applied_discounts(rule, conditions),
            'valid_until': rule.valid_until.isoformat() if rule.valid_until else None,
            'conditions': conditions or {}
        }
    
    def generate_safe_response_template(self, query_type: str) -> str:
        """生成安全的回應模板"""
        templates = {
            'price_inquiry': """
🎯 您詢問的是{service_name}的價格。

💰 標準價格：${base_price}

🎁 現正優惠：
{discount_info}

✅ 服務包含：
{service_includes}

📋 溫馨提示：
- 預約時間：週一至週六 9:00-18:00
- 地點：佐敦/銅鑼灣
- 檢查前須空腹8小時

需要我為您預約嗎？
""",
            'package_inquiry': """
📊 您詢問的{package_name}包含以下項目：

主要檢查（部分）：
{main_items}

🌟 特色服務：
{special_features}

📍 地點選擇：
- 佐敦：交通方便，鄰近地鐵站
- 銅鑼灣：購物區中心，方便前往

需要查看完整的檢查項目清單嗎？
""",
            'promotion_info': """
🎉 目前有以下優惠方案：

{promotion_details}

⚠️ 優惠條款：
{terms_conditions}

📅 優惠期限：{valid_period}

立即預約享受優惠！
"""
        }
        
        return templates.get(query_type, '請稍後，我正在查詢相關信息...')


class SafeResponseGenerator:
    """安全回應生成器"""
    
    def __init__(self, validator: PriceValidator):
        self.validator = validator
    
    def generate_price_response(self, service_name: str, conditions: Dict[str, Any] = None) -> str:
        """生成經過驗證的價格回應"""
        price_info = self.validator.get_correct_price_info(service_name, conditions)
        
        if 'error' in price_info:
            return f"抱歉，{price_info['error']}。請聯繫客服人員獲取最新價格。"
        
        # 構建優惠信息
        discount_info = ""
        if price_info['applicable_discounts']:
            for discount in price_info['applicable_discounts']:
                discount_info += f"- {discount}\n"
        else:
            discount_info = "- 標準價格，無額外折扣\n"
        
        # 填充模板
        template = self.validator.generate_safe_response_template('price_inquiry')
        response = template.format(
            service_name=price_info['service_name'],
            base_price=f"{price_info['base_price']:,.0f}",
            discount_info=discount_info,
            service_includes=self._get_service_includes(service_name)
        )
        
        return response
    
    def _get_service_includes(self, service_name: str) -> str:
        """獲取服務包含內容"""
        # 這裡應該從套餐數據中獲取
        includes_map = {
            '130項': '- 基本身體檢查\n- 血液分析\n- 肝腎功能檢查\n- 心電圖',
            '158項': '- 130項所有內容\n- 甲狀腺功能\n- 腫瘤指標\n- 超聲波檢查',
            '女士尊尚': '- 專為女性設計\n- 婦科檢查\n- 乳房檢查\n- 骨質密度'
        }
        
        for key, value in includes_map.items():
            if key in service_name:
                return value
        
        return '- 全面健康檢查\n- 專業醫療團隊\n- 詳細報告解讀'


class AIResponseMiddleware:
    """中間件：在AI回應前後進行驗證"""
    
    def __init__(self, validator: PriceValidator):
        self.validator = validator
        self.correction_log = []
    
    async def process_ai_response(self, 
                                ai_response: str, 
                                user_query: str,
                                ai_provider: str = 'chatgpt') -> Dict[str, Any]:
        """處理AI回應，確保準確性"""
        # 1. 驗證AI回應
        validation_result = self.validator.validate_ai_response(ai_response)
        
        # 2. 如果有問題，進行修正
        if validation_result['requires_correction']:
            corrected_response = await self._correct_response(
                ai_response, 
                validation_result,
                user_query
            )
            
            # 記錄修正
            self.correction_log.append({
                'timestamp': datetime.now().isoformat(),
                'original': ai_response,
                'corrected': corrected_response,
                'validations': validation_result['validations'],
                'ai_provider': ai_provider
            })
            
            return {
                'response': corrected_response,
                'was_corrected': True,
                'validation_details': validation_result,
                'confidence': 0.95  # 修正後的信心度
            }
        
        # 3. 如果沒有問題，直接返回
        return {
            'response': ai_response,
            'was_corrected': False,
            'validation_details': validation_result,
            'confidence': 1.0
        }
    
    async def _correct_response(self, 
                              original_response: str,
                              validation_result: Dict[str, Any],
                              user_query: str) -> str:
        """修正不準確的回應"""
        corrected = original_response
        
        for validation in validation_result['validations']:
            if validation['status'] != 'valid':
                if validation['type'] == 'price':
                    # 修正價格
                    old_price = validation['quoted_price']
                    new_price = validation['details'].get('correct_price', old_price)
                    
                    # 替換價格
                    corrected = corrected.replace(
                        f"${old_price:.0f}",
                        f"${new_price:.0f}"
                    )
                    corrected = corrected.replace(
                        f"{old_price:.0f}元",
                        f"{new_price:.0f}元"
                    )
                
                elif validation['type'] == 'compliance':
                    # 移除禁止詞彙
                    for phrase in validation['details']['forbidden_phrases']:
                        if phrase == '最低價':
                            corrected = corrected.replace(phrase, '優惠價')
                        elif phrase == '最便宜':
                            corrected = corrected.replace(phrase, '超值')
                        elif phrase in ['醫生建議', '醫生說']:
                            corrected = corrected.replace(phrase, '專業建議')
                        else:
                            corrected = corrected.replace(phrase, '')
        
        # 添加免責聲明（如果需要）
        if validation_result['requires_correction']:
            corrected += "\n\n📌 溫馨提示：以上價格和優惠以實際到店諮詢為準。"
        
        return corrected


def create_price_config() -> Dict[str, Any]:
    """創建價格配置文件"""
    config = {
        "version": "1.0.0",
        "last_updated": datetime.now().isoformat(),
        "price_rules": [
            {
                "id": "130_standard",
                "service_name": "130項全面健康檢查計劃",
                "base_price": 2950,
                "currency": "HKD",
                "valid_from": "2025-01-01T00:00:00",
                "valid_until": "2025-12-31T23:59:59",
                "discounts": [
                    {
                        "name": "個人現金券優惠",
                        "type": "cash_voucher",
                        "value": 300,
                        "condition": {"group_size": 1}
                    },
                    {
                        "name": "2人同行8折",
                        "type": "percentage",
                        "value": 20,
                        "condition": {"min_people": 2}
                    },
                    {
                        "name": "3人同行1人免費",
                        "type": "group_buy",
                        "min_people": 3,
                        "free_people": 1,
                        "condition": {"min_people": 3}
                    }
                ]
            },
            {
                "id": "158_premium",
                "service_name": "158項尊尚健康檢查計劃",
                "base_price": 4580,
                "currency": "HKD",
                "valid_from": "2025-01-01T00:00:00",
                "valid_until": "2025-12-31T23:59:59",
                "discounts": [
                    {
                        "name": "2人同行9折",
                        "type": "percentage",
                        "value": 10,
                        "condition": {"min_people": 2}
                    },
                    {
                        "name": "3人同行85折",
                        "type": "percentage",
                        "value": 15,
                        "condition": {"min_people": 3}
                    }
                ]
            },
            {
                "id": "ladies_premium",
                "service_name": "女士尊尚體檢套餐",
                "base_price": 1580,
                "currency": "HKD",
                "valid_from": "2025-01-01T00:00:00",
                "valid_until": "2025-12-31T23:59:59",
                "discounts": [
                    {
                        "name": "送腎功能檢查",
                        "type": "value_add",
                        "value": 0,
                        "condition": {"immediate_booking": True}
                    }
                ]
            }
        ],
        "packages": [
            {
                "id": "pkg_130",
                "name": "130項全面健康檢查計劃",
                "category": "comprehensive",
                "items": [
                    "身高、體重、BMI",
                    "血壓測量",
                    "全血球計數",
                    "血型及Rh因子",
                    "肝功能測試(10項)",
                    "腎功能測試(5項)",
                    "血脂肪檢查(6項)",
                    "糖尿檢查",
                    "甲狀腺功能",
                    "尿液常規",
                    "心電圖",
                    "胸部X光"
                ],
                "features": [
                    "全面身體檢查",
                    "專業醫療團隊",
                    "詳細報告解讀"
                ],
                "restrictions": [
                    "需要空腹8小時",
                    "檢查前一晚避免酒精"
                ],
                "version": "2.0",
                "last_updated": "2025-01-01T00:00:00"
            }
        ]
    }
    
    return config


if __name__ == "__main__":
    # 創建配置文件
    config = create_price_config()
    config_path = "/Users/<USER>/Desktop/Claude/healthcheck-ai-platform/config/price_config.json"
    
    import os
    os.makedirs(os.path.dirname(config_path), exist_ok=True)
    
    with open(config_path, 'w', encoding='utf-8') as f:
        json.dump(config, f, ensure_ascii=False, indent=2)
    
    print(f"✓ 價格配置文件已創建: {config_path}")
    
    # 測試驗證器
    validator = PriceValidator(config_path)
    
    # 測試價格驗證
    test_cases = [
        ("130項全面體檢", 2950, {}),
        ("130項全面體檢", 2360, {"group_size": 2}),
        ("130項全面體檢", 1967, {"group_size": 3}),
        ("158項尊尚體檢", 4580, {}),
        ("女士尊尚", 1580, {})
    ]
    
    print("\n🧪 測試價格驗證：")
    for service, price, conditions in test_cases:
        status, message, details = validator.validate_price(service, price, conditions)
        print(f"\n{service} @ ${price} {conditions}")
        print(f"  狀態: {status.value}")
        print(f"  訊息: {message}")
    
    # 測試AI回應驗證
    test_ai_response = """
    您好！我們的130項全面體檢原價是$2,950，現在有優惠：
    - 2人同行8折，每人只需$2,360
    - 3人同行1人免費，每人$1,967
    
    這是全港最低價，醫生建議定期檢查。
    """
    
    print("\n🤖 測試AI回應驗證：")
    validation_result = validator.validate_ai_response(test_ai_response)
    print(f"\n整體狀態: {validation_result['overall_status']}")
    print(f"需要修正: {validation_result['requires_correction']}")
    
    for v in validation_result['validations']:
        print(f"\n- {v['type']}: {v.get('status', 'N/A')}")
        print(f"  {v.get('message', '')}")