"""
知識庫驗證器
確保AI回應基於準確的知識庫信息
"""
import json
import re
from typing import Dict, Any, List, Optional, Tuple, Set
from datetime import datetime
import logging
from dataclasses import dataclass
import hashlib

logger = logging.getLogger(__name__)

@dataclass
class KnowledgeItem:
    """知識項目"""
    id: str
    category: str
    topic: str
    content: Dict[str, Any]
    source: str
    last_updated: datetime
    confidence: float
    tags: List[str]
    
@dataclass
class ValidationReport:
    """知識驗證報告"""
    is_valid: bool
    accuracy_score: float
    matched_items: List[KnowledgeItem]
    missing_info: List[str]
    conflicting_info: List[Dict[str, Any]]
    suggestions: List[str]

class KnowledgeValidator:
    """知識庫驗證器"""
    
    def __init__(self):
        # 初始化知識庫
        self.knowledge_base = self._init_knowledge_base()
        
        # 知識類別
        self.categories = {
            "packages": "體檢套餐",
            "pricing": "價格信息",
            "locations": "地點信息",
            "procedures": "檢查流程",
            "promotions": "優惠活動",
            "policies": "政策條款",
            "medical": "醫療信息"
        }
        
        # 信息準確性權重
        self.accuracy_weights = {
            "exact_match": 1.0,
            "partial_match": 0.7,
            "semantic_match": 0.5,
            "context_match": 0.3
        }
        
    def _init_knowledge_base(self) -> Dict[str, List[KnowledgeItem]]:
        """初始化知識庫"""
        kb = {}
        
        # 套餐信息
        kb["packages"] = [
            KnowledgeItem(
                id="pkg_001",
                category="packages",
                topic="精選計劃",
                content={
                    "name": "精選計劃",
                    "price": 1950,
                    "original_price": 5880,
                    "items": 110,
                    "key_features": ["超聲波檢查", "癌症指標", "血液分析", "心肝腎功能"],
                    "duration": "1.5-2小時",
                    "target_audience": "首次體檢或定期檢查人士"
                },
                source="official_catalog",
                last_updated=datetime.now(),
                confidence=1.0,
                tags=["基礎", "入門", "體檢"]
            ),
            KnowledgeItem(
                id="pkg_002",
                category="packages",
                topic="全面計劃",
                content={
                    "name": "全面計劃",
                    "price": 2950,
                    "original_price": 8280,
                    "items": 130,
                    "key_features": ["超聲波+胸部X光", "心電圖", "肝炎篩查", "荷爾蒙檢測"],
                    "duration": "2-2.5小時",
                    "target_audience": "注重全面健康檢查人士"
                },
                source="official_catalog",
                last_updated=datetime.now(),
                confidence=1.0,
                tags=["熱門", "全面", "體檢"]
            ),
            KnowledgeItem(
                id="pkg_003",
                category="packages",
                topic="優越計劃",
                content={
                    "name": "優越計劃",
                    "price": 4380,
                    "items": 158,
                    "key_features": ["全身超聲波", "胸腹部X光", "性病篩查", "身體成分分析"],
                    "duration": "2.5-3小時",
                    "target_audience": "追求深度檢查人士"
                },
                source="official_catalog",
                last_updated=datetime.now(),
                confidence=1.0,
                tags=["高端", "深度", "體檢"]
            ),
            KnowledgeItem(
                id="pkg_004",
                category="packages",
                topic="尊尚計劃",
                content={
                    "name": "尊尚計劃",
                    "price": 6380,
                    "items": 172,
                    "key_features": ["172項檢查", "多器官超聲波", "癌症標記", "VIP服務"],
                    "duration": "3-3.5小時",
                    "target_audience": "追求最全面檢查的VIP客戶"
                },
                source="official_catalog",
                last_updated=datetime.now(),
                confidence=1.0,
                tags=["VIP", "尊貴", "最全面", "體檢"]
            )
        ]
        
        # 優惠信息
        kb["promotions"] = [
            KnowledgeItem(
                id="promo_001",
                category="promotions",
                topic="優惠券",
                content={
                    "CPN100": {"discount": 100, "applicable": "精選計劃"},
                    "CPN300": {"discount": 300, "applicable": "全面計劃"},
                    "CPN500": {"discount": 500, "applicable": "優越計劃"},
                    "CPN700": {"discount": 700, "applicable": "尊尚計劃"},
                    "validity": "2025-03-31"
                },
                source="promotion_system",
                last_updated=datetime.now(),
                confidence=1.0,
                tags=["優惠", "折扣", "優惠券"]
            ),
            KnowledgeItem(
                id="promo_002",
                category="promotions",
                topic="團體優惠",
                content={
                    "2_people": {"discount_rate": 0.9, "description": "2人同行9折"},
                    "3_people": {"discount_rate": 0.67, "description": "3人同行1人免費"},
                    "applicable_packages": ["全面計劃", "優越計劃", "尊尚計劃"],
                    "cannot_combine": True
                },
                source="promotion_system",
                last_updated=datetime.now(),
                confidence=1.0,
                tags=["團體", "優惠", "折扣"]
            )
        ]
        
        # 地點信息
        kb["locations"] = [
            KnowledgeItem(
                id="loc_001",
                category="locations",
                topic="銅鑼灣分店",
                content={
                    "name": "銅鑼灣",
                    "address": "軒尼詩道555號東角中心13樓",
                    "phone": "3614 5393",
                    "transport": ["港鐵銅鑼灣站E出口", "多條巴士線路"],
                    "parking": "大廈設有停車場"
                },
                source="official_info",
                last_updated=datetime.now(),
                confidence=1.0,
                tags=["地點", "銅鑼灣", "分店"]
            ),
            KnowledgeItem(
                id="loc_002",
                category="locations",
                topic="佐敦分店",
                content={
                    "name": "佐敦",
                    "address": "彌敦道301-309號嘉賓商業大廈9樓",
                    "phone": "3614 5393",
                    "transport": ["港鐵佐敦站A出口", "多條巴士線路"],
                    "parking": "附近有公共停車場"
                },
                source="official_info",
                last_updated=datetime.now(),
                confidence=1.0,
                tags=["地點", "佐敦", "分店"]
            )
        ]
        
        # 政策信息
        kb["policies"] = [
            KnowledgeItem(
                id="pol_001",
                category="policies",
                topic="預約政策",
                content={
                    "advance_notice": "需提前2個工作天預約",
                    "cancellation": "需提前24小時取消或改期",
                    "no_show_policy": "無故缺席可能影響下次預約"
                },
                source="terms_conditions",
                last_updated=datetime.now(),
                confidence=1.0,
                tags=["政策", "預約", "規則"]
            ),
            KnowledgeItem(
                id="pol_002",
                category="policies",
                topic="體檢須知",
                content={
                    "fasting": "體檢前一晚12點後需禁食",
                    "documents": "請攜帶身份證",
                    "clothing": "建議穿著寬鬆舒適的衣物",
                    "report_time": "報告一般7-10個工作天"
                },
                source="medical_guidelines",
                last_updated=datetime.now(),
                confidence=1.0,
                tags=["須知", "準備", "體檢"]
            )
        ]
        
        return kb
    
    def validate_response(self, ai_response: str, context: Dict[str, Any]) -> ValidationReport:
        """
        驗證AI回應的準確性
        
        Args:
            ai_response: AI生成的回應
            context: 對話上下文
            
        Returns:
            ValidationReport: 驗證報告
        """
        matched_items = []
        missing_info = []
        conflicting_info = []
        accuracy_scores = []
        
        # 1. 提取回應中的實體和聲明
        entities = self._extract_entities(ai_response)
        claims = self._extract_claims(ai_response)
        
        # 2. 驗證每個聲明
        for claim in claims:
            validation_result = self._validate_claim(claim, entities)
            
            if validation_result["is_valid"]:
                matched_items.extend(validation_result["matched_items"])
                accuracy_scores.append(validation_result["accuracy_score"])
            else:
                if validation_result["type"] == "missing":
                    missing_info.append(validation_result["detail"])
                elif validation_result["type"] == "conflict":
                    conflicting_info.append(validation_result["detail"])
                accuracy_scores.append(0.0)
        
        # 3. 檢查是否遺漏重要信息
        required_info = self._get_required_info(context)
        for info in required_info:
            if not self._is_info_mentioned(info, ai_response):
                missing_info.append(f"未提及{info}")
        
        # 4. 計算總體準確度
        overall_accuracy = sum(accuracy_scores) / len(accuracy_scores) if accuracy_scores else 1.0
        
        # 5. 生成建議
        suggestions = self._generate_suggestions(missing_info, conflicting_info, overall_accuracy)
        
        return ValidationReport(
            is_valid=len(conflicting_info) == 0 and overall_accuracy > 0.8,
            accuracy_score=overall_accuracy,
            matched_items=matched_items,
            missing_info=missing_info,
            conflicting_info=conflicting_info,
            suggestions=suggestions
        )
    
    def _extract_entities(self, text: str) -> Dict[str, List[str]]:
        """提取文本中的實體"""
        entities = {
            "packages": [],
            "prices": [],
            "locations": [],
            "features": [],
            "policies": []
        }
        
        # 提取套餐名稱
        package_names = ["精選計劃", "全面計劃", "優越計劃", "尊尚計劃"]
        for pkg in package_names:
            if pkg in text:
                entities["packages"].append(pkg)
        
        # 提取價格
        price_pattern = r'\$?(\d{1,5}(?:,\d{3})*)'
        prices = re.findall(price_pattern, text)
        entities["prices"] = [p.replace(',', '') for p in prices]
        
        # 提取地點
        locations = ["銅鑼灣", "佐敦"]
        for loc in locations:
            if loc in text:
                entities["locations"].append(loc)
        
        return entities
    
    def _extract_claims(self, text: str) -> List[Dict[str, Any]]:
        """提取文本中的聲明"""
        claims = []
        
        # 價格聲明
        price_claims = re.findall(r'([\u4e00-\u9fff]+)\s*(?:的)?(?:優惠)?價格?[是為：]\s*\$?(\d+(?:,\d{3})*)', text)
        for package, price in price_claims:
            claims.append({
                "type": "price",
                "subject": package,
                "value": price.replace(',', ''),
                "raw_text": f"{package}價格{price}"
            })
        
        # 項目數量聲明
        item_claims = re.findall(r'([\u4e00-\u9fff]+)\s*(?:包含|含有|共)\s*(\d+)\s*項', text)
        for package, items in item_claims:
            claims.append({
                "type": "items",
                "subject": package,
                "value": items,
                "raw_text": f"{package}包含{items}項"
            })
        
        # 優惠聲明
        discount_claims = re.findall(r'優惠券\s*([A-Z]+\d+)\s*(?:可)?再?減\s*\$?(\d+)', text)
        for coupon, discount in discount_claims:
            claims.append({
                "type": "discount",
                "subject": coupon,
                "value": discount,
                "raw_text": f"優惠券{coupon}減{discount}"
            })
        
        return claims
    
    def _validate_claim(self, claim: Dict[str, Any], entities: Dict[str, List[str]]) -> Dict[str, Any]:
        """驗證單個聲明"""
        claim_type = claim["type"]
        subject = claim["subject"]
        value = claim["value"]
        
        # 根據聲明類型查找相關知識
        if claim_type == "price":
            for item in self.knowledge_base.get("packages", []):
                if subject in item.content.get("name", ""):
                    actual_price = str(item.content.get("price", ""))
                    if value == actual_price:
                        return {
                            "is_valid": True,
                            "matched_items": [item],
                            "accuracy_score": self.accuracy_weights["exact_match"]
                        }
                    else:
                        return {
                            "is_valid": False,
                            "type": "conflict",
                            "detail": {
                                "claim": claim["raw_text"],
                                "actual": f"{subject}的實際價格是${actual_price}",
                                "knowledge_source": item.source
                            }
                        }
        
        elif claim_type == "items":
            for item in self.knowledge_base.get("packages", []):
                if subject in item.content.get("name", ""):
                    actual_items = str(item.content.get("items", ""))
                    if value == actual_items:
                        return {
                            "is_valid": True,
                            "matched_items": [item],
                            "accuracy_score": self.accuracy_weights["exact_match"]
                        }
                    else:
                        return {
                            "is_valid": False,
                            "type": "conflict",
                            "detail": {
                                "claim": claim["raw_text"],
                                "actual": f"{subject}實際包含{actual_items}項檢查",
                                "knowledge_source": item.source
                            }
                        }
        
        # 未找到相關知識
        return {
            "is_valid": False,
            "type": "missing",
            "detail": f"無法驗證聲明：{claim['raw_text']}"
        }
    
    def _get_required_info(self, context: Dict[str, Any]) -> List[str]:
        """根據上下文獲取必需的信息"""
        required = []
        
        # 根據用戶意圖
        if context.get("intent") == "price_inquiry":
            required.extend(["價格", "優惠信息"])
        elif context.get("intent") == "appointment_booking":
            required.extend(["預約方式", "營業時間", "地址"])
        elif context.get("intent") == "package_comparison":
            required.extend(["套餐差異", "推薦理由"])
        
        return required
    
    def _is_info_mentioned(self, info: str, text: str) -> bool:
        """檢查信息是否被提及"""
        info_patterns = {
            "價格": r'\$\d+',
            "優惠信息": r'(優惠|折扣|減\$)',
            "預約方式": r'(預約|致電|WhatsApp)',
            "營業時間": r'\d+:\d+',
            "地址": r'(軒尼詩道|彌敦道)',
            "套餐差異": r'(區別|不同|相比)',
            "推薦理由": r'(適合|推薦|建議)'
        }
        
        pattern = info_patterns.get(info, info)
        return bool(re.search(pattern, text))
    
    def _generate_suggestions(
        self,
        missing_info: List[str],
        conflicting_info: List[Dict[str, Any]],
        accuracy_score: float
    ) -> List[str]:
        """生成改進建議"""
        suggestions = []
        
        if conflicting_info:
            suggestions.append("請更正以下錯誤信息：")
            for conflict in conflicting_info[:3]:  # 最多顯示3個
                suggestions.append(f"- {conflict['actual']}")
        
        if missing_info:
            suggestions.append("建議補充以下信息：")
            for info in missing_info[:3]:  # 最多顯示3個
                suggestions.append(f"- {info}")
        
        if accuracy_score < 0.8:
            suggestions.append("建議參考知識庫提供更準確的信息")
        
        return suggestions
    
    def get_accurate_info(self, topic: str, detail: Optional[str] = None) -> Optional[Dict[str, Any]]:
        """
        獲取準確的信息
        
        Args:
            topic: 主題
            detail: 具體細節
            
        Returns:
            Optional[Dict[str, Any]]: 準確的信息
        """
        # 搜索所有類別
        for category, items in self.knowledge_base.items():
            for item in items:
                if topic in item.topic or topic in item.tags:
                    if detail:
                        # 返回特定細節
                        return item.content.get(detail)
                    else:
                        # 返回整個內容
                        return item.content
        
        return None
    
    def update_knowledge(self, category: str, item: KnowledgeItem) -> bool:
        """
        更新知識庫
        
        Args:
            category: 類別
            item: 知識項目
            
        Returns:
            bool: 是否成功
        """
        if category not in self.knowledge_base:
            self.knowledge_base[category] = []
        
        # 檢查是否已存在
        for i, existing in enumerate(self.knowledge_base[category]):
            if existing.id == item.id:
                # 更新現有項目
                self.knowledge_base[category][i] = item
                logger.info(f"Updated knowledge item: {item.id}")
                return True
        
        # 添加新項目
        self.knowledge_base[category].append(item)
        logger.info(f"Added new knowledge item: {item.id}")
        return True

# 全局知識驗證器實例
knowledge_validator = KnowledgeValidator()