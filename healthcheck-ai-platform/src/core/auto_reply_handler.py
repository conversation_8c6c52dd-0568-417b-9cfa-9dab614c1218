"""
自動回覆處理器 - 基於關鍵詞匹配的快速回應系統
"""
import re
from typing import Dict, Optional, List, Tuple
from dataclasses import dataclass
import json

@dataclass
class KeywordPattern:
    """關鍵詞模式"""
    keywords: List[str]
    template_key: str
    priority: int = 0
    require_all: bool = False  # 是否需要匹配所有關鍵詞

class AutoReplyHandler:
    """自動回覆處理器"""
    
    def __init__(self, template_path: str = "config/response_templates.json"):
        self.templates = self._load_templates(template_path)
        self.keyword_patterns = self._initialize_patterns()
        
    def _load_templates(self, template_path: str) -> Dict:
        """載入回覆模板"""
        try:
            with open(template_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
                return data.get('templates', {})
        except Exception as e:
            print(f"Error loading templates: {e}")
            return {}
    
    def _initialize_patterns(self) -> List[KeywordPattern]:
        """初始化關鍵詞模式"""
        return [
            # 多人同行優惠詢問 (最高優先級)
            KeywordPattern(
                keywords=["多人", "同行", "優惠", "團購", "一齊", "朋友", "家人", "2人", "3人", "幾個人"],
                template_key="auto_reply.group_discount_inquiry",
                priority=10
            ),
            
            # 價格詢問
            KeywordPattern(
                keywords=["價格", "價錢", "收費", "多少錢", "幾多錢", "費用", "價目表"],
                template_key="auto_reply.price_inquiry",
                priority=9
            ),
            
            # 110項計劃詢問
            KeywordPattern(
                keywords=["110", "特選", "110項"],
                template_key="product_intro.110_package",
                priority=8
            ),
            
            # 130項計劃詢問
            KeywordPattern(
                keywords=["130", "全面健康", "130項"],
                template_key="product_intro.130_package",
                priority=8
            ),
            
            # 158項計劃詢問
            KeywordPattern(
                keywords=["158", "尊尚", "158項"],
                template_key="product_intro.158_package",
                priority=8
            ),
            
            # 女士套餐詢問
            KeywordPattern(
                keywords=["女士", "女性", "婦科", "婦女"],
                template_key="product_intro.ladies_package",
                priority=8
            ),
            
            # 全面檢查詢問（通用）
            KeywordPattern(
                keywords=["全面", "檢查", "體檢", "身體檢查", "健康檢查"],
                template_key="product_intro.full_check_package",
                priority=7
            ),
            
            # 預約時間詢問
            KeywordPattern(
                keywords=["時間", "幾點", "什麼時候", "營業", "開門", "週末", "星期"],
                template_key="auto_reply.appointment_time",
                priority=6
            ),
            
            # 地點詢問
            KeywordPattern(
                keywords=["地址", "位置", "地點", "在哪", "點去", "交通", "地鐵", "怎麼去"],
                template_key="auto_reply.location_inquiry",
                priority=6
            ),
            
            # 報告詢問
            KeywordPattern(
                keywords=["報告", "結果", "幾時有", "幾耐", "多久"],
                template_key="auto_reply.report_inquiry",
                priority=6
            ),
        ]
    
    def match_keywords(self, message: str) -> Optional[Tuple[str, Dict]]:
        """
        匹配關鍵詞並返回對應的模板
        
        Returns:
            Tuple[str, Dict]: (template_key, template_content)
        """
        message_lower = message.lower()
        
        # 按優先級排序
        sorted_patterns = sorted(self.keyword_patterns, key=lambda x: x.priority, reverse=True)
        
        for pattern in sorted_patterns:
            if pattern.require_all:
                # 需要匹配所有關鍵詞
                if all(keyword in message_lower for keyword in pattern.keywords):
                    return self._get_template_response(pattern.template_key)
            else:
                # 只需匹配任一關鍵詞
                if any(keyword in message_lower for keyword in pattern.keywords):
                    return self._get_template_response(pattern.template_key)
        
        return None
    
    def _get_template_response(self, template_key: str) -> Optional[Tuple[str, Dict]]:
        """獲取模板響應"""
        keys = template_key.split('.')
        template = self.templates
        
        try:
            for key in keys:
                template = template[key]
            return (template_key, template)
        except (KeyError, TypeError):
            return None
    
    def format_response(self, template: Dict, **kwargs) -> str:
        """格式化回覆內容"""
        if isinstance(template, str):
            return template.format(**kwargs)
        
        # 如果模板包含 'standard' 或 'simple' 等子鍵，選擇第一個可用的
        if 'standard' in template:
            return self.format_response(template['standard'], **kwargs)
        elif 'simple' in template:
            return self.format_response(template['simple'], **kwargs)
        elif 'detailed' in template:
            return self.format_response(template['detailed'], **kwargs)
        elif 'available' in template:
            return self.format_response(template['available'], **kwargs)
        
        # 組合多個部分的模板
        parts = []
        for key in ['header', 'content', 'benefit', 'target', 'promotion', 'location', 'cta']:
            if key in template:
                parts.append(template[key])
        
        return '\n\n'.join(parts)
    
    def should_auto_reply(self, message: str) -> bool:
        """判斷是否應該自動回覆"""
        # 排除某些不需要自動回覆的情況
        exclude_patterns = [
            r'^(好的|收到|明白|知道|ok|OK|Ok|謝謝|多謝|唔該)$',
            r'^(是|否|係|唔係|yes|no|Y|N)$',
            r'^\d{1,2}$',  # 純數字（可能是選擇選項）
        ]
        
        for pattern in exclude_patterns:
            if re.match(pattern, message.strip()):
                return False
        
        # 訊息太短可能不適合自動回覆
        if len(message.strip()) < 2:
            return False
            
        return True