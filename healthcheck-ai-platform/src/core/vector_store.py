"""
向量數據庫集成
支持Pinecone和本地FAISS，用於語義搜索和RAG
"""
import os
import json
import numpy as np
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime
import logging
from dataclasses import dataclass
import hashlib

# 可選的向量數據庫
try:
    import pinecone
    PINECONE_AVAILABLE = True
except ImportError:
    PINECONE_AVAILABLE = False
    
try:
    import faiss
    FAISS_AVAILABLE = True
except ImportError:
    FAISS_AVAILABLE = False

# 嵌入模型
try:
    from sentence_transformers import SentenceTransformer
    SENTENCE_TRANSFORMERS_AVAILABLE = True
except ImportError:
    SENTENCE_TRANSFORMERS_AVAILABLE = False
    
try:
    import openai
    OPENAI_AVAILABLE = True
except ImportError:
    OPENAI_AVAILABLE = False

logger = logging.getLogger(__name__)

@dataclass
class VectorSearchResult:
    """向量搜索結果"""
    item_id: str
    score: float
    content: Dict[str, Any]
    metadata: Dict[str, Any]

class EmbeddingGenerator:
    """嵌入向量生成器"""
    
    def __init__(self, model_name: str = "text-embedding-ada-002"):
        self.model_name = model_name
        self.dimension = None
        
        # 初始化模型
        if "ada" in model_name and OPENAI_AVAILABLE:
            self.model_type = "openai"
            self.dimension = 1536  # OpenAI ada-002 dimension
            openai.api_key = os.getenv("OPENAI_API_KEY")
        elif SENTENCE_TRANSFORMERS_AVAILABLE:
            self.model_type = "sentence_transformer"
            self.model = SentenceTransformer(model_name)
            self.dimension = self.model.get_sentence_embedding_dimension()
        else:
            raise ValueError("No embedding model available")
            
        logger.info(f"Initialized {self.model_type} embedding model: {model_name}")
    
    async def generate_embedding(self, text: str) -> List[float]:
        """生成文本嵌入向量"""
        if self.model_type == "openai":
            response = await openai.Embedding.acreate(
                input=[text],
                model=self.model_name
            )
            return response["data"][0]["embedding"]
        else:
            # Sentence Transformer
            embedding = self.model.encode(text)
            return embedding.tolist()
    
    async def generate_embeddings(self, texts: List[str]) -> List[List[float]]:
        """批量生成嵌入向量"""
        if self.model_type == "openai":
            response = await openai.Embedding.acreate(
                input=texts,
                model=self.model_name
            )
            return [item["embedding"] for item in response["data"]]
        else:
            # Sentence Transformer
            embeddings = self.model.encode(texts)
            return embeddings.tolist()

class PineconeVectorStore:
    """Pinecone向量存儲"""
    
    def __init__(self, index_name: str, api_key: str, environment: str):
        if not PINECONE_AVAILABLE:
            raise ImportError("Pinecone not installed. Install with: pip install pinecone-client")
            
        pinecone.init(api_key=api_key, environment=environment)
        
        # 創建或連接索引
        if index_name not in pinecone.list_indexes():
            pinecone.create_index(
                name=index_name,
                dimension=1536,  # OpenAI embeddings
                metric="cosine"
            )
            
        self.index = pinecone.Index(index_name)
        logger.info(f"Connected to Pinecone index: {index_name}")
    
    async def upsert(self, item_id: str, embedding: List[float], metadata: Dict[str, Any]):
        """插入或更新向量"""
        self.index.upsert([(item_id, embedding, metadata)])
    
    async def search(self, query_embedding: List[float], top_k: int = 10, 
                    filter: Optional[Dict] = None) -> List[Tuple[str, float, Dict]]:
        """搜索相似向量"""
        results = self.index.query(
            vector=query_embedding,
            top_k=top_k,
            include_metadata=True,
            filter=filter
        )
        
        return [(match.id, match.score, match.metadata) for match in results.matches]
    
    async def delete(self, item_ids: List[str]):
        """刪除向量"""
        self.index.delete(ids=item_ids)

class FAISSVectorStore:
    """本地FAISS向量存儲"""
    
    def __init__(self, dimension: int, index_path: Optional[str] = None):
        if not FAISS_AVAILABLE:
            raise ImportError("FAISS not installed. Install with: pip install faiss-cpu")
            
        self.dimension = dimension
        self.index_path = index_path
        
        # 創建或加載索引
        if index_path and os.path.exists(index_path):
            self.index = faiss.read_index(index_path)
            with open(f"{index_path}.metadata", 'r') as f:
                self.metadata = json.load(f)
            with open(f"{index_path}.id_map", 'r') as f:
                self.id_to_idx = json.load(f)
        else:
            self.index = faiss.IndexFlatL2(dimension)
            self.metadata = {}
            self.id_to_idx = {}
            
        self.idx_to_id = {v: k for k, v in self.id_to_idx.items()}
        logger.info(f"Initialized FAISS index with dimension {dimension}")
    
    async def upsert(self, item_id: str, embedding: List[float], metadata: Dict[str, Any]):
        """插入或更新向量"""
        embedding_array = np.array([embedding], dtype=np.float32)
        
        if item_id in self.id_to_idx:
            # 更新現有向量（FAISS不支持直接更新，需要刪除後重新添加）
            idx = self.id_to_idx[item_id]
            # 這裡簡化處理，實際使用時需要更複雜的邏輯
            logger.warning(f"Update not supported in FAISS, skipping {item_id}")
        else:
            # 添加新向量
            idx = self.index.ntotal
            self.index.add(embedding_array)
            self.id_to_idx[item_id] = idx
            self.idx_to_id[idx] = item_id
            self.metadata[item_id] = metadata
            
        # 保存索引
        if self.index_path:
            self._save_index()
    
    async def search(self, query_embedding: List[float], top_k: int = 10,
                    filter: Optional[Dict] = None) -> List[Tuple[str, float, Dict]]:
        """搜索相似向量"""
        query_array = np.array([query_embedding], dtype=np.float32)
        
        # FAISS搜索
        distances, indices = self.index.search(query_array, top_k)
        
        results = []
        for i, (dist, idx) in enumerate(zip(distances[0], indices[0])):
            if idx == -1:  # FAISS返回-1表示沒有結果
                continue
                
            item_id = self.idx_to_id.get(idx)
            if not item_id:
                continue
                
            metadata = self.metadata.get(item_id, {})
            
            # 應用過濾器
            if filter:
                match = all(metadata.get(k) == v for k, v in filter.items())
                if not match:
                    continue
                    
            # 轉換距離為相似度分數（餘弦相似度）
            score = 1 / (1 + dist)
            results.append((item_id, score, metadata))
            
        return results
    
    def _save_index(self):
        """保存索引到磁盤"""
        if self.index_path:
            faiss.write_index(self.index, self.index_path)
            with open(f"{self.index_path}.metadata", 'w') as f:
                json.dump(self.metadata, f)
            with open(f"{self.index_path}.id_map", 'w') as f:
                json.dump(self.id_to_idx, f)

class VectorStore:
    """統一的向量存儲接口"""
    
    def __init__(self, store_type: str = "faiss", **kwargs):
        self.store_type = store_type
        self.embedding_generator = EmbeddingGenerator(
            kwargs.get("embedding_model", "text-embedding-ada-002")
        )
        
        if store_type == "pinecone":
            self.store = PineconeVectorStore(
                index_name=kwargs.get("index_name", "healthcheck-knowledge"),
                api_key=kwargs.get("api_key") or os.getenv("PINECONE_API_KEY"),
                environment=kwargs.get("environment") or os.getenv("PINECONE_ENV")
            )
        elif store_type == "faiss":
            self.store = FAISSVectorStore(
                dimension=self.embedding_generator.dimension,
                index_path=kwargs.get("index_path", "data/faiss_index")
            )
        else:
            raise ValueError(f"Unsupported store type: {store_type}")
            
        logger.info(f"Initialized vector store: {store_type}")
    
    async def add_knowledge_item(
        self,
        item_id: str,
        content: Dict[str, Any],
        metadata: Optional[Dict[str, Any]] = None
    ):
        """添加知識項到向量存儲"""
        # 生成文本表示
        text_representation = self._generate_text_representation(content)
        
        # 生成嵌入向量
        embedding = await self.embedding_generator.generate_embedding(text_representation)
        
        # 準備元數據
        full_metadata = {
            "item_id": item_id,
            "content_hash": hashlib.md5(json.dumps(content, sort_keys=True).encode()).hexdigest(),
            "indexed_at": datetime.now().isoformat(),
            **(metadata or {})
        }
        
        # 存儲
        await self.store.upsert(item_id, embedding, full_metadata)
        
        logger.info(f"Added knowledge item to vector store: {item_id}")
    
    async def search_knowledge(
        self,
        query: str,
        top_k: int = 10,
        category: Optional[str] = None,
        min_score: float = 0.7
    ) -> List[VectorSearchResult]:
        """搜索知識庫"""
        # 生成查詢嵌入
        query_embedding = await self.embedding_generator.generate_embedding(query)
        
        # 構建過濾器
        filter_dict = None
        if category:
            filter_dict = {"category": category}
        
        # 執行搜索
        results = await self.store.search(query_embedding, top_k * 2, filter_dict)
        
        # 處理結果
        search_results = []
        for item_id, score, metadata in results:
            if score >= min_score:
                # 從數據庫或緩存獲取完整內容
                content = await self._get_content_by_id(item_id)
                if content:
                    search_results.append(
                        VectorSearchResult(
                            item_id=item_id,
                            score=score,
                            content=content,
                            metadata=metadata
                        )
                    )
                    
            if len(search_results) >= top_k:
                break
                
        return search_results
    
    async def update_knowledge_item(
        self,
        item_id: str,
        content: Dict[str, Any],
        metadata: Optional[Dict[str, Any]] = None
    ):
        """更新知識項"""
        await self.add_knowledge_item(item_id, content, metadata)
    
    async def delete_knowledge_item(self, item_id: str):
        """刪除知識項"""
        if self.store_type == "pinecone":
            await self.store.delete([item_id])
        else:
            logger.warning(f"Delete not fully supported for {self.store_type}")
    
    async def batch_add_knowledge(
        self,
        items: List[Dict[str, Any]]
    ):
        """批量添加知識項"""
        texts = []
        item_ids = []
        metadatas = []
        
        for item in items:
            item_id = item["item_id"]
            content = item["content"]
            metadata = item.get("metadata", {})
            
            # 生成文本表示
            text_representation = self._generate_text_representation(content)
            texts.append(text_representation)
            item_ids.append(item_id)
            
            # 準備元數據
            full_metadata = {
                "item_id": item_id,
                "category": metadata.get("category"),
                "content_hash": hashlib.md5(json.dumps(content, sort_keys=True).encode()).hexdigest(),
                "indexed_at": datetime.now().isoformat(),
                **metadata
            }
            metadatas.append(full_metadata)
        
        # 批量生成嵌入
        embeddings = await self.embedding_generator.generate_embeddings(texts)
        
        # 批量存儲
        for item_id, embedding, metadata in zip(item_ids, embeddings, metadatas):
            await self.store.upsert(item_id, embedding, metadata)
            
        logger.info(f"Batch added {len(items)} knowledge items to vector store")
    
    def _generate_text_representation(self, content: Dict[str, Any]) -> str:
        """生成內容的文本表示"""
        parts = []
        
        # 添加名稱或標題
        if "name" in content:
            parts.append(f"名稱: {content['name']}")
        if "title" in content:
            parts.append(f"標題: {content['title']}")
            
        # 添加描述
        if "description" in content:
            parts.append(f"描述: {content['description']}")
            
        # 添加價格信息
        if "price" in content:
            parts.append(f"價格: ${content['price']}")
            
        # 添加特性
        if "features" in content and isinstance(content["features"], list):
            parts.append(f"特性: {', '.join(content['features'])}")
            
        # 添加其他字段
        for key, value in content.items():
            if key not in ["name", "title", "description", "price", "features"]:
                if isinstance(value, (str, int, float)):
                    parts.append(f"{key}: {value}")
                elif isinstance(value, list):
                    parts.append(f"{key}: {', '.join(map(str, value))}")
                    
        return "\n".join(parts)
    
    async def _get_content_by_id(self, item_id: str) -> Optional[Dict[str, Any]]:
        """根據ID獲取內容（需要與知識庫管理器集成）"""
        # 這裡簡化處理，實際應該從數據庫或緩存獲取
        # 在實際實現中，應該注入知識庫管理器實例
        return {"item_id": item_id, "placeholder": True}
    
    def get_statistics(self) -> Dict[str, Any]:
        """獲取統計信息"""
        stats = {
            "store_type": self.store_type,
            "embedding_model": self.embedding_generator.model_name,
            "embedding_dimension": self.embedding_generator.dimension
        }
        
        if self.store_type == "faiss":
            stats["total_vectors"] = self.store.index.ntotal
            
        return stats

# 創建全局向量存儲實例
def create_vector_store(store_type: str = None, **kwargs) -> VectorStore:
    """創建向量存儲實例"""
    if store_type is None:
        # 根據環境變量決定使用哪種存儲
        if os.getenv("PINECONE_API_KEY"):
            store_type = "pinecone"
        else:
            store_type = "faiss"
            
    return VectorStore(store_type, **kwargs)