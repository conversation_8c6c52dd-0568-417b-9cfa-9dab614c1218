"""
輸入驗證模塊
實現多層輸入驗證以防止惡意輸入和減少AI幻覺
"""
import re
import json
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime
from enum import Enum
import logging

logger = logging.getLogger(__name__)

class ValidationLevel(Enum):
    """驗證級別"""
    CRITICAL = "critical"  # 關鍵驗證（如SQL注入）
    HIGH = "high"         # 高級驗證（如業務規則）
    MEDIUM = "medium"     # 中級驗證（如格式檢查）
    LOW = "low"          # 低級驗證（如長度檢查）

class ValidationResult:
    """驗證結果"""
    def __init__(self, is_valid: bool, message: str = "", 
                 level: ValidationLevel = ValidationLevel.MEDIUM,
                 sanitized_input: Optional[str] = None):
        self.is_valid = is_valid
        self.message = message
        self.level = level
        self.sanitized_input = sanitized_input
        self.timestamp = datetime.now()

class InputValidator:
    """輸入驗證器"""
    
    def __init__(self):
        # 敏感詞列表
        self.sensitive_words = {
            "medical_misleading": ["治癒", "神奇", "百分百", "保證康復", "萬能"],
            "financial_scam": ["匯款", "轉賬", "密碼", "信用卡號", "CVV"],
            "inappropriate": ["色情", "暴力", "歧視"],
            "competitor": ["競爭對手名稱列表"]  # 實際使用時應填入真實競爭對手
        }
        
        # 業務規則
        self.business_rules = {
            "max_message_length": 1000,
            "min_message_length": 1,
            "max_conversation_length": 100,
            "allowed_languages": ["zh-TW", "zh-CN", "en"],
            "business_hours": {"start": 8, "end": 22}  # 8 AM to 10 PM
        }
        
        # SQL注入模式
        self.sql_injection_patterns = [
            r"(\b(SELECT|INSERT|UPDATE|DELETE|DROP|UNION|CREATE|ALTER)\b)",
            r"(--|#|/\*|\*/)",
            r"(\bOR\b\s*\d+\s*=\s*\d+)",
            r"(\bAND\b\s*\d+\s*=\s*\d+)",
            r"(';|';--|';\s*DROP)",
            r"(\bEXEC\b|\bEXECUTE\b)"
        ]
        
        # XSS攻擊模式
        self.xss_patterns = [
            r"<script[^>]*>.*?</script>",
            r"javascript:",
            r"on\w+\s*=",
            r"<iframe[^>]*>",
            r"<object[^>]*>",
            r"<embed[^>]*>"
        ]
        
        # 有效的查詢模式
        self.valid_query_patterns = {
            "price_inquiry": r"(價格|費用|多少錢|收費|優惠|折扣)",
            "appointment": r"(預約|時間|幾點|星期|週末|平日)",
            "package_info": r"(套餐|計劃|項目|檢查|包含|精選|全面|優越|尊尚)",
            "location": r"(地址|位置|怎麼去|銅鑼灣|佐敦|分店)",
            "general": r"(諮詢|了解|問問|想知道|請問)"
        }
        
    def validate_input(self, user_input: str, context: Dict[str, Any] = None) -> ValidationResult:
        """
        綜合驗證用戶輸入
        
        Args:
            user_input: 用戶輸入的文本
            context: 對話上下文信息
            
        Returns:
            ValidationResult: 驗證結果
        """
        if context is None:
            context = {}
            
        # 第一層：安全性驗證
        security_result = self._security_validation(user_input)
        if not security_result.is_valid:
            logger.warning(f"Security validation failed: {security_result.message}")
            return security_result
            
        # 第二層：格式驗證
        format_result = self._format_validation(user_input)
        if not format_result.is_valid:
            return format_result
            
        # 第三層：業務規則驗證
        business_result = self._business_validation(user_input, context)
        if not business_result.is_valid:
            return business_result
            
        # 第四層：語義驗證
        semantic_result = self._semantic_validation(user_input, context)
        if not semantic_result.is_valid:
            return semantic_result
            
        # 清理和標準化輸入
        sanitized_input = self._sanitize_input(user_input)
        
        return ValidationResult(
            is_valid=True,
            message="Input validation passed",
            sanitized_input=sanitized_input
        )
    
    def _security_validation(self, user_input: str) -> ValidationResult:
        """安全性驗證：檢測SQL注入、XSS等攻擊"""
        # 檢測SQL注入
        for pattern in self.sql_injection_patterns:
            if re.search(pattern, user_input, re.IGNORECASE):
                return ValidationResult(
                    is_valid=False,
                    message="Potential SQL injection detected",
                    level=ValidationLevel.CRITICAL
                )
        
        # 檢測XSS
        for pattern in self.xss_patterns:
            if re.search(pattern, user_input, re.IGNORECASE):
                return ValidationResult(
                    is_valid=False,
                    message="Potential XSS attack detected",
                    level=ValidationLevel.CRITICAL
                )
        
        # 檢測敏感詞
        for category, words in self.sensitive_words.items():
            for word in words:
                if word in user_input:
                    return ValidationResult(
                        is_valid=False,
                        message=f"Sensitive content detected: {category}",
                        level=ValidationLevel.HIGH
                    )
        
        return ValidationResult(is_valid=True)
    
    def _format_validation(self, user_input: str) -> ValidationResult:
        """格式驗證：檢查輸入格式是否符合要求"""
        # 檢查長度
        if len(user_input) > self.business_rules["max_message_length"]:
            return ValidationResult(
                is_valid=False,
                message=f"Message too long (max {self.business_rules['max_message_length']} characters)",
                level=ValidationLevel.MEDIUM
            )
        
        if len(user_input.strip()) < self.business_rules["min_message_length"]:
            return ValidationResult(
                is_valid=False,
                message="Message too short or empty",
                level=ValidationLevel.MEDIUM
            )
        
        # 檢查是否包含可讀內容
        if not re.search(r'[\w\u4e00-\u9fff]+', user_input):
            return ValidationResult(
                is_valid=False,
                message="Message contains no readable content",
                level=ValidationLevel.MEDIUM
            )
        
        return ValidationResult(is_valid=True)
    
    def _business_validation(self, user_input: str, context: Dict[str, Any]) -> ValidationResult:
        """業務規則驗證"""
        # 檢查業務時間
        current_hour = datetime.now().hour
        if not (self.business_rules["business_hours"]["start"] <= 
                current_hour < self.business_rules["business_hours"]["end"]):
            # 非業務時間仍然接受，但記錄
            logger.info(f"Message received outside business hours: {current_hour}")
        
        # 檢查對話長度
        if context.get("message_count", 0) > self.business_rules["max_conversation_length"]:
            return ValidationResult(
                is_valid=False,
                message="Conversation too long, please start a new session",
                level=ValidationLevel.MEDIUM
            )
        
        return ValidationResult(is_valid=True)
    
    def _semantic_validation(self, user_input: str, context: Dict[str, Any]) -> ValidationResult:
        """語義驗證：檢查輸入是否有意義"""
        # 檢查是否為有效查詢
        is_valid_query = False
        matched_patterns = []
        
        for pattern_name, pattern in self.valid_query_patterns.items():
            if re.search(pattern, user_input):
                is_valid_query = True
                matched_patterns.append(pattern_name)
        
        # 如果沒有匹配任何模式，可能是閒聊或無關查詢
        if not is_valid_query and len(user_input) > 10:
            # 使用更寬鬆的標準，允許一般對話
            if re.search(r'[\u4e00-\u9fff]{2,}', user_input):  # 至少包含2個中文字符
                is_valid_query = True
                matched_patterns.append("general_conversation")
        
        if not is_valid_query:
            return ValidationResult(
                is_valid=False,
                message="Unable to understand the query, please rephrase",
                level=ValidationLevel.LOW
            )
        
        # 將匹配的模式添加到上下文中
        if context is not None:
            context["matched_patterns"] = matched_patterns
        
        return ValidationResult(is_valid=True)
    
    def _sanitize_input(self, user_input: str) -> str:
        """清理和標準化輸入"""
        # 移除多餘的空白
        sanitized = re.sub(r'\s+', ' ', user_input.strip())
        
        # 移除特殊控制字符
        sanitized = re.sub(r'[\x00-\x1F\x7F-\x9F]', '', sanitized)
        
        # HTML實體編碼
        html_escape_table = {
            "&": "&amp;",
            '"': "&quot;",
            "'": "&apos;",
            ">": "&gt;",
            "<": "&lt;",
        }
        for char, escape in html_escape_table.items():
            sanitized = sanitized.replace(char, escape)
        
        return sanitized
    
    def validate_phone_number(self, phone: str) -> Tuple[bool, Optional[str]]:
        """驗證電話號碼"""
        # 移除空格和特殊字符
        cleaned_phone = re.sub(r'[\s\-\(\)]', '', phone)
        
        # 香港電話號碼模式
        hk_pattern = r'^(\+852)?[5-9]\d{7}$'
        
        if re.match(hk_pattern, cleaned_phone):
            # 標準化為8位數字
            if cleaned_phone.startswith('+852'):
                cleaned_phone = cleaned_phone[4:]
            return True, cleaned_phone
        
        return False, None
    
    def validate_email(self, email: str) -> bool:
        """驗證電子郵件地址"""
        email_pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        return bool(re.match(email_pattern, email))
    
    def validate_date(self, date_str: str) -> Tuple[bool, Optional[datetime]]:
        """驗證日期格式"""
        date_formats = [
            "%Y-%m-%d",
            "%Y/%m/%d",
            "%d-%m-%Y",
            "%d/%m/%Y",
            "%Y年%m月%d日"
        ]
        
        for fmt in date_formats:
            try:
                parsed_date = datetime.strptime(date_str, fmt)
                return True, parsed_date
            except ValueError:
                continue
        
        return False, None
    
    def extract_intent(self, user_input: str) -> Dict[str, Any]:
        """從用戶輸入中提取意圖"""
        intents = {
            "price_inquiry": False,
            "appointment_booking": False,
            "package_comparison": False,
            "location_inquiry": False,
            "general_inquiry": False
        }
        
        # 價格查詢
        if re.search(r'(價格|費用|多少錢|收費|優惠|折扣)', user_input):
            intents["price_inquiry"] = True
            
        # 預約相關
        if re.search(r'(預約|時間|幾點|星期|週末|平日|安排)', user_input):
            intents["appointment_booking"] = True
            
        # 套餐比較
        if re.search(r'(比較|區別|差異|哪個好|推薦)', user_input):
            intents["package_comparison"] = True
            
        # 位置查詢
        if re.search(r'(地址|位置|怎麼去|在哪|交通)', user_input):
            intents["location_inquiry"] = True
            
        # 如果沒有特定意圖，歸類為一般諮詢
        if not any(intents.values()):
            intents["general_inquiry"] = True
            
        return {
            "intents": intents,
            "primary_intent": next((k for k, v in intents.items() if v), "general_inquiry")
        }

# 全局驗證器實例
input_validator = InputValidator()