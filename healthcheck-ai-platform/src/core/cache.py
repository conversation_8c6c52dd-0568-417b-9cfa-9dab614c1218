from typing import Optional, Any, Dict, List, Union
import json
import asyncio
import os
from datetime import datetime, timedelta
import logging
from functools import wraps
import hashlib

try:
    import redis.asyncio as redis
    REDIS_AVAILABLE = True
except ImportError:
    REDIS_AVAILABLE = False
    
logger = logging.getLogger(__name__)

class CacheManager:
    """Redis-based caching with fallback to in-memory cache"""
    
    def __init__(self, redis_url: Optional[str] = None):
        self.redis_url = redis_url or os.getenv("REDIS_URL", "redis://localhost:6379/0")
        self.redis_client = None
        self.memory_cache: Dict[str, Dict[str, Any]] = {}  # Fallback cache
        self.cache_stats = {
            "hits": 0,
            "misses": 0,
            "errors": 0
        }
        
        # Initialize Redis connection if available
        if REDIS_AVAILABLE:
            try:
                self.redis_client = redis.from_url(self.redis_url, decode_responses=True)
                logger.info("Redis cache initialized")
            except Exception as e:
                logger.warning(f"Failed to connect to Redis: {e}. Using in-memory cache.")
        else:
            logger.warning("Redis not available. Using in-memory cache.")
    
    async def get(self, key: str) -> Optional[Any]:
        """Get value from cache"""
        try:
            if self.redis_client:
                value = await self.redis_client.get(key)
                if value:
                    self.cache_stats["hits"] += 1
                    return json.loads(value) if value else None
                else:
                    self.cache_stats["misses"] += 1
                    return None
            else:
                # Fallback to memory cache
                if key in self.memory_cache:
                    entry = self.memory_cache[key]
                    if entry["expires_at"] > datetime.now():
                        self.cache_stats["hits"] += 1
                        return entry["value"]
                    else:
                        # Expired
                        del self.memory_cache[key]
                
                self.cache_stats["misses"] += 1
                return None
                
        except Exception as e:
            logger.error(f"Cache get error: {e}")
            self.cache_stats["errors"] += 1
            return None
    
    async def set(self, key: str, value: Any, ttl: int = 3600) -> bool:
        """Set value in cache with TTL in seconds"""
        try:
            if self.redis_client:
                serialized = json.dumps(value)
                await self.redis_client.setex(key, ttl, serialized)
                return True
            else:
                # Fallback to memory cache
                self.memory_cache[key] = {
                    "value": value,
                    "expires_at": datetime.now() + timedelta(seconds=ttl)
                }
                # Clean up expired entries periodically
                if len(self.memory_cache) > 1000:
                    self._cleanup_memory_cache()
                return True
                
        except Exception as e:
            logger.error(f"Cache set error: {e}")
            self.cache_stats["errors"] += 1
            return False
    
    async def delete(self, key: str) -> bool:
        """Delete key from cache"""
        try:
            if self.redis_client:
                await self.redis_client.delete(key)
            else:
                self.memory_cache.pop(key, None)
            return True
        except Exception as e:
            logger.error(f"Cache delete error: {e}")
            return False
    
    async def exists(self, key: str) -> bool:
        """Check if key exists in cache"""
        try:
            if self.redis_client:
                return await self.redis_client.exists(key) > 0
            else:
                if key in self.memory_cache:
                    entry = self.memory_cache[key]
                    if entry["expires_at"] > datetime.now():
                        return True
                    else:
                        del self.memory_cache[key]
                return False
        except Exception as e:
            logger.error(f"Cache exists error: {e}")
            return False
    
    async def get_many(self, keys: List[str]) -> Dict[str, Any]:
        """Get multiple values from cache"""
        result = {}
        
        try:
            if self.redis_client:
                values = await self.redis_client.mget(keys)
                for key, value in zip(keys, values):
                    if value:
                        result[key] = json.loads(value)
                        self.cache_stats["hits"] += 1
                    else:
                        self.cache_stats["misses"] += 1
            else:
                # Fallback to memory cache
                for key in keys:
                    value = await self.get(key)
                    if value is not None:
                        result[key] = value
                        
        except Exception as e:
            logger.error(f"Cache get_many error: {e}")
            self.cache_stats["errors"] += 1
            
        return result
    
    async def set_many(self, mapping: Dict[str, Any], ttl: int = 3600) -> bool:
        """Set multiple values in cache"""
        try:
            if self.redis_client:
                # Redis doesn't have native msetex, so we use pipeline
                pipe = self.redis_client.pipeline()
                for key, value in mapping.items():
                    pipe.setex(key, ttl, json.dumps(value))
                await pipe.execute()
                return True
            else:
                # Fallback to memory cache
                for key, value in mapping.items():
                    await self.set(key, value, ttl)
                return True
                
        except Exception as e:
            logger.error(f"Cache set_many error: {e}")
            self.cache_stats["errors"] += 1
            return False
    
    async def increment(self, key: str, amount: int = 1) -> Optional[int]:
        """Increment a counter in cache"""
        try:
            if self.redis_client:
                return await self.redis_client.incrby(key, amount)
            else:
                # Fallback implementation
                current = await self.get(key) or 0
                new_value = current + amount
                await self.set(key, new_value)
                return new_value
        except Exception as e:
            logger.error(f"Cache increment error: {e}")
            return None
    
    async def expire(self, key: str, ttl: int) -> bool:
        """Set expiration time for a key"""
        try:
            if self.redis_client:
                return await self.redis_client.expire(key, ttl)
            else:
                if key in self.memory_cache:
                    self.memory_cache[key]["expires_at"] = datetime.now() + timedelta(seconds=ttl)
                return True
        except Exception as e:
            logger.error(f"Cache expire error: {e}")
            return False
    
    async def clear_pattern(self, pattern: str) -> int:
        """Clear all keys matching pattern"""
        count = 0
        try:
            if self.redis_client:
                cursor = 0
                while True:
                    cursor, keys = await self.redis_client.scan(cursor, match=pattern, count=100)
                    if keys:
                        await self.redis_client.delete(*keys)
                        count += len(keys)
                    if cursor == 0:
                        break
            else:
                # Fallback for memory cache
                keys_to_delete = [k for k in self.memory_cache.keys() if pattern.replace('*', '') in k]
                for key in keys_to_delete:
                    del self.memory_cache[key]
                    count += 1
                    
        except Exception as e:
            logger.error(f"Cache clear_pattern error: {e}")
            
        return count
    
    def _cleanup_memory_cache(self):
        """Clean up expired entries from memory cache"""
        now = datetime.now()
        expired_keys = [k for k, v in self.memory_cache.items() if v["expires_at"] <= now]
        for key in expired_keys:
            del self.memory_cache[key]
    
    async def close(self):
        """Close Redis connection"""
        if self.redis_client:
            await self.redis_client.close()
    
    @property
    def stats(self) -> Dict[str, int]:
        """Get cache statistics"""
        return {
            **self.cache_stats,
            "memory_cache_size": len(self.memory_cache) if not self.redis_client else 0
        }

# Cache key builders
class CacheKeys:
    """Standardized cache key builders"""
    
    @staticmethod
    def conversation(customer_id: str) -> str:
        return f"conversation:{customer_id}"
    
    @staticmethod
    def customer_profile(customer_id: str) -> str:
        return f"customer:{customer_id}"
    
    @staticmethod
    def package_price(package_code: str, promotions: str = "") -> str:
        promo_hash = hashlib.md5(promotions.encode()).hexdigest()[:8] if promotions else "none"
        return f"price:{package_code}:{promo_hash}"
    
    @staticmethod
    def agent_state(agent_name: str, conversation_id: str) -> str:
        return f"agent_state:{agent_name}:{conversation_id}"
    
    @staticmethod
    def follow_up_tasks(customer_id: str) -> str:
        return f"follow_up:{customer_id}"
    
    @staticmethod
    def metrics_hourly(metric_type: str, hour: str) -> str:
        return f"metrics:{metric_type}:{hour}"
    
    @staticmethod
    def rate_limit(identifier: str, action: str) -> str:
        return f"rate_limit:{identifier}:{action}"

# Caching decorator
def cached(ttl: int = 3600, key_prefix: str = ""):
    """Decorator for caching function results"""
    def decorator(func):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            # Generate cache key
            cache_key = f"{key_prefix}:{func.__name__}"
            if args:
                cache_key += f":{':'.join(str(arg) for arg in args)}"
            if kwargs:
                cache_key += f":{':'.join(f'{k}={v}' for k, v in sorted(kwargs.items()))}"
            
            # Try to get from cache
            cache_manager = args[0].cache_manager if hasattr(args[0], 'cache_manager') else None
            if cache_manager:
                cached_value = await cache_manager.get(cache_key)
                if cached_value is not None:
                    logger.debug(f"Cache hit for {cache_key}")
                    return cached_value
            
            # Execute function
            result = await func(*args, **kwargs)
            
            # Store in cache
            if cache_manager and result is not None:
                await cache_manager.set(cache_key, result, ttl)
                logger.debug(f"Cached result for {cache_key}")
            
            return result
        
        return wrapper
    return decorator

# Global cache instance
cache_manager = CacheManager()

# Conversation state manager using cache
class ConversationStateManager:
    """Manage conversation state in cache"""
    
    def __init__(self, cache: CacheManager):
        self.cache = cache
    
    async def get_state(self, conversation_id: str) -> Optional[Dict[str, Any]]:
        """Get conversation state"""
        key = f"conv_state:{conversation_id}"
        return await self.cache.get(key)
    
    async def save_state(self, conversation_id: str, state: Dict[str, Any], ttl: int = 7200):
        """Save conversation state"""
        key = f"conv_state:{conversation_id}"
        await self.cache.set(key, state, ttl)
    
    async def update_state(self, conversation_id: str, updates: Dict[str, Any]):
        """Update specific fields in conversation state"""
        current_state = await self.get_state(conversation_id) or {}
        current_state.update(updates)
        await self.save_state(conversation_id, current_state)
    
    async def delete_state(self, conversation_id: str):
        """Delete conversation state"""
        key = f"conv_state:{conversation_id}"
        await self.cache.delete(key)
    
    async def get_active_conversations(self) -> List[str]:
        """Get all active conversation IDs"""
        # This is a simplified implementation
        # In production, you might want to maintain a separate set
        pattern = "conv_state:*"
        if self.cache.redis_client:
            cursor = 0
            conversations = []
            while True:
                cursor, keys = await self.cache.redis_client.scan(cursor, match=pattern, count=100)
                conversations.extend([k.split(":")[-1] for k in keys])
                if cursor == 0:
                    break
            return conversations
        else:
            # Fallback for memory cache
            return [k.split(":")[-1] for k in self.cache.memory_cache.keys() if k.startswith("conv_state:")]