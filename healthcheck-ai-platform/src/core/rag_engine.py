"""
RAG (Retrieval-Augmented Generation) 引擎
結合向量檢索和LLM生成，提供準確的回答
"""
import asyncio
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime
import logging
import json
from dataclasses import dataclass

from .vector_store import VectorStore, VectorSearchResult, create_vector_store
from .knowledge_base_manager import KnowledgeBaseManager
from .knowledge_validator import knowledge_validator
from .output_validator import output_validator
from ..integrations.llm_client import llm_manager

logger = logging.getLogger(__name__)

@dataclass
class RAGContext:
    """RAG上下文"""
    query: str
    retrieved_items: List[VectorSearchResult]
    selected_items: List[Dict[str, Any]]
    confidence_score: float
    metadata: Dict[str, Any]

@dataclass
class RAGResponse:
    """RAG響應"""
    answer: str
    context: RAGContext
    sources: List[str]
    confidence: float
    needs_validation: bool

class RAGEngine:
    """RAG引擎"""
    
    def __init__(
        self,
        vector_store: VectorStore,
        knowledge_manager: KnowledgeBaseManager,
        llm_temperature: float = 0.3
    ):
        self.vector_store = vector_store
        self.knowledge_manager = knowledge_manager
        self.llm_temperature = llm_temperature
        
        # 配置
        self.config = {
            "max_retrieval_items": 5,
            "min_relevance_score": 0.7,
            "context_window_size": 4000,  # tokens
            "answer_max_length": 500,
            "use_reranking": True,
            "validate_output": True
        }
        
        # 緩存最近的查詢
        self.query_cache: Dict[str, RAGResponse] = {}
        self.cache_size = 100
        
        logger.info("RAG Engine initialized")
    
    async def generate_answer(
        self,
        query: str,
        context: Optional[Dict[str, Any]] = None,
        category: Optional[str] = None
    ) -> RAGResponse:
        """
        生成基於檢索的回答
        
        Args:
            query: 用戶查詢
            context: 對話上下文
            category: 限定搜索類別
            
        Returns:
            RAGResponse: 包含答案和來源的響應
        """
        # 檢查緩存
        cache_key = self._generate_cache_key(query, category)
        if cache_key in self.query_cache:
            logger.info(f"Cache hit for query: {query}")
            return self.query_cache[cache_key]
        
        # 1. 檢索相關知識
        retrieved_items = await self._retrieve_knowledge(query, category)
        
        # 2. 重新排序和選擇最相關的項目
        selected_items = await self._rerank_and_select(query, retrieved_items)
        
        # 3. 構建上下文
        rag_context = self._build_context(query, retrieved_items, selected_items)
        
        # 4. 生成答案
        answer = await self._generate_with_context(query, selected_items, context)
        
        # 5. 驗證答案
        confidence = 1.0
        needs_validation = False
        
        if self.config["validate_output"]:
            validation_result = output_validator.validate_output(answer, context or {})
            confidence = validation_result.confidence_score
            needs_validation = not validation_result.is_valid
            
            # 如果驗證失敗，嘗試修正
            if needs_validation and validation_result.corrections:
                answer = output_validator.generate_safe_response(answer, validation_result)
        
        # 6. 提取來源
        sources = self._extract_sources(selected_items)
        
        # 創建響應
        response = RAGResponse(
            answer=answer,
            context=rag_context,
            sources=sources,
            confidence=confidence * rag_context.confidence_score,
            needs_validation=needs_validation
        )
        
        # 更新緩存
        self._update_cache(cache_key, response)
        
        return response
    
    async def _retrieve_knowledge(
        self,
        query: str,
        category: Optional[str] = None
    ) -> List[VectorSearchResult]:
        """檢索相關知識"""
        # 向量搜索
        results = await self.vector_store.search_knowledge(
            query=query,
            top_k=self.config["max_retrieval_items"] * 2,  # 檢索更多以便重排序
            category=category,
            min_score=self.config["min_relevance_score"]
        )
        
        # 如果結果不足，降低閾值再試一次
        if len(results) < self.config["max_retrieval_items"] // 2:
            logger.info("Insufficient results, lowering threshold")
            results = await self.vector_store.search_knowledge(
                query=query,
                top_k=self.config["max_retrieval_items"],
                category=category,
                min_score=self.config["min_relevance_score"] * 0.7
            )
        
        return results
    
    async def _rerank_and_select(
        self,
        query: str,
        retrieved_items: List[VectorSearchResult]
    ) -> List[Dict[str, Any]]:
        """重新排序並選擇最相關的項目"""
        if not self.config["use_reranking"] or len(retrieved_items) <= self.config["max_retrieval_items"]:
            # 直接使用向量搜索的排序
            return [
                {"item_id": item.item_id, **item.content}
                for item in retrieved_items[:self.config["max_retrieval_items"]]
            ]
        
        # 使用LLM進行重新排序
        rerank_prompt = self._build_rerank_prompt(query, retrieved_items)
        
        messages = [{"role": "system", "content": rerank_prompt}]
        response = await llm_manager.chat(messages, temperature=0.1)
        
        # 解析重排序結果
        try:
            reranked_ids = json.loads(response.content)
            selected_items = []
            
            # 按重排序結果選擇項目
            for item_id in reranked_ids[:self.config["max_retrieval_items"]]:
                for item in retrieved_items:
                    if item.item_id == item_id:
                        selected_items.append({
                            "item_id": item.item_id,
                            **item.content
                        })
                        break
                        
            return selected_items
            
        except Exception as e:
            logger.error(f"Reranking failed: {e}")
            # 回退到原始排序
            return [
                {"item_id": item.item_id, **item.content}
                for item in retrieved_items[:self.config["max_retrieval_items"]]
            ]
    
    def _build_rerank_prompt(
        self,
        query: str,
        items: List[VectorSearchResult]
    ) -> str:
        """構建重排序提示"""
        prompt = f"""你是一個知識檢索專家。請根據用戶查詢的相關性對以下項目進行重新排序。

用戶查詢：{query}

檢索到的項目：
"""
        for item in items:
            prompt += f"\nID: {item.item_id}\n"
            prompt += f"內容: {json.dumps(item.content, ensure_ascii=False)}\n"
            prompt += f"相關性分數: {item.score:.3f}\n"
            prompt += "-" * 40
            
        prompt += """

請按相關性從高到低排序，只返回項目ID的JSON數組，格式如下：
["item_id_1", "item_id_2", "item_id_3", ...]

只包含最相關的項目ID。"""
        
        return prompt
    
    def _build_context(
        self,
        query: str,
        retrieved_items: List[VectorSearchResult],
        selected_items: List[Dict[str, Any]]
    ) -> RAGContext:
        """構建RAG上下文"""
        # 計算整體置信度
        if retrieved_items:
            avg_score = sum(item.score for item in retrieved_items) / len(retrieved_items)
            confidence_score = min(avg_score * 1.2, 1.0)  # 稍微放大但不超過1
        else:
            confidence_score = 0.0
            
        metadata = {
            "retrieval_count": len(retrieved_items),
            "selected_count": len(selected_items),
            "timestamp": datetime.now().isoformat()
        }
        
        return RAGContext(
            query=query,
            retrieved_items=retrieved_items,
            selected_items=selected_items,
            confidence_score=confidence_score,
            metadata=metadata
        )
    
    async def _generate_with_context(
        self,
        query: str,
        selected_items: List[Dict[str, Any]],
        conversation_context: Optional[Dict[str, Any]] = None
    ) -> str:
        """基於檢索上下文生成答案"""
        # 構建增強提示
        enhanced_prompt = self._build_generation_prompt(
            query,
            selected_items,
            conversation_context
        )
        
        # 使用LLM生成答案
        messages = [
            {
                "role": "system",
                "content": """你是森仁健康檢查中心的專業客服。
基於提供的知識庫信息回答客戶問題。
只使用提供的信息，不要編造或猜測。
如果信息不足，請誠實說明。"""
            },
            {
                "role": "user",
                "content": enhanced_prompt
            }
        ]
        
        response = await llm_manager.chat(
            messages,
            temperature=self.llm_temperature,
            max_tokens=self.config["answer_max_length"]
        )
        
        return response.content
    
    def _build_generation_prompt(
        self,
        query: str,
        selected_items: List[Dict[str, Any]],
        conversation_context: Optional[Dict[str, Any]] = None
    ) -> str:
        """構建生成提示"""
        prompt = f"客戶問題：{query}\n\n"
        
        if conversation_context:
            prompt += "對話背景：\n"
            if conversation_context.get("identified_needs"):
                prompt += f"- 客戶需求：{', '.join(conversation_context['identified_needs'])}\n"
            if conversation_context.get("customer_segment"):
                prompt += f"- 客戶類型：{conversation_context['customer_segment']}\n"
            prompt += "\n"
        
        prompt += "相關知識：\n"
        for i, item in enumerate(selected_items, 1):
            prompt += f"\n【知識{i}】\n"
            
            # 格式化知識內容
            if "name" in item:
                prompt += f"名稱：{item['name']}\n"
            if "price" in item:
                prompt += f"價格：${item['price']}\n"
            if "description" in item:
                prompt += f"描述：{item['description']}\n"
            if "features" in item and isinstance(item["features"], list):
                prompt += f"特點：{', '.join(item['features'])}\n"
                
            # 添加其他相關字段
            for key, value in item.items():
                if key not in ["item_id", "name", "price", "description", "features"]:
                    if isinstance(value, (str, int, float)):
                        prompt += f"{key}：{value}\n"
                        
            prompt += "-" * 40
            
        prompt += "\n\n請基於以上信息，用專業、友好的語氣回答客戶問題。"
        
        return prompt
    
    def _extract_sources(self, selected_items: List[Dict[str, Any]]) -> List[str]:
        """提取信息來源"""
        sources = []
        
        for item in selected_items:
            if "item_id" in item:
                sources.append(f"知識庫項目：{item['item_id']}")
            elif "name" in item:
                sources.append(f"來源：{item['name']}")
            else:
                sources.append("知識庫")
                
        return list(set(sources))  # 去重
    
    def _generate_cache_key(self, query: str, category: Optional[str]) -> str:
        """生成緩存鍵"""
        import hashlib
        
        key_parts = [query.lower().strip()]
        if category:
            key_parts.append(category)
            
        key_string = "|".join(key_parts)
        return hashlib.md5(key_string.encode()).hexdigest()
    
    def _update_cache(self, key: str, response: RAGResponse):
        """更新緩存"""
        # 限制緩存大小
        if len(self.query_cache) >= self.cache_size:
            # 刪除最舊的項目（簡單的FIFO）
            oldest_key = next(iter(self.query_cache))
            del self.query_cache[oldest_key]
            
        self.query_cache[key] = response
    
    async def enhance_answer_with_examples(
        self,
        answer: str,
        context: RAGContext
    ) -> str:
        """用例子增強答案"""
        # 如果答案中提到了套餐，添加具體例子
        enhanced_answer = answer
        
        for item in context.selected_items:
            if "name" in item and item["name"] in answer:
                # 添加更多細節
                if "key_features" in item:
                    features_text = f"\n{item['name']}包含：{'、'.join(item['key_features'][:3])}等"
                    if features_text not in enhanced_answer:
                        enhanced_answer += features_text
                        
        return enhanced_answer
    
    async def validate_and_correct(
        self,
        response: RAGResponse
    ) -> RAGResponse:
        """驗證並修正響應"""
        if not response.needs_validation:
            return response
            
        # 使用知識驗證器檢查
        validation_report = knowledge_validator.validate_response(
            response.answer,
            {"sources": response.sources}
        )
        
        if validation_report.is_valid:
            response.needs_validation = False
        else:
            # 嘗試修正
            corrected_answer = response.answer
            
            # 應用修正建議
            for correction in validation_report.corrections:
                if "actual" in correction:
                    corrected_answer = corrected_answer.replace(
                        correction.get("incorrect", ""),
                        correction["actual"]
                    )
                    
            response.answer = corrected_answer
            response.confidence *= 0.9  # 稍微降低置信度
            
        return response
    
    def get_statistics(self) -> Dict[str, Any]:
        """獲取統計信息"""
        return {
            "cache_size": len(self.query_cache),
            "cache_hit_rate": self._calculate_cache_hit_rate(),
            "avg_retrieval_count": self._calculate_avg_retrieval_count(),
            "config": self.config
        }
    
    def _calculate_cache_hit_rate(self) -> float:
        """計算緩存命中率（需要額外的計數器實現）"""
        # 簡化版本
        return len(self.query_cache) / self.cache_size if self.cache_size > 0 else 0
    
    def _calculate_avg_retrieval_count(self) -> float:
        """計算平均檢索數量"""
        if not self.query_cache:
            return 0
            
        total_count = sum(
            len(response.context.retrieved_items)
            for response in self.query_cache.values()
        )
        
        return total_count / len(self.query_cache)

# 創建RAG引擎的工廠函數
async def create_rag_engine(
    knowledge_manager: KnowledgeBaseManager,
    vector_store: Optional[VectorStore] = None
) -> RAGEngine:
    """創建RAG引擎"""
    if vector_store is None:
        vector_store = create_vector_store()
        
        # 初始化向量數據庫（如果需要）
        logger.info("Initializing vector store with existing knowledge...")
        
        # 獲取所有知識項
        all_items = []
        for category in ["packages", "pricing", "promotions", "policies"]:
            items = await knowledge_manager.get_knowledge(
                category=knowledge_manager.KnowledgeCategory(category)
            )
            all_items.extend(items)
            
        # 批量添加到向量存儲
        if all_items:
            await vector_store.batch_add_knowledge(all_items)
            logger.info(f"Indexed {len(all_items)} knowledge items")
    
    return RAGEngine(vector_store, knowledge_manager)