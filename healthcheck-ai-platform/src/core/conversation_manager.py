from typing import Dict, Any, List, Optional
from datetime import datetime
from enum import Enum
import asyncio
from src.agents.traffic_reception_agent import TrafficReceptionAgent
from src.agents.pricing_calculator_agent import PricingCalculatorAgent
from src.integrations.messaging_interface import Message, MessagingInterface
from src.core.auto_reply_handler import AutoReplyHandler

class ConversationState(Enum):
    """對話狀態枚舉"""
    INIT = "init"
    GREETING = "greeting"
    UNDERSTANDING_NEEDS = "understanding_needs"
    PRESENTING_OPTIONS = "presenting_options"
    CALCULATING_PRICE = "calculating_price"
    HANDLING_OBJECTIONS = "handling_objections"
    CLOSING_SALE = "closing_sale"
    FOLLOW_UP = "follow_up"
    COMPLETED = "completed"

class ConversationContext:
    """對話上下文"""
    def __init__(self, customer_id: str, source: str = "organic"):
        self.customer_id = customer_id
        self.source = source
        self.state = ConversationState.INIT
        self.history: List[Message] = []
        self.customer_info: Dict[str, Any] = {}
        self.identified_needs: List[str] = []
        self.quoted_packages: List[Dict] = []
        self.objections: List[str] = []
        self.metadata: Dict[str, Any] = {
            'start_time': datetime.now(),
            'language': 'zh-TW'
        }

class ConversationManager:
    """對話流程管理器"""
    
    def __init__(self, messaging_client: MessagingInterface):
        self.messaging_client = messaging_client
        self.agents = self._initialize_agents()
        self.active_conversations: Dict[str, ConversationContext] = {}
        self.auto_reply_handler = AutoReplyHandler()
        
    def _initialize_agents(self) -> Dict[str, Any]:
        """初始化所有 Agents"""
        return {
            'traffic_reception': TrafficReceptionAgent(),
            'pricing_calculator': PricingCalculatorAgent(),
            # TODO: 初始化其他 Agents
        }
    
    async def handle_message(self, message: Message) -> None:
        """處理收到的消息"""
        customer_id = message.sender_id
        
        # 獲取或創建對話上下文
        context = self._get_or_create_context(customer_id)
        context.history.append(message)
        
        # 檢查是否可以自動回覆
        if self.auto_reply_handler.should_auto_reply(message.content):
            auto_reply_result = self.auto_reply_handler.match_keywords(message.content)
            if auto_reply_result:
                template_key, template_content = auto_reply_result
                response = self.auto_reply_handler.format_response(template_content)
                
                # 發送自動回覆
                await self.messaging_client.send_message(
                    recipient_id=customer_id,
                    content=response
                )
                
                # 如果是初次對話，更新狀態
                if context.state == ConversationState.INIT:
                    context.state = ConversationState.GREETING
                
                # 記錄自動回覆
                context.metadata['last_auto_reply'] = {
                    'template_key': template_key,
                    'timestamp': datetime.now()
                }
                return
        
        # 如果沒有匹配的自動回覆，則根據當前狀態處理消息
        await self._process_conversation_state(context, message)
    
    def _get_or_create_context(self, customer_id: str) -> ConversationContext:
        """獲取或創建對話上下文"""
        if customer_id not in self.active_conversations:
            # TODO: 從數據庫加載客戶信息
            self.active_conversations[customer_id] = ConversationContext(
                customer_id=customer_id,
                source=self._detect_source(customer_id)
            )
        return self.active_conversations[customer_id]
    
    async def _process_conversation_state(
        self,
        context: ConversationContext,
        message: Message
    ) -> None:
        """根據對話狀態處理消息"""
        
        if context.state == ConversationState.INIT:
            await self._handle_initial_contact(context, message)
            
        elif context.state == ConversationState.GREETING:
            await self._handle_needs_discovery(context, message)
            
        elif context.state == ConversationState.UNDERSTANDING_NEEDS:
            await self._handle_package_selection(context, message)
            
        elif context.state == ConversationState.PRESENTING_OPTIONS:
            await self._handle_pricing_request(context, message)
            
        elif context.state == ConversationState.CALCULATING_PRICE:
            await self._handle_price_response(context, message)
            
        elif context.state == ConversationState.HANDLING_OBJECTIONS:
            await self._handle_objections(context, message)
            
        elif context.state == ConversationState.CLOSING_SALE:
            await self._handle_closing(context, message)
    
    async def _handle_initial_contact(
        self,
        context: ConversationContext,
        message: Message
    ) -> None:
        """處理初次接觸"""
        # 調用 Traffic Reception Agent
        reception_result = await self.agents['traffic_reception'].process({
            'customer_info': context.customer_info,
            'message': message.content,
            'source': context.source
        })
        
        # 發送問候消息
        await self.messaging_client.send_message(
            recipient_id=context.customer_id,
            content=reception_result['greeting']
        )
        
        # 更新上下文
        context.state = ConversationState.GREETING
        context.identified_needs = reception_result['context']['identified_needs']
    
    async def _handle_needs_discovery(
        self,
        context: ConversationContext,
        message: Message
    ) -> None:
        """處理需求發現階段"""
        content = message.content.strip()
        
        # 簡單的選項處理
        if content in ['1', '2', '3', '4']:
            need_mapping = {
                '1': 'personal_checkup',
                '2': 'family_package',
                '3': 'corporate_checkup',
                '4': 'specific_test'
            }
            context.identified_needs.append(need_mapping[content])
            
            # 根據選擇提供相應信息
            await self._present_package_options(context)
            context.state = ConversationState.PRESENTING_OPTIONS
        else:
            # 自然語言理解
            # TODO: 使用 NLP 理解客戶需求
            await self._ask_clarifying_question(context)
    
    async def _present_package_options(self, context: ConversationContext) -> None:
        """展示套餐選項"""
        if 'personal_checkup' in context.identified_needs:
            message = """為您推薦以下個人體檢套餐：

🏥 **基礎體檢** - $2,980
✓ 血常規、尿常規、肝腎功能
✓ 心電圖檢查
⏱ 約需2小時

🏥 **標準體檢** - $4,980
✓ 包含基礎體檢所有項目
✓ 胸部X光、腹部超聲波
✓ 甲狀腺功能檢查
⏱ 約需3小時

🏥 **尊享體檢** - $8,980
✓ 包含標準體檢所有項目
✓ 胃鏡檢查、CT掃描
✓ 腫瘤標記物檢測
⏱ 約需4小時

請問您對哪個套餐感興趣？我可以為您計算優惠價格 😊"""
            
            await self.messaging_client.send_message(
                recipient_id=context.customer_id,
                content=message
            )
    
    async def _handle_pricing_request(
        self,
        context: ConversationContext,
        message: Message
    ) -> None:
        """處理價格查詢請求"""
        # 解析客戶選擇的套餐
        selected_packages = self._parse_package_selection(message.content)
        
        if selected_packages:
            context.state = ConversationState.CALCULATING_PRICE
            
            # 調用價格計算 Agent
            pricing_result = await self.agents['pricing_calculator'].process({
                'pricing_request': {
                    'packages': selected_packages,
                    'quantity': 1,
                    'booking_date': None
                },
                'customer_info': context.customer_info
            })
            
            # 發送報價
            await self.messaging_client.send_message(
                recipient_id=context.customer_id,
                content=pricing_result['quote_message']
            )
            
            # 保存報價信息
            context.quoted_packages.append(pricing_result['calculation'])
            
            # 添加行動召喚
            await asyncio.sleep(1)  # 短暫延遲
            await self.messaging_client.send_message(
                recipient_id=context.customer_id,
                content="📱 如需預約或有任何疑問，我隨時為您服務！\n\n立即預約可享額外優惠哦~ 😊"
            )
            
            context.state = ConversationState.CLOSING_SALE
    
    def _parse_package_selection(self, content: str) -> List[str]:
        """解析套餐選擇"""
        content_lower = content.lower()
        packages = []
        
        package_keywords = {
            'basic': ['基礎', 'basic', '2980'],
            'standard': ['標準', 'standard', '4980'],
            'premium': ['尊享', 'premium', '8980'],
            'vip': ['vip', '全面', '15980']
        }
        
        for package_id, keywords in package_keywords.items():
            if any(keyword in content_lower for keyword in keywords):
                packages.append(package_id)
        
        return packages
    
    async def _ask_clarifying_question(self, context: ConversationContext) -> None:
        """詢問澄清問題"""
        questions = [
            "請問您主要想檢查哪些方面呢？",
            "您是為自己還是家人諮詢體檢呢？",
            "請問您有特別關注的健康問題嗎？",
            "您希望在哪個分店進行體檢呢？"
        ]
        
        # 根據上下文選擇合適的問題
        question = questions[0]  # 簡化處理
        
        await self.messaging_client.send_message(
            recipient_id=context.customer_id,
            content=question
        )
    
    def _detect_source(self, customer_id: str) -> str:
        """檢測客戶來源"""
        # TODO: 實現來源追踪邏輯
        return "organic"
    
    async def _handle_objections(self, context: ConversationContext, message: Message) -> None:
        """處理客戶異議"""
        # TODO: 實現異議處理邏輯
        pass
    
    async def _handle_closing(self, context: ConversationContext, message: Message) -> None:
        """處理成交階段"""
        # TODO: 實現成交邏輯
        pass