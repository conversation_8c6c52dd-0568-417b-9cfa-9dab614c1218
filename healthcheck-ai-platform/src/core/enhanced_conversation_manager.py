"""
增強型對話管理器
整合輸入驗證、輸出驗證、知識驗證和回退處理
"""
import asyncio
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime
from enum import Enum
import logging
import json

from .conversation_manager import ConversationManager, ConversationState, ConversationContext
from .input_validator import input_validator
from .output_validator import output_validator
from .knowledge_validator import knowledge_validator
from .fallback_handler import fallback_handler, FallbackDecision
from .cache import cache_manager, CacheKeys
from ..integrations.messaging_interface import Message, MessagingInterface
from ..integrations.llm_client import llm_manager

logger = logging.getLogger(__name__)

class EnhancedConversationContext(ConversationContext):
    """增強的對話上下文，包含更多追踪信息"""
    def __init__(self, customer_id: str, source: str = "organic"):
        super().__init__(customer_id, source)
        self.validation_history: List[Dict[str, Any]] = []
        self.confidence_scores: List[float] = []
        self.fallback_attempts: int = 0
        self.human_escalated: bool = False
        self.knowledge_references: List[str] = []
        self.error_count: int = 0
        self.last_successful_interaction: Optional[datetime] = None
        
class EnhancedConversationManager(ConversationManager):
    """增強型對話流程管理器"""
    
    def __init__(self, messaging_client: MessagingInterface):
        super().__init__(messaging_client)
        self.input_validator = input_validator
        self.output_validator = output_validator
        self.knowledge_validator = knowledge_validator
        self.fallback_handler = fallback_handler
        self.cache_manager = cache_manager
        
        # 對話質量指標
        self.quality_metrics = {
            "total_messages": 0,
            "validated_messages": 0,
            "fallback_triggers": 0,
            "human_escalations": 0,
            "avg_confidence": 0.0
        }
        
    async def handle_message(self, message: Message) -> None:
        """
        處理收到的消息（增強版）
        
        Args:
            message: 收到的消息
        """
        customer_id = message.sender_id
        
        # 獲取或創建增強的對話上下文
        context = self._get_or_create_enhanced_context(customer_id)
        context.history.append(message)
        self.quality_metrics["total_messages"] += 1
        
        try:
            # 1. 輸入驗證
            input_validation = self.input_validator.validate_input(
                message.content,
                self._prepare_validation_context(context)
            )
            
            if not input_validation.is_valid:
                await self._handle_invalid_input(context, message, input_validation)
                return
            
            # 2. 提取用戶意圖
            intent_info = self.input_validator.extract_intent(message.content)
            context.metadata["current_intent"] = intent_info["primary_intent"]
            
            # 3. 生成AI回應
            ai_response = await self._generate_ai_response(
                context,
                input_validation.sanitized_input or message.content,
                intent_info
            )
            
            # 4. 輸出驗證
            output_validation = self.output_validator.validate_output(
                ai_response,
                self._prepare_validation_context(context)
            )
            
            # 5. 知識驗證
            knowledge_validation = self.knowledge_validator.validate_response(
                ai_response,
                self._prepare_validation_context(context)
            )
            
            # 6. 計算置信度並決定是否需要回退
            confidence_score = await self.fallback_handler.evaluate_confidence(
                message.content,
                ai_response,
                self._prepare_validation_context(context)
            )
            
            context.confidence_scores.append(confidence_score.overall_score)
            
            # 7. 做出回退決策
            fallback_decision = await self.fallback_handler.make_fallback_decision(
                message.content,
                ai_response,
                self._prepare_validation_context(context),
                confidence_score
            )
            
            # 8. 根據決策採取行動
            if fallback_decision.should_fallback:
                await self._handle_fallback(context, message, fallback_decision)
            else:
                # 發送驗證和優化後的回應
                final_response = self._optimize_response(
                    ai_response,
                    output_validation,
                    knowledge_validation,
                    fallback_decision
                )
                
                await self._send_validated_response(context, final_response)
                
            # 9. 更新對話狀態
            await self._update_conversation_state(context, message)
            
            # 10. 保存對話記錄和指標
            await self._save_conversation_metrics(context)
            
        except Exception as e:
            logger.error(f"Error handling message: {e}")
            await self._handle_system_error(context, message, e)
    
    def _get_or_create_enhanced_context(self, customer_id: str) -> EnhancedConversationContext:
        """獲取或創建增強的對話上下文"""
        if customer_id not in self.active_conversations:
            # 嘗試從緩存恢復
            cached_context = asyncio.run(
                self.cache_manager.get(CacheKeys.conversation(customer_id))
            )
            
            if cached_context:
                context = EnhancedConversationContext(customer_id)
                # 恢復重要字段
                for key, value in cached_context.items():
                    if hasattr(context, key):
                        setattr(context, key, value)
                self.active_conversations[customer_id] = context
            else:
                self.active_conversations[customer_id] = EnhancedConversationContext(
                    customer_id=customer_id,
                    source=self._detect_source(customer_id)
                )
                
        return self.active_conversations[customer_id]
    
    def _prepare_validation_context(self, context: EnhancedConversationContext) -> Dict[str, Any]:
        """準備驗證所需的上下文信息"""
        return {
            "customer_id": context.customer_id,
            "conversation_id": context.metadata.get("conversation_id"),
            "message_count": len(context.history),
            "current_state": context.state.value,
            "identified_needs": context.identified_needs,
            "quoted_packages": context.quoted_packages,
            "customer_sentiment": self._analyze_sentiment(context),
            "topic_changes": self._count_topic_changes(context),
            "last_customer_message": context.history[-1].content if context.history else None,
            "intent": context.metadata.get("current_intent"),
            "previous_quotes": context.quoted_packages
        }
    
    async def _generate_ai_response(
        self,
        context: EnhancedConversationContext,
        sanitized_input: str,
        intent_info: Dict[str, Any]
    ) -> str:
        """
        生成AI回應（增強版）
        
        Args:
            context: 對話上下文
            sanitized_input: 清理後的輸入
            intent_info: 意圖信息
            
        Returns:
            str: AI生成的回應
        """
        # 首先嘗試從知識庫獲取標準回應
        factual_response = self.output_validator.get_factual_response(
            intent_info["primary_intent"],
            self._extract_entity_from_context(context, intent_info)
        )
        
        if factual_response:
            return factual_response
        
        # 構建增強的提示
        enhanced_prompt = self._build_enhanced_prompt(
            context,
            sanitized_input,
            intent_info
        )
        
        # 調用適當的Agent或LLM
        if context.state == ConversationState.INIT:
            agent_response = await self.agents['traffic_reception'].process({
                'customer_info': context.customer_info,
                'message': sanitized_input,
                'source': context.source,
                'intent': intent_info,
                'knowledge_check': True  # 要求Agent檢查知識庫
            })
            return agent_response.get('response', '')
        else:
            # 使用LLM生成回應
            messages = self._prepare_llm_messages(context, enhanced_prompt)
            llm_response = await llm_manager.chat(messages, temperature=0.3)
            return llm_response.content
    
    def _build_enhanced_prompt(
        self,
        context: EnhancedConversationContext,
        user_input: str,
        intent_info: Dict[str, Any]
    ) -> str:
        """構建增強的提示，包含知識庫參考和驗證要求"""
        prompt_parts = [
            "你是森仁健康檢查中心的專業客服。",
            f"客戶意圖：{intent_info['primary_intent']}",
            f"客戶問題：{user_input}",
            "\n重要提醒：",
            "1. 必須基於準確的套餐和價格信息回答",
            "2. 不要做出醫療診斷或治療建議",
            "3. 價格信息必須準確無誤",
            "4. 避免使用絕對性承諾詞彙"
        ]
        
        # 添加相關知識參考
        if intent_info["primary_intent"] == "price_inquiry":
            prompt_parts.append("\n套餐價格參考：")
            prompt_parts.append("- 精選計劃：$1,950（110項）")
            prompt_parts.append("- 全面計劃：$2,950（130項）")
            prompt_parts.append("- 優越計劃：$4,380（158項）")
            prompt_parts.append("- 尊尚計劃：$6,380（172項）")
        
        return "\n".join(prompt_parts)
    
    def _optimize_response(
        self,
        ai_response: str,
        output_validation: Any,
        knowledge_validation: Any,
        fallback_decision: FallbackDecision
    ) -> str:
        """優化和修正AI回應"""
        optimized_response = ai_response
        
        # 應用輸出驗證的修正
        if not output_validation.is_valid and output_validation.corrections:
            optimized_response = self.output_validator.generate_safe_response(
                ai_response,
                output_validation
            )
        
        # 應用知識驗證的建議
        if not knowledge_validation.is_valid and knowledge_validation.suggestions:
            # 添加準確性聲明
            if knowledge_validation.accuracy_score < 0.8:
                optimized_response += "\n\n*具體信息請以最新資料為準。"
        
        # 根據置信度添加適當的語氣詞
        if fallback_decision.confidence_score.overall_score < 0.7:
            optimized_response = self._add_uncertainty_markers(optimized_response)
        
        return optimized_response
    
    def _add_uncertainty_markers(self, response: str) -> str:
        """添加不確定性標記"""
        uncertainty_phrases = {
            "是": "應該是",
            "會": "可能會",
            "需要": "建議",
            "一定": "通常"
        }
        
        for certain, uncertain in uncertainty_phrases.items():
            response = response.replace(certain, uncertain)
            
        return response
    
    async def _send_validated_response(
        self,
        context: EnhancedConversationContext,
        response: str
    ) -> None:
        """發送驗證後的回應"""
        # 添加人性化處理
        humanized_response = await self._humanize_response(response)
        
        # 發送消息
        await self.messaging_client.send_message(
            recipient_id=context.customer_id,
            content=humanized_response
        )
        
        # 記錄成功的交互
        context.last_successful_interaction = datetime.now()
        self.quality_metrics["validated_messages"] += 1
        
        # 更新平均置信度
        if context.confidence_scores:
            self.quality_metrics["avg_confidence"] = sum(context.confidence_scores) / len(context.confidence_scores)
    
    async def _humanize_response(self, response: str) -> str:
        """使回應更加人性化"""
        # 這裡可以集成 anti_bot_service 的人性化功能
        # 暫時返回原始回應
        return response
    
    async def _handle_invalid_input(
        self,
        context: EnhancedConversationContext,
        message: Message,
        validation_result: Any
    ) -> None:
        """處理無效輸入"""
        if validation_result.level.value == "critical":
            # 嚴重問題，記錄並忽略
            logger.warning(f"Critical input validation failure: {validation_result.message}")
            return
        
        # 發送友好的錯誤提示
        error_messages = {
            "too_long": "您的消息太長了，請簡短一些再試。",
            "too_short": "請告訴我更多信息，這樣我才能更好地幫助您。",
            "no_content": "抱歉，我沒有收到您的消息內容，請重新發送。",
            "unknown": "抱歉，我暫時無法理解您的問題，請換個方式描述。"
        }
        
        error_type = self._classify_validation_error(validation_result)
        response = error_messages.get(error_type, error_messages["unknown"])
        
        await self.messaging_client.send_message(
            recipient_id=context.customer_id,
            content=response
        )
        
        context.error_count += 1
    
    async def _handle_fallback(
        self,
        context: EnhancedConversationContext,
        message: Message,
        decision: FallbackDecision
    ) -> None:
        """處理回退情況"""
        context.fallback_attempts += 1
        self.quality_metrics["fallback_triggers"] += 1
        
        if decision.suggested_action == "immediate_transfer":
            # 立即轉接人工
            context.human_escalated = True
            self.quality_metrics["human_escalations"] += 1
            
            escalation_result = await self.fallback_handler.handle_escalation(
                decision,
                self._prepare_validation_context(context)
            )
            
            await self.messaging_client.send_message(
                recipient_id=context.customer_id,
                content=decision.customer_message
            )
            
            # 通知系統人工介入
            await self._notify_human_agent(context, escalation_result)
        else:
            # 發送帶有適當處理的消息
            await self.messaging_client.send_message(
                recipient_id=context.customer_id,
                content=decision.customer_message
            )
    
    async def _handle_system_error(
        self,
        context: EnhancedConversationContext,
        message: Message,
        error: Exception
    ) -> None:
        """處理系統錯誤"""
        logger.error(f"System error in conversation {context.customer_id}: {error}")
        
        # 記錄失敗
        self.fallback_handler.record_failure(context.customer_id)
        
        # 發送通用錯誤消息
        await self.messaging_client.send_message(
            recipient_id=context.customer_id,
            content="抱歉，系統遇到了一些問題。請稍後再試，或直接致電 3614 5393 聯繫我們。"
        )
    
    async def _update_conversation_state(
        self,
        context: EnhancedConversationContext,
        message: Message
    ) -> None:
        """更新對話狀態"""
        # 根據當前狀態和用戶意圖更新狀態
        intent = context.metadata.get("current_intent")
        
        state_transitions = {
            (ConversationState.INIT, "general_inquiry"): ConversationState.GREETING,
            (ConversationState.GREETING, "price_inquiry"): ConversationState.PRESENTING_OPTIONS,
            (ConversationState.PRESENTING_OPTIONS, "appointment_booking"): ConversationState.CLOSING_SALE,
            # 更多狀態轉換...
        }
        
        transition_key = (context.state, intent)
        if transition_key in state_transitions:
            context.state = state_transitions[transition_key]
    
    async def _save_conversation_metrics(self, context: EnhancedConversationContext) -> None:
        """保存對話指標"""
        # 保存到緩存
        await self.cache_manager.set(
            CacheKeys.conversation(context.customer_id),
            {
                "state": context.state.value,
                "identified_needs": context.identified_needs,
                "quoted_packages": context.quoted_packages,
                "confidence_scores": context.confidence_scores[-10:],  # 保留最近10個
                "error_count": context.error_count,
                "fallback_attempts": context.fallback_attempts,
                "human_escalated": context.human_escalated,
                "last_interaction": datetime.now().isoformat()
            },
            ttl=7200  # 2小時
        )
        
        # 更新質量指標
        await self._update_quality_metrics()
    
    def _analyze_sentiment(self, context: EnhancedConversationContext) -> str:
        """分析客戶情緒"""
        # 簡單的情緒分析
        if context.error_count > 2 or context.fallback_attempts > 1:
            return "negative"
        elif context.last_successful_interaction and \
             (datetime.now() - context.last_successful_interaction).seconds < 300:
            return "positive"
        return "neutral"
    
    def _count_topic_changes(self, context: EnhancedConversationContext) -> int:
        """計算話題變化次數"""
        # 簡化實現
        return len(set(context.identified_needs))
    
    def _extract_entity_from_context(
        self,
        context: EnhancedConversationContext,
        intent_info: Dict[str, Any]
    ) -> Optional[str]:
        """從上下文中提取實體"""
        # 根據意圖和上下文返回相關實體
        if intent_info["primary_intent"] == "price_inquiry" and context.identified_needs:
            # 返回最近討論的套餐
            return context.identified_needs[-1] if context.identified_needs else None
        return None
    
    def _prepare_llm_messages(
        self,
        context: EnhancedConversationContext,
        prompt: str
    ) -> List[Dict[str, str]]:
        """準備LLM消息"""
        messages = [
            {"role": "system", "content": prompt}
        ]
        
        # 添加最近的對話歷史
        for msg in context.history[-5:]:  # 最近5條
            role = "user" if msg.sender_id == context.customer_id else "assistant"
            messages.append({"role": role, "content": msg.content})
            
        return messages
    
    def _classify_validation_error(self, validation_result: Any) -> str:
        """分類驗證錯誤"""
        if "too long" in validation_result.message:
            return "too_long"
        elif "too short" in validation_result.message:
            return "too_short"
        elif "no readable content" in validation_result.message:
            return "no_content"
        return "unknown"
    
    async def _notify_human_agent(
        self,
        context: EnhancedConversationContext,
        escalation_result: Dict[str, Any]
    ) -> None:
        """通知人工客服"""
        # 在實際實現中，這裡會調用客服系統的API
        logger.info(f"Human agent notified for customer {context.customer_id}")
        
    async def _update_quality_metrics(self) -> None:
        """更新質量指標"""
        # 保存到緩存供監控使用
        await self.cache_manager.set(
            "conversation_quality_metrics",
            self.quality_metrics,
            ttl=3600
        )
    
    async def get_conversation_quality_report(self) -> Dict[str, Any]:
        """獲取對話質量報告"""
        return {
            "metrics": self.quality_metrics,
            "timestamp": datetime.now().isoformat(),
            "recommendations": self._generate_quality_recommendations()
        }
    
    def _generate_quality_recommendations(self) -> List[str]:
        """生成質量改進建議"""
        recommendations = []
        
        if self.quality_metrics["avg_confidence"] < 0.7:
            recommendations.append("平均置信度較低，建議優化AI模型或擴充知識庫")
            
        fallback_rate = (self.quality_metrics["fallback_triggers"] / 
                        max(1, self.quality_metrics["total_messages"]))
        if fallback_rate > 0.2:
            recommendations.append("回退率較高，建議分析常見問題並改進處理流程")
            
        escalation_rate = (self.quality_metrics["human_escalations"] / 
                          max(1, self.quality_metrics["total_messages"]))
        if escalation_rate > 0.1:
            recommendations.append("人工介入率較高，建議增強AI處理複雜查詢的能力")
            
        return recommendations

# 創建增強型對話管理器的工廠函數
def create_enhanced_conversation_manager(messaging_client: MessagingInterface) -> EnhancedConversationManager:
    """創建增強型對話管理器"""
    return EnhancedConversationManager(messaging_client)