"""
輸出驗證模塊
驗證AI生成的輸出內容，確保準確性和一致性
"""
import re
import json
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime
import logging
from dataclasses import dataclass

logger = logging.getLogger(__name__)

@dataclass
class OutputValidationResult:
    """輸出驗證結果"""
    is_valid: bool
    confidence_score: float  # 0.0 - 1.0
    issues: List[str]
    corrections: Dict[str, Any]
    requires_human_review: bool
    
class OutputValidator:
    """輸出驗證器"""
    
    def __init__(self):
        # 準確的套餐信息（作為事實來源）
        self.package_info = {
            "精選計劃": {
                "price": 1950,
                "original_price": 5880,
                "items": 110,
                "coupon": "CPN100",
                "discount": 100,
                "key_features": ["超聲波檢查", "癌症指標", "血液分析", "心肝腎功能"]
            },
            "全面計劃": {
                "price": 2950,
                "original_price": 8280,
                "items": 130,
                "coupon": "CPN300",
                "discount": 300,
                "key_features": ["超聲波+胸部X光", "心電圖", "肝炎篩查", "荷爾蒙檢測"]
            },
            "優越計劃": {
                "price": 4380,
                "original_price": None,
                "items": 158,
                "coupon": "CPN500",
                "discount": 500,
                "key_features": ["全身超聲波", "胸腹部X光", "性病篩查", "身體成分分析"]
            },
            "尊尚計劃": {
                "price": 6380,
                "original_price": None,
                "items": 172,
                "coupon": "CPN700",
                "discount": 700,
                "key_features": ["172項檢查", "多器官超聲波", "癌症標記", "VIP服務"]
            }
        }
        
        # 營業信息
        self.business_info = {
            "locations": {
                "銅鑼灣": {
                    "address": "軒尼詩道555號東角中心13樓",
                    "phone": "3614 5393"
                },
                "佐敦": {
                    "address": "彌敦道301-309號嘉賓商業大廈9樓",
                    "phone": "3614 5393"
                }
            },
            "hours": {
                "weekday": "8:00-18:00",
                "weekend": "8:00-18:00"
            },
            "booking_notice": "需提前2個工作天預約"
        }
        
        # 團體優惠規則
        self.group_discount_rules = {
            2: 0.9,  # 9折
            3: 0.67  # 約67折（買2送1）
        }
        
        # 禁止的承諾
        self.forbidden_promises = [
            "保證", "百分百", "一定", "絕對", "治癒", "根治",
            "最好", "最便宜", "獨家", "唯一", "神奇", "萬能"
        ]
        
        # 醫療相關的限制詞
        self.medical_restrictions = [
            "診斷", "治療", "處方", "用藥建議", "醫療建議",
            "確診", "病因", "預後", "治療方案"
        ]
        
    def validate_output(self, ai_output: str, context: Dict[str, Any] = None) -> OutputValidationResult:
        """
        驗證AI輸出
        
        Args:
            ai_output: AI生成的回覆
            context: 對話上下文
            
        Returns:
            OutputValidationResult: 驗證結果
        """
        if context is None:
            context = {}
            
        issues = []
        corrections = {}
        confidence_score = 1.0
        
        # 1. 價格準確性驗證
        price_result = self._validate_prices(ai_output)
        if price_result["issues"]:
            issues.extend(price_result["issues"])
            corrections.update(price_result["corrections"])
            confidence_score *= 0.7
            
        # 2. 套餐信息準確性驗證
        package_result = self._validate_package_info(ai_output)
        if package_result["issues"]:
            issues.extend(package_result["issues"])
            corrections.update(package_result["corrections"])
            confidence_score *= 0.8
            
        # 3. 營業信息準確性驗證
        business_result = self._validate_business_info(ai_output)
        if business_result["issues"]:
            issues.extend(business_result["issues"])
            corrections.update(business_result["corrections"])
            confidence_score *= 0.9
            
        # 4. 禁止承諾檢查
        promise_result = self._check_forbidden_promises(ai_output)
        if promise_result["issues"]:
            issues.extend(promise_result["issues"])
            confidence_score *= 0.5
            
        # 5. 醫療限制檢查
        medical_result = self._check_medical_restrictions(ai_output)
        if medical_result["issues"]:
            issues.extend(medical_result["issues"])
            confidence_score *= 0.6
            
        # 6. 邏輯一致性檢查
        consistency_result = self._check_consistency(ai_output, context)
        if consistency_result["issues"]:
            issues.extend(consistency_result["issues"])
            confidence_score *= 0.7
            
        # 決定是否需要人工審核
        requires_human_review = (
            confidence_score < 0.7 or
            len(issues) > 3 or
            any("critical" in issue.lower() for issue in issues)
        )
        
        return OutputValidationResult(
            is_valid=len(issues) == 0,
            confidence_score=max(0.0, confidence_score),
            issues=issues,
            corrections=corrections,
            requires_human_review=requires_human_review
        )
    
    def _validate_prices(self, text: str) -> Dict[str, Any]:
        """驗證價格信息的準確性"""
        issues = []
        corrections = {}
        
        # 查找所有價格提及
        price_pattern = r'\$?(\d{1,5}(?:,\d{3})*)'
        price_matches = re.findall(price_pattern, text)
        
        for match in price_matches:
            price_str = match.replace(',', '')
            try:
                price = int(price_str)
                
                # 檢查是否為有效的套餐價格
                valid_prices = []
                for package_name, info in self.package_info.items():
                    valid_prices.append(info["price"])
                    if info.get("original_price"):
                        valid_prices.append(info["original_price"])
                    if info.get("discount"):
                        valid_prices.append(info["discount"])
                        
                # 檢查團體優惠價格
                for base_price in [p["price"] for p in self.package_info.values()]:
                    for people, discount in self.group_discount_rules.items():
                        valid_prices.append(int(base_price * discount))
                        
                # 如果價格不在有效列表中，檢查是否接近某個有效價格
                if price not in valid_prices:
                    for valid_price in valid_prices:
                        if abs(price - valid_price) < 100:  # 容差100元
                            issues.append(f"Price ${price} might be incorrect, should be ${valid_price}")
                            corrections[f"price_{price}"] = valid_price
                            break
                            
            except ValueError:
                continue
                
        return {"issues": issues, "corrections": corrections}
    
    def _validate_package_info(self, text: str) -> Dict[str, Any]:
        """驗證套餐信息的準確性"""
        issues = []
        corrections = {}
        
        for package_name, info in self.package_info.items():
            if package_name in text:
                # 檢查項目數量
                items_pattern = rf'{package_name}.*?(\d+)\s*項'
                items_match = re.search(items_pattern, text)
                if items_match:
                    mentioned_items = int(items_match.group(1))
                    if mentioned_items != info["items"]:
                        issues.append(f"{package_name} should have {info['items']} items, not {mentioned_items}")
                        corrections[f"{package_name}_items"] = info["items"]
                
                # 檢查優惠券代碼
                if info.get("coupon"):
                    if info["coupon"] not in text and package_name in text:
                        # 不一定是錯誤，可能只是沒提及
                        logger.info(f"Coupon {info['coupon']} not mentioned for {package_name}")
                        
        return {"issues": issues, "corrections": corrections}
    
    def _validate_business_info(self, text: str) -> Dict[str, Any]:
        """驗證營業信息的準確性"""
        issues = []
        corrections = {}
        
        # 檢查地址
        for location, info in self.business_info["locations"].items():
            if location in text:
                if info["address"] not in text:
                    # 可能使用了簡化地址，進行部分匹配
                    address_parts = info["address"].split()
                    if not any(part in text for part in address_parts[:2]):
                        issues.append(f"Incorrect address for {location}")
                        corrections[f"{location}_address"] = info["address"]
        
        # 檢查營業時間
        time_pattern = r'(\d{1,2}:\d{2})\s*[-到至]\s*(\d{1,2}:\d{2})'
        time_matches = re.findall(time_pattern, text)
        
        for start, end in time_matches:
            if start != "8:00" or end not in ["18:00", "6:00"]:
                issues.append(f"Business hours should be 8:00-18:00")
                corrections["business_hours"] = "8:00-18:00"
                
        return {"issues": issues, "corrections": corrections}
    
    def _check_forbidden_promises(self, text: str) -> Dict[str, Any]:
        """檢查是否包含禁止的承諾"""
        issues = []
        
        for forbidden_word in self.forbidden_promises:
            if forbidden_word in text:
                issues.append(f"Critical: Contains forbidden promise word: {forbidden_word}")
                
        return {"issues": issues}
    
    def _check_medical_restrictions(self, text: str) -> Dict[str, Any]:
        """檢查是否包含醫療限制詞"""
        issues = []
        
        for restricted_word in self.medical_restrictions:
            if restricted_word in text:
                issues.append(f"Critical: Contains medical restriction word: {restricted_word}")
                
        return {"issues": issues}
    
    def _check_consistency(self, text: str, context: Dict[str, Any]) -> Dict[str, Any]:
        """檢查輸出的邏輯一致性"""
        issues = []
        
        # 檢查是否有矛盾的信息
        # 例如：同時提到不同的價格
        prices_mentioned = re.findall(r'\$(\d+(?:,\d{3})*)', text)
        if len(prices_mentioned) > 1:
            # 檢查是否在合理的上下文中（如原價和優惠價）
            if "原價" not in text and "優惠" not in text:
                issues.append("Multiple prices mentioned without clear context")
        
        # 檢查與之前對話的一致性
        if context.get("previous_quotes"):
            # 比較當前報價與之前的報價
            for prev_quote in context["previous_quotes"]:
                if prev_quote["package"] in text:
                    current_price_match = re.search(rf'{prev_quote["package"]}.*?\$(\d+(?:,\d{{3}})*)', text)
                    if current_price_match:
                        current_price = int(current_price_match.group(1).replace(',', ''))
                        if current_price != prev_quote["price"]:
                            issues.append(f"Price inconsistency for {prev_quote['package']}")
                            
        return {"issues": issues}
    
    def generate_safe_response(self, original_output: str, validation_result: OutputValidationResult) -> str:
        """
        根據驗證結果生成安全的回應
        
        Args:
            original_output: 原始AI輸出
            validation_result: 驗證結果
            
        Returns:
            str: 安全的回應文本
        """
        if validation_result.is_valid:
            return original_output
            
        if validation_result.requires_human_review:
            return ("抱歉，我需要確認一下信息的準確性。請稍等，我會為您轉接專業客服人員。\n"
                   "同時，您可以直接撥打我們的熱線電話：3614 5393")
        
        # 嘗試自動修正
        corrected_output = original_output
        for issue in validation_result.issues:
            if "price" in issue.lower():
                # 移除可能錯誤的價格信息
                corrected_output = re.sub(r'\$\d+(?:,\d{3})*', '[價格待確認]', corrected_output)
            elif "forbidden" in issue.lower():
                # 移除禁止的承諾
                for word in self.forbidden_promises:
                    corrected_output = corrected_output.replace(word, "")
                    
        # 添加免責聲明
        disclaimer = "\n\n*以上信息僅供參考，具體詳情請以實際為準。"
        corrected_output += disclaimer
        
        return corrected_output
    
    def get_factual_response(self, intent: str, entity: Optional[str] = None) -> Optional[str]:
        """
        獲取基於事實的標準回應
        
        Args:
            intent: 用戶意圖
            entity: 相關實體（如套餐名稱）
            
        Returns:
            Optional[str]: 標準回應文本
        """
        if intent == "price_inquiry" and entity in self.package_info:
            info = self.package_info[entity]
            return (f"{entity}的優惠價格是 ${info['price']}，"
                   f"包含{info['items']}項檢查。"
                   f"使用優惠券{info['coupon']}可再減${info['discount']}。")
        
        elif intent == "location_inquiry":
            locations = []
            for name, info in self.business_info["locations"].items():
                locations.append(f"{name}：{info['address']}")
            return f"我們的體檢中心位於：\n" + "\n".join(locations)
        
        elif intent == "hours_inquiry":
            return f"我們的營業時間是{self.business_info['hours']['weekday']}，"
                   f"需要{self.business_info['booking_notice']}。"
        
        return None

# 全局驗證器實例
output_validator = OutputValidator()