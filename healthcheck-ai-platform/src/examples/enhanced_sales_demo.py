#!/usr/bin/env python3
"""
增強型銷售系統演示
展示如何使用心理專家、銷售專家、前線銷售專家、線上下單專家等子代理
進行深度銷售分析和優化
"""

import asyncio
import json
from datetime import datetime
from typing import Dict, Any, List

# 假設已經設置好相關導入路徑
from src.agents.enhanced_sales_orchestrator import EnhancedSalesOrchestrator


class SalesConversationSimulator:
    """銷售對話模擬器"""
    
    def __init__(self):
        self.orchestrator = EnhancedSalesOrchestrator()
        
    async def simulate_customer_journey(self):
        """模擬完整的客戶旅程"""
        print("=" * 80)
        print("🚀 增強型銷售系統演示 - 深度客戶分析與轉化")
        print("=" * 80)
        
        # 場景1：謹慎型客戶初次諮詢
        print("\n📋 場景1：謹慎型客戶初次諮詢")
        print("-" * 40)
        
        cautious_customer_context = {
            "conversation_id": "CONV-001",
            "customer_id": "CUST-12345",
            "conversation_stage": "discovery",
            "current_sentiment": "skeptical",
            "customer_profile": {
                "name": "張先生",
                "age": 45,
                "occupation": "office_worker",
                "first_time_buyer": True,
                "has_family": True,
                "health_concerns": ["工作壓力大", "家族有心臟病史"],
                "budget_range": "medium"
            },
            "conversation_history": [
                {"role": "customer", "content": "你好，我想了解一下你們的體檢套餐，但是聽說價格很貴？"},
                {"role": "agent", "content": "張先生您好！很高興為您服務。我們的體檢套餐確實是物有所值的。請問您最關心哪方面的健康問題呢？"},
                {"role": "customer", "content": "主要是工作壓力大，最近總覺得心臟不太舒服，我爸爸有心臟病..."},
                {"role": "agent", "content": "我完全理解您的擔憂。心血管健康確實需要重視，特別是有家族病史的情況。"},
                {"role": "customer", "content": "是啊，但是我看了一下價格，真的有點貴，需要考慮一下..."}
            ],
            "current_message": "是啊，但是我看了一下價格，真的有點貴，需要考慮一下...",
            "session_data": {
                "time_on_site": 420,  # 7分鐘
                "pages_viewed": ["pricing", "testimonials", "comparison"],
                "cart_modifications": 2,
                "price_check_frequency": 4
            },
            "cart_contents": {
                "package_type": "standard",
                "total_amount": 5800,
                "items_count": 25
            }
        }
        
        # 執行深度分析
        analysis_result = await self.orchestrator.analyze_conversation(
            cautious_customer_context,
            analysis_depth="comprehensive"
        )
        
        self._print_analysis_results(analysis_result)
        
        # 場景2：衝動型客戶準備購買
        print("\n\n📋 場景2：衝動型客戶準備購買")
        print("-" * 40)
        
        impulsive_customer_context = {
            "conversation_id": "CONV-002",
            "customer_id": "CUST-67890",
            "conversation_stage": "closing",
            "current_sentiment": "excited",
            "customer_profile": {
                "name": "李小姐",
                "age": 28,
                "occupation": "entrepreneur",
                "first_time_buyer": False,
                "has_family": False,
                "health_concerns": ["保持健康", "預防勝於治療"],
                "budget_range": "high",
                "tech_savvy": True
            },
            "conversation_history": [
                {"role": "customer", "content": "Hi！我想做個全面的體檢，你們最好的套餐是什麼？"},
                {"role": "agent", "content": "李小姐您好！看您這麼注重健康真是太好了。我們的VIP全面體檢套餐最受成功人士歡迎！"},
                {"role": "customer", "content": "聽起來不錯！都包含什麼項目？多快能安排？"},
                {"role": "agent", "content": "包含50+項深度檢查，還有專屬健康顧問全年服務。最快本週末就能安排！"},
                {"role": "customer", "content": "Perfect! 我想要週六上午，越早越好。怎麼付款？"}
            ],
            "current_message": "Perfect! 我想要週六上午，越早越好。怎麼付款？",
            "session_data": {
                "time_on_site": 180,  # 3分鐘
                "pages_viewed": ["vip_package", "booking"],
                "cart_modifications": 0,
                "price_check_frequency": 1
            },
            "cart_contents": {
                "package_type": "vip",
                "total_amount": 12800,
                "items_count": 50,
                "add_ons": ["營養諮詢", "運動指導"]
            },
            "order_stage": "payment"
        }
        
        analysis_result2 = await self.orchestrator.analyze_conversation(
            impulsive_customer_context,
            analysis_depth="comprehensive"
        )
        
        self._print_analysis_results(analysis_result2)
        
        # 場景3：企業客戶團購諮詢
        print("\n\n📋 場景3：企業客戶團購諮詢")
        print("-" * 40)
        
        corporate_customer_context = {
            "conversation_id": "CONV-003",
            "customer_id": "CORP-11111",
            "conversation_stage": "evaluation",
            "current_sentiment": "interested",
            "customer_profile": {
                "name": "王經理",
                "age": 38,
                "occupation": "hr_manager",
                "corporate_customer": True,
                "company_size": 200,
                "health_concerns": ["員工福利", "團隊健康", "預算控制"],
                "budget_range": "corporate",
                "decision_maker": True
            },
            "conversation_history": [
                {"role": "customer", "content": "我們公司想為員工安排年度體檢，大概200人左右"},
                {"role": "agent", "content": "王經理您好！企業員工健康管理確實很重要。我們有專門的企業套餐，還可以根據需求定制。"},
                {"role": "customer", "content": "需要了解一下具體的方案和報價，還有你們能不能上門服務？"},
                {"role": "agent", "content": "當然可以！200人以上的企業我們提供上門體檢服務，還有專屬的項目經理全程協調。"},
                {"role": "customer", "content": "聽起來不錯，但是需要跟其他供應商比較一下，能給個詳細方案嗎？"}
            ],
            "current_message": "聽起來不錯，但是需要跟其他供應商比較一下，能給個詳細方案嗎？",
            "session_data": {
                "time_on_site": 600,  # 10分鐘
                "pages_viewed": ["corporate", "pricing", "comparison", "case_studies"],
                "cart_modifications": 3,
                "price_check_frequency": 8
            },
            "cart_contents": {
                "package_type": "corporate_standard",
                "total_amount": 680000,  # 200人 x 3400
                "items_count": 30,
                "quantity": 200
            }
        }
        
        analysis_result3 = await self.orchestrator.analyze_conversation(
            corporate_customer_context,
            analysis_depth="comprehensive"
        )
        
        self._print_analysis_results(analysis_result3)
        
        # 獲取整體性能報告
        print("\n\n📊 系統性能報告")
        print("=" * 80)
        
        performance_report = await self.orchestrator.get_performance_report()
        self._print_performance_report(performance_report)
    
    def _print_analysis_results(self, analysis: Dict[str, Any]):
        """打印分析結果"""
        print(f"\n🔍 分析ID: {analysis['analysis_id']}")
        print(f"⏰ 時間: {analysis['timestamp']}")
        
        # 對話摘要
        summary = analysis['conversation_summary']
        print(f"\n📝 對話摘要:")
        print(f"  - 階段: {summary['stage']}")
        print(f"  - 長度: {summary['duration']}")
        print(f"  - 情緒: {summary['customer_sentiment']}")
        print(f"  - 意圖: {summary['current_intent']}")
        
        # 多代理洞察
        insights = analysis['multi_agent_insights']
        
        print(f"\n🧠 心理分析:")
        psych = insights['psychology']
        print(f"  - 決策風格: {psych['decision_style']['style']} (信心度: {psych['decision_style']['confidence']:.2f})")
        print(f"  - 情緒狀態: {psych['emotional_state']['primary_emotion']} (強度: {psych['emotional_state']['intensity']:.2f})")
        print(f"  - 心理準備度: {psych['readiness_score']:.0f}/100")
        print(f"  - 主要障礙: {psych['barriers'][0]['type'] if psych['barriers'] else '無'}")
        
        print(f"\n💼 銷售策略:")
        sales = insights['sales_strategy']
        print(f"  - 推薦框架: {sales['framework']['framework']} (信心度: {sales['framework']['confidence']:.2f})")
        print(f"  - 成功概率: {sales['success_probability']:.2%}")
        print(f"  - 關鍵機會: {sales['opportunities'][0]['type'] if sales['opportunities'] else '無'}")
        
        print(f"\n🎯 前線執行:")
        frontline = insights['frontline_execution']
        print(f"  - 對話流程: {frontline['flow']['flow_type']}")
        print(f"  - 語氣建議: {frontline['tone']}")
        print(f"  - 成交準備: {frontline['closing_readiness']['readiness_score']:.2f}")
        
        print(f"\n🛒 訂單優化:")
        order = insights['order_optimization']
        print(f"  - 推薦流程: {order['workflow']['selected_workflow']}")
        print(f"  - 放棄風險: {order['abandonment_risk']['risk_level']} ({order['abandonment_risk']['risk_score']:.2f})")
        
        # 綜合分析
        synthesis = analysis['synthesis']
        print(f"\n📊 綜合分析:")
        print(f"  - 整體成功率: {synthesis['overall_success_probability']:.2%}")
        print(f"  - 客戶類型: {synthesis['customer_archetype']['name']}")
        print(f"  - 主要風險: {synthesis['identified_risks'][0]['description'] if synthesis['identified_risks'] else '無'}")
        print(f"  - 代理共識: {synthesis['agent_consensus']['level']}")
        
        # 行動計劃
        action_plan = analysis['action_plan']
        print(f"\n🎬 行動計劃:")
        print(f"  立即行動 (0-15分鐘):")
        for action in action_plan['immediate_actions'][:2]:
            print(f"    - {action['description']}")
        
        print(f"  短期行動 (1-7天):")
        for action in action_plan['short_term_actions'][:2]:
            print(f"    - Day {action['day']}: {action['action']} ({action['channel']})")
    
    def _print_performance_report(self, report: Dict[str, Any]):
        """打印性能報告"""
        metrics = report['overall_metrics']
        print(f"\n📈 整體指標:")
        print(f"  - 總分析次數: {metrics['total_analyses']}")
        print(f"  - 平均準備度: {metrics['average_readiness_score']:.1f}/100")
        print(f"  - 生成洞察數: {metrics['insights_generated']}")
        
        print(f"\n🤖 代理性能:")
        for agent, performance in report['agent_performance'].items():
            print(f"  {agent}:")
            print(f"    - 使用率: {performance['usage_rate']:.2%}")
            print(f"    - 貢獻度: {performance['contribution_score']:.2f}")
        
        trends = report['success_trends']
        print(f"\n📊 成功趨勢:")
        print(f"  - 趨勢方向: {trends['trend']}")
        print(f"  - 平均成功率: {trends.get('average_success_rate', 0):.2%}")
        
        print(f"\n💡 系統建議:")
        for rec in report['recommendations']:
            print(f"  - {rec}")


async def main():
    """主函數"""
    simulator = SalesConversationSimulator()
    await simulator.simulate_customer_journey()


if __name__ == "__main__":
    print("🚀 啟動增強型銷售系統演示...")
    asyncio.run(main())