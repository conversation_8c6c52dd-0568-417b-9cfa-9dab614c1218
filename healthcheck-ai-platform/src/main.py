from fastapi import FastAPI, Request, BackgroundTasks
from fastapi.responses import JSONResponse
import uvicorn
import os
from dotenv import load_dotenv
from src.core.conversation_manager import ConversationManager
from src.integrations.mock_whatsapp import MockWhatsAppClient
from src.integrations.messaging_interface import Message

# 加載環境變量
load_dotenv()

# 創建 FastAPI 應用
app = FastAPI(
    title="HealthCheck AI Sales Platform",
    description="智能體檢銷售與服務系統",
    version="1.0.0"
)

# 初始化組件
messaging_client = MockWhatsAppClient()
conversation_manager = ConversationManager(messaging_client)

@app.get("/")
async def root():
    """健康檢查端點"""
    return {
        "status": "healthy",
        "service": "HealthCheck AI Platform",
        "version": "1.0.0"
    }

@app.post("/webhook/whatsapp")
async def whatsapp_webhook(request: Request, background_tasks: BackgroundTasks):
    """WhatsApp Webhook 端點"""
    data = await request.json()
    
    # 模擬消息處理
    # 實際應該解析 WhatsApp 的 webhook 格式
    if "message" in data:
        message = Message(
            content=data["message"]["text"],
            sender_id=data["message"]["from"],
            recipient_id=data["message"]["to"]
        )
        
        # 異步處理消息
        background_tasks.add_task(
            conversation_manager.handle_message,
            message
        )
    
    return JSONResponse({"status": "ok"})

@app.post("/test/send_message")
async def test_send_message(request: Request):
    """測試端點：模擬用戶發送消息"""
    data = await request.json()
    
    # 模擬用戶消息
    messaging_client.simulate_user_message(
        sender_id=data.get("sender_id", "test_user"),
        content=data.get("content", "我想了解體檢套餐")
    )
    
    # 處理消息
    message = await messaging_client.receive_message()
    if message:
        await conversation_manager.handle_message(message)
    
    # 返回對話歷史
    return {
        "conversation": messaging_client.conversations.get(
            data.get("sender_id", "test_user"),
            []
        )
    }

@app.get("/conversations/{customer_id}")
async def get_conversation(customer_id: str):
    """獲取對話歷史"""
    return {
        "customer_id": customer_id,
        "messages": messaging_client.conversations.get(customer_id, [])
    }

if __name__ == "__main__":
    uvicorn.run(
        "src.main:app",
        host="0.0.0.0",
        port=8000,
        reload=True
    )