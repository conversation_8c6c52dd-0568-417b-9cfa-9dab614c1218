from fastapi import FastAPI, Request, BackgroundTasks, Depends
from fastapi.responses import JSONResponse
import uvicorn
import os
from dotenv import load_dotenv
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker, Session
from src.core.conversation_manager import ConversationManager
from src.integrations.mcp_whatsapp_client import MCP<PERSON>hatsAppClient
from src.integrations.messaging_interface import Message
from src.core.anti_bot_service import AntiBotService, HumanizationService
from src.database.models import Base
import logging
from datetime import datetime
import asyncio

# 配置日誌
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 加載環境變量
load_dotenv()

# 數據庫配置
DATABASE_URL = os.getenv("DATABASE_URL", "mysql+pymysql://root:password@localhost/healthcheck_db")
engine = create_engine(DATABASE_URL)
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

# 創建數據庫表
Base.metadata.create_all(bind=engine)

# 創建 FastAPI 應用
app = FastAPI(
    title="HealthCheck AI Sales Platform with MCP",
    description="整合 MCP 的智能體檢銷售與服務系統",
    version="2.0.0"
)

# 依賴注入
def get_db():
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()

# 初始化服務
humanization_service = HumanizationService()

@app.on_event("startup")
async def startup_event():
    """應用啟動時的初始化"""
    logger.info("HealthCheck AI Platform with MCP 正在啟動...")
    
    # 運行數據庫遷移
    try:
        from src.database.add_anti_bot_columns import add_anti_bot_columns
        add_anti_bot_columns(DATABASE_URL)
        logger.info("數據庫遷移完成")
    except Exception as e:
        logger.error(f"數據庫遷移失敗: {e}")

@app.get("/")
async def root():
    """健康檢查端點"""
    return {
        "status": "healthy",
        "service": "HealthCheck AI Platform with MCP",
        "version": "2.0.0",
        "features": ["MCP Integration", "Anti-Bot Detection", "Human-like Behavior"]
    }

@app.post("/webhook/mcp")
async def mcp_webhook(request: Request, background_tasks: BackgroundTasks, db: Session = Depends(get_db)):
    """MCP Webhook 端點"""
    data = await request.json()
    
    try:
        # 初始化服務
        messaging_client = MCPWhatsAppClient(db)
        anti_bot_service = AntiBotService(db)
        conversation_manager = ConversationManager(messaging_client)
        
        # 解析 MCP 消息格式
        if "message" in data:
            message = Message(
                content=data["message"].get("text", ""),
                sender_id=data["message"].get("from", ""),
                recipient_id=data["message"].get("to", ""),
                metadata={
                    "timestamp": datetime.utcnow().isoformat(),
                    "mcp_message_id": data.get("id"),
                    "chat_jid": data.get("chat_jid")
                }
            )
            
            # 異步處理消息
            background_tasks.add_task(
                process_message_with_anti_bot,
                message,
                messaging_client,
                conversation_manager,
                anti_bot_service,
                db
            )
        
        return JSONResponse({"status": "ok", "message": "Message received"})
    
    except Exception as e:
        logger.error(f"處理 MCP webhook 時出錯: {e}")
        return JSONResponse({"status": "error", "message": str(e)}, status_code=500)

async def process_message_with_anti_bot(
    message: Message,
    messaging_client: MCPWhatsAppClient,
    conversation_manager: ConversationManager,
    anti_bot_service: AntiBotService,
    db: Session
):
    """處理消息並應用防機器人偵測"""
    try:
        # 處理接收到的消息
        await conversation_manager.handle_message(message)
        
        # 獲取對話ID（這裡需要實際的邏輯來獲取）
        # 簡化示例
        conversation_id = "current_conversation_id"
        
        # 分析對話以檢測機器人行為
        analysis_result = await anti_bot_service.analyze_conversation(conversation_id)
        
        if analysis_result["risk_score"] > 0.5:
            logger.warning(f"檢測到可疑行為，風險分數: {analysis_result['risk_score']}")
            
            # 應用建議的改進
            for recommendation in analysis_result["recommendations"]:
                logger.info(f"應用建議: {recommendation}")
        
    except Exception as e:
        logger.error(f"處理消息時出錯: {e}")

@app.post("/api/send_message")
async def send_message(request: Request, db: Session = Depends(get_db)):
    """發送消息 API"""
    data = await request.json()
    
    try:
        messaging_client = MCPWhatsAppClient(db)
        
        recipient_id = data.get("recipient_id")
        content = data.get("content")
        
        if not recipient_id or not content:
            return JSONResponse(
                {"status": "error", "message": "Missing recipient_id or content"},
                status_code=400
            )
        
        # 人性化處理消息
        humanized_content = await humanization_service.humanize_message(content)
        
        # 發送消息
        message = await messaging_client.send_message(recipient_id, humanized_content)
        
        return {
            "status": "success",
            "message": "Message sent",
            "message_id": message.metadata.get("message_id"),
            "humanized": humanized_content != content
        }
    
    except Exception as e:
        logger.error(f"發送消息時出錯: {e}")
        return JSONResponse(
            {"status": "error", "message": str(e)},
            status_code=500
        )

@app.get("/api/conversations/{customer_phone}")
async def get_conversation_history(customer_phone: str, db: Session = Depends(get_db)):
    """獲取對話歷史"""
    try:
        from src.database.models import Customer, Conversation, Message
        
        # 查找客戶
        customer = db.query(Customer).filter_by(phone_number=customer_phone).first()
        if not customer:
            return JSONResponse(
                {"status": "error", "message": "Customer not found"},
                status_code=404
            )
        
        # 獲取最近的對話
        conversations = db.query(Conversation).filter_by(
            customer_id=customer.id
        ).order_by(Conversation.started_at.desc()).limit(5).all()
        
        result = []
        for conv in conversations:
            messages = db.query(Message).filter_by(
                conversation_id=conv.id
            ).order_by(Message.timestamp).all()
            
            result.append({
                "conversation_id": conv.id,
                "started_at": conv.started_at.isoformat(),
                "status": conv.status.value,
                "behavior_score": conv.behavior_score,
                "messages": [
                    {
                        "content": msg.content,
                        "type": msg.message_type.value,
                        "timestamp": msg.timestamp.isoformat()
                    } for msg in messages
                ]
            })
        
        return {
            "status": "success",
            "customer": {
                "phone": customer.phone_number,
                "name": customer.name,
                "segment": customer.segment.value if customer.segment else None
            },
            "conversations": result
        }
    
    except Exception as e:
        logger.error(f"獲取對話歷史時出錯: {e}")
        return JSONResponse(
            {"status": "error", "message": str(e)},
            status_code=500
        )

@app.get("/api/anti_bot/analysis/{conversation_id}")
async def get_anti_bot_analysis(conversation_id: str, db: Session = Depends(get_db)):
    """獲取防機器人分析結果"""
    try:
        anti_bot_service = AntiBotService(db)
        analysis_result = await anti_bot_service.analyze_conversation(conversation_id)
        
        return {
            "status": "success",
            "analysis": analysis_result
        }
    
    except Exception as e:
        logger.error(f"獲取防機器人分析時出錯: {e}")
        return JSONResponse(
            {"status": "error", "message": str(e)},
            status_code=500
        )

@app.post("/api/test/simulate_conversation")
async def simulate_conversation(request: Request, db: Session = Depends(get_db)):
    """模擬對話測試端點"""
    data = await request.json()
    
    try:
        messaging_client = MCPWhatsAppClient(db)
        anti_bot_service = AntiBotService(db)
        
        sender_id = data.get("sender_id", "test_user")
        messages = data.get("messages", ["我想了解體檢套餐"])
        
        results = []
        
        for msg_content in messages:
            # 模擬接收用戶消息
            user_message = Message(
                content=msg_content,
                sender_id=sender_id,
                recipient_id="business"
            )
            
            # 保存用戶消息
            await messaging_client._save_message_to_db(user_message, MessageType.CUSTOMER)
            
            # 生成回應
            response_content = f"感謝您的諮詢。關於「{msg_content[:20]}...」，我們有多種選擇。"
            humanized_response = await humanization_service.humanize_message(response_content)
            
            # 發送回應
            response = await messaging_client.send_message(sender_id, humanized_response)
            
            results.append({
                "user": msg_content,
                "bot": humanized_response,
                "humanized": humanized_response != response_content
            })
            
            # 等待一下模擬真實對話節奏
            await asyncio.sleep(2)
        
        # 分析對話
        # 這裡需要實際的對話ID
        # analysis = await anti_bot_service.analyze_conversation("conversation_id")
        
        return {
            "status": "success",
            "conversation": results,
            "recommendations": [
                "保持當前的對話節奏",
                "繼續使用人性化表達"
            ]
        }
    
    except Exception as e:
        logger.error(f"模擬對話時出錯: {e}")
        return JSONResponse(
            {"status": "error", "message": str(e)},
            status_code=500
        )

if __name__ == "__main__":
    uvicorn.run(
        "src.main_mcp:app",
        host="0.0.0.0",
        port=8001,
        reload=True
    )