from abc import ABC, abstractmethod
from typing import Dict, Any, Optional
from datetime import datetime
import uuid

class Message:
    """統一的消息格式"""
    def __init__(
        self,
        content: str,
        sender_id: str,
        recipient_id: str,
        message_id: Optional[str] = None,
        timestamp: Optional[datetime] = None,
        metadata: Optional[Dict[str, Any]] = None
    ):
        self.message_id = message_id or str(uuid.uuid4())
        self.content = content
        self.sender_id = sender_id
        self.recipient_id = recipient_id
        self.timestamp = timestamp or datetime.now()
        self.metadata = metadata or {}

class MessagingInterface(ABC):
    """消息接口抽象類"""
    
    @abstractmethod
    async def send_message(self, recipient_id: str, content: str, **kwargs) -> Message:
        pass
    
    @abstractmethod
    async def receive_message(self) -> Optional[Message]:
        pass
    
    @abstractmethod
    async def send_media(self, recipient_id: str, media_url: str, media_type: str, caption: str = "") -> Message:
        pass