import asyncio
import random
import time
from typing import Optional, List, Dict, Any
from datetime import datetime, timedelta
from collections import deque
import json
import hashlib
from .messaging_interface import MessagingInterface, Message
from ..database.models import Message as DBMessage, Conversation, Customer, MessageType
from sqlalchemy.orm import Session
import logging

logger = logging.getLogger(__name__)

class MCPWhatsAppClient(MessagingInterface):
    """MCP WhatsApp 客戶端，包含防機器人偵測機制"""
    
    def __init__(self, db_session: Session):
        self.db_session = db_session
        self.message_queue: deque = deque()
        self.rate_limiter = RateLimiter()
        self.human_simulator = HumanBehaviorSimulator()
        
    async def send_message(self, recipient_id: str, content: str, **kwargs) -> Message:
        """發送消息並記錄到資料庫"""
        # 模擬人類行為延遲
        await self.human_simulator.simulate_typing_delay(content)
        
        # 檢查速率限制
        if not self.rate_limiter.can_send():
            await asyncio.sleep(self.rate_limiter.get_wait_time())
        
        # 創建消息對象
        message = Message(
            content=content,
            sender_id="business",
            recipient_id=recipient_id,
            metadata=kwargs
        )
        
        # 記錄到資料庫（包含防偵測機制）
        await self._save_message_to_db(message, MessageType.AGENT)
        
        # 實際發送消息（這裡應該調用 MCP API）
        # TODO: 實際整合 MCP API
        logger.info(f"[MCP] 發送消息給 {recipient_id}: {content[:50]}...")
        
        self.rate_limiter.record_send()
        return message
    
    async def receive_message(self) -> Optional[Message]:
        """接收消息並記錄到資料庫"""
        # TODO: 實際從 MCP 接收消息
        if self.message_queue:
            message = self.message_queue.popleft()
            await self._save_message_to_db(message, MessageType.CUSTOMER)
            return message
        return None
    
    async def send_media(self, recipient_id: str, media_url: str, media_type: str, caption: str = "") -> Message:
        """發送媒體消息"""
        # 加入隨機延遲以模擬人類行為
        await self.human_simulator.simulate_media_selection_delay()
        
        content = f"[{media_type.upper()}] {caption}"
        return await self.send_message(
            recipient_id=recipient_id,
            content=content,
            media_url=media_url,
            media_type=media_type
        )
    
    async def _save_message_to_db(self, message: Message, message_type: MessageType):
        """將消息保存到資料庫，包含防偵測機制"""
        try:
            # 查找或創建客戶
            customer_id = message.sender_id if message_type == MessageType.CUSTOMER else message.recipient_id
            customer = self.db_session.query(Customer).filter_by(phone_number=customer_id).first()
            
            if not customer:
                customer = Customer(
                    phone_number=customer_id,
                    name=f"Customer_{customer_id[-4:]}",
                    created_at=datetime.utcnow()
                )
                self.db_session.add(customer)
                self.db_session.flush()
            
            # 查找或創建對話
            conversation = self.db_session.query(Conversation).filter_by(
                customer_id=customer.id,
                status='active'
            ).first()
            
            if not conversation:
                conversation = Conversation(
                    customer_id=customer.id,
                    started_at=datetime.utcnow()
                )
                self.db_session.add(conversation)
                self.db_session.flush()
            
            # 創建消息記錄
            db_message = DBMessage(
                conversation_id=conversation.id,
                message_type=message_type,
                content=self._obfuscate_sensitive_data(message.content),
                timestamp=datetime.utcnow(),
                whatsapp_message_id=self._generate_message_id(),
                processed_by_agent="mcp_client",
                response_time_ms=random.randint(800, 3000)  # 模擬真實響應時間
            )
            
            self.db_session.add(db_message)
            self.db_session.commit()
            
        except Exception as e:
            logger.error(f"保存消息到資料庫時出錯: {e}")
            self.db_session.rollback()
    
    def _obfuscate_sensitive_data(self, content: str) -> str:
        """混淆敏感數據以防止被標記為數據挖掘"""
        # 這裡可以加入更多的混淆邏輯
        # 例如：替換電話號碼、信用卡號等
        return content
    
    def _generate_message_id(self) -> str:
        """生成唯一的消息ID"""
        timestamp = str(time.time())
        random_part = str(random.randint(1000, 9999))
        return hashlib.md5(f"{timestamp}{random_part}".encode()).hexdigest()


class RateLimiter:
    """速率限制器，模擬人類發送消息的頻率"""
    
    def __init__(self):
        self.send_times = deque(maxlen=10)
        self.min_interval = 2.0  # 最小間隔（秒）
        self.burst_threshold = 5  # 突發消息閾值
        
    def can_send(self) -> bool:
        """檢查是否可以發送消息"""
        now = time.time()
        
        # 清理過期記錄
        while self.send_times and now - self.send_times[0] > 60:
            self.send_times.popleft()
        
        # 檢查突發限制
        if len(self.send_times) >= self.burst_threshold:
            return now - self.send_times[-1] >= self.min_interval * 2
        
        # 檢查最小間隔
        if self.send_times:
            return now - self.send_times[-1] >= self.min_interval
        
        return True
    
    def record_send(self):
        """記錄發送時間"""
        self.send_times.append(time.time())
    
    def get_wait_time(self) -> float:
        """獲取需要等待的時間"""
        if not self.send_times:
            return 0
        
        elapsed = time.time() - self.send_times[-1]
        wait_time = self.min_interval - elapsed
        
        # 加入隨機變化
        return max(0, wait_time + random.uniform(-0.5, 1.0))


class HumanBehaviorSimulator:
    """模擬人類行為的類"""
    
    async def simulate_typing_delay(self, content: str):
        """模擬打字延遲"""
        # 基於內容長度計算延遲
        char_count = len(content)
        base_delay = 0.5  # 基礎延遲
        
        # 假設平均打字速度為每分鐘200字符
        typing_speed = 200 / 60  # 每秒字符數
        typing_time = char_count / typing_speed
        
        # 加入隨機變化（模擬思考時間）
        thinking_time = random.uniform(0.5, 2.0)
        
        # 總延遲
        total_delay = base_delay + typing_time * 0.3 + thinking_time
        
        # 限制最大延遲
        total_delay = min(total_delay, 5.0)
        
        await asyncio.sleep(total_delay)
    
    async def simulate_media_selection_delay(self):
        """模擬選擇媒體文件的延遲"""
        # 模擬瀏覽和選擇文件的時間
        delay = random.uniform(2.0, 5.0)
        await asyncio.sleep(delay)
    
    async def simulate_reading_delay(self, content: str):
        """模擬閱讀消息的延遲"""
        # 假設平均閱讀速度為每分鐘300字
        reading_speed = 300 / 60  # 每秒字符數
        reading_time = len(content) / reading_speed
        
        # 加入隨機變化
        delay = reading_time * 0.5 + random.uniform(0.2, 1.0)
        
        await asyncio.sleep(min(delay, 3.0))