from typing import Dict, Any, List, Optional, Union
from abc import ABC, abstractmethod
import os
import asyncio
import logging
from datetime import datetime
import json

# Optional imports with fallbacks
try:
    import openai
    OPENAI_AVAILABLE = True
except ImportError:
    OPENAI_AVAILABLE = False
    
try:
    import anthropic
    ANTHROPIC_AVAILABLE = True
except ImportError:
    ANTHROPIC_AVAILABLE = False

logger = logging.getLogger(__name__)

class LLMResponse:
    """Standardized LLM response"""
    def __init__(self, content: str, model: str, usage: Dict[str, int], metadata: Dict[str, Any] = None):
        self.content = content
        self.model = model
        self.usage = usage
        self.metadata = metadata or {}
        self.timestamp = datetime.now()

class BaseLLMClient(ABC):
    """Base class for LLM clients"""
    
    @abstractmethod
    async def generate(self, prompt: str, **kwargs) -> LLMResponse:
        """Generate response from prompt"""
        pass
    
    @abstractmethod
    async def chat(self, messages: List[Dict[str, str]], **kwargs) -> LLMResponse:
        """Chat completion with message history"""
        pass

class OpenAIClient(BaseLLMClient):
    """OpenAI API client"""
    
    def __init__(self, api_key: Optional[str] = None, model: str = "gpt-4-turbo-preview"):
        if not OPENAI_AVAILABLE:
            raise ImportError("OpenAI package not installed. Install with: pip install openai")
            
        self.api_key = api_key or os.getenv("OPENAI_API_KEY")
        if not self.api_key:
            raise ValueError("OpenAI API key not provided")
            
        self.client = openai.AsyncOpenAI(api_key=self.api_key)
        self.model = model
        
    async def generate(self, prompt: str, **kwargs) -> LLMResponse:
        """Generate response using completion API"""
        messages = [{"role": "user", "content": prompt}]
        return await self.chat(messages, **kwargs)
    
    async def chat(self, messages: List[Dict[str, str]], **kwargs) -> LLMResponse:
        """Chat completion"""
        try:
            # Merge default parameters with kwargs
            params = {
                "model": kwargs.get("model", self.model),
                "messages": messages,
                "temperature": kwargs.get("temperature", 0.7),
                "max_tokens": kwargs.get("max_tokens", 1000),
                "top_p": kwargs.get("top_p", 1.0),
                "frequency_penalty": kwargs.get("frequency_penalty", 0.0),
                "presence_penalty": kwargs.get("presence_penalty", 0.0),
            }
            
            # Add optional parameters if provided
            if "stop" in kwargs:
                params["stop"] = kwargs["stop"]
            if "n" in kwargs:
                params["n"] = kwargs["n"]
                
            response = await self.client.chat.completions.create(**params)
            
            return LLMResponse(
                content=response.choices[0].message.content,
                model=response.model,
                usage={
                    "prompt_tokens": response.usage.prompt_tokens,
                    "completion_tokens": response.usage.completion_tokens,
                    "total_tokens": response.usage.total_tokens
                },
                metadata={
                    "finish_reason": response.choices[0].finish_reason,
                    "response_id": response.id
                }
            )
            
        except Exception as e:
            logger.error(f"OpenAI API error: {e}")
            raise

class AnthropicClient(BaseLLMClient):
    """Anthropic Claude API client"""
    
    def __init__(self, api_key: Optional[str] = None, model: str = "claude-3-sonnet-20240229"):
        if not ANTHROPIC_AVAILABLE:
            raise ImportError("Anthropic package not installed. Install with: pip install anthropic")
            
        self.api_key = api_key or os.getenv("ANTHROPIC_API_KEY")
        if not self.api_key:
            raise ValueError("Anthropic API key not provided")
            
        self.client = anthropic.AsyncAnthropic(api_key=self.api_key)
        self.model = model
        
    async def generate(self, prompt: str, **kwargs) -> LLMResponse:
        """Generate response"""
        messages = [{"role": "user", "content": prompt}]
        return await self.chat(messages, **kwargs)
    
    async def chat(self, messages: List[Dict[str, str]], **kwargs) -> LLMResponse:
        """Chat completion"""
        try:
            # Convert OpenAI format to Anthropic format if needed
            anthropic_messages = []
            for msg in messages:
                if msg["role"] == "system":
                    # Anthropic doesn't have system role, prepend to first user message
                    continue
                anthropic_messages.append({
                    "role": msg["role"] if msg["role"] in ["user", "assistant"] else "user",
                    "content": msg["content"]
                })
            
            # Add system message to first user message if exists
            system_msg = next((msg["content"] for msg in messages if msg["role"] == "system"), None)
            if system_msg and anthropic_messages:
                anthropic_messages[0]["content"] = f"{system_msg}\n\n{anthropic_messages[0]['content']}"
            
            response = await self.client.messages.create(
                model=kwargs.get("model", self.model),
                messages=anthropic_messages,
                max_tokens=kwargs.get("max_tokens", 1000),
                temperature=kwargs.get("temperature", 0.7),
                top_p=kwargs.get("top_p", 1.0),
            )
            
            # Calculate token usage (Anthropic provides this differently)
            usage = {
                "prompt_tokens": response.usage.input_tokens,
                "completion_tokens": response.usage.output_tokens,
                "total_tokens": response.usage.input_tokens + response.usage.output_tokens
            }
            
            return LLMResponse(
                content=response.content[0].text,
                model=response.model,
                usage=usage,
                metadata={
                    "stop_reason": response.stop_reason,
                    "response_id": response.id
                }
            )
            
        except Exception as e:
            logger.error(f"Anthropic API error: {e}")
            raise

class MockLLMClient(BaseLLMClient):
    """Mock LLM client for testing"""
    
    def __init__(self, model: str = "mock-model"):
        self.model = model
        self.response_templates = {
            "greeting": "您好！歡迎來到森仁健康檢查中心。我可以為您介紹我們的體檢套餐，請問您最關心哪方面的健康檢查呢？",
            "price_inquiry": "我們的{package}套餐原價${original_price}，現在有優惠只需${final_price}。這個套餐包含{items}項檢查，性價比很高！",
            "appointment": "好的，我可以為您安排預約。請問您方便週末還是平日來檢查呢？我們的營業時間是早上8點到晚上6點。",
            "objection_handling": "我完全理解您的考慮。其實定期體檢是對健康最好的投資，及早發現問題可以節省更多醫療費用。而且我們現在的優惠真的很難得！",
            "closing": "太好了！那我現在就幫您確認預約。請問您的姓名和聯繫電話是？",
            "follow_up": "您好！之前您諮詢過我們的體檢套餐，不知道您考慮得怎麼樣了？現在預約還能享受優惠價格哦！"
        }
        
    async def generate(self, prompt: str, **kwargs) -> LLMResponse:
        """Generate mock response"""
        await asyncio.sleep(0.1)  # Simulate API latency
        
        # Simple pattern matching for response
        response = self._generate_mock_response(prompt)
        
        return LLMResponse(
            content=response,
            model=self.model,
            usage={
                "prompt_tokens": len(prompt.split()),
                "completion_tokens": len(response.split()),
                "total_tokens": len(prompt.split()) + len(response.split())
            },
            metadata={
                "mock": True,
                "template_used": self._detect_template(prompt)
            }
        )
    
    async def chat(self, messages: List[Dict[str, str]], **kwargs) -> LLMResponse:
        """Chat completion"""
        # Use the last user message for response generation
        last_user_msg = next((msg["content"] for msg in reversed(messages) if msg["role"] == "user"), "")
        return await self.generate(last_user_msg, **kwargs)
    
    def _generate_mock_response(self, prompt: str) -> str:
        """Generate appropriate mock response based on prompt"""
        prompt_lower = prompt.lower()
        
        if any(word in prompt_lower for word in ["價格", "多少錢", "費用", "price"]):
            return self.response_templates["price_inquiry"].format(
                package="全面健康檢查",
                original_price="8000",
                final_price="5800",
                items="35"
            )
        elif any(word in prompt_lower for word in ["預約", "時間", "什麼時候", "appointment"]):
            return self.response_templates["appointment"]
        elif any(word in prompt_lower for word in ["貴", "考慮", "不確定"]):
            return self.response_templates["objection_handling"]
        elif any(word in prompt_lower for word in ["好的", "可以", "確認"]):
            return self.response_templates["closing"]
        elif any(word in prompt_lower for word in ["跟進", "之前", "上次"]):
            return self.response_templates["follow_up"]
        else:
            return self.response_templates["greeting"]
    
    def _detect_template(self, prompt: str) -> str:
        """Detect which template was used"""
        prompt_lower = prompt.lower()
        for key in self.response_templates:
            if key in prompt_lower:
                return key
        return "greeting"

class LLMManager:
    """Manager for multiple LLM providers with fallback support"""
    
    def __init__(self, primary_provider: str = "openai", fallback_provider: str = "mock"):
        self.primary_provider = primary_provider
        self.fallback_provider = fallback_provider
        self.clients: Dict[str, BaseLLMClient] = {}
        
        # Initialize available clients
        self._init_clients()
        
    def _init_clients(self):
        """Initialize available LLM clients"""
        # Always add mock client
        self.clients["mock"] = MockLLMClient()
        
        # Try to add OpenAI
        if OPENAI_AVAILABLE and os.getenv("OPENAI_API_KEY"):
            try:
                self.clients["openai"] = OpenAIClient()
                logger.info("OpenAI client initialized")
            except Exception as e:
                logger.warning(f"Failed to initialize OpenAI client: {e}")
        
        # Try to add Anthropic
        if ANTHROPIC_AVAILABLE and os.getenv("ANTHROPIC_API_KEY"):
            try:
                self.clients["anthropic"] = AnthropicClient()
                logger.info("Anthropic client initialized")
            except Exception as e:
                logger.warning(f"Failed to initialize Anthropic client: {e}")
    
    async def generate(self, prompt: str, **kwargs) -> LLMResponse:
        """Generate response with fallback support"""
        # Try primary provider
        if self.primary_provider in self.clients:
            try:
                return await self.clients[self.primary_provider].generate(prompt, **kwargs)
            except Exception as e:
                logger.error(f"Primary provider {self.primary_provider} failed: {e}")
        
        # Try fallback provider
        if self.fallback_provider in self.clients:
            try:
                logger.info(f"Using fallback provider: {self.fallback_provider}")
                return await self.clients[self.fallback_provider].generate(prompt, **kwargs)
            except Exception as e:
                logger.error(f"Fallback provider {self.fallback_provider} failed: {e}")
        
        # Last resort: use mock
        if "mock" in self.clients:
            logger.warning("Using mock LLM client as last resort")
            return await self.clients["mock"].generate(prompt, **kwargs)
        
        raise RuntimeError("No LLM providers available")
    
    async def chat(self, messages: List[Dict[str, str]], **kwargs) -> LLMResponse:
        """Chat completion with fallback support"""
        # Try primary provider
        if self.primary_provider in self.clients:
            try:
                return await self.clients[self.primary_provider].chat(messages, **kwargs)
            except Exception as e:
                logger.error(f"Primary provider {self.primary_provider} failed: {e}")
        
        # Try fallback provider
        if self.fallback_provider in self.clients:
            try:
                logger.info(f"Using fallback provider: {self.fallback_provider}")
                return await self.clients[self.fallback_provider].chat(messages, **kwargs)
            except Exception as e:
                logger.error(f"Fallback provider {self.fallback_provider} failed: {e}")
        
        # Last resort: use mock
        if "mock" in self.clients:
            logger.warning("Using mock LLM client as last resort")
            return await self.clients["mock"].chat(messages, **kwargs)
        
        raise RuntimeError("No LLM providers available")
    
    def get_client(self, provider: Optional[str] = None) -> BaseLLMClient:
        """Get specific LLM client"""
        provider = provider or self.primary_provider
        if provider not in self.clients:
            raise ValueError(f"Provider {provider} not available")
        return self.clients[provider]
    
    @property
    def available_providers(self) -> List[str]:
        """Get list of available providers"""
        return list(self.clients.keys())

# Global LLM manager instance
llm_manager = LLMManager(
    primary_provider=os.getenv("LLM_PRIMARY_PROVIDER", "openai"),
    fallback_provider=os.getenv("LLM_FALLBACK_PROVIDER", "mock")
)