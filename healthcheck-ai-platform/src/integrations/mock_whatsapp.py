import asyncio
from typing import Optional, List, Dict, Any
from collections import deque
from .messaging_interface import MessagingInterface, Message

class MockWhatsAppClient(MessagingInterface):
    """模擬 WhatsApp 客戶端，用於開發和測試"""
    
    def __init__(self):
        self.incoming_queue: deque = deque()
        self.outgoing_queue: deque = deque()
        self.conversations: Dict[str, List[Message]] = {}
        
    async def send_message(self, recipient_id: str, content: str, **kwargs) -> Message:
        """發送消息"""
        message = Message(
            content=content,
            sender_id="business",
            recipient_id=recipient_id,
            metadata=kwargs
        )
        
        # 保存到對話歷史
        if recipient_id not in self.conversations:
            self.conversations[recipient_id] = []
        self.conversations[recipient_id].append(message)
        
        # 加入發送隊列
        self.outgoing_queue.append(message)
        
        print(f"[Mock WhatsApp] 發送消息給 {recipient_id}: {content[:50]}...")
        return message
    
    async def receive_message(self) -> Optional[Message]:
        """接收消息"""
        if self.incoming_queue:
            return self.incoming_queue.popleft()
        return None
    
    async def send_media(self, recipient_id: str, media_url: str, media_type: str, caption: str = "") -> Message:
        """發送媒體消息"""
        content = f"[{media_type.upper()}] {caption}"
        return await self.send_message(
            recipient_id=recipient_id,
            content=content,
            media_url=media_url,
            media_type=media_type
        )
    
    # 測試輔助方法
    def simulate_user_message(self, sender_id: str, content: str):
        """模擬用戶發送消息"""
        message = Message(
            content=content,
            sender_id=sender_id,
            recipient_id="business"
        )
        self.incoming_queue.append(message)
        
        if sender_id not in self.conversations:
            self.conversations[sender_id] = []
        self.conversations[sender_id].append(message)