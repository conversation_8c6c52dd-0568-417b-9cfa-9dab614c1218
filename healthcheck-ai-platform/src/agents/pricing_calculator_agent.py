from typing import Dict, Any, List, Tuple
from decimal import Decimal
from datetime import datetime, date, timedelta
from crewai import Agent
from .base_agent import BaseHealthCheckAgent

class PricingCalculatorAgent(BaseHealthCheckAgent):
    """價格計算 Agent - 負責精確計算套餐價格和優惠組合"""
    
    def __init__(self):
        super().__init__(
            name="Pricing Calculator Agent",
            config_path="config/agents/pricing_calculator.yaml"
        )
        self.packages = self._load_packages()
        self.promotions = self._load_promotions()
        self.rules_engine = PricingRulesEngine()
        
    def _create_agent(self) -> Agent:
        return Agent(
            role=self.config['role'],
            goal=self.config['goal'],
            backstory=self.config['backstory'],
            tools=[],
            verbose=True
        )
    
    def _load_packages(self) -> Dict[str, Any]:
        """加載體檢套餐信息"""
        # 實際應從數據庫加載
        return {
            "basic": {
                "name": "基礎體檢套餐",
                "original_price": Decimal("2980"),
                "items": ["血常規", "尿常規", "肝功能", "腎功能", "心電圖"],
                "duration": "2小時"
            },
            "standard": {
                "name": "標準體檢套餐",
                "original_price": Decimal("4980"),
                "items": ["基礎套餐所有項目", "胸部X光", "腹部超聲波", "甲狀腺功能"],
                "duration": "3小時"
            },
            "premium": {
                "name": "尊享體檢套餐",
                "original_price": Decimal("8980"),
                "items": ["標準套餐所有項目", "胃鏡", "CT掃描", "腫瘤標記物", "骨密度檢測"],
                "duration": "4小時"
            },
            "vip": {
                "name": "VIP全面體檢",
                "original_price": Decimal("15980"),
                "items": ["所有檢查項目", "專屬醫生諮詢", "營養師評估", "年度健康管理"],
                "duration": "全天"
            }
        }
    
    def _load_promotions(self) -> List[Dict[str, Any]]:
        """加載當前促銷活動"""
        return [
            {
                "id": "new_customer_2024",
                "name": "新客戶專享優惠",
                "type": "percentage",
                "value": 15,
                "conditions": ["is_new_customer"],
                "valid_until": "2024-12-31"
            },
            {
                "id": "family_package",
                "name": "家庭套餐優惠",
                "type": "fixed_discount",
                "value": 500,
                "conditions": ["min_people:2"],
                "valid_until": "2024-12-31"
            },
            {
                "id": "early_bird",
                "name": "早鳥優惠",
                "type": "percentage",
                "value": 10,
                "conditions": ["booking_days_advance:7"],
                "valid_until": "2024-12-31"
            },
            {
                "id": "flash_sale",
                "name": "限時快閃",
                "type": "percentage",
                "value": 20,
                "conditions": ["within_24_hours"],
                "valid_until": "2024-11-30"
            }
        ]
    
    async def process(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """處理價格計算請求"""
        request = context.get('pricing_request', {})
        customer_info = context.get('customer_info', {})
        
        # 解析請求
        package_ids = request.get('packages', [])
        quantity = request.get('quantity', 1)
        booking_date = request.get('booking_date')
        
        # 計算價格
        calculation_result = await self._calculate_total_price(
            package_ids=package_ids,
            quantity=quantity,
            customer_info=customer_info,
            booking_date=booking_date
        )
        
        # 生成報價說明
        quote_explanation = await self._generate_quote_explanation(calculation_result)
        
        return {
            'calculation': calculation_result,
            'quote_message': quote_explanation,
            'valid_until': datetime.now().date() + timedelta(days=3),
            'next_agent': 'sales_progression'
        }
    
    async def _calculate_total_price(
        self,
        package_ids: List[str],
        quantity: int,
        customer_info: Dict,
        booking_date: str
    ) -> Dict[str, Any]:
        """計算總價格"""
        # 基礎價格計算
        base_total = Decimal("0")
        package_details = []
        
        for package_id in package_ids:
            if package_id in self.packages:
                package = self.packages[package_id]
                base_price = package['original_price'] * quantity
                base_total += base_price
                
                package_details.append({
                    'id': package_id,
                    'name': package['name'],
                    'unit_price': package['original_price'],
                    'quantity': quantity,
                    'subtotal': base_price
                })
        
        # 應用優惠規則
        applicable_promotions = await self._get_applicable_promotions(
            customer_info=customer_info,
            booking_date=booking_date,
            quantity=quantity,
            base_total=base_total
        )
        
        # 計算折扣
        total_discount = Decimal("0")
        discount_details = []
        
        for promo in applicable_promotions:
            discount = self._calculate_discount(promo, base_total)
            total_discount += discount
            discount_details.append({
                'promotion_id': promo['id'],
                'promotion_name': promo['name'],
                'discount_amount': discount
            })
        
        # 最終價格
        final_price = base_total - total_discount
        
        # 計算節省百分比
        savings_percentage = (total_discount / base_total * 100) if base_total > 0 else 0
        
        return {
            'packages': package_details,
            'base_total': float(base_total),
            'discounts': discount_details,
            'total_discount': float(total_discount),
            'final_price': float(final_price),
            'savings_percentage': float(savings_percentage),
            'currency': 'HKD'
        }
    
    async def _get_applicable_promotions(
        self,
        customer_info: Dict,
        booking_date: str,
        quantity: int,
        base_total: Decimal
    ) -> List[Dict[str, Any]]:
        """獲取適用的優惠"""
        applicable = []
        
        for promo in self.promotions:
            if await self._check_promotion_conditions(
                promo,
                customer_info,
                booking_date,
                quantity,
                base_total
            ):
                applicable.append(promo)
        
        # 優惠互斥規則
        return self._apply_exclusion_rules(applicable)
    
    async def _check_promotion_conditions(
        self,
        promotion: Dict,
        customer_info: Dict,
        booking_date: str,
        quantity: int,
        base_total: Decimal
    ) -> bool:
        """檢查優惠條件"""
        conditions = promotion.get('conditions', [])
        
        for condition in conditions:
            if condition == 'is_new_customer':
                if not customer_info.get('is_new', True):
                    return False
                    
            elif condition.startswith('min_people:'):
                min_people = int(condition.split(':')[1])
                if quantity < min_people:
                    return False
                    
            elif condition.startswith('booking_days_advance:'):
                days = int(condition.split(':')[1])
                if booking_date:
                    booking = datetime.strptime(booking_date, '%Y-%m-%d').date()
                    if (booking - datetime.now().date()).days < days:
                        return False
                        
            elif condition == 'within_24_hours':
                # 檢查是否在首次諮詢24小時內
                first_inquiry = customer_info.get('first_inquiry_time')
                if first_inquiry:
                    hours_passed = (datetime.now() - first_inquiry).total_seconds() / 3600
                    if hours_passed > 24:
                        return False
        
        return True
    
    def _calculate_discount(self, promotion: Dict, base_total: Decimal) -> Decimal:
        """計算單個優惠的折扣金額"""
        promo_type = promotion['type']
        value = Decimal(str(promotion['value']))
        
        if promo_type == 'percentage':
            return base_total * value / 100
        elif promo_type == 'fixed_discount':
            return min(value, base_total)  # 不能超過總價
        else:
            return Decimal("0")
    
    def _apply_exclusion_rules(self, promotions: List[Dict]) -> List[Dict]:
        """應用優惠互斥規則"""
        # 按優惠力度排序，保留最優惠的
        # 這裡簡化處理，實際可能有更複雜的規則
        if not promotions:
            return []
            
        # 如果有快閃優惠，只保留快閃
        flash_sales = [p for p in promotions if p['id'] == 'flash_sale']
        if flash_sales:
            return flash_sales[:1]
        
        # 否則可以疊加部分優惠
        return promotions[:2]  # 最多疊加2個優惠
    
    async def _generate_quote_explanation(self, calculation: Dict[str, Any]) -> str:
        """生成報價說明"""
        message = "💰 **您的專屬報價**\n\n"
        
        # 套餐明細
        message += "📋 選擇的體檢套餐：\n"
        for package in calculation['packages']:
            message += f"• {package['name']} × {package['quantity']} = ${package['subtotal']:,.0f}\n"
        
        message += f"\n原價總計：${calculation['base_total']:,.0f}\n"
        
        # 優惠明細
        if calculation['discounts']:
            message += "\n🎁 已應用優惠：\n"
            for discount in calculation['discounts']:
                message += f"• {discount['promotion_name']}: -${discount['discount_amount']:,.0f}\n"
            
            message += f"\n💸 優惠總額：${calculation['total_discount']:,.0f}"
            message += f" (節省{calculation['savings_percentage']:.0f}%!)\n"
        
        # 最終價格
        message += f"\n✨ **最終價格：${calculation['final_price']:,.0f}**\n"
        
        # 溫馨提示
        message += "\n⏰ 此報價3天內有效"
        if calculation['savings_percentage'] > 15:
            message += "\n🔥 您享受了超值優惠！建議盡快預訂~"
        
        return message


class PricingRulesEngine:
    """價格規則引擎"""
    
    def __init__(self):
        self.rules = self._load_rules()
    
    def _load_rules(self) -> List[Dict]:
        """加載定價規則"""
        return [
            {
                "name": "團體折扣",
                "condition": "quantity >= 5",
                "action": "additional_discount",
                "value": 5
            },
            {
                "name": "VIP升級優惠",
                "condition": "has_premium_and_wants_vip",
                "action": "upgrade_discount",
                "value": 1000
            }
        ]
    
    def apply_rules(self, context: Dict) -> List[Dict]:
        """應用業務規則"""
        applied_rules = []
        # TODO: 實現規則引擎邏輯
        return applied_rules