"""
HealthCheck AI Platform 銷售代理系統

包含原有代理和新增的增強型銷售代理
"""

# 原有代理
from .base_agent import BaseHealthCheckAgent
from .traffic_reception_agent import TrafficReceptionAgent
from .pricing_calculator_agent import PricingCalculatorAgent
from .sales_progression_agent import SalesProgressionAgent
from .follow_up_strategy_agent import FollowUpStrategyAgent
from .quality_monitor_agent import QualityMonitorAgent

# 新增增強型銷售代理
from .psychology_expert_agent import PsychologyExpertAgent
from .sales_expert_agent import SalesExpertAgent
from .frontline_sales_agent import FrontlineSalesAgent
from .online_order_agent import OnlineOrderAgent

# 增強型協調器
from .enhanced_sales_orchestrator import EnhancedSalesOrchestrator

__all__ = [
    # 基礎類
    'BaseHealthCheckAgent',
    
    # 原有代理
    'TrafficReceptionAgent',
    'PricingCalculatorAgent', 
    'SalesProgressionAgent',
    'FollowUpStrategyAgent',
    'QualityMonitorAgent',
    
    # 新增代理
    'PsychologyExpertAgent',
    'SalesExpertAgent',
    'FrontlineSalesAgent',
    'OnlineOrderAgent',
    
    # 協調器
    'EnhancedSalesOrchestrator'
]

# 版本信息
__version__ = '2.0.0'

# 代理分類
AGENT_CATEGORIES = {
    'traffic': ['TrafficReceptionAgent'],
    'sales': [
        'SalesProgressionAgent',
        'SalesExpertAgent',
        'FrontlineSalesAgent',
        'PsychologyExpertAgent'
    ],
    'operations': [
        'PricingCalculatorAgent',
        'OnlineOrderAgent'
    ],
    'retention': ['FollowUpStrategyAgent'],
    'quality': ['QualityMonitorAgent']
}

# 代理描述
AGENT_DESCRIPTIONS = {
    'TrafficReceptionAgent': '初始客戶接待和分流',
    'PricingCalculatorAgent': '價格計算和優惠管理',
    'SalesProgressionAgent': '銷售推進和成交',
    'FollowUpStrategyAgent': '客戶跟進和留存',
    'QualityMonitorAgent': '對話質量監控',
    'PsychologyExpertAgent': '客戶心理深度分析',
    'SalesExpertAgent': '高級銷售策略制定',
    'FrontlineSalesAgent': '前線銷售執行指導',
    'OnlineOrderAgent': '線上訂單轉化優化'
}