from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime
from crewai import Agent, Task
from .base_agent import BaseHealthCheckAgent
import re

class SalesExpertAgent(BaseHealthCheckAgent):
    """銷售專家 Agent - 制定高級銷售策略和技巧指導"""
    
    def __init__(self):
        super().__init__(
            name="Sales Expert Agent", 
            config_path="config/agents/sales_expert.yaml"
        )
        self.sales_frameworks = self._load_sales_frameworks()
        self.negotiation_tactics = self._load_negotiation_tactics()
        self.closing_strategies = self._load_closing_strategies()
        self.upsell_techniques = self._load_upsell_techniques()
        
    def _create_agent(self) -> Agent:
        return Agent(
            role=self.config['role'],
            goal=self.config['goal'],
            backstory=self.config['backstory'],
            tools=[],
            verbose=True,
            max_iter=5
        )
    
    def _load_sales_frameworks(self) -> Dict[str, Any]:
        """加載銷售框架"""
        return {
            "SPIN": {
                "situation": ["了解當前健康管理方式", "詢問體檢頻率", "探詢健康困擾"],
                "problem": ["發現潛在健康風險", "指出當前方式不足", "強調預防重要性"],
                "implication": ["疾病發展後果", "醫療費用增加", "生活質量影響"],
                "need_payoff": ["定期體檢益處", "早期發現優勢", "整體健康改善"]
            },
            "AIDA": {
                "attention": ["健康數據分享", "成功案例", "限時優惠信息"],
                "interest": ["個性化方案", "專業醫療團隊", "先進設備介紹"],
                "desire": ["健康願景描繪", "家庭幸福連結", "生活品質提升"],
                "action": ["立即預約", "限時優惠把握", "簡單預約流程"]
            },
            "Challenger": {
                "teach": ["健康知識教育", "疾病預防觀念", "體檢必要性"],
                "tailor": ["個性化風險評估", "定制檢查方案", "符合預算選擇"],
                "take_control": ["引導決策過程", "消除決策障礙", "創造購買氛圍"]
            },
            "Solution_Selling": {
                "identify_pain": ["健康焦慮點", "時間成本", "醫療費用擔憂"],
                "explore_impact": ["問題惡化可能", "家庭影響", "工作影響"],
                "present_solution": ["完整解決方案", "便捷服務流程", "專業保障"],
                "confirm_value": ["投資回報分析", "長期價值確認", "滿意度保證"]
            }
        }
    
    def _load_negotiation_tactics(self) -> Dict[str, Any]:
        """加載談判策略"""
        return {
            "price_negotiation": {
                "anchoring": {
                    "technique": "先展示高價套餐建立錨點",
                    "example": "我們的VIP套餐包含50項檢查，價值$15000..."
                },
                "bundling": {
                    "technique": "打包銷售增加感知價值",
                    "example": "如果您選擇年度套餐，我們送3次複檢..."
                },
                "contrast_principle": {
                    "technique": "價格對比凸顯優惠",
                    "example": "相比單項檢查總價$8000，套餐只需$5000..."
                },
                "loss_leader": {
                    "technique": "虧本項目吸引購買",
                    "example": "基礎體檢我們是虧本的，但希望您體驗我們的服務..."
                }
            },
            "objection_reversal": {
                "feel_felt_found": {
                    "template": "我理解您的{feeling}，很多客戶一開始也{felt}，但他們{found}...",
                    "example": "我理解您覺得貴，很多客戶一開始也這麼想，但他們發現這其實是最省錢的方式"
                },
                "preemptive_strike": {
                    "technique": "主動提出可能的異議",
                    "example": "您可能會想這個價格有點高，讓我解釋為什麼這是物超所值的..."
                },
                "reframe": {
                    "technique": "重新定義問題角度",
                    "example": "這不是消費，而是對健康的投資，而且是回報率最高的投資"
                }
            },
            "value_building": {
                "feature_advantage_benefit": {
                    "structure": "特點→優勢→利益",
                    "example": "包含腫瘤標記檢測→比常規體檢更全面→能提早5年發現癌症風險"
                },
                "roi_demonstration": {
                    "technique": "投資回報率展示",
                    "calculation": "預防成本 vs 治療成本對比"
                },
                "value_stacking": {
                    "technique": "價值疊加展示總價值",
                    "elements": ["檢查項目", "專家諮詢", "健康管理", "優先預約", "家屬優惠"]
                }
            }
        }
    
    def _load_closing_strategies(self) -> Dict[str, Any]:
        """加載成交策略"""
        return {
            "assumptive_close": {
                "description": "假設客戶已決定購買",
                "phrases": [
                    "那我幫您安排下週二上午的檢查，可以嗎？",
                    "您的健康檔案我現在就建立，請提供您的身份證號碼",
                    "檢查報告您要寄到家裡還是公司？"
                ],
                "when_to_use": "客戶顯示多個購買信號時"
            },
            "urgency_close": {
                "description": "創造時間壓力促進決策",
                "phrases": [
                    "這個優惠只到今晚12點，過了就恢復原價了",
                    "本月就剩3個優惠名額了，要幫您保留嗎？",
                    "下週就是體檢高峰期，現在預約還能選好時段"
                ],
                "when_to_use": "客戶猶豫不決時"
            },
            "alternative_close": {
                "description": "提供選擇而非是否",
                "phrases": [
                    "您是選擇標準套餐還是升級到高級套餐？",
                    "週六上午還是週日下午來檢查比較方便？",
                    "一次付清有額外折扣，還是選擇分期付款？"
                ],
                "when_to_use": "引導客戶做決定"
            },
            "trial_close": {
                "description": "測試購買意願",
                "phrases": [
                    "如果我們能解決時間問題，您會考慮嗎？",
                    "假如價格在您預算內，還有什麼顧慮嗎？",
                    "除了價格，還有什麼阻止您現在預約的嗎？"
                ],
                "when_to_use": "了解真實異議"
            },
            "benjamin_franklin_close": {
                "description": "利弊分析法",
                "process": "列出購買的好處vs不購買的風險",
                "when_to_use": "理性客戶需要邏輯支持"
            },
            "puppy_dog_close": {
                "description": "先試後買策略",
                "phrases": [
                    "先幫您預約，如果改變主意24小時內可以免費取消",
                    "您可以先做基礎檢查，滿意再升級套餐",
                    "我們有滿意保證，不滿意可以退款"
                ],
                "when_to_use": "客戶擔心風險時"
            }
        }
    
    def _load_upsell_techniques(self) -> Dict[str, Any]:
        """加載追加銷售技巧"""
        return {
            "timing_strategies": {
                "post_purchase_high": "購買後立即追加（客戶最開放時）",
                "midway_point": "檢查過程中發現需求",
                "results_delivery": "報告解讀時提供升級建議"
            },
            "upgrade_paths": {
                "basic_to_standard": {
                    "trigger": "基礎套餐客戶",
                    "approach": "只需加$XXX就能多15項重要檢查",
                    "value_props": ["癌症篩查", "心腦血管評估", "內分泌檢測"]
                },
                "standard_to_premium": {
                    "trigger": "標準套餐客戶",
                    "approach": "升級獲得VIP服務和全年健康管理",
                    "value_props": ["專屬健康顧問", "優先預約", "家屬優惠"]
                },
                "single_to_family": {
                    "trigger": "個人客戶",
                    "approach": "家庭套餐更優惠，全家健康一起守護",
                    "value_props": ["家庭優惠價", "統一管理", "健康傳承"]
                }
            },
            "cross_sell_opportunities": {
                "complementary_services": [
                    "營養諮詢服務",
                    "運動康復指導", 
                    "心理健康評估",
                    "基因檢測",
                    "防癌險推薦"
                ],
                "seasonal_promotions": [
                    "母親節家庭體檢",
                    "父親節男性健康",
                    "新年健康計劃",
                    "員工福利方案"
                ]
            }
        }
    
    async def process(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """高級銷售策略處理"""
        conversation_stage = context.get('conversation_stage', 'initial')
        customer_profile = context.get('customer_profile', {})
        psychological_insights = context.get('psychological_insights', {})
        conversation_history = context.get('conversation_history', [])
        current_objections = context.get('current_objections', [])
        
        # 選擇最適合的銷售框架
        optimal_framework = await self._select_sales_framework(
            conversation_stage, customer_profile, psychological_insights
        )
        
        # 分析當前銷售機會
        sales_opportunities = await self._analyze_sales_opportunities(
            conversation_history, customer_profile
        )
        
        # 制定談判策略
        negotiation_plan = await self._develop_negotiation_strategy(
            current_objections, customer_profile, psychological_insights
        )
        
        # 選擇成交技巧
        closing_approach = await self._select_closing_strategy(
            conversation_stage, psychological_insights, sales_opportunities
        )
        
        # 識別追加銷售機會
        upsell_opportunities = await self._identify_upsell_opportunities(
            customer_profile, conversation_history
        )
        
        # 生成戰術建議
        tactical_recommendations = await self._generate_tactical_recommendations(
            optimal_framework, negotiation_plan, closing_approach, context
        )
        
        # 預測成功概率
        success_probability = await self._calculate_success_probability(
            psychological_insights, sales_opportunities, conversation_stage
        )
        
        return {
            'sales_framework': optimal_framework,
            'current_opportunities': sales_opportunities,
            'negotiation_strategy': negotiation_plan,
            'closing_approach': closing_approach,
            'upsell_potential': upsell_opportunities,
            'tactical_recommendations': tactical_recommendations,
            'success_probability': success_probability,
            'next_best_action': tactical_recommendations[0] if tactical_recommendations else None,
            'risk_factors': await self._identify_risk_factors(context),
            'conversation_pivots': await self._suggest_conversation_pivots(context)
        }
    
    async def _select_sales_framework(
        self,
        stage: str,
        customer_profile: Dict,
        psychological_insights: Dict
    ) -> Dict[str, Any]:
        """選擇最適合的銷售框架"""
        framework_scores = {}
        
        # 根據客戶特徵評分各框架
        decision_style = psychological_insights.get('decision_style', {}).get('style', 'unknown')
        
        if decision_style == 'analytical':
            framework_scores['SPIN'] = 0.9
            framework_scores['Solution_Selling'] = 0.8
            framework_scores['Challenger'] = 0.7
            framework_scores['AIDA'] = 0.5
        elif decision_style == 'driver':
            framework_scores['Challenger'] = 0.9
            framework_scores['AIDA'] = 0.8
            framework_scores['Solution_Selling'] = 0.6
            framework_scores['SPIN'] = 0.5
        elif decision_style in ['amiable', 'expressive']:
            framework_scores['AIDA'] = 0.9
            framework_scores['Solution_Selling'] = 0.7
            framework_scores['SPIN'] = 0.6
            framework_scores['Challenger'] = 0.4
        else:
            # 默認平衡方法
            framework_scores = {
                'SPIN': 0.7,
                'AIDA': 0.7,
                'Challenger': 0.6,
                'Solution_Selling': 0.7
            }
        
        # 根據對話階段調整
        if stage == 'initial':
            framework_scores['SPIN'] *= 1.2
        elif stage == 'consideration':
            framework_scores['Solution_Selling'] *= 1.2
        elif stage == 'decision':
            framework_scores['AIDA'] *= 1.2
            framework_scores['Challenger'] *= 1.1
        
        # 選擇最高分框架
        best_framework = max(framework_scores, key=framework_scores.get)
        
        return {
            'framework': best_framework,
            'confidence': framework_scores[best_framework],
            'implementation': self.sales_frameworks[best_framework],
            'stage_specific_tactics': self._get_framework_tactics(best_framework, stage)
        }
    
    def _get_framework_tactics(self, framework: str, stage: str) -> List[str]:
        """獲取框架特定階段策略"""
        tactics_map = {
            'SPIN': {
                'initial': ["探詢現況", "了解健康管理習慣"],
                'consideration': ["深挖問題影響", "強調後果嚴重性"],
                'decision': ["展示解決方案價值", "確認需求滿足"]
            },
            'AIDA': {
                'initial': ["吸引注意力的開場", "分享驚人健康數據"],
                'consideration': ["建立興趣點", "個性化方案展示"],
                'decision': ["激發購買慾望", "立即行動號召"]
            },
            'Challenger': {
                'initial': ["教育新觀念", "挑戰現有認知"],
                'consideration': ["定制化見解", "展示獨特價值"],
                'decision': ["掌控購買流程", "引導決策"]
            },
            'Solution_Selling': {
                'initial': ["識別痛點", "探索需求"],
                'consideration': ["影響分析", "解決方案匹配"],
                'decision': ["價值確認", "風險消除"]
            }
        }
        
        return tactics_map.get(framework, {}).get(stage, [])
    
    async def _analyze_sales_opportunities(
        self,
        conversation_history: List[Dict],
        customer_profile: Dict
    ) -> List[Dict[str, Any]]:
        """分析銷售機會"""
        opportunities = []
        
        # 分析對話內容尋找機會點
        recent_messages = ' '.join([msg.get('content', '') for msg in conversation_history[-5:]])
        
        # 價格詢問 = 高購買意向
        if any(keyword in recent_messages for keyword in ['價格', '多少錢', '費用', '優惠']):
            opportunities.append({
                'type': 'price_inquiry',
                'strength': 'high',
                'action': 'present_value_before_price',
                'tactics': ['價值疊加', '錨定效應', '對比原理']
            })
        
        # 時間討論 = 準備行動
        if any(keyword in recent_messages for keyword in ['什麼時候', '預約', '時間', '幾點']):
            opportunities.append({
                'type': 'scheduling_interest',
                'strength': 'very_high',
                'action': 'assumptive_close',
                'tactics': ['假設成交', '選擇性關閉']
            })
        
        # 比較行為 = 評估階段
        if any(keyword in recent_messages for keyword in ['比較', '區別', '不同', '其他']):
            opportunities.append({
                'type': 'comparison_shopping',
                'strength': 'medium',
                'action': 'differentiation_emphasis',
                'tactics': ['獨特賣點', '競爭優勢', '專屬價值']
            })
        
        # 家人提及 = 決策影響者
        if any(keyword in recent_messages for keyword in ['家人', '老公', '老婆', '父母', '孩子']):
            opportunities.append({
                'type': 'family_influence',
                'strength': 'medium',
                'action': 'family_package_proposal',
                'tactics': ['家庭套餐', '團購優惠', '守護家人']
            })
        
        # 健康擔憂 = 需求明確
        if any(keyword in recent_messages for keyword in ['擔心', '不舒服', '檢查', '問題', '症狀']):
            opportunities.append({
                'type': 'health_concern',
                'strength': 'high',
                'action': 'solution_presentation',
                'tactics': ['針對性方案', '專業建議', '及時性強調']
            })
        
        return sorted(opportunities, key=lambda x: {'very_high': 4, 'high': 3, 'medium': 2, 'low': 1}[x['strength']], reverse=True)
    
    async def _develop_negotiation_strategy(
        self,
        objections: List[str],
        customer_profile: Dict,
        psychological_insights: Dict
    ) -> Dict[str, Any]:
        """制定談判策略"""
        strategy = {
            'primary_approach': '',
            'tactics': [],
            'concessions': [],
            'red_lines': [],
            'value_enhancers': []
        }
        
        # 根據主要異議選擇策略
        if 'price' in str(objections).lower():
            strategy['primary_approach'] = 'value_building'
            strategy['tactics'] = [
                self.negotiation_tactics['price_negotiation']['bundling'],
                self.negotiation_tactics['price_negotiation']['contrast_principle'],
                self.negotiation_tactics['value_building']['value_stacking']
            ]
            strategy['concessions'] = ['分期付款', '額外折扣5%', '贈送項目']
            strategy['value_enhancers'] = ['年度會員權益', '家屬優惠', '健康管理服務']
        
        elif 'time' in str(objections).lower():
            strategy['primary_approach'] = 'convenience_emphasis'
            strategy['tactics'] = ['VIP快速通道', '彈性預約', '上門服務']
            strategy['concessions'] = ['分次完成', '週末加班', '就近安排']
        
        elif 'trust' in str(objections).lower():
            strategy['primary_approach'] = 'credibility_building'
            strategy['tactics'] = ['資質展示', '客戶見證', '試用保證']
            strategy['value_enhancers'] = ['滿意保證', '專家背書', '透明流程']
        
        # 設置談判紅線
        strategy['red_lines'] = [
            '最低折扣不超過15%',
            '必須當天確認預約',
            '套餐內容不可大幅刪減'
        ]
        
        # 根據心理類型調整
        personality_type = psychological_insights.get('psychological_profile', {}).get('personality_type')
        if personality_type == 'cautious_optimizer':
            strategy['tactics'].append(self.negotiation_tactics['objection_reversal']['feel_felt_found'])
        elif personality_type == 'value_seeker':
            strategy['tactics'].append(self.negotiation_tactics['value_building']['roi_demonstration'])
        
        return strategy
    
    async def _select_closing_strategy(
        self,
        stage: str,
        psychological_insights: Dict,
        opportunities: List[Dict]
    ) -> Dict[str, Any]:
        """選擇成交策略"""
        # 根據心理準備度選擇策略
        readiness_score = psychological_insights.get('psychological_readiness_score', 50)
        
        if readiness_score >= 80:
            # 高準備度 - 直接成交
            primary_close = 'assumptive_close'
            backup_close = 'alternative_close'
        elif readiness_score >= 60:
            # 中等準備度 - 創造緊迫感
            primary_close = 'urgency_close'
            backup_close = 'trial_close'
        elif readiness_score >= 40:
            # 低準備度 - 降低風險
            primary_close = 'puppy_dog_close'
            backup_close = 'benjamin_franklin_close'
        else:
            # 很低準備度 - 試探性關閉
            primary_close = 'trial_close'
            backup_close = None
        
        # 根據機會調整
        if any(opp['type'] == 'scheduling_interest' for opp in opportunities):
            primary_close = 'assumptive_close'
        elif any(opp['type'] == 'price_inquiry' for opp in opportunities):
            primary_close = 'alternative_close'
        
        return {
            'primary_strategy': self.closing_strategies[primary_close],
            'backup_strategy': self.closing_strategies.get(backup_close) if backup_close else None,
            'timing': 'immediate' if readiness_score >= 70 else 'after_objection_handling',
            'confidence_level': readiness_score / 100
        }
    
    async def _identify_upsell_opportunities(
        self,
        customer_profile: Dict,
        conversation_history: List[Dict]
    ) -> List[Dict[str, Any]]:
        """識別追加銷售機會"""
        opportunities = []
        
        # 基於客戶檔案識別機會
        if customer_profile.get('has_family'):
            opportunities.append({
                'type': 'family_package',
                'probability': 0.7,
                'approach': self.upsell_techniques['upgrade_paths']['single_to_family'],
                'timing': 'post_purchase'
            })
        
        age = customer_profile.get('age', 0)
        if age > 40:
            opportunities.append({
                'type': 'premium_health_screening',
                'probability': 0.6,
                'approach': '40歲以上建議選擇更全面的檢查',
                'add_ons': ['心腦血管深度檢查', '腫瘤標記全套', '骨密度檢測']
            })
        
        # 基於對話內容識別機會
        conversation_text = ' '.join([msg.get('content', '') for msg in conversation_history])
        
        if '公司' in conversation_text or '同事' in conversation_text:
            opportunities.append({
                'type': 'corporate_package',
                'probability': 0.5,
                'approach': '可以組織公司團檢，有特別優惠',
                'benefits': ['團體折扣', '上門服務', '定制方案']
            })
        
        if any(word in conversation_text for word in ['運動', '健身', '跑步']):
            opportunities.append({
                'type': 'sports_medicine_addon',
                'probability': 0.6,
                'approach': '運動愛好者專屬運動醫學評估',
                'add_ons': ['運動心肺功能', '肌肉骨骼評估', '營養指導']
            })
        
        return sorted(opportunities, key=lambda x: x['probability'], reverse=True)
    
    async def _generate_tactical_recommendations(
        self,
        framework: Dict,
        negotiation: Dict,
        closing: Dict,
        context: Dict
    ) -> List[str]:
        """生成戰術建議"""
        recommendations = []
        
        # 基於銷售框架的建議
        current_step = self._identify_framework_step(framework, context)
        recommendations.append(f"執行{framework['framework']}框架的{current_step}步驟")
        
        # 基於談判策略的建議
        if negotiation['tactics']:
            recommendations.append(f"運用{negotiation['tactics'][0]['technique']}處理異議")
        
        # 基於成交策略的建議
        if closing['timing'] == 'immediate':
            recommendations.append(f"立即使用{closing['primary_strategy']['description']}技巧")
        else:
            recommendations.append("先處理異議，建立更多價值感知")
        
        # 情境化建議
        stage = context.get('conversation_stage', 'initial')
        if stage == 'initial':
            recommendations.append("專注於需求探索和信任建立")
        elif stage == 'consideration':
            recommendations.append("強化價值展示和差異化優勢")
        elif stage == 'decision':
            recommendations.append("消除最後顧慮並促進行動")
        
        # 基於心理洞察的建議
        emotion = context.get('psychological_insights', {}).get('emotional_state', {}).get('primary_emotion')
        if emotion == 'price_sensitive':
            recommendations.append("強調投資回報和長期價值")
        elif emotion == 'skeptical':
            recommendations.append("提供更多社會證明和保證")
        elif emotion == 'interested':
            recommendations.append("把握時機，積極推進成交")
        
        return recommendations[:5]  # 返回前5個最重要的建議
    
    def _identify_framework_step(self, framework: Dict, context: Dict) -> str:
        """識別當前框架步驟"""
        framework_name = framework['framework']
        conversation_length = len(context.get('conversation_history', []))
        
        if framework_name == 'SPIN':
            if conversation_length < 3:
                return "Situation(現況了解)"
            elif conversation_length < 6:
                return "Problem(問題探索)"
            elif conversation_length < 9:
                return "Implication(影響分析)"
            else:
                return "Need-Payoff(價值確認)"
        
        elif framework_name == 'AIDA':
            if conversation_length < 2:
                return "Attention(吸引注意)"
            elif conversation_length < 5:
                return "Interest(建立興趣)"
            elif conversation_length < 8:
                return "Desire(激發慾望)"
            else:
                return "Action(促進行動)"
        
        return "Initial"
    
    async def _calculate_success_probability(
        self,
        psychological_insights: Dict,
        opportunities: List[Dict],
        stage: str
    ) -> float:
        """計算成功概率"""
        base_probability = 0.3  # 基礎概率
        
        # 心理準備度影響 (最高+0.3)
        readiness = psychological_insights.get('psychological_readiness_score', 50)
        base_probability += (readiness / 100) * 0.3
        
        # 銷售機會影響 (最高+0.2)
        high_strength_opportunities = sum(1 for opp in opportunities if opp.get('strength') in ['high', 'very_high'])
        base_probability += min(high_strength_opportunities * 0.1, 0.2)
        
        # 對話階段影響 (最高+0.2)
        stage_multipliers = {
            'initial': 0.05,
            'consideration': 0.1,
            'evaluation': 0.15,
            'decision': 0.2
        }
        base_probability += stage_multipliers.get(stage, 0.05)
        
        # 情緒狀態影響 (-0.1到+0.1)
        emotion = psychological_insights.get('emotional_state', {}).get('primary_emotion')
        emotion_modifiers = {
            'interested': 0.1,
            'excited': 0.1,
            'neutral': 0,
            'hesitant': -0.05,
            'skeptical': -0.1
        }
        base_probability += emotion_modifiers.get(emotion, 0)
        
        return min(max(base_probability, 0.1), 0.95)  # 確保在10%-95%範圍內
    
    async def _identify_risk_factors(self, context: Dict) -> List[Dict[str, Any]]:
        """識別風險因素"""
        risks = []
        
        # 檢查對話趨勢
        emotion_trend = context.get('psychological_insights', {}).get('emotional_state', {}).get('trend')
        if emotion_trend == 'declining':
            risks.append({
                'type': 'engagement_decline',
                'severity': 'high',
                'mitigation': '重新激發興趣，可能需要改變方法'
            })
        
        # 檢查未解決異議
        unresolved_objections = context.get('unresolved_objections', [])
        if len(unresolved_objections) > 2:
            risks.append({
                'type': 'multiple_objections',
                'severity': 'medium',
                'mitigation': '優先處理核心異議，簡化方案'
            })
        
        # 檢查回應時間
        response_delays = context.get('response_delays', [])
        if response_delays and sum(response_delays[-3:]) / 3 > 300:  # 平均超過5分鐘
            risks.append({
                'type': 'low_engagement',
                'severity': 'medium',
                'mitigation': '創造緊迫感或改變溝通時間'
            })
        
        return risks
    
    async def _suggest_conversation_pivots(self, context: Dict) -> List[Dict[str, str]]:
        """建議對話轉折點"""
        pivots = []
        
        current_topic = context.get('current_topic', '')
        stuck_duration = context.get('topic_duration', 0)
        
        if stuck_duration > 5:  # 在同一話題超過5輪對話
            pivots.append({
                'from': current_topic,
                'to': 'value_demonstration',
                'transition': "讓我從另一個角度為您介紹..."
            })
        
        emotion = context.get('psychological_insights', {}).get('emotional_state', {}).get('primary_emotion')
        if emotion in ['skeptical', 'concerned']:
            pivots.append({
                'from': 'product_features',
                'to': 'social_proof',
                'transition': "很多客戶一開始也有同樣的擔心，讓我分享一個案例..."
            })
        
        if context.get('price_mentioned', False) and not context.get('value_demonstrated', False):
            pivots.append({
                'from': 'pricing',
                'to': 'value_building',
                'transition': "在談價格之前，讓我先為您介紹您能獲得的價值..."
            })
        
        return pivots