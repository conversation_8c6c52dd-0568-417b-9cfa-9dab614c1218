from typing import Dict, Any, List, Optional
from datetime import datetime, timedelta
from crewai import Agent, Task
from .base_agent import BaseHealthCheckAgent
import random

class FollowUpStrategyAgent(BaseHealthCheckAgent):
    """跟進策略 Agent - 負責制定和執行客戶跟進計劃"""
    
    def __init__(self):
        super().__init__(
            name="Follow-up Strategy Agent",
            config_path="config/agents/follow_up_strategy.yaml"
        )
        self.follow_up_templates = self._load_follow_up_templates()
        self.timing_rules = self._load_timing_rules()
        self.channel_preferences = self._load_channel_preferences()
        
    def _create_agent(self) -> Agent:
        return Agent(
            role=self.config['role'],
            goal=self.config['goal'],
            backstory=self.config['backstory'],
            tools=[],
            verbose=True,
            max_iter=5
        )
    
    def _load_follow_up_templates(self) -> Dict[str, List[Dict[str, Any]]]:
        """加載跟進消息模板"""
        return {
            "abandoned_cart": [
                {
                    "delay_hours": 2,
                    "message": "👋 {customer_name}您好！剛才看到您對{package_name}套餐很感興趣，有什麼我可以幫您解答的嗎？",
                    "urgency": "low"
                },
                {
                    "delay_hours": 24,
                    "message": "🎁 {customer_name}，好消息！我為您爭取到了額外5%優惠，現在{package_name}只需${final_price}！優惠只到今晚12點哦～",
                    "urgency": "high"
                },
                {
                    "delay_hours": 72,
                    "message": "💌 {customer_name}，最後提醒！您關注的{package_name}優惠即將結束。錯過要等明年了，要不要再考慮一下？",
                    "urgency": "medium"
                }
            ],
            "price_sensitive": [
                {
                    "delay_hours": 6,
                    "message": "🔥 {customer_name}，剛收到通知！公司推出限時團購優惠，{package_name}現在買2送1，要不要幫您家人也預約？",
                    "urgency": "high"
                },
                {
                    "delay_hours": 48,
                    "message": "💰 {customer_name}您好！我們新推出了分期付款服務，{package_name}可以分3期免息支付，每期只需${monthly_payment}！",
                    "urgency": "medium"
                }
            ],
            "busy_professional": [
                {
                    "delay_hours": 12,
                    "message": "⚡ {customer_name}，為您安排了VIP快速通道！全程只需2小時完成所有檢查，還包含專屬健康顧問。週末有空嗎？",
                    "urgency": "medium"
                },
                {
                    "delay_hours": 96,
                    "message": "📅 {customer_name}您好！下個月我們在您公司附近新開了檢查中心，開幕優惠85折，預約還送健康餐券！",
                    "urgency": "low"
                }
            ],
            "health_conscious": [
                {
                    "delay_hours": 4,
                    "message": "📊 {customer_name}，分享一個數據：我們去年幫助了3,000+客戶發現早期健康問題。預防永遠比治療更重要！",
                    "urgency": "medium"
                },
                {
                    "delay_hours": 168,
                    "message": "🏃 {customer_name}您好！新年新開始，是時候為健康投資了。現在預約{package_name}，送價值$500的運動手環！",
                    "urgency": "low"
                }
            ]
        }
    
    def _load_timing_rules(self) -> Dict[str, Dict[str, Any]]:
        """加載跟進時間規則"""
        return {
            "best_times": {
                "morning": {"start": 9, "end": 11, "response_rate": 0.65},
                "lunch": {"start": 12, "end": 13, "response_rate": 0.45},
                "afternoon": {"start": 15, "end": 17, "response_rate": 0.70},
                "evening": {"start": 19, "end": 21, "response_rate": 0.85}
            },
            "avoid_times": {
                "early_morning": {"start": 0, "end": 8},
                "late_night": {"start": 22, "end": 24},
                "dinner_time": {"start": 18, "end": 19}
            },
            "day_preferences": {
                "weekday": {"response_modifier": 1.0},
                "weekend": {"response_modifier": 1.2},
                "holiday": {"response_modifier": 0.7}
            }
        }
    
    def _load_channel_preferences(self) -> Dict[str, Dict[str, float]]:
        """加載渠道偏好設置"""
        return {
            "whatsapp": {"open_rate": 0.98, "response_rate": 0.75},
            "email": {"open_rate": 0.25, "response_rate": 0.15},
            "sms": {"open_rate": 0.95, "response_rate": 0.40},
            "phone": {"open_rate": 0.60, "response_rate": 0.85}
        }
    
    async def process(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """制定跟進策略"""
        customer_profile = context.get('customer_profile', {})
        interaction_history = context.get('interaction_history', [])
        last_interaction = context.get('last_interaction_time')
        conversion_stage = context.get('conversion_stage', 'awareness')
        
        # 分析客戶狀態
        customer_segment = await self._segment_customer(customer_profile, interaction_history)
        engagement_score = await self._calculate_engagement_score(interaction_history)
        churn_risk = await self._assess_churn_risk(last_interaction, engagement_score)
        
        # 制定跟進計劃
        follow_up_plan = await self._create_follow_up_plan(
            segment=customer_segment,
            stage=conversion_stage,
            engagement=engagement_score,
            churn_risk=churn_risk,
            context=context
        )
        
        # 優化發送時間
        optimal_timing = await self._optimize_send_time(
            customer_profile,
            follow_up_plan
        )
        
        return {
            'follow_up_plan': follow_up_plan,
            'customer_segment': customer_segment,
            'engagement_score': engagement_score,
            'churn_risk': churn_risk,
            'optimal_timing': optimal_timing,
            'next_action': follow_up_plan[0] if follow_up_plan else None,
            'context': {
                **context,
                'strategy_time': datetime.now().isoformat(),
                'total_touchpoints': len(follow_up_plan)
            }
        }
    
    async def _segment_customer(
        self, 
        profile: Dict[str, Any], 
        history: List[Dict[str, Any]]
    ) -> str:
        """客戶分群"""
        # 基於互動歷史分析客戶類型
        
        # 檢查價格敏感度
        price_mentions = sum(1 for interaction in history 
                           if any(word in str(interaction.get('content', '')) 
                                 for word in ['價格', '優惠', '便宜', '貴']))
        
        # 檢查時間限制
        time_mentions = sum(1 for interaction in history 
                          if any(word in str(interaction.get('content', '')) 
                                for word in ['忙', '沒時間', '週末', '快速']))
        
        # 檢查健康意識
        health_mentions = sum(1 for interaction in history 
                            if any(word in str(interaction.get('content', '')) 
                                  for word in ['健康', '預防', '檢查', '家人']))
        
        # 判斷主要類型
        if price_mentions >= 2:
            return "price_sensitive"
        elif time_mentions >= 2:
            return "busy_professional"
        elif health_mentions >= 2:
            return "health_conscious"
        elif len(history) > 0 and not profile.get('completed_purchase'):
            return "abandoned_cart"
        else:
            return "general"
    
    async def _calculate_engagement_score(self, history: List[Dict[str, Any]]) -> float:
        """計算客戶參與度分數"""
        if not history:
            return 0.0
        
        score = 0.0
        
        # 互動頻率
        if len(history) >= 5:
            score += 30
        elif len(history) >= 3:
            score += 20
        elif len(history) >= 1:
            score += 10
        
        # 最近互動時間
        if history:
            last_interaction = history[-1].get('timestamp')
            if last_interaction:
                hours_since = (datetime.now() - datetime.fromisoformat(last_interaction)).total_seconds() / 3600
                if hours_since < 24:
                    score += 30
                elif hours_since < 72:
                    score += 20
                elif hours_since < 168:
                    score += 10
        
        # 互動質量
        positive_signals = ['想要', '預約', '什麼時候', '可以', '好的']
        for interaction in history:
            content = str(interaction.get('content', ''))
            if any(signal in content for signal in positive_signals):
                score += 5
        
        # 詢問具體信息
        info_requests = ['價格', '時間', '地點', '套餐', '優惠']
        for interaction in history:
            content = str(interaction.get('content', ''))
            if any(request in content for request in info_requests):
                score += 5
        
        return min(100.0, score)
    
    async def _assess_churn_risk(
        self, 
        last_interaction: Optional[str], 
        engagement_score: float
    ) -> str:
        """評估流失風險"""
        if not last_interaction:
            return "high"
        
        hours_since = (datetime.now() - datetime.fromisoformat(last_interaction)).total_seconds() / 3600
        
        if engagement_score >= 70:
            if hours_since > 72:
                return "medium"
            return "low"
        elif engagement_score >= 40:
            if hours_since > 48:
                return "high"
            return "medium"
        else:
            if hours_since > 24:
                return "high"
            return "medium"
    
    async def _create_follow_up_plan(
        self,
        segment: str,
        stage: str,
        engagement: float,
        churn_risk: str,
        context: Dict[str, Any]
    ) -> List[Dict[str, Any]]:
        """創建跟進計劃"""
        plan = []
        
        # 獲取對應的模板
        templates = self.follow_up_templates.get(segment, self.follow_up_templates.get("general", []))
        
        # 根據流失風險調整策略
        if churn_risk == "high":
            # 高風險：立即跟進
            urgent_template = templates[0] if templates else {
                "delay_hours": 1,
                "message": "👋 您好！有什麼我可以幫助您的嗎？",
                "urgency": "high"
            }
            plan.append(self._create_follow_up_task(urgent_template, context, immediate=True))
        
        # 添加常規跟進任務
        for template in templates:
            # 根據參與度調整時間
            adjusted_delay = template['delay_hours']
            if engagement < 40:
                adjusted_delay = adjusted_delay * 0.7  # 加快跟進
            elif engagement > 70:
                adjusted_delay = adjusted_delay * 1.3  # 放慢節奏
            
            task = self._create_follow_up_task(template, context)
            task['scheduled_time'] = (datetime.now() + timedelta(hours=adjusted_delay)).isoformat()
            plan.append(task)
        
        # 根據轉化階段添加特定任務
        if stage == "consideration" and len(plan) < 3:
            plan.append({
                'type': 'value_reinforcement',
                'channel': 'whatsapp',
                'scheduled_time': (datetime.now() + timedelta(hours=36)).isoformat(),
                'message': self._generate_value_message(context),
                'urgency': 'medium'
            })
        
        return sorted(plan, key=lambda x: x['scheduled_time'])
    
    def _create_follow_up_task(
        self, 
        template: Dict[str, Any], 
        context: Dict[str, Any],
        immediate: bool = False
    ) -> Dict[str, Any]:
        """創建跟進任務"""
        # 填充模板變量
        message = template['message'].format(
            customer_name=context.get('customer_name', '客戶'),
            package_name=context.get('package_name', '健康檢查套餐'),
            final_price=context.get('quoted_price', {}).get('final_price', 5000),
            monthly_payment=context.get('quoted_price', {}).get('final_price', 5000) / 3
        )
        
        return {
            'type': 'follow_up',
            'channel': 'whatsapp',
            'message': message,
            'urgency': template.get('urgency', 'medium'),
            'scheduled_time': datetime.now().isoformat() if immediate else None,
            'metadata': {
                'template_used': template,
                'customer_segment': context.get('customer_segment'),
                'attempt_number': len(context.get('follow_up_history', [])) + 1
            }
        }
    
    def _generate_value_message(self, context: Dict[str, Any]) -> str:
        """生成價值強化消息"""
        value_points = [
            "🏆 我們是香港最大的體檢中心，已服務超過100萬客戶",
            "👨‍⚕️ 所有檢查由資深醫生親自解讀，不是機器報告",
            "📱 手機App隨時查看報告，終身保存",
            "🎯 根據您的年齡和健康狀況，定制個人化建議",
            "💝 家人一起檢查有額外優惠，照顧全家健康"
        ]
        
        selected_point = random.choice(value_points)
        return f"{context.get('customer_name', '客戶')}您好！{selected_point} 現在預約還來得及享受本月優惠！"
    
    async def _optimize_send_time(
        self,
        profile: Dict[str, Any],
        plan: List[Dict[str, Any]]
    ) -> Dict[str, Any]:
        """優化發送時間"""
        # 獲取客戶時區
        timezone = profile.get('timezone', 'Asia/Hong_Kong')
        
        # 分析歷史最佳互動時間
        best_response_times = profile.get('best_response_times', [])
        
        # 應用時間規則
        optimal_slots = []
        for time_slot, rules in self.timing_rules['best_times'].items():
            score = rules['response_rate']
            
            # 根據客戶歷史調整
            if best_response_times:
                for hist_time in best_response_times:
                    hour = datetime.fromisoformat(hist_time).hour
                    if rules['start'] <= hour <= rules['end']:
                        score *= 1.3
            
            optimal_slots.append({
                'slot': time_slot,
                'hours': f"{rules['start']}:00-{rules['end']}:00",
                'score': score
            })
        
        # 排序獲取最佳時段
        optimal_slots.sort(key=lambda x: x['score'], reverse=True)
        
        return {
            'timezone': timezone,
            'best_slots': optimal_slots[:3],
            'avoid_hours': self.timing_rules['avoid_times'],
            'personalized': len(best_response_times) > 0
        }