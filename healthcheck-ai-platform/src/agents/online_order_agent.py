from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime, timedelta
from crewai import Agent, Task
from .base_agent import BaseHealthCheckAgent
import json
import re

class OnlineOrderAgent(BaseHealthCheckAgent):
    """線上下單專家 Agent - 簡化購買流程，提高線上轉換率"""
    
    def __init__(self):
        super().__init__(
            name="Online Order Agent",
            config_path="config/agents/online_order.yaml"
        )
        self.order_workflows = self._load_order_workflows()
        self.payment_options = self._load_payment_options()
        self.booking_rules = self._load_booking_rules()
        self.conversion_optimizers = self._load_conversion_optimizers()
        
    def _create_agent(self) -> Agent:
        return Agent(
            role=self.config['role'],
            goal=self.config['goal'],
            backstory=self.config['backstory'],
            tools=[],
            verbose=True,
            max_iter=5
        )
    
    def _load_order_workflows(self) -> Dict[str, Any]:
        """加載訂單流程"""
        return {
            "express_checkout": {
                "steps": [
                    {"step": "package_selection", "time": "30s", "fields": ["package_type"]},
                    {"step": "quick_info", "time": "45s", "fields": ["name", "phone", "id"]},
                    {"step": "instant_payment", "time": "30s", "fields": ["payment_method"]},
                    {"step": "confirmation", "time": "15s", "fields": ["booking_slot"]}
                ],
                "total_time": "2 minutes",
                "conversion_rate": 0.85
            },
            "standard_checkout": {
                "steps": [
                    {"step": "package_browse", "time": "2min", "fields": ["compare_packages"]},
                    {"step": "customization", "time": "1min", "fields": ["add_ons", "preferences"]},
                    {"step": "personal_info", "time": "1min", "fields": ["full_details"]},
                    {"step": "payment_selection", "time": "1min", "fields": ["payment_plan"]},
                    {"step": "review_confirm", "time": "30s", "fields": ["final_review"]}
                ],
                "total_time": "5-6 minutes",
                "conversion_rate": 0.65
            },
            "guided_checkout": {
                "steps": [
                    {"step": "needs_assessment", "time": "1min", "ai_assisted": True},
                    {"step": "recommendation", "time": "30s", "personalized": True},
                    {"step": "one_click_accept", "time": "15s", "simplified": True},
                    {"step": "auto_fill", "time": "30s", "data_prefilled": True},
                    {"step": "instant_confirm", "time": "15s", "immediate": True}
                ],
                "total_time": "2-3 minutes",
                "conversion_rate": 0.92
            }
        }
    
    def _load_payment_options(self) -> Dict[str, Any]:
        """加載支付選項"""
        return {
            "instant_payment": {
                "credit_card": {
                    "process_time": "即時",
                    "convenience_fee": 0,
                    "security": "3D Secure",
                    "popular_cards": ["Visa", "Mastercard", "AMEX"]
                },
                "digital_wallet": {
                    "options": ["PayPal", "Apple Pay", "Google Pay", "支付寶", "微信支付"],
                    "process_time": "即時",
                    "user_preference": 0.45
                },
                "bank_transfer": {
                    "instant_banks": ["恆生", "匯豐", "中銀"],
                    "process_time": "即時確認",
                    "reference_auto_generated": True
                }
            },
            "installment_plans": {
                "interest_free": {
                    "3_months": {"min_amount": 3000, "approval_rate": 0.95},
                    "6_months": {"min_amount": 5000, "approval_rate": 0.90},
                    "12_months": {"min_amount": 10000, "approval_rate": 0.85}
                },
                "low_interest": {
                    "24_months": {"rate": "2.5%", "min_amount": 15000}
                }
            },
            "corporate_billing": {
                "invoice_payment": {
                    "net_terms": [30, 60, 90],
                    "approval_time": "1-2 business days",
                    "bulk_discount": True
                },
                "purchase_order": {
                    "accepted": True,
                    "verification_required": True
                }
            },
            "promotional_payment": {
                "early_bird": {
                    "discount": "10%",
                    "payment_window": "48 hours"
                },
                "group_payment": {
                    "min_people": 3,
                    "group_discount": "15%"
                }
            }
        }
    
    def _load_booking_rules(self) -> Dict[str, Any]:
        """加載預約規則"""
        return {
            "slot_availability": {
                "peak_times": {
                    "weekday_morning": {"slots": 20, "premium": False},
                    "weekend_morning": {"slots": 30, "premium": True, "surcharge": "10%"},
                    "weekday_evening": {"slots": 15, "premium": True, "surcharge": "15%"}
                },
                "booking_window": {
                    "advance_booking": "90 days",
                    "last_minute": "24 hours",
                    "cancellation_cutoff": "48 hours"
                }
            },
            "package_specific_rules": {
                "basic": {
                    "time_required": "1.5 hours",
                    "fasting_required": False,
                    "prep_instructions": "簡單"
                },
                "comprehensive": {
                    "time_required": "3 hours",
                    "fasting_required": True,
                    "prep_instructions": "詳細"
                },
                "vip": {
                    "time_required": "2 hours",
                    "fasting_required": True,
                    "includes_consultation": True,
                    "dedicated_staff": True
                }
            },
            "smart_scheduling": {
                "ai_optimization": True,
                "preference_learning": True,
                "conflict_detection": True,
                "auto_reminder": True
            }
        }
    
    def _load_conversion_optimizers(self) -> Dict[str, Any]:
        """加載轉換優化策略"""
        return {
            "friction_reducers": {
                "auto_fill": {
                    "from_previous_orders": True,
                    "from_social_login": True,
                    "smart_suggestions": True
                },
                "one_click_options": {
                    "repeat_last_order": True,
                    "recommended_package": True,
                    "express_checkout": True
                },
                "progress_indicators": {
                    "visual_progress_bar": True,
                    "time_estimate": True,
                    "save_for_later": True
                }
            },
            "trust_builders": {
                "security_badges": ["SSL", "Payment Security", "Privacy"],
                "guarantees": ["滿意保證", "價格保證", "預約保證"],
                "social_proof": {
                    "recent_purchases": "顯示最近購買",
                    "reviews": "即時評價展示",
                    "statistics": "成功預約數"
                }
            },
            "urgency_creators": {
                "real_time_availability": True,
                "countdown_timers": {
                    "offer_expiry": True,
                    "slot_availability": True,
                    "price_hold": "10 minutes"
                },
                "scarcity_indicators": {
                    "remaining_slots": True,
                    "popular_times": True,
                    "waitlist_option": True
                }
            },
            "abandonment_prevention": {
                "exit_intent_popup": True,
                "cart_recovery_email": "30 minutes",
                "chat_intervention": "高價值訂單",
                "special_offers": "離開前優惠"
            }
        }
    
    async def process(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """處理線上訂單流程"""
        customer_profile = context.get('customer_profile', {})
        cart_contents = context.get('cart_contents', {})
        order_stage = context.get('order_stage', 'browsing')
        psychological_insights = context.get('psychological_insights', {})
        
        # 分析客戶訂單行為
        order_behavior = await self._analyze_order_behavior(
            customer_profile, cart_contents, context.get('session_data', {})
        )
        
        # 選擇最優訂單流程
        optimal_workflow = await self._select_optimal_workflow(
            customer_profile, psychological_insights, order_behavior
        )
        
        # 識別並消除購買障礙
        purchase_barriers = await self._identify_purchase_barriers(
            order_stage, cart_contents, customer_profile
        )
        
        # 生成個性化訂單體驗
        personalized_experience = await self._create_personalized_experience(
            customer_profile, psychological_insights, optimal_workflow
        )
        
        # 優化支付選項
        payment_recommendations = await self._optimize_payment_options(
            cart_contents, customer_profile, psychological_insights
        )
        
        # 智能預約安排
        booking_optimization = await self._optimize_booking(
            customer_profile, cart_contents.get('package_type')
        )
        
        # 轉換率優化策略
        conversion_strategies = await self._apply_conversion_optimization(
            order_stage, order_behavior, purchase_barriers
        )
        
        # 生成即時指導
        real_time_assistance = await self._generate_order_assistance(
            order_stage, purchase_barriers, customer_profile
        )
        
        return {
            'optimal_workflow': optimal_workflow,
            'order_behavior_analysis': order_behavior,
            'purchase_barriers': purchase_barriers,
            'personalized_experience': personalized_experience,
            'payment_recommendations': payment_recommendations,
            'booking_optimization': booking_optimization,
            'conversion_strategies': conversion_strategies,
            'real_time_assistance': real_time_assistance,
            'abandonment_risk': await self._calculate_abandonment_risk(context),
            'next_best_action': self._determine_next_action(order_stage, purchase_barriers)
        }
    
    async def _analyze_order_behavior(
        self,
        customer_profile: Dict,
        cart_contents: Dict,
        session_data: Dict
    ) -> Dict[str, Any]:
        """分析訂單行為"""
        behavior_analysis = {
            'order_velocity': 'normal',
            'decision_pattern': 'unknown',
            'price_sensitivity': 'medium',
            'feature_preferences': [],
            'abandonment_triggers': []
        }
        
        # 分析決策速度
        if session_data.get('time_on_site', 0) < 300:  # 少於5分鐘
            behavior_analysis['order_velocity'] = 'fast'
        elif session_data.get('time_on_site', 0) > 900:  # 超過15分鐘
            behavior_analysis['order_velocity'] = 'slow'
        
        # 分析決策模式
        pages_viewed = session_data.get('pages_viewed', [])
        if 'comparison' in pages_viewed:
            behavior_analysis['decision_pattern'] = 'comparative'
        elif 'testimonials' in pages_viewed:
            behavior_analysis['decision_pattern'] = 'social_proof_seeking'
        elif 'pricing' in pages_viewed:
            behavior_analysis['decision_pattern'] = 'price_focused'
        
        # 分析價格敏感度
        if cart_contents.get('coupon_attempts', 0) > 0:
            behavior_analysis['price_sensitivity'] = 'high'
        elif cart_contents.get('package_type') == 'vip':
            behavior_analysis['price_sensitivity'] = 'low'
        
        # 識別功能偏好
        if customer_profile.get('age', 0) > 50:
            behavior_analysis['feature_preferences'].append('comprehensive_testing')
        if customer_profile.get('has_family'):
            behavior_analysis['feature_preferences'].append('family_packages')
        if customer_profile.get('occupation') in ['executive', 'entrepreneur']:
            behavior_analysis['feature_preferences'].append('time_efficiency')
        
        # 識別放棄觸發因素
        if session_data.get('cart_modifications', 0) > 3:
            behavior_analysis['abandonment_triggers'].append('indecision')
        if session_data.get('payment_page_exits', 0) > 0:
            behavior_analysis['abandonment_triggers'].append('payment_friction')
        if session_data.get('price_check_frequency', 0) > 5:
            behavior_analysis['abandonment_triggers'].append('price_concern')
        
        return behavior_analysis
    
    async def _select_optimal_workflow(
        self,
        customer_profile: Dict,
        psychological_insights: Dict,
        order_behavior: Dict
    ) -> Dict[str, Any]:
        """選擇最優訂單流程"""
        # 根據客戶特徵評分各流程
        workflow_scores = {}
        
        # 快速決策者適合快速結賬
        if order_behavior['order_velocity'] == 'fast':
            workflow_scores['express_checkout'] = 0.9
            workflow_scores['guided_checkout'] = 0.7
            workflow_scores['standard_checkout'] = 0.4
        
        # 比較型決策者適合標準流程
        elif order_behavior['decision_pattern'] == 'comparative':
            workflow_scores['standard_checkout'] = 0.8
            workflow_scores['guided_checkout'] = 0.6
            workflow_scores['express_checkout'] = 0.5
        
        # 需要引導的客戶適合AI輔助流程
        elif psychological_insights.get('psychological_profile', {}).get('trust_level', 0) < 0.4:
            workflow_scores['guided_checkout'] = 0.9
            workflow_scores['standard_checkout'] = 0.6
            workflow_scores['express_checkout'] = 0.4
        
        # 默認平衡選擇
        else:
            workflow_scores = {
                'guided_checkout': 0.8,
                'express_checkout': 0.7,
                'standard_checkout': 0.6
            }
        
        # 選擇最高分流程
        best_workflow = max(workflow_scores, key=workflow_scores.get)
        
        return {
            'selected_workflow': best_workflow,
            'workflow_details': self.order_workflows[best_workflow],
            'confidence_score': workflow_scores[best_workflow],
            'estimated_completion_time': self.order_workflows[best_workflow]['total_time'],
            'expected_conversion_rate': self.order_workflows[best_workflow]['conversion_rate']
        }
    
    async def _identify_purchase_barriers(
        self,
        order_stage: str,
        cart_contents: Dict,
        customer_profile: Dict
    ) -> List[Dict[str, Any]]:
        """識別購買障礙"""
        barriers = []
        
        # 階段相關障礙
        stage_barriers = {
            'browsing': ['選擇困難', '信息不足', '價格不透明'],
            'cart': ['總價震驚', '額外費用', '優惠碼需求'],
            'checkout': ['表單複雜', '支付選項', '安全顧慮'],
            'payment': ['支付失敗', '認證問題', '技術故障']
        }
        
        if order_stage in stage_barriers:
            for barrier in stage_barriers[order_stage]:
                barriers.append({
                    'type': barrier,
                    'stage': order_stage,
                    'severity': 'medium',
                    'solution': self._get_barrier_solution(barrier)
                })
        
        # 客戶特定障礙
        if customer_profile.get('first_time_buyer', True):
            barriers.append({
                'type': '首次購買不確定',
                'severity': 'high',
                'solution': '提供新客戶保證和優惠'
            })
        
        if customer_profile.get('age', 0) > 60:
            barriers.append({
                'type': '技術操作困難',
                'severity': 'medium',
                'solution': '簡化界面和提供電話協助'
            })
        
        # 購物車特定障礙
        if cart_contents.get('total_amount', 0) > customer_profile.get('typical_spend', 5000):
            barriers.append({
                'type': '價格超出預期',
                'severity': 'high',
                'solution': '提供分期付款選項'
            })
        
        if not cart_contents.get('promo_applied', False) and cart_contents.get('total_amount', 0) > 3000:
            barriers.append({
                'type': '尋求更好優惠',
                'severity': 'medium',
                'solution': '主動提供限時折扣'
            })
        
        return sorted(barriers, key=lambda x: {'high': 0, 'medium': 1, 'low': 2}[x['severity']])
    
    def _get_barrier_solution(self, barrier_type: str) -> str:
        """獲取障礙解決方案"""
        solutions = {
            '選擇困難': '提供AI推薦和比較工具',
            '信息不足': '添加詳細說明和FAQ',
            '價格不透明': '顯示完整價格明細',
            '總價震驚': '解釋價值和提供分期',
            '額外費用': '提供全包價格選項',
            '優惠碼需求': '自動應用最佳優惠',
            '表單複雜': '減少必填項和自動填充',
            '支付選項': '增加支付方式',
            '安全顧慮': '顯示安全認證和保證',
            '支付失敗': '提供備選支付方式',
            '認證問題': '簡化認證流程',
            '技術故障': '提供人工協助選項'
        }
        return solutions.get(barrier_type, '提供即時客服支持')
    
    async def _create_personalized_experience(
        self,
        customer_profile: Dict,
        psychological_insights: Dict,
        workflow: Dict
    ) -> Dict[str, Any]:
        """創建個性化訂單體驗"""
        personalization = {
            'ui_customization': {},
            'content_adaptation': {},
            'process_optimization': {},
            'communication_style': {}
        }
        
        # UI個性化
        if customer_profile.get('age', 0) > 50:
            personalization['ui_customization'] = {
                'font_size': 'large',
                'button_size': 'extra_large',
                'contrast': 'high',
                'simplified_layout': True
            }
        elif customer_profile.get('tech_savvy', False):
            personalization['ui_customization'] = {
                'advanced_features': True,
                'keyboard_shortcuts': True,
                'bulk_operations': True
            }
        
        # 內容適配
        decision_style = psychological_insights.get('decision_style', {}).get('style')
        if decision_style == 'analytical':
            personalization['content_adaptation'] = {
                'show_details': True,
                'comparison_tables': True,
                'data_visualization': True,
                'testimonial_focus': 'data_driven'
            }
        elif decision_style == 'emotional':
            personalization['content_adaptation'] = {
                'story_telling': True,
                'visual_emphasis': True,
                'testimonial_focus': 'emotional_stories',
                'benefit_framing': 'personal_impact'
            }
        
        # 流程優化
        if workflow['selected_workflow'] == 'express_checkout':
            personalization['process_optimization'] = {
                'skip_optional_steps': True,
                'auto_select_defaults': True,
                'one_page_checkout': True
            }
        elif workflow['selected_workflow'] == 'guided_checkout':
            personalization['process_optimization'] = {
                'step_by_step_guidance': True,
                'contextual_help': True,
                'progress_celebration': True
            }
        
        # 溝通風格
        personality_type = psychological_insights.get('psychological_profile', {}).get('personality_type')
        style_map = {
            'analytical': {'tone': 'professional', 'detail_level': 'high', 'proof_points': True},
            'driver': {'tone': 'direct', 'detail_level': 'summary', 'efficiency_focus': True},
            'amiable': {'tone': 'friendly', 'detail_level': 'moderate', 'reassurance': True},
            'expressive': {'tone': 'enthusiastic', 'detail_level': 'visual', 'excitement': True}
        }
        personalization['communication_style'] = style_map.get(personality_type, style_map['amiable'])
        
        return personalization
    
    async def _optimize_payment_options(
        self,
        cart_contents: Dict,
        customer_profile: Dict,
        psychological_insights: Dict
    ) -> Dict[str, Any]:
        """優化支付選項"""
        recommendations = {
            'primary_method': None,
            'alternative_methods': [],
            'installment_suggestion': None,
            'incentives': [],
            'payment_flow_optimization': {}
        }
        
        total_amount = cart_contents.get('total_amount', 0)
        
        # 推薦主要支付方式
        if customer_profile.get('age', 0) < 35:
            recommendations['primary_method'] = 'digital_wallet'
            recommendations['alternative_methods'] = ['credit_card', 'bank_transfer']
        elif customer_profile.get('corporate_customer', False):
            recommendations['primary_method'] = 'invoice_payment'
            recommendations['alternative_methods'] = ['purchase_order', 'corporate_card']
        else:
            recommendations['primary_method'] = 'credit_card'
            recommendations['alternative_methods'] = ['bank_transfer', 'digital_wallet']
        
        # 分期建議
        if total_amount > 5000 and psychological_insights.get('emotional_state', {}).get('primary_emotion') == 'price_sensitive':
            if total_amount < 10000:
                recommendations['installment_suggestion'] = {
                    'plan': '3_months',
                    'monthly_amount': total_amount / 3,
                    'interest': 0,
                    'messaging': f"只需每月 ${total_amount/3:.0f}，無利息！"
                }
            else:
                recommendations['installment_suggestion'] = {
                    'plan': '6_months',
                    'monthly_amount': total_amount / 6,
                    'interest': 0,
                    'messaging': f"輕鬆分6期，每月只需 ${total_amount/6:.0f}"
                }
        
        # 支付激勵
        if customer_profile.get('payment_history', {}).get('on_time_rate', 1.0) > 0.9:
            recommendations['incentives'].append({
                'type': 'trusted_customer_discount',
                'amount': '3%',
                'message': '信任客戶專享折扣'
            })
        
        if total_amount > 10000:
            recommendations['incentives'].append({
                'type': 'instant_payment_bonus',
                'amount': '5%',
                'message': '立即付款額外優惠5%'
            })
        
        # 支付流程優化
        recommendations['payment_flow_optimization'] = {
            'saved_cards': customer_profile.get('has_saved_payment', False),
            'quick_checkout': total_amount < 3000,
            'payment_verification': 'simplified' if customer_profile.get('trust_score', 0) > 0.8 else 'standard',
            'currency_display': customer_profile.get('preferred_currency', 'HKD')
        }
        
        return recommendations
    
    async def _optimize_booking(
        self,
        customer_profile: Dict,
        package_type: str
    ) -> Dict[str, Any]:
        """優化預約安排"""
        booking_optimization = {
            'recommended_slots': [],
            'availability_forecast': {},
            'smart_suggestions': [],
            'booking_incentives': []
        }
        
        # 推薦時段
        if customer_profile.get('occupation') in ['office_worker', 'executive']:
            booking_optimization['recommended_slots'] = [
                {'day': 'Saturday', 'time': '9:00 AM', 'reason': '週末早晨，避開工作'},
                {'day': 'Friday', 'time': '6:00 PM', 'reason': '下班後，週末前完成'},
                {'day': 'Sunday', 'time': '10:00 AM', 'reason': '週日上午，輕鬆愉快'}
            ]
        elif customer_profile.get('has_young_children', False):
            booking_optimization['recommended_slots'] = [
                {'day': 'Weekday', 'time': '10:00 AM', 'reason': '孩子上學時間'},
                {'day': 'Weekday', 'time': '2:00 PM', 'reason': '下午時段，避開接送'}
            ]
        
        # 可用性預測
        booking_optimization['availability_forecast'] = {
            'next_7_days': {'availability': 'high', 'best_days': ['Tuesday', 'Wednesday']},
            'next_14_days': {'availability': 'medium', 'book_soon': True},
            'next_30_days': {'availability': 'low', 'waitlist_available': True}
        }
        
        # 智能建議
        if package_type == 'comprehensive':
            booking_optimization['smart_suggestions'].append({
                'suggestion': '建議預留3-4小時完成全面檢查',
                'tip': '選擇早上時段，空腹檢查效果最佳'
            })
        
        if customer_profile.get('birthday_month') == datetime.now().month:
            booking_optimization['smart_suggestions'].append({
                'suggestion': '生日月特別優惠！',
                'tip': '生日當月預約享受額外折扣'
            })
        
        # 預約激勵
        booking_optimization['booking_incentives'] = [
            {
                'type': 'early_bird',
                'condition': '提前14天預約',
                'benefit': '優先選擇時段 + 5%折扣'
            },
            {
                'type': 'off_peak',
                'condition': '選擇非繁忙時段',
                'benefit': '快速通道 + 免費升級一項檢查'
            }
        ]
        
        return booking_optimization
    
    async def _apply_conversion_optimization(
        self,
        order_stage: str,
        order_behavior: Dict,
        purchase_barriers: List[Dict]
    ) -> Dict[str, Any]:
        """應用轉換優化策略"""
        strategies = {
            'friction_reduction': [],
            'trust_enhancement': [],
            'urgency_tactics': [],
            'personalization': []
        }
        
        # 減少摩擦策略
        if order_stage == 'checkout':
            strategies['friction_reduction'].extend([
                {
                    'tactic': 'form_simplification',
                    'implementation': '只顯示必要字段，其他設為可選'
                },
                {
                    'tactic': 'guest_checkout',
                    'implementation': '允許不註冊直接購買'
                }
            ])
        
        if any(barrier['type'] == '技術操作困難' for barrier in purchase_barriers):
            strategies['friction_reduction'].append({
                'tactic': 'assisted_checkout',
                'implementation': '提供一鍵通話客服協助'
            })
        
        # 信任增強策略
        if order_behavior.get('decision_pattern') == 'social_proof_seeking':
            strategies['trust_enhancement'].extend([
                {
                    'tactic': 'live_testimonials',
                    'implementation': '顯示最近的真實客戶評價'
                },
                {
                    'tactic': 'trust_badges',
                    'implementation': '突出顯示安全認證和保證'
                }
            ])
        
        # 緊迫感策略
        if order_behavior.get('order_velocity') == 'slow':
            strategies['urgency_tactics'].extend([
                {
                    'tactic': 'limited_time_offer',
                    'implementation': f'優惠倒計時：還剩{3600 - (datetime.now().minute * 60)}秒'
                },
                {
                    'tactic': 'stock_scarcity',
                    'implementation': '顯示剩餘預約名額'
                }
            ])
        
        # 個性化策略
        if order_behavior.get('abandonment_triggers'):
            strategies['personalization'].append({
                'tactic': 'smart_recommendations',
                'implementation': '基於瀏覽歷史的個性化套餐推薦'
            })
        
        return strategies
    
    async def _generate_order_assistance(
        self,
        order_stage: str,
        purchase_barriers: List[Dict],
        customer_profile: Dict
    ) -> List[Dict[str, str]]:
        """生成訂單協助"""
        assistance = []
        
        # 階段特定協助
        stage_assistance = {
            'browsing': [
                {'type': 'proactive_chat', 'message': '需要幫您推薦合適的體檢套餐嗎？'},
                {'type': 'comparison_tool', 'message': '點擊這裡快速比較不同套餐'}
            ],
            'cart': [
                {'type': 'savings_highlight', 'message': '您已節省了$500！'},
                {'type': 'upsell_suggestion', 'message': '加$300升級到全面檢查套餐？'}
            ],
            'checkout': [
                {'type': 'form_help', 'message': '信息已自動填充，請確認'},
                {'type': 'security_assurance', 'message': '🔒 您的信息完全安全'}
            ],
            'payment': [
                {'type': 'payment_options', 'message': '支持多種支付方式'},
                {'type': 'instant_support', 'message': '遇到問題？點擊獲得即時幫助'}
            ]
        }
        
        assistance.extend(stage_assistance.get(order_stage, []))
        
        # 障礙特定協助
        for barrier in purchase_barriers[:2]:  # 處理前兩個主要障礙
            if barrier['type'] == '價格超出預期':
                assistance.append({
                    'type': 'financing_option',
                    'message': '💳 可選擇3-6期免息分期'
                })
            elif barrier['type'] == '首次購買不確定':
                assistance.append({
                    'type': 'guarantee',
                    'message': '✅ 30天滿意保證，不滿意全額退款'
                })
        
        # 個性化協助
        if customer_profile.get('vip_customer', False):
            assistance.append({
                'type': 'vip_service',
                'message': '🌟 VIP客戶專線：400-XXX-XXXX'
            })
        
        return assistance[:4]  # 限制最多4條協助信息
    
    async def _calculate_abandonment_risk(self, context: Dict) -> Dict[str, Any]:
        """計算放棄風險"""
        risk_score = 0.3  # 基礎風險
        risk_factors = []
        
        # 時間因素
        session_duration = context.get('session_data', {}).get('time_on_site', 0)
        if session_duration > 600:  # 超過10分鐘
            risk_score += 0.2
            risk_factors.append('瀏覽時間過長')
        
        # 購物車修改
        cart_modifications = context.get('session_data', {}).get('cart_modifications', 0)
        if cart_modifications > 3:
            risk_score += 0.15
            risk_factors.append('頻繁修改購物車')
        
        # 價格檢查
        price_checks = context.get('session_data', {}).get('price_check_frequency', 0)
        if price_checks > 5:
            risk_score += 0.15
            risk_factors.append('多次查看價格')
        
        # 支付頁面退出
        payment_exits = context.get('session_data', {}).get('payment_page_exits', 0)
        if payment_exits > 0:
            risk_score += 0.25
            risk_factors.append('支付頁面退出過')
        
        # 心理因素
        psychological_readiness = context.get('psychological_insights', {}).get('psychological_readiness_score', 50)
        if psychological_readiness < 40:
            risk_score += 0.1
            risk_factors.append('心理準備度低')
        
        # 風險等級
        if risk_score >= 0.7:
            risk_level = 'high'
        elif risk_score >= 0.5:
            risk_level = 'medium'
        else:
            risk_level = 'low'
        
        return {
            'risk_score': min(risk_score, 1.0),
            'risk_level': risk_level,
            'risk_factors': risk_factors,
            'intervention_needed': risk_score > 0.6,
            'recommended_interventions': self._get_risk_interventions(risk_level, risk_factors)
        }
    
    def _get_risk_interventions(self, risk_level: str, risk_factors: List[str]) -> List[Dict[str, str]]:
        """獲取風險干預措施"""
        interventions = []
        
        if risk_level == 'high':
            interventions.extend([
                {'type': 'exit_intent_popup', 'action': '提供限時優惠碼'},
                {'type': 'live_chat', 'action': '主動發起對話'},
                {'type': 'cart_save', 'action': '保存購物車並發送提醒'}
            ])
        elif risk_level == 'medium':
            interventions.extend([
                {'type': 'trust_building', 'action': '顯示安全保證'},
                {'type': 'social_proof', 'action': '展示實時購買信息'},
                {'type': 'assistance_offer', 'action': '提供幫助按鈕'}
            ])
        
        # 針對特定風險因素
        if '支付頁面退出過' in risk_factors:
            interventions.append({
                'type': 'payment_simplification',
                'action': '簡化支付流程或提供替代方式'
            })
        
        if '價格超出預期' in risk_factors:
            interventions.append({
                'type': 'value_reinforcement',
                'action': '再次強調套餐價值和優惠'
            })
        
        return interventions
    
    def _determine_next_action(self, order_stage: str, purchase_barriers: List[Dict]) -> Dict[str, str]:
        """確定下一步最佳行動"""
        if not purchase_barriers:
            # 無障礙時的標準流程
            stage_actions = {
                'browsing': {'action': 'guide_to_recommendation', 'description': '引導至個性化推薦'},
                'cart': {'action': 'proceed_to_checkout', 'description': '進入結賬流程'},
                'checkout': {'action': 'complete_order', 'description': '完成訂單'},
                'payment': {'action': 'process_payment', 'description': '處理支付'}
            }
            return stage_actions.get(order_stage, {'action': 'continue', 'description': '繼續當前流程'})
        
        # 有障礙時優先處理障礙
        primary_barrier = purchase_barriers[0]
        barrier_actions = {
            '價格超出預期': {'action': 'offer_financing', 'description': '提供分期付款選項'},
            '選擇困難': {'action': 'start_recommendation_quiz', 'description': '開始個性化推薦問卷'},
            '技術操作困難': {'action': 'enable_assisted_mode', 'description': '啟用協助模式'},
            '首次購買不確定': {'action': 'show_guarantees', 'description': '展示保證和承諾'},
            '信息不足': {'action': 'provide_detailed_info', 'description': '提供詳細信息'}
        }
        
        return barrier_actions.get(
            primary_barrier['type'],
            {'action': 'address_barrier', 'description': f"處理{primary_barrier['type']}"}
        )