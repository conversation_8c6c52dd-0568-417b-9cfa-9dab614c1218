from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime, timedelta
from crewai import Agent, Task
from .base_agent import BaseHealthCheckAgent
import json
import re

class FrontlineSalesAgent(BaseHealthCheckAgent):
    """前線銷售 Agent - 直接執行銷售對話和即時應對"""
    
    def __init__(self):
        super().__init__(
            name="Frontline Sales Agent",
            config_path="config/agents/frontline_sales.yaml"
        )
        self.response_templates = self._load_response_templates()
        self.conversation_flows = self._load_conversation_flows()
        self.rapport_builders = self._load_rapport_builders()
        self.power_phrases = self._load_power_phrases()
        
    def _create_agent(self) -> Agent:
        return Agent(
            role=self.config['role'],
            goal=self.config['goal'], 
            backstory=self.config['backstory'],
            tools=[],
            verbose=True,
            max_iter=5
        )
    
    def _load_response_templates(self) -> Dict[str, List[str]]:
        """加載回應模板庫"""
        return {
            "greeting_templates": {
                "first_time": [
                    "您好！歡迎來到森仁醫健💚 我是您的健康顧問{name}，很高興為您服務！請問怎麼稱呼您呢？",
                    "Hi！我是森仁醫健的{name}😊 看到您對我們的健康檢查有興趣，想了解哪方面的信息呢？",
                    "您好！感謝關注森仁醫健🌟 我是{name}，專門負責為您設計最適合的健康檢查方案，方便告訴我您的稱呼嗎？"
                ],
                "returning": [
                    "{customer_name}您好！好久不見了😊 最近身體怎麼樣？是時候安排今年的健康檢查了！",
                    "Hi {customer_name}！我是{name}，又到了年度體檢的時間了～您去年檢查的項目今年要不要升級一下？",
                    "{customer_name}，歡迎回來！💚 您的健康檔案我都有保存，今年想做個更全面的檢查嗎？"
                ],
                "referral": [
                    "您好！是{referrer_name}推薦您來的吧？😊 他/她說您很注重健康，我是{name}，來幫您安排檢查方案！",
                    "Hi！{referrer_name}已經跟我提過您了，說您想了解我們的健康檢查。我準備了一些特別適合您的方案！"
                ]
            },
            "need_discovery": {
                "opening_questions": [
                    "想請問您最近有什麼特別關注的健康問題嗎？",
                    "您上一次做全面體檢是什麼時候呢？",
                    "家裡有哪些疾病史需要特別注意的嗎？",
                    "您現在的工作壓力大嗎？作息規律嗎？"
                ],
                "probing_questions": [
                    "除了{mentioned_concern}，還有其他擔心的地方嗎？",
                    "您提到{symptom}，這種情況持續多久了？",
                    "家人對您做體檢這件事怎麼看？",
                    "如果能早期發現並預防{concern}，對您來說意味著什麼？"
                ],
                "empathy_responses": [
                    "我完全理解您的擔憂，{concern}確實需要重視。",
                    "您的想法很對，預防永遠比治療重要！",
                    "很多客戶都有類似的困擾，您並不孤單。",
                    "您真的很有健康意識，家人有您真幸福！"
                ]
            },
            "value_presentation": {
                "benefit_statements": [
                    "這個套餐最大的價值在於{key_benefit}，特別適合您這種{customer_type}。",
                    "選擇我們，您獲得的不只是一次檢查，而是全年的健康守護。",
                    "相比去醫院排隊的麻煩，我們的VIP通道能為您節省至少3小時。",
                    "早期發現一個小問題，可能為您省下幾十萬的治療費用。"
                ],
                "differentiation": [
                    "我們和普通體檢中心最大的不同是{unique_value}。",
                    "很多客戶選擇我們，就是因為{competitive_advantage}。",
                    "您知道嗎？我們是唯一一家提供{exclusive_service}的機構。"
                ],
                "testimonials": [
                    "上個月王女士通過檢查及時發現了早期問題，現在已經完全康復了！",
                    "有位企業家客戶說，我們的服務讓他節省了寶貴的時間。",
                    "很多家庭選擇我們的年度套餐，一家人的健康都有保障。"
                ]
            },
            "objection_responses": {
                "price_objection": [
                    "我理解您覺得價格有點高。不過想想看，這個投資能為您帶來什麼？健康是無價的！",
                    "確實不便宜，但是分攤到365天，每天只要{daily_cost}元，比一杯咖啡還便宜！",
                    "價格確實是個考慮因素。讓我為您調整一下方案，看看哪些項目是您最需要的。"
                ],
                "time_objection": [
                    "時間確實寶貴！所以我們特設了快速通道，2小時內完成所有檢查。",
                    "理解您很忙。我們有週末營業，還可以為您安排專人陪同，效率最大化。",
                    "其實預防比治療省時間多了。想想看，生病住院要耽誤多少工作？"
                ],
                "trust_objection": [
                    "您的謹慎是對的！我們是政府認證的醫療機構，這是我們的資質...",
                    "理解您的顧慮。要不您先來參觀一下我們的環境？眼見為實！",
                    "我們有30天滿意保證，如果您不滿意，我們全額退款。"
                ],
                "need_objection": [
                    "您覺得自己很健康，這太好了！體檢就是為了保持這種狀態。",
                    "很多疾病早期都沒有症狀，等有感覺時可能就晚了。",
                    "就當是給自己一個安心，也讓家人放心，不好嗎？"
                ]
            },
            "closing_phrases": {
                "soft_close": [
                    "那我先幫您了解一下具體流程，您看可以嗎？",
                    "要不我先幫您查查最近的預約時間？",
                    "您看這個方案是否符合您的需求？"
                ],
                "assumptive_close": [
                    "那我就幫您預約這週六上午9點，{customer_name}您看怎麼樣？",
                    "我現在就為您保留這個優惠價格，請問您的身份證號是？",
                    "檢查報告您是要寄到家裡還是公司呢？"
                ],
                "urgency_close": [
                    "這個優惠只到今晚12點，現在確認還來得及！",
                    "週末的預約位置不多了，要幫您鎖定一個嗎？",
                    "剛剛又有2個人預約了，現在只剩最後{spots}個名額！"
                ],
                "choice_close": [
                    "您是選擇基礎套餐還是全面一點的標準套餐？",
                    "付款的話，是一次付清享受額外5%折扣，還是分期付款？",
                    "您方便週六來還是週日來？"
                ]
            }
        }
    
    def _load_conversation_flows(self) -> Dict[str, Any]:
        """加載對話流程"""
        return {
            "standard_flow": [
                {"stage": "greeting", "objective": "建立融洽關係", "duration": "1-2 exchanges"},
                {"stage": "discovery", "objective": "了解需求和痛點", "duration": "3-5 exchanges"},
                {"stage": "presentation", "objective": "展示價值方案", "duration": "2-4 exchanges"},
                {"stage": "handling", "objective": "處理異議", "duration": "2-3 exchanges"},
                {"stage": "closing", "objective": "促成交易", "duration": "1-3 exchanges"},
                {"stage": "confirmation", "objective": "確認細節", "duration": "1-2 exchanges"}
            ],
            "fast_track_flow": [
                {"stage": "greeting", "objective": "快速建立信任", "duration": "1 exchange"},
                {"stage": "value_prop", "objective": "直接展示核心價值", "duration": "1-2 exchanges"},
                {"stage": "close", "objective": "快速成交", "duration": "1-2 exchanges"}
            ],
            "consultative_flow": [
                {"stage": "rapport", "objective": "深度關係建立", "duration": "2-3 exchanges"},
                {"stage": "diagnosis", "objective": "全面需求診斷", "duration": "5-8 exchanges"},
                {"stage": "education", "objective": "健康教育", "duration": "3-5 exchanges"},
                {"stage": "solution", "objective": "定制方案", "duration": "3-4 exchanges"},
                {"stage": "partnership", "objective": "長期合作", "duration": "2-3 exchanges"}
            ]
        }
    
    def _load_rapport_builders(self) -> List[Dict[str, Any]]:
        """加載建立親和力的技巧"""
        return [
            {
                "technique": "mirroring",
                "description": "鏡像對方的語言風格和節奏",
                "examples": {
                    "formal_customer": "使用專業術語，保持禮貌距離",
                    "casual_customer": "輕鬆對話，適度使用表情符號",
                    "analytical_customer": "提供數據支持，邏輯清晰",
                    "emotional_customer": "表達同理心，分享感受"
                }
            },
            {
                "technique": "common_ground",
                "description": "尋找共同點建立連接",
                "topics": ["家鄉", "興趣愛好", "家庭", "工作行業", "健康目標"]
            },
            {
                "technique": "active_listening",
                "description": "積極傾聽並給予回應",
                "responses": [
                    "您說得很對...",
                    "我注意到您提到...",
                    "聽起來您最關心的是...",
                    "如果我理解正確的話..."
                ]
            },
            {
                "technique": "personalization",
                "description": "個性化稱呼和內容",
                "elements": ["記住名字", "引用之前對話", "了解背景", "客製化建議"]
            }
        ]
    
    def _load_power_phrases(self) -> Dict[str, List[str]]:
        """加載強力話術"""
        return {
            "trust_builders": [
                "我會全程陪同您完成檢查",
                "您的健康資料我們會嚴格保密",
                "我個人也是在這裡做檢查的",
                "很多醫生都推薦他們的家人來我們這裡"
            ],
            "value_enhancers": [
                "這不僅是一次檢查，更是對未來的投資",
                "想像一下，擁有健康的身體能讓您實現多少夢想",
                "您的家人一定會為您的決定感到驕傲",
                "健康是您給自己最好的禮物"
            ],
            "urgency_creators": [
                "機會不等人，健康更是如此",
                "現在的決定，影響未來十年的生活質量",
                "每延遲一天，就是給疾病多一天機會",
                "優惠有期限，但健康沒有"
            ],
            "commitment_getters": [
                "讓我們一起為您的健康努力",
                "您準備好開始這段健康之旅了嗎？",
                "今天的決定，會是您最正確的選擇",
                "我相信您會為今天的決定感到慶幸"
            ],
            "objection_softeners": [
                "我完全理解您的顧慮...",
                "您的想法很有道理，不過...",
                "很多客戶一開始也這麼想...",
                "讓我從另一個角度為您分析..."
            ]
        }
    
    async def process(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """處理前線銷售對話"""
        current_message = context.get('current_message', '')
        conversation_history = context.get('conversation_history', [])
        customer_profile = context.get('customer_profile', {})
        sales_insights = context.get('sales_insights', {})
        psychological_insights = context.get('psychological_insights', {})
        
        # 分析當前對話狀態
        conversation_state = await self._analyze_conversation_state(
            conversation_history, current_message
        )
        
        # 選擇合適的對話流程
        selected_flow = await self._select_conversation_flow(
            customer_profile, psychological_insights, conversation_state
        )
        
        # 生成個性化回應
        response = await self._generate_response(
            conversation_state, selected_flow, context
        )
        
        # 準備下一步行動建議
        next_actions = await self._prepare_next_actions(
            conversation_state, sales_insights, psychological_insights
        )
        
        # 評估對話效果
        effectiveness = await self._evaluate_conversation_effectiveness(
            conversation_history, customer_profile
        )
        
        # 生成實時指導
        real_time_guidance = await self._generate_real_time_guidance(
            conversation_state, psychological_insights, sales_insights
        )
        
        return {
            'response_message': response['message'],
            'response_tone': response['tone'],
            'conversation_flow': selected_flow,
            'current_stage': conversation_state['stage'],
            'next_actions': next_actions,
            'effectiveness_score': effectiveness,
            'real_time_guidance': real_time_guidance,
            'power_phrases_to_use': response.get('power_phrases', []),
            'rapport_level': conversation_state.get('rapport_level', 0),
            'closing_readiness': await self._assess_closing_readiness(context)
        }
    
    async def _analyze_conversation_state(
        self, 
        history: List[Dict],
        current_message: str
    ) -> Dict[str, Any]:
        """分析當前對話狀態"""
        state = {
            'stage': 'greeting',
            'customer_engagement': 'neutral',
            'topic': 'initial',
            'momentum': 'steady',
            'rapport_level': 0,
            'objections_raised': []
        }
        
        if not history:
            return state
        
        # 判斷對話階段
        message_count = len(history)
        if message_count <= 2:
            state['stage'] = 'greeting'
        elif message_count <= 6:
            state['stage'] = 'discovery'
        elif message_count <= 10:
            state['stage'] = 'presentation'
        elif message_count <= 14:
            state['stage'] = 'handling'
        else:
            state['stage'] = 'closing'
        
        # 分析客戶參與度
        recent_messages = history[-3:] if len(history) >= 3 else history
        avg_length = sum(len(msg.get('content', '')) for msg in recent_messages) / len(recent_messages)
        
        if avg_length > 50:
            state['customer_engagement'] = 'high'
        elif avg_length > 20:
            state['customer_engagement'] = 'medium'
        else:
            state['customer_engagement'] = 'low'
        
        # 識別當前話題
        topics = {
            'price': ['價格', '多少錢', '費用', '優惠', '折扣'],
            'time': ['時間', '預約', '什麼時候', '多久', '幾點'],
            'health': ['健康', '檢查', '疾病', '症狀', '不舒服'],
            'process': ['流程', '步驟', '怎麼做', '需要什麼'],
            'comparison': ['比較', '區別', '其他', '別的地方']
        }
        
        for topic, keywords in topics.items():
            if any(keyword in current_message for keyword in keywords):
                state['topic'] = topic
                break
        
        # 評估對話動能
        if len(history) >= 2:
            time_gaps = []  # 實際應用中應該有時間戳
            response_lengths = [len(msg.get('content', '')) for msg in history[-3:]]
            
            if all(length > 30 for length in response_lengths):
                state['momentum'] = 'accelerating'
            elif all(length < 15 for length in response_lengths):
                state['momentum'] = 'declining'
            else:
                state['momentum'] = 'steady'
        
        # 計算親和力水平
        positive_indicators = ['謝謝', '好的', '明白', '理解', '👍', '😊']
        negative_indicators = ['但是', '可是', '不', '沒有', '算了']
        
        all_messages = ' '.join([msg.get('content', '') for msg in history])
        positive_count = sum(1 for indicator in positive_indicators if indicator in all_messages)
        negative_count = sum(1 for indicator in negative_indicators if indicator in all_messages)
        
        state['rapport_level'] = min(max((positive_count - negative_count) / max(len(history), 1), 0), 1)
        
        return state
    
    async def _select_conversation_flow(
        self,
        customer_profile: Dict,
        psychological_insights: Dict,
        conversation_state: Dict
    ) -> Dict[str, Any]:
        """選擇對話流程"""
        # 根據客戶類型選擇流程
        decision_style = psychological_insights.get('decision_style', {}).get('style', 'unknown')
        readiness_score = psychological_insights.get('psychological_readiness_score', 50)
        
        if decision_style == 'driver' or readiness_score > 75:
            selected_flow = self.conversation_flows['fast_track_flow']
            flow_name = 'fast_track'
        elif decision_style == 'analytical' or conversation_state['customer_engagement'] == 'high':
            selected_flow = self.conversation_flows['consultative_flow']
            flow_name = 'consultative'
        else:
            selected_flow = self.conversation_flows['standard_flow']
            flow_name = 'standard'
        
        # 確定當前在流程中的位置
        current_stage = conversation_state['stage']
        stage_index = next((i for i, stage in enumerate(selected_flow) 
                          if stage['stage'] == current_stage), 0)
        
        return {
            'flow_type': flow_name,
            'stages': selected_flow,
            'current_stage_index': stage_index,
            'current_stage_info': selected_flow[stage_index] if stage_index < len(selected_flow) else None,
            'next_stage_info': selected_flow[stage_index + 1] if stage_index + 1 < len(selected_flow) else None,
            'progress': (stage_index + 1) / len(selected_flow)
        }
    
    async def _generate_response(
        self,
        conversation_state: Dict,
        selected_flow: Dict,
        context: Dict
    ) -> Dict[str, Any]:
        """生成個性化回應"""
        stage = conversation_state['stage']
        topic = conversation_state['topic']
        customer_name = context.get('customer_profile', {}).get('name', '您')
        
        response = {
            'message': '',
            'tone': 'professional_friendly',
            'power_phrases': []
        }
        
        # 根據階段選擇回應模板
        if stage == 'greeting':
            if context.get('is_first_time', True):
                templates = self.response_templates['greeting_templates']['first_time']
            else:
                templates = self.response_templates['greeting_templates']['returning']
            response['message'] = templates[0].format(name="小王", customer_name=customer_name)
            
        elif stage == 'discovery':
            if topic == 'health':
                response['message'] = self.response_templates['need_discovery']['empathy_responses'][0]
            else:
                response['message'] = self.response_templates['need_discovery']['opening_questions'][0]
            response['power_phrases'] = self.power_phrases['trust_builders'][:2]
            
        elif stage == 'presentation':
            response['message'] = self.response_templates['value_presentation']['benefit_statements'][0].format(
                key_benefit="全面健康評估",
                customer_type="注重健康的人士"
            )
            response['power_phrases'] = self.power_phrases['value_enhancers'][:2]
            
        elif stage == 'handling':
            # 根據具體異議選擇回應
            if 'price' in str(context.get('current_objections', [])):
                response['message'] = self.response_templates['objection_responses']['price_objection'][0]
            elif 'time' in str(context.get('current_objections', [])):
                response['message'] = self.response_templates['objection_responses']['time_objection'][0]
            else:
                response['message'] = self.response_templates['objection_responses']['need_objection'][0]
            response['power_phrases'] = self.power_phrases['objection_softeners']
            
        elif stage == 'closing':
            readiness = context.get('psychological_insights', {}).get('psychological_readiness_score', 50)
            if readiness > 70:
                response['message'] = self.response_templates['closing_phrases']['assumptive_close'][0].format(
                    customer_name=customer_name
                )
            else:
                response['message'] = self.response_templates['closing_phrases']['soft_close'][0]
            response['power_phrases'] = self.power_phrases['commitment_getters']
        
        # 根據客戶類型調整語氣
        personality = context.get('psychological_insights', {}).get('psychological_profile', {}).get('personality_type')
        if personality == 'analytical':
            response['tone'] = 'professional_detailed'
        elif personality == 'expressive':
            response['tone'] = 'enthusiastic_warm'
        elif personality == 'driver':
            response['tone'] = 'direct_efficient'
        elif personality == 'amiable':
            response['tone'] = 'gentle_supportive'
        
        # 添加個性化元素
        if conversation_state['rapport_level'] > 0.6:
            response['message'] = self._add_personal_touch(response['message'], context)
        
        return response
    
    def _add_personal_touch(self, message: str, context: Dict) -> str:
        """添加個性化元素"""
        customer_profile = context.get('customer_profile', {})
        
        # 添加個人化稱呼
        if customer_profile.get('name'):
            message = message.replace('您', customer_profile['name'])
        
        # 引用之前的對話
        if context.get('previous_concerns'):
            message += f" 剛才您提到的{context['previous_concerns'][0]}，我都記下了。"
        
        # 添加相關性內容
        if customer_profile.get('occupation'):
            occupation_health_tips = {
                'office_worker': "像您這樣的辦公室工作者，頸椎和眼睛檢查特別重要",
                'driver': "職業司機最需要關注心血管和腰椎健康",
                'teacher': "老師們要特別注意咽喉和心理健康"
            }
            if customer_profile['occupation'] in occupation_health_tips:
                message += f" {occupation_health_tips[customer_profile['occupation']]}。"
        
        return message
    
    async def _prepare_next_actions(
        self,
        conversation_state: Dict,
        sales_insights: Dict,
        psychological_insights: Dict
    ) -> List[Dict[str, Any]]:
        """準備下一步行動建議"""
        actions = []
        
        stage = conversation_state['stage']
        engagement = conversation_state['customer_engagement']
        momentum = conversation_state['momentum']
        
        # 基於當前階段的行動
        stage_actions = {
            'greeting': [
                {'action': 'build_rapport', 'priority': 'high', 'method': '找到共同話題'},
                {'action': 'assess_mood', 'priority': 'medium', 'method': '觀察客戶情緒'}
            ],
            'discovery': [
                {'action': 'dig_deeper', 'priority': 'high', 'method': '使用開放式問題'},
                {'action': 'show_empathy', 'priority': 'high', 'method': '表達理解和關心'}
            ],
            'presentation': [
                {'action': 'customize_solution', 'priority': 'high', 'method': '根據需求調整方案'},
                {'action': 'build_value', 'priority': 'high', 'method': '強調獨特價值'}
            ],
            'handling': [
                {'action': 'address_concerns', 'priority': 'high', 'method': '直接回應異議'},
                {'action': 'provide_proof', 'priority': 'medium', 'method': '提供證據支持'}
            ],
            'closing': [
                {'action': 'create_urgency', 'priority': 'high', 'method': '使用稀缺性'},
                {'action': 'confirm_commitment', 'priority': 'high', 'method': '獲得明確承諾'}
            ]
        }
        
        actions.extend(stage_actions.get(stage, []))
        
        # 基於客戶狀態的額外行動
        if engagement == 'low':
            actions.append({
                'action': 're_engage', 
                'priority': 'urgent',
                'method': '改變話題或提出引人注目的信息'
            })
        
        if momentum == 'declining':
            actions.append({
                'action': 'inject_energy',
                'priority': 'high',
                'method': '分享成功案例或限時優惠'
            })
        
        # 基於心理洞察的行動
        if psychological_insights.get('psychological_readiness_score', 0) > 70:
            actions.append({
                'action': 'push_for_close',
                'priority': 'high',
                'method': '使用假設成交法'
            })
        
        return sorted(actions, key=lambda x: {'urgent': 0, 'high': 1, 'medium': 2}[x['priority']])
    
    async def _evaluate_conversation_effectiveness(
        self,
        history: List[Dict],
        customer_profile: Dict
    ) -> float:
        """評估對話效果"""
        if not history:
            return 0.5
        
        effectiveness_score = 0.5  # 基礎分數
        
        # 對話長度合理性 (過短或過長都不好)
        optimal_length = 12
        length_diff = abs(len(history) - optimal_length)
        effectiveness_score += (1 - min(length_diff / optimal_length, 1)) * 0.2
        
        # 客戶參與度
        customer_messages = [msg for msg in history if msg.get('role') == 'customer']
        if customer_messages:
            avg_customer_length = sum(len(msg.get('content', '')) for msg in customer_messages) / len(customer_messages)
            engagement_score = min(avg_customer_length / 50, 1)
            effectiveness_score += engagement_score * 0.3
        
        # 積極情緒比例
        positive_keywords = ['好', '可以', '謝謝', '明白', '想要', '什麼時候']
        negative_keywords = ['不', '沒有', '算了', '再說', '考慮', '貴']
        
        all_text = ' '.join([msg.get('content', '') for msg in history])
        positive_ratio = sum(1 for kw in positive_keywords if kw in all_text) / len(positive_keywords)
        negative_ratio = sum(1 for kw in negative_keywords if kw in all_text) / len(negative_keywords)
        
        sentiment_score = (positive_ratio - negative_ratio + 1) / 2
        effectiveness_score += sentiment_score * 0.3
        
        # 階段進展
        if len(history) > 6:  # 應該已經過了發現階段
            if any('價格' in msg.get('content', '') or '預約' in msg.get('content', '') 
                  for msg in history[-3:]):
                effectiveness_score += 0.2  # 進入了決策階段
        
        return min(max(effectiveness_score, 0), 1)
    
    async def _generate_real_time_guidance(
        self,
        conversation_state: Dict,
        psychological_insights: Dict,
        sales_insights: Dict
    ) -> List[str]:
        """生成實時指導"""
        guidance = []
        
        # 基於對話階段的指導
        stage = conversation_state['stage']
        if stage == 'discovery':
            guidance.append("💡 多使用開放式問題，讓客戶多說話")
        elif stage == 'presentation':
            guidance.append("💡 將功能轉化為對客戶的具體好處")
        elif stage == 'closing':
            guidance.append("💡 使用選擇性問題，不要問是否要購買")
        
        # 基於客戶狀態的指導
        if conversation_state['customer_engagement'] == 'low':
            guidance.append("⚠️ 客戶參與度低，嘗試問一個引人入勝的問題")
        
        if conversation_state['momentum'] == 'declining':
            guidance.append("⚠️ 對話失去動力，注入一些能量或緊迫感")
        
        # 基於心理洞察的指導
        emotion = psychological_insights.get('emotional_state', {}).get('primary_emotion')
        if emotion == 'skeptical':
            guidance.append("🎯 客戶有疑慮，提供更多證據和保證")
        elif emotion == 'interested':
            guidance.append("🎯 客戶有興趣，現在推進到下一步")
        
        # 基於銷售機會的指導
        opportunities = sales_insights.get('current_opportunities', [])
        if any(opp['type'] == 'price_inquiry' for opp in opportunities):
            guidance.append("💰 客戶詢問價格，先建立價值再報價")
        
        # 技巧提醒
        if conversation_state['rapport_level'] < 0.3:
            guidance.append("🤝 親和力不足，嘗試找到共同點")
        
        if len(conversation_state.get('objections_raised', [])) > 2:
            guidance.append("🛡️ 多個異議，可能需要重新建立信任")
        
        return guidance[:4]  # 限制最多4條指導，避免信息過載
    
    async def _assess_closing_readiness(self, context: Dict) -> Dict[str, Any]:
        """評估成交準備度"""
        readiness_indicators = {
            'buying_signals': 0,
            'objections_resolved': 0,
            'value_understood': False,
            'urgency_felt': False,
            'trust_established': False
        }
        
        conversation_history = context.get('conversation_history', [])
        if not conversation_history:
            return {
                'ready_to_close': False,
                'readiness_score': 0,
                'missing_elements': list(readiness_indicators.keys())
            }
        
        # 檢查購買信號
        buying_signal_keywords = ['什麼時候', '預約', '怎麼付款', '多少錢', '可以']
        recent_messages = ' '.join([msg.get('content', '') for msg in conversation_history[-5:]])
        
        for keyword in buying_signal_keywords:
            if keyword in recent_messages:
                readiness_indicators['buying_signals'] += 1
        
        # 檢查異議是否已解決
        if context.get('unresolved_objections', []):
            readiness_indicators['objections_resolved'] = 0
        else:
            readiness_indicators['objections_resolved'] = 1
        
        # 檢查價值理解
        if any('明白' in msg.get('content', '') or '理解' in msg.get('content', '') 
              for msg in conversation_history[-3:]):
            readiness_indicators['value_understood'] = True
        
        # 檢查緊迫感
        if any('盡快' in msg.get('content', '') or '馬上' in msg.get('content', '')
              for msg in conversation_history[-3:]):
            readiness_indicators['urgency_felt'] = True
        
        # 檢查信任度
        psychological_insights = context.get('psychological_insights', {})
        if psychological_insights.get('psychological_profile', {}).get('trust_level', 0) > 0.5:
            readiness_indicators['trust_established'] = True
        
        # 計算準備度分數
        score = 0
        score += min(readiness_indicators['buying_signals'] * 0.2, 0.4)
        score += readiness_indicators['objections_resolved'] * 0.2
        score += 0.2 if readiness_indicators['value_understood'] else 0
        score += 0.1 if readiness_indicators['urgency_felt'] else 0  
        score += 0.1 if readiness_indicators['trust_established'] else 0
        
        # 確定缺失元素
        missing_elements = []
        if readiness_indicators['buying_signals'] < 2:
            missing_elements.append('需要更多購買信號')
        if not readiness_indicators['objections_resolved']:
            missing_elements.append('還有未解決的異議')
        if not readiness_indicators['value_understood']:
            missing_elements.append('客戶可能還不理解價值')
        if not readiness_indicators['trust_established']:
            missing_elements.append('信任度不足')
        
        return {
            'ready_to_close': score > 0.7,
            'readiness_score': score,
            'indicators': readiness_indicators,
            'missing_elements': missing_elements,
            'recommended_closing_technique': self._recommend_closing_technique(score, context)
        }
    
    def _recommend_closing_technique(self, readiness_score: float, context: Dict) -> str:
        """推薦成交技巧"""
        if readiness_score > 0.8:
            return "assumptive_close"
        elif readiness_score > 0.6:
            return "alternative_close"
        elif readiness_score > 0.4:
            return "trial_close"
        else:
            return "soft_close"