from abc import ABC, abstractmethod
from typing import Dict, Any, Optional
from crewai import Agent
import yaml

class BaseHealthCheckAgent(ABC):
    """所有 Agent 的基礎類"""
    
    def __init__(self, name: str, config_path: str):
        self.name = name
        self.config = self._load_config(config_path)
        self.agent = self._create_agent()
        
    def _load_config(self, config_path: str) -> Dict[str, Any]:
        """加載配置文件"""
        with open(config_path, 'r', encoding='utf-8') as f:
            return yaml.safe_load(f)
    
    @abstractmethod
    def _create_agent(self) -> Agent:
        """創建 CrewAI Agent"""
        pass
    
    @abstractmethod
    async def process(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """處理邏輯"""
        pass