from typing import Dict, Any, List
from datetime import datetime
from crewai import Agent, Task
from .base_agent import BaseHealthCheckAgent

class TrafficReceptionAgent(BaseHealthCheckAgent):
    """流量接待 Agent - 負責初次接待和客戶分類"""
    
    def __init__(self):
        super().__init__(
            name="Traffic Reception Agent",
            config_path="config/agents/traffic_reception.yaml"
        )
        self.greeting_templates = self._load_greeting_templates()
        
    def _create_agent(self) -> Agent:
        """創建 CrewAI Agent"""
        return Agent(
            role=self.config['role'],
            goal=self.config['goal'],
            backstory=self.config['backstory'],
            tools=[],  # 暫時不需要工具
            verbose=True,
            max_iter=3
        )
    
    def _load_greeting_templates(self) -> Dict[str, str]:
        """加載問候語模板"""
        return {
            "first_time": """您好！歡迎來到森仁醫健 👋
我是您的專屬健康顧問，很高興為您服務！

請問您是想了解：
1️⃣ 個人體檢套餐
2️⃣ 家庭體檢計劃
3️⃣ 企業團體體檢
4️⃣ 特定檢查項目

請回復數字或直接告訴我您的需求~""",
            
            "returning": """歡迎回來！{customer_name} 😊
上次您諮詢的是{last_inquiry}，請問這次有什麼可以幫助您的嗎？

🔥 最新優惠：本月體檢套餐全線88折！""",
            
            "from_ad": """您好！看到您是從{ad_source}了解到我們的 👍
{ad_campaign}現正進行中！

請問您對哪個體檢項目感興趣呢？我可以為您詳細介紹並計算優惠價格。"""
        }
    
    async def process(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """處理客戶接待"""
        customer_info = context.get('customer_info', {})
        message = context.get('message', '')
        source = context.get('source', 'organic')
        
        # 分析客戶類型
        customer_type = await self._analyze_customer_type(customer_info, source)
        
        # 生成個性化問候
        greeting = await self._generate_greeting(customer_type, customer_info, source)
        
        # 初步意圖識別
        intent = await self._identify_intent(message)
        
        # 準備上下文給下一個 Agent
        return {
            'customer_type': customer_type,
            'intent': intent,
            'greeting': greeting,
            'next_agent': self._determine_next_agent(intent),
            'context': {
                **context,
                'reception_time': datetime.now().isoformat(),
                'identified_needs': intent.get('needs', [])
            }
        }
    
    async def _analyze_customer_type(self, customer_info: Dict, source: str) -> str:
        """分析客戶類型"""
        if customer_info.get('previous_purchases'):
            return 'returning'
        elif source in ['facebook_ad', 'google_ad']:
            return 'from_ad'
        else:
            return 'first_time'
    
    async def _generate_greeting(self, customer_type: str, customer_info: Dict, source: str) -> str:
        """生成個性化問候語"""
        template = self.greeting_templates.get(customer_type, self.greeting_templates['first_time'])
        
        # 填充模板變量
        if customer_type == 'returning':
            return template.format(
                customer_name=customer_info.get('name', ''),
                last_inquiry=customer_info.get('last_inquiry', '體檢套餐')
            )
        elif customer_type == 'from_ad':
            return template.format(
                ad_source=self._translate_source(source),
                ad_campaign=self._get_campaign_name(source)
            )
        
        return template
    
    async def _identify_intent(self, message: str) -> Dict[str, Any]:
        """識別客戶意圖"""
        # 簡單的關鍵詞匹配，後續可以用 NLP 模型
        intents = {
            'price_inquiry': ['價格', '多少錢', '費用', '收費', 'price'],
            'package_info': ['套餐', '檢查項目', '包含', '體檢'],
            'booking': ['預約', '預訂', 'book', '時間'],
            'location': ['地址', '位置', '在哪', '怎麼去'],
            'report': ['報告', '結果', '多久'],
        }
        
        message_lower = message.lower()
        identified_intents = []
        
        for intent, keywords in intents.items():
            if any(keyword in message_lower for keyword in keywords):
                identified_intents.append(intent)
        
        # 如果沒有識別到明確意圖，默認為諮詢套餐
        if not identified_intents:
            identified_intents = ['package_info']
        
        return {
            'primary_intent': identified_intents[0],
            'all_intents': identified_intents,
            'confidence': 0.8 if identified_intents else 0.3
        }
    
    def _determine_next_agent(self, intent: Dict[str, Any]) -> str:
        """決定下一個處理的 Agent"""
        primary_intent = intent.get('primary_intent')
        
        intent_to_agent = {
            'price_inquiry': 'pricing_calculator',
            'package_info': 'sales_progression',
            'booking': 'sales_progression',
            'location': 'sales_progression',
            'report': 'quality_monitor'
        }
        
        return intent_to_agent.get(primary_intent, 'sales_progression')
    
    def _translate_source(self, source: str) -> str:
        """翻譯流量來源"""
        translations = {
            'facebook_ad': 'Facebook廣告',
            'google_ad': 'Google搜索',
            'instagram': 'Instagram',
            'whatsapp': 'WhatsApp'
        }
        return translations.get(source, source)
    
    def _get_campaign_name(self, source: str) -> str:
        """獲取活動名稱"""
        # 實際應該從數據庫或配置讀取
        campaigns = {
            'facebook_ad': '【限時優惠】全面體檢套餐買一送一',
            'google_ad': '【新客專享】首次體檢立減$500'
        }
        return campaigns.get(source, '本月特惠活動')