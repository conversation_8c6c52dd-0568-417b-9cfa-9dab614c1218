from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime
import asyncio
from dataclasses import dataclass
import json

@dataclass
class AgentInsight:
    """代理洞察數據結構"""
    agent_name: str
    insights: Dict[str, Any]
    confidence: float
    recommendations: List[str]
    timestamp: datetime

class EnhancedSalesOrchestrator:
    """增強型銷售協調器 - 整合所有銷售代理進行深度分析"""
    
    def __init__(self):
        self.agents = self._initialize_agents()
        self.analysis_history = []
        self.performance_metrics = self._initialize_metrics()
        
    def _initialize_agents(self) -> Dict[str, Any]:
        """初始化所有銷售代理"""
        from .psychology_expert_agent import PsychologyExpertAgent
        from .sales_expert_agent import SalesExpertAgent
        from .frontline_sales_agent import FrontlineSalesAgent
        from .online_order_agent import OnlineOrderAgent
        from .sales_progression_agent import SalesProgressionAgent
        from .follow_up_strategy_agent import FollowUpStrategyAgent
        
        return {
            'psychology_expert': PsychologyExpertAgent(),
            'sales_expert': SalesExpertAgent(),
            'frontline_sales': FrontlineSalesAgent(),
            'online_order': OnlineOrderAgent(),
            'sales_progression': SalesProgressionAgent(),
            'follow_up_strategy': FollowUpStrategyAgent()
        }
    
    def _initialize_metrics(self) -> Dict[str, Any]:
        """初始化性能指標"""
        return {
            'total_analyses': 0,
            'successful_conversions': 0,
            'average_readiness_score': 0,
            'agent_performance': {agent: {'calls': 0, 'avg_confidence': 0} 
                               for agent in self.agents.keys()},
            'insights_generated': 0,
            'recommendations_accepted': 0
        }
    
    async def analyze_conversation(
        self, 
        conversation_context: Dict[str, Any],
        analysis_depth: str = 'comprehensive'
    ) -> Dict[str, Any]:
        """對話深度分析 - 協調所有代理進行全方位分析"""
        
        # 第一階段：心理分析（基礎）
        psychology_insights = await self._get_psychology_insights(conversation_context)
        
        # 第二階段：並行分析（基於心理洞察）
        enhanced_context = {**conversation_context, 'psychological_insights': psychology_insights}
        
        parallel_analyses = await asyncio.gather(
            self._get_sales_strategy(enhanced_context),
            self._get_frontline_guidance(enhanced_context),
            self._get_order_optimization(enhanced_context),
            self._get_progression_plan(enhanced_context),
            self._get_followup_strategy(enhanced_context)
        )
        
        sales_strategy, frontline_guidance, order_optimization, progression_plan, followup_strategy = parallel_analyses
        
        # 第三階段：綜合分析與決策
        synthesis = await self._synthesize_insights(
            psychology_insights,
            sales_strategy,
            frontline_guidance,
            order_optimization,
            progression_plan,
            followup_strategy,
            conversation_context
        )
        
        # 第四階段：生成行動計劃
        action_plan = await self._generate_action_plan(synthesis, analysis_depth)
        
        # 更新指標
        self._update_metrics(synthesis)
        
        # 記錄分析歷史
        self._record_analysis(conversation_context, synthesis, action_plan)
        
        return {
            'timestamp': datetime.now().isoformat(),
            'analysis_id': self._generate_analysis_id(),
            'conversation_summary': self._summarize_conversation(conversation_context),
            'multi_agent_insights': {
                'psychology': psychology_insights,
                'sales_strategy': sales_strategy,
                'frontline_execution': frontline_guidance,
                'order_optimization': order_optimization,
                'progression_tactics': progression_plan,
                'followup_plan': followup_strategy
            },
            'synthesis': synthesis,
            'action_plan': action_plan,
            'success_probability': synthesis['overall_success_probability'],
            'key_risks': synthesis['identified_risks'],
            'critical_actions': action_plan['immediate_actions']
        }
    
    async def _get_psychology_insights(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """獲取心理分析洞察"""
        result = await self.agents['psychology_expert'].process(context)
        
        return {
            'profile': result.get('psychological_profile'),
            'decision_style': result.get('decision_style'),
            'emotional_state': result.get('emotional_state'),
            'readiness_score': result.get('psychological_readiness_score'),
            'barriers': result.get('purchase_barriers'),
            'triggers': result.get('key_triggers'),
            'approach': result.get('recommended_approach')
        }
    
    async def _get_sales_strategy(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """獲取銷售策略"""
        result = await self.agents['sales_expert'].process(context)
        
        return {
            'framework': result.get('sales_framework'),
            'opportunities': result.get('current_opportunities'),
            'negotiation': result.get('negotiation_strategy'),
            'closing': result.get('closing_approach'),
            'success_probability': result.get('success_probability'),
            'tactical_recommendations': result.get('tactical_recommendations')
        }
    
    async def _get_frontline_guidance(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """獲取前線執行指導"""
        result = await self.agents['frontline_sales'].process(context)
        
        return {
            'response': result.get('response_message'),
            'tone': result.get('response_tone'),
            'flow': result.get('conversation_flow'),
            'next_actions': result.get('next_actions'),
            'real_time_tips': result.get('real_time_guidance'),
            'closing_readiness': result.get('closing_readiness')
        }
    
    async def _get_order_optimization(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """獲取訂單優化建議"""
        result = await self.agents['online_order'].process(context)
        
        return {
            'workflow': result.get('optimal_workflow'),
            'barriers': result.get('purchase_barriers'),
            'payment_options': result.get('payment_recommendations'),
            'booking': result.get('booking_optimization'),
            'abandonment_risk': result.get('abandonment_risk'),
            'conversion_strategies': result.get('conversion_strategies')
        }
    
    async def _get_progression_plan(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """獲取銷售推進計劃"""
        result = await self.agents['sales_progression'].process(context)
        
        return {
            'current_stage': result.get('next_stage'),
            'tactics_used': result.get('tactics_used'),
            'readiness_score': result.get('readiness_score'),
            'response': result.get('response'),
            'objections_identified': result.get('context', {}).get('objections_identified', [])
        }
    
    async def _get_followup_strategy(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """獲取跟進策略"""
        result = await self.agents['follow_up_strategy'].process(context)
        
        return {
            'plan': result.get('follow_up_plan'),
            'segment': result.get('customer_segment'),
            'engagement_score': result.get('engagement_score'),
            'churn_risk': result.get('churn_risk'),
            'optimal_timing': result.get('optimal_timing'),
            'next_action': result.get('next_action')
        }
    
    async def _synthesize_insights(
        self,
        psychology: Dict,
        sales_strategy: Dict,
        frontline: Dict,
        order: Dict,
        progression: Dict,
        followup: Dict,
        original_context: Dict
    ) -> Dict[str, Any]:
        """綜合所有洞察生成統一見解"""
        
        # 計算整體成功概率（加權平均）
        success_scores = {
            'psychological_readiness': psychology.get('readiness_score', 50) / 100,
            'sales_probability': sales_strategy.get('success_probability', 0.5),
            'closing_readiness': frontline.get('closing_readiness', {}).get('readiness_score', 0.5),
            'order_conversion': 1 - order.get('abandonment_risk', {}).get('risk_score', 0.5),
            'progression_readiness': progression.get('readiness_score', 50) / 100
        }
        
        weights = {
            'psychological_readiness': 0.25,
            'sales_probability': 0.20,
            'closing_readiness': 0.20,
            'order_conversion': 0.20,
            'progression_readiness': 0.15
        }
        
        overall_probability = sum(
            score * weights[key] for key, score in success_scores.items()
        )
        
        # 識別關鍵風險
        risks = []
        
        if psychology.get('barriers'):
            for barrier in psychology['barriers'][:2]:
                risks.append({
                    'type': 'psychological',
                    'description': barrier.get('type'),
                    'severity': barrier.get('probability', 0.5)
                })
        
        if order.get('abandonment_risk', {}).get('risk_level') == 'high':
            risks.append({
                'type': 'abandonment',
                'description': '高購物車放棄風險',
                'severity': 0.8
            })
        
        if followup.get('churn_risk') == 'high':
            risks.append({
                'type': 'churn',
                'description': '高流失風險',
                'severity': 0.7
            })
        
        # 確定客戶類型和最佳方法
        customer_archetype = self._determine_customer_archetype(
            psychology, sales_strategy, order
        )
        
        # 整合關鍵洞察
        key_insights = {
            'customer_mindset': f"{psychology.get('emotional_state', {}).get('primary_emotion', 'neutral')} - {psychology.get('decision_style', {}).get('style', 'unknown')}",
            'sales_readiness': self._categorize_readiness(overall_probability),
            'primary_motivation': psychology.get('profile', {}).get('primary_motivations', []),
            'biggest_obstacle': risks[0]['description'] if risks else 'None identified',
            'recommended_approach': self._determine_unified_approach(
                psychology, sales_strategy, frontline
            )
        }
        
        return {
            'overall_success_probability': overall_probability,
            'success_factors': success_scores,
            'identified_risks': sorted(risks, key=lambda x: x['severity'], reverse=True),
            'customer_archetype': customer_archetype,
            'key_insights': key_insights,
            'agent_consensus': self._check_agent_consensus(
                psychology, sales_strategy, frontline, progression
            )
        }
    
    def _determine_customer_archetype(
        self,
        psychology: Dict,
        sales_strategy: Dict,
        order: Dict
    ) -> Dict[str, Any]:
        """確定客戶原型"""
        personality = psychology.get('profile', {}).get('personality_type', 'unknown')
        decision_style = psychology.get('decision_style', {}).get('style', 'unknown')
        order_behavior = order.get('workflow', {}).get('selected_workflow', 'standard')
        
        archetype_map = {
            ('analytical', 'analytical', 'standard'): 'The Researcher',
            ('driver', 'driver', 'express'): 'The Executive',
            ('amiable', 'amiable', 'guided'): 'The Relationship Builder',
            ('expressive', 'expressive', 'express'): 'The Enthusiast',
            ('cautious_optimizer', 'analytical', 'standard'): 'The Careful Planner',
            ('spontaneous_buyer', 'driver', 'express'): 'The Quick Decider',
            ('relationship_oriented', 'amiable', 'guided'): 'The Trust Seeker',
            ('value_seeker', 'analytical', 'standard'): 'The Value Hunter'
        }
        
        archetype_key = (personality, decision_style, order_behavior)
        archetype_name = archetype_map.get(archetype_key, 'The Explorer')
        
        return {
            'name': archetype_name,
            'characteristics': self._get_archetype_characteristics(archetype_name),
            'preferred_approach': self._get_archetype_approach(archetype_name)
        }
    
    def _get_archetype_characteristics(self, archetype: str) -> List[str]:
        """獲取原型特徵"""
        characteristics = {
            'The Researcher': ['詳細分析', '數據驅動', '謹慎決策', '需要完整信息'],
            'The Executive': ['快速決策', '效率優先', '結果導向', '重視時間'],
            'The Relationship Builder': ['信任重要', '需要連接', '重視服務', '長期關係'],
            'The Enthusiast': ['情感驅動', '喜歡新體驗', '社交分享', '即時滿足'],
            'The Careful Planner': ['風險規避', '多重比較', '需要保證', '慢熱型'],
            'The Quick Decider': ['衝動購買', '直覺判斷', '行動快速', '不喜歡等待'],
            'The Trust Seeker': ['需要認可', '重視推薦', '安全第一', '群體認同'],
            'The Value Hunter': ['價格敏感', 'CP值導向', '善於比較', '理性分析']
        }
        return characteristics.get(archetype, ['探索型客戶', '需要引導'])
    
    def _get_archetype_approach(self, archetype: str) -> str:
        """獲取原型最佳方法"""
        approaches = {
            'The Researcher': '提供詳盡數據和比較分析',
            'The Executive': '直接展示核心價值和ROI',
            'The Relationship Builder': '建立個人連接和信任',
            'The Enthusiast': '創造興奮感和獨特體驗',
            'The Careful Planner': '逐步建立信心和消除風險',
            'The Quick Decider': '簡化流程立即行動',
            'The Trust Seeker': '展示社會認同和保證',
            'The Value Hunter': '強調性價比和優惠'
        }
        return approaches.get(archetype, '個性化引導和探索需求')
    
    def _categorize_readiness(self, probability: float) -> str:
        """分類準備度"""
        if probability >= 0.8:
            return "High - Ready to close"
        elif probability >= 0.6:
            return "Medium - Needs nurturing"
        elif probability >= 0.4:
            return "Low - Building interest"
        else:
            return "Very Low - Early stage"
    
    def _determine_unified_approach(
        self,
        psychology: Dict,
        sales_strategy: Dict,
        frontline: Dict
    ) -> str:
        """確定統一方法"""
        # 綜合各代理建議
        approaches = []
        
        if psychology.get('approach'):
            approaches.append(psychology['approach'])
        
        if sales_strategy.get('framework', {}).get('framework'):
            approaches.append(f"使用{sales_strategy['framework']['framework']}框架")
        
        if frontline.get('tone'):
            approaches.append(f"以{frontline['tone']}語氣溝通")
        
        return " + ".join(approaches) if approaches else "標準銷售流程"
    
    def _check_agent_consensus(
        self,
        psychology: Dict,
        sales_strategy: Dict,
        frontline: Dict,
        progression: Dict
    ) -> Dict[str, Any]:
        """檢查代理共識"""
        readiness_scores = [
            psychology.get('readiness_score', 50),
            sales_strategy.get('success_probability', 0.5) * 100,
            frontline.get('closing_readiness', {}).get('readiness_score', 0.5) * 100,
            progression.get('readiness_score', 50)
        ]
        
        avg_readiness = sum(readiness_scores) / len(readiness_scores)
        variance = sum((score - avg_readiness) ** 2 for score in readiness_scores) / len(readiness_scores)
        
        consensus_level = 'high' if variance < 100 else 'medium' if variance < 400 else 'low'
        
        return {
            'level': consensus_level,
            'average_readiness': avg_readiness,
            'variance': variance,
            'interpretation': self._interpret_consensus(consensus_level)
        }
    
    def _interpret_consensus(self, level: str) -> str:
        """解釋共識水平"""
        interpretations = {
            'high': '所有代理高度一致，策略明確',
            'medium': '代理基本一致，需要微調',
            'low': '代理意見分歧，需要更多信息'
        }
        return interpretations.get(level, '需要進一步分析')
    
    async def _generate_action_plan(
        self,
        synthesis: Dict[str, Any],
        analysis_depth: str
    ) -> Dict[str, Any]:
        """生成行動計劃"""
        plan = {
            'immediate_actions': [],
            'short_term_actions': [],
            'long_term_actions': [],
            'contingency_plans': []
        }
        
        # 立即行動（0-15分鐘）
        success_prob = synthesis['overall_success_probability']
        
        if success_prob >= 0.7:
            plan['immediate_actions'].extend([
                {
                    'action': 'push_for_close',
                    'description': '立即推進成交',
                    'tactics': ['假設成交法', '限時優惠', '立即預約']
                },
                {
                    'action': 'remove_final_barriers',
                    'description': '消除最後顧慮',
                    'tactics': ['滿意保證', '靈活付款', 'VIP服務']
                }
            ])
        elif success_prob >= 0.5:
            plan['immediate_actions'].extend([
                {
                    'action': 'build_value',
                    'description': '強化價值認知',
                    'tactics': ['ROI展示', '成功案例', '獨特優勢']
                },
                {
                    'action': 'create_urgency',
                    'description': '創造適度緊迫感',
                    'tactics': ['限量名額', '價格優勢', '季節因素']
                }
            ])
        else:
            plan['immediate_actions'].extend([
                {
                    'action': 'establish_trust',
                    'description': '建立基礎信任',
                    'tactics': ['專業展示', '客戶見證', '資質證明']
                },
                {
                    'action': 'understand_needs',
                    'description': '深入了解需求',
                    'tactics': ['開放問題', '需求探索', '痛點挖掘']
                }
            ])
        
        # 短期行動（1-7天）
        plan['short_term_actions'] = self._generate_short_term_actions(
            synthesis, analysis_depth
        )
        
        # 長期行動（7-30天）
        plan['long_term_actions'] = self._generate_long_term_actions(
            synthesis
        )
        
        # 應急計劃
        for risk in synthesis['identified_risks'][:3]:
            plan['contingency_plans'].append({
                'risk': risk['description'],
                'mitigation': self._get_risk_mitigation(risk),
                'trigger': f"當{risk['description']}出現時"
            })
        
        return plan
    
    def _generate_short_term_actions(
        self,
        synthesis: Dict[str, Any],
        depth: str
    ) -> List[Dict[str, Any]]:
        """生成短期行動"""
        actions = []
        
        if synthesis['key_insights']['sales_readiness'] == 'High - Ready to close':
            actions.extend([
                {
                    'day': 1,
                    'action': 'follow_up_close',
                    'channel': 'phone',
                    'objective': '完成訂單確認'
                },
                {
                    'day': 2,
                    'action': 'onboarding_preparation',
                    'channel': 'email',
                    'objective': '準備客戶體驗'
                }
            ])
        else:
            actions.extend([
                {
                    'day': 1,
                    'action': 'value_reinforcement',
                    'channel': 'whatsapp',
                    'objective': '強化價值認知'
                },
                {
                    'day': 3,
                    'action': 'objection_handling',
                    'channel': 'phone',
                    'objective': '處理主要顧慮'
                },
                {
                    'day': 5,
                    'action': 'special_offer',
                    'channel': 'email',
                    'objective': '提供限時優惠'
                }
            ])
        
        return actions
    
    def _generate_long_term_actions(self, synthesis: Dict[str, Any]) -> List[Dict[str, Any]]:
        """生成長期行動"""
        return [
            {
                'week': 2,
                'action': 'relationship_building',
                'description': '持續建立信任關係',
                'tactics': ['健康資訊分享', '個人化關懷', '專屬優惠']
            },
            {
                'week': 3,
                'action': 'education_nurturing',
                'description': '教育培育',
                'tactics': ['健康知識', '預防重要性', '檢查必要性']
            },
            {
                'week': 4,
                'action': 're_engagement',
                'description': '重新激活興趣',
                'tactics': ['新優惠方案', '季節性推廣', '推薦計劃']
            }
        ]
    
    def _get_risk_mitigation(self, risk: Dict[str, Any]) -> str:
        """獲取風險緩解措施"""
        mitigations = {
            'psychological': '調整溝通方式，建立更多信任',
            'abandonment': '簡化購買流程，提供即時協助',
            'churn': '加強跟進頻率，提供專屬優惠',
            'trust': '展示更多證據，提供試用保證',
            'price': '詳細解釋價值，提供靈活付款'
        }
        
        risk_type = risk.get('type', 'general')
        return mitigations.get(risk_type, '提供個性化解決方案')
    
    def _update_metrics(self, synthesis: Dict[str, Any]) -> None:
        """更新性能指標"""
        self.performance_metrics['total_analyses'] += 1
        
        # 更新平均準備度分數
        current_avg = self.performance_metrics['average_readiness_score']
        new_score = synthesis['overall_success_probability'] * 100
        self.performance_metrics['average_readiness_score'] = (
            (current_avg * (self.performance_metrics['total_analyses'] - 1) + new_score) /
            self.performance_metrics['total_analyses']
        )
        
        # 更新洞察計數
        self.performance_metrics['insights_generated'] += len(synthesis['key_insights'])
    
    def _record_analysis(
        self,
        context: Dict[str, Any],
        synthesis: Dict[str, Any],
        action_plan: Dict[str, Any]
    ) -> None:
        """記錄分析歷史"""
        record = {
            'timestamp': datetime.now(),
            'conversation_id': context.get('conversation_id'),
            'customer_id': context.get('customer_id'),
            'success_probability': synthesis['overall_success_probability'],
            'customer_archetype': synthesis['customer_archetype']['name'],
            'key_insights': synthesis['key_insights'],
            'action_plan_summary': len(action_plan['immediate_actions']),
            'risks_identified': len(synthesis['identified_risks'])
        }
        
        self.analysis_history.append(record)
        
        # 保持歷史記錄在合理範圍內
        if len(self.analysis_history) > 1000:
            self.analysis_history = self.analysis_history[-1000:]
    
    def _summarize_conversation(self, context: Dict[str, Any]) -> Dict[str, str]:
        """總結對話"""
        return {
            'stage': context.get('conversation_stage', 'unknown'),
            'duration': f"{len(context.get('conversation_history', []))} messages",
            'customer_sentiment': context.get('current_sentiment', 'neutral'),
            'main_topics': context.get('discussed_topics', []),
            'current_intent': context.get('customer_intent', 'exploring')
        }
    
    def _generate_analysis_id(self) -> str:
        """生成分析ID"""
        timestamp = datetime.now().strftime('%Y%m%d%H%M%S')
        analysis_count = self.performance_metrics['total_analyses']
        return f"ANALYSIS-{timestamp}-{analysis_count:05d}"
    
    async def get_performance_report(self) -> Dict[str, Any]:
        """獲取性能報告"""
        return {
            'overall_metrics': self.performance_metrics,
            'agent_performance': self._calculate_agent_performance(),
            'success_trends': self._analyze_success_trends(),
            'insights_summary': self._summarize_insights(),
            'recommendations': self._generate_performance_recommendations()
        }
    
    def _calculate_agent_performance(self) -> Dict[str, Any]:
        """計算代理性能"""
        performance = {}
        for agent_name, metrics in self.performance_metrics['agent_performance'].items():
            if metrics['calls'] > 0:
                performance[agent_name] = {
                    'usage_rate': metrics['calls'] / self.performance_metrics['total_analyses'],
                    'average_confidence': metrics['avg_confidence'],
                    'contribution_score': self._calculate_contribution_score(agent_name)
                }
        return performance
    
    def _calculate_contribution_score(self, agent_name: str) -> float:
        """計算貢獻分數"""
        # 簡化的貢獻分數計算
        base_scores = {
            'psychology_expert': 0.25,
            'sales_expert': 0.20,
            'frontline_sales': 0.20,
            'online_order': 0.15,
            'sales_progression': 0.10,
            'follow_up_strategy': 0.10
        }
        return base_scores.get(agent_name, 0.1)
    
    def _analyze_success_trends(self) -> Dict[str, Any]:
        """分析成功趨勢"""
        if len(self.analysis_history) < 10:
            return {'trend': 'insufficient_data', 'message': '需要更多數據進行趨勢分析'}
        
        recent_analyses = self.analysis_history[-20:]
        recent_success_rates = [a['success_probability'] for a in recent_analyses]
        
        # 計算趨勢
        if len(recent_success_rates) > 1:
            trend = 'improving' if recent_success_rates[-1] > recent_success_rates[0] else 'declining'
        else:
            trend = 'stable'
        
        return {
            'trend': trend,
            'average_success_rate': sum(recent_success_rates) / len(recent_success_rates),
            'peak_performance': max(recent_success_rates),
            'consistency': 1 - (max(recent_success_rates) - min(recent_success_rates))
        }
    
    def _summarize_insights(self) -> Dict[str, Any]:
        """總結洞察"""
        if not self.analysis_history:
            return {'message': '尚無分析歷史'}
        
        # 統計最常見的客戶原型
        archetypes = [a['customer_archetype'] for a in self.analysis_history]
        archetype_counts = {}
        for archetype in archetypes:
            archetype_counts[archetype] = archetype_counts.get(archetype, 0) + 1
        
        most_common_archetype = max(archetype_counts, key=archetype_counts.get) if archetype_counts else 'Unknown'
        
        return {
            'total_insights_generated': self.performance_metrics['insights_generated'],
            'most_common_customer_type': most_common_archetype,
            'average_risks_per_conversation': sum(a['risks_identified'] for a in self.analysis_history) / len(self.analysis_history),
            'action_plans_generated': len(self.analysis_history)
        }
    
    def _generate_performance_recommendations(self) -> List[str]:
        """生成性能建議"""
        recommendations = []
        
        avg_readiness = self.performance_metrics['average_readiness_score']
        
        if avg_readiness < 50:
            recommendations.append("整體準備度偏低，建議加強初期信任建立和需求探索")
        elif avg_readiness < 70:
            recommendations.append("準備度中等，建議優化價值展示和異議處理流程")
        else:
            recommendations.append("準備度良好，建議專注於快速成交和追加銷售")
        
        # 基於趨勢的建議
        trends = self._analyze_success_trends()
        if trends['trend'] == 'declining':
            recommendations.append("成功率呈下降趨勢，建議檢查銷售流程和客戶反饋")
        elif trends['trend'] == 'improving':
            recommendations.append("成功率持續改善，保持當前策略並尋找進一步優化點")
        
        return recommendations