from typing import Dict, Any, List, Optional
from datetime import datetime
from crewai import Agent, Task
from .base_agent import BaseHealthCheckAgent
import re

class SalesProgressionAgent(BaseHealthCheckAgent):
    """銷售推進 Agent - 負責引導客戶完成購買決策"""
    
    def __init__(self):
        super().__init__(
            name="Sales Progression Agent",
            config_path="config/agents/sales_progression.yaml"
        )
        self.objection_handlers = self._load_objection_handlers()
        self.urgency_tactics = self._load_urgency_tactics()
        self.closing_techniques = self._load_closing_techniques()
        
    def _create_agent(self) -> Agent:
        return Agent(
            role=self.config['role'],
            goal=self.config['goal'],
            backstory=self.config['backstory'],
            tools=[],
            verbose=True,
            max_iter=5
        )
    
    def _load_objection_handlers(self) -> Dict[str, List[str]]:
        """加載異議處理話術"""
        return {
            "too_expensive": [
                "我完全理解您的考慮！其實這個價格包含了{items_count}項專業檢查，平均每項只需${per_item_price}。",
                "確實要仔細考慮預算。不過想想看，及早發現健康問題能省下的醫療費用，這其實是最划算的投資呢！",
                "我幫您申請到了額外{extra_discount}%的優惠，現在只需${final_price}，這已經是我們最優惠的價格了。"
            ],
            "need_to_think": [
                "當然要慎重考慮！不過我想提醒您，現在的優惠只到{promo_end_date}，之後就恢復原價了。",
                "理解您需要時間。為了幫您保留優惠，我可以先幫您預留名額，您看怎麼樣？",
                "沒問題！不過最近預約的人特別多，{popular_slot}的時段已經快滿了。要不我先幫您佔個位置？"
            ],
            "need_family_discussion": [
                "家人的意見確實很重要！要不這樣，我先發一份詳細資料給您，方便您和家人討論？",
                "很好，健康決定應該和家人商量。我們有家庭套餐優惠，一起來檢查更划算哦！",
                "理解的。順便問一下，您家人有定期體檢嗎？我們的家庭套餐買2送1，特別受歡迎。"
            ],
            "not_urgent": [
                "預防勝於治療啊！很多疾病早期都沒有症狀，等有感覺時可能就晚了。",
                "您知道嗎？{disease_stat}%的{disease_name}在早期發現的治癒率超過90%！",
                "我們最近有位客戶，體檢時意外發現了早期問題，現在特別感謝當時的決定。"
            ]
        }
    
    def _load_urgency_tactics(self) -> List[Dict[str, Any]]:
        """加載緊迫感策略"""
        return [
            {
                "trigger": "price_inquiry",
                "tactics": [
                    "📢 限時優惠提醒：此優惠價格僅限本月！",
                    "⏰ 僅剩{days_left}天！優惠即將結束",
                    "🔥 本月已有{booked_count}位客戶預約了這個套餐"
                ]
            },
            {
                "trigger": "availability_check",
                "tactics": [
                    "📅 本週末僅剩{slots_left}個預約名額",
                    "⚡ 您想要的時段很搶手，建議盡快確認",
                    "📊 根據往年經驗，下個月會進入預約高峰期"
                ]
            }
        ]
    
    def _load_closing_techniques(self) -> Dict[str, str]:
        """加載成交技巧"""
        return {
            "assumptive_close": "那我就幫您預約{preferred_date}的{preferred_time}，您看可以嗎？",
            "alternative_close": "您想預約週六上午還是週日下午呢？",
            "incentive_close": "現在確認預約，我還能為您爭取到一份價值$300的營養諮詢服務！",
            "scarcity_close": "這個優惠價格今天就要結束了，錯過真的很可惜。我現在就幫您確認？",
            "trial_close": "如果價格合適的話，您最快什麼時候方便來檢查呢？"
        }
    
    async def process(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """處理銷售推進"""
        stage = context.get('sales_stage', 'interest')
        customer_responses = context.get('customer_responses', [])
        quoted_price = context.get('quoted_price', {})
        
        # 分析客戶狀態
        customer_sentiment = await self._analyze_sentiment(customer_responses)
        objections = await self._identify_objections(customer_responses)
        readiness_score = await self._calculate_readiness_score(context)
        
        # 生成推進策略
        strategy = await self._generate_progression_strategy(
            stage=stage,
            sentiment=customer_sentiment,
            objections=objections,
            readiness_score=readiness_score
        )
        
        # 生成回應消息
        response_message = await self._craft_response(
            strategy=strategy,
            context=context,
            objections=objections
        )
        
        return {
            'response': response_message,
            'next_stage': strategy['next_stage'],
            'tactics_used': strategy['tactics'],
            'readiness_score': readiness_score,
            'follow_up_needed': readiness_score < 70,
            'context': {
                **context,
                'progression_time': datetime.now().isoformat(),
                'objections_identified': objections
            }
        }
    
    async def _analyze_sentiment(self, responses: List[str]) -> str:
        """分析客戶情緒"""
        if not responses:
            return 'neutral'
        
        positive_keywords = ['好', '可以', '興趣', '想要', '什麼時候', '預約']
        negative_keywords = ['貴', '再想想', '不急', '以後', '算了', '不需要']
        
        last_response = responses[-1].lower()
        
        positive_count = sum(1 for keyword in positive_keywords if keyword in last_response)
        negative_count = sum(1 for keyword in negative_keywords if keyword in last_response)
        
        if positive_count > negative_count:
            return 'positive'
        elif negative_count > positive_count:
            return 'negative'
        return 'neutral'
    
    async def _identify_objections(self, responses: List[str]) -> List[str]:
        """識別客戶異議"""
        objections = []
        
        if not responses:
            return objections
        
        last_response = ' '.join(responses[-2:]).lower()  # 檢查最近兩條消息
        
        objection_patterns = {
            'too_expensive': ['太貴', '價格高', '預算', '便宜點', '優惠', 'expensive'],
            'need_to_think': ['考慮', '想想', '再說', '不確定', 'think about it'],
            'need_family_discussion': ['問問家人', '商量', '家裡人', '老公', '老婆', '爸媽'],
            'not_urgent': ['不急', '以後', '明年', '下次', '沒必要', 'not urgent']
        }
        
        for objection_type, patterns in objection_patterns.items():
            if any(pattern in last_response for pattern in patterns):
                objections.append(objection_type)
        
        return objections
    
    async def _calculate_readiness_score(self, context: Dict[str, Any]) -> int:
        """計算購買準備度分數 (0-100)"""
        score = 50  # 基礎分數
        
        # 詢問價格 +10
        if 'price_inquiry' in context.get('intents', []):
            score += 10
        
        # 查看了報價 +15
        if context.get('quoted_price'):
            score += 15
        
        # 詢問預約時間 +20
        if any(word in str(context.get('customer_responses', [])) 
               for word in ['預約', '時間', '什麼時候', 'book']):
            score += 20
        
        # 正面情緒 +15
        if context.get('sentiment') == 'positive':
            score += 15
        
        # 有異議 -10 per objection
        objections = context.get('objections_identified', [])
        score -= len(objections) * 10
        
        # 互動次數獎勵
        interaction_count = len(context.get('customer_responses', []))
        if interaction_count > 5:
            score += 10
        
        return max(0, min(100, score))
    
    async def _generate_progression_strategy(
        self,
        stage: str,
        sentiment: str,
        objections: List[str],
        readiness_score: int
    ) -> Dict[str, Any]:
        """生成銷售推進策略"""
        strategy = {
            'tactics': [],
            'next_stage': stage,
            'approach': 'standard'
        }
        
        # 根據準備度分數決定策略
        if readiness_score >= 80:
            strategy['approach'] = 'close_now'
            strategy['tactics'] = ['assumptive_close', 'incentive_offer']
            strategy['next_stage'] = 'closing'
            
        elif readiness_score >= 60:
            strategy['approach'] = 'create_urgency'
            strategy['tactics'] = ['scarcity', 'social_proof', 'trial_close']
            strategy['next_stage'] = 'negotiation'
            
        elif readiness_score >= 40:
            strategy['approach'] = 'build_value'
            strategy['tactics'] = ['benefit_emphasis', 'handle_objections']
            strategy['next_stage'] = 'consideration'
            
        else:
            strategy['approach'] = 'nurture'
            strategy['tactics'] = ['education', 'soft_follow_up']
            strategy['next_stage'] = 'interest'
        
        # 如果有異議，優先處理
        if objections:
            strategy['tactics'].insert(0, 'objection_handling')
        
        return strategy
    
    async def _craft_response(
        self,
        strategy: Dict[str, Any],
        context: Dict[str, Any],
        objections: List[str]
    ) -> str:
        """生成回應消息"""
        messages = []
        
        # 1. 處理異議
        if objections and 'objection_handling' in strategy['tactics']:
            for objection in objections[:1]:  # 一次處理一個主要異議
                handlers = self.objection_handlers.get(objection, [])
                if handlers:
                    handler = handlers[0]  # 選擇第一個處理方式
                    # 填充模板變量
                    message = handler.format(
                        items_count=context.get('package_items_count', 20),
                        per_item_price=context.get('quoted_price', {}).get('final_price', 5000) / 20,
                        extra_discount=5,
                        final_price=context.get('quoted_price', {}).get('final_price', 5000) * 0.95,
                        promo_end_date="本月底",
                        popular_slot="週末上午",
                        disease_stat=85,
                        disease_name="早期癌症"
                    )
                    messages.append(message)
        
        # 2. 創造緊迫感
        if 'scarcity' in strategy['tactics']:
            urgency_message = "🔥 特別提醒：這個月已經過了一半，優惠名額真的不多了！"
            messages.append(urgency_message)
        
        # 3. 社交證明
        if 'social_proof' in strategy['tactics']:
            social_proof = "👥 本週已有23位客戶選擇了這個套餐，評價都非常好！"
            messages.append(social_proof)
        
        # 4. 成交嘗試
        if strategy['approach'] == 'close_now':
            closing_message = self.closing_techniques.get(
                'incentive_close',
                "現在確認預約還有額外優惠，我馬上幫您安排？"
            )
            messages.append(closing_message)
        
        # 5. 軟性跟進
        elif strategy['approach'] == 'nurture':
            nurture_message = "沒關係，健康檢查確實需要好好考慮。我先把詳細資料發給您，有任何問題隨時問我哦！😊"
            messages.append(nurture_message)
        
        return "\n\n".join(messages)