from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime, timedelta
from crewai import Agent, Task
from .base_agent import BaseHealthCheckAgent
import re
from collections import defaultdict

class QualityMonitorAgent(BaseHealthCheckAgent):
    """質量監控 Agent - 負責監控對話質量和優化建議"""
    
    def __init__(self):
        super().__init__(
            name="Quality Monitor Agent",
            config_path="config/agents/quality_monitor.yaml"
        )
        self.quality_metrics = self._load_quality_metrics()
        self.compliance_rules = self._load_compliance_rules()
        self.optimization_rules = self._load_optimization_rules()
        self.performance_benchmarks = self._load_performance_benchmarks()
        
    def _create_agent(self) -> Agent:
        return Agent(
            role=self.config['role'],
            goal=self.config['goal'],
            backstory=self.config['backstory'],
            tools=[],
            verbose=True,
            max_iter=3
        )
    
    def _load_quality_metrics(self) -> Dict[str, Dict[str, Any]]:
        """加載質量評估指標"""
        return {
            "response_quality": {
                "accuracy": {"weight": 0.3, "threshold": 0.95},
                "relevance": {"weight": 0.25, "threshold": 0.90},
                "completeness": {"weight": 0.20, "threshold": 0.85},
                "tone": {"weight": 0.15, "threshold": 0.90},
                "grammar": {"weight": 0.10, "threshold": 0.95}
            },
            "conversation_flow": {
                "coherence": {"weight": 0.35, "threshold": 0.85},
                "progression": {"weight": 0.30, "threshold": 0.80},
                "engagement": {"weight": 0.35, "threshold": 0.75}
            },
            "business_impact": {
                "conversion_potential": {"weight": 0.40, "threshold": 0.70},
                "customer_satisfaction": {"weight": 0.35, "threshold": 0.85},
                "efficiency": {"weight": 0.25, "threshold": 0.80}
            }
        }
    
    def _load_compliance_rules(self) -> Dict[str, List[Dict[str, Any]]]:
        """加載合規檢查規則"""
        return {
            "medical_compliance": [
                {
                    "rule": "no_diagnosis",
                    "pattern": r"(診斷|確診|你有|你患有|病症|疾病診斷)",
                    "severity": "critical",
                    "message": "不可提供醫療診斷"
                },
                {
                    "rule": "no_treatment_advice",
                    "pattern": r"(建議服用|應該吃|藥物|治療方案|療程)",
                    "severity": "critical",
                    "message": "不可提供治療建議"
                }
            ],
            "business_compliance": [
                {
                    "rule": "accurate_pricing",
                    "check": "verify_price_calculation",
                    "severity": "high",
                    "message": "價格計算必須準確"
                },
                {
                    "rule": "no_false_claims",
                    "pattern": r"(保證|百分百|一定會|絕對)",
                    "severity": "medium",
                    "message": "避免絕對性承諾"
                }
            ],
            "data_privacy": [
                {
                    "rule": "no_data_sharing",
                    "pattern": r"(分享給|告訴|透露|其他客戶)",
                    "severity": "critical",
                    "message": "不可洩露客戶隱私"
                }
            ]
        }
    
    def _load_optimization_rules(self) -> Dict[str, List[Dict[str, Any]]]:
        """加載優化建議規則"""
        return {
            "response_optimization": [
                {
                    "condition": "long_response",
                    "threshold": 200,  # 字數
                    "suggestion": "回應過長，建議精簡至100字以內",
                    "impact": "medium"
                },
                {
                    "condition": "slow_response",
                    "threshold": 5000,  # 毫秒
                    "suggestion": "響應時間過長，檢查系統性能",
                    "impact": "high"
                }
            ],
            "conversation_optimization": [
                {
                    "condition": "too_many_questions",
                    "threshold": 3,
                    "suggestion": "連續提問過多，應適當提供信息",
                    "impact": "medium"
                },
                {
                    "condition": "no_progression",
                    "threshold": 5,  # 輪次
                    "suggestion": "對話停滯，需要推進策略",
                    "impact": "high"
                }
            ],
            "sales_optimization": [
                {
                    "condition": "missed_buying_signal",
                    "patterns": ["想要", "什麼時候", "可以預約"],
                    "suggestion": "檢測到購買信號，應立即推進成交",
                    "impact": "critical"
                },
                {
                    "condition": "no_urgency_created",
                    "after_messages": 3,
                    "suggestion": "未創造緊迫感，建議加入限時優惠",
                    "impact": "medium"
                }
            ]
        }
    
    def _load_performance_benchmarks(self) -> Dict[str, Dict[str, float]]:
        """加載性能基準"""
        return {
            "response_time": {
                "excellent": 1000,  # ms
                "good": 2000,
                "acceptable": 3000,
                "poor": 5000
            },
            "conversion_rate": {
                "excellent": 0.25,
                "good": 0.15,
                "acceptable": 0.10,
                "poor": 0.05
            },
            "customer_satisfaction": {
                "excellent": 4.5,
                "good": 4.0,
                "acceptable": 3.5,
                "poor": 3.0
            },
            "engagement_rate": {
                "excellent": 0.80,
                "good": 0.60,
                "acceptable": 0.40,
                "poor": 0.20
            }
        }
    
    async def process(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """執行質量監控"""
        conversation_history = context.get('conversation_history', [])
        agent_responses = context.get('agent_responses', [])
        metrics = context.get('current_metrics', {})
        
        # 評估對話質量
        quality_scores = await self._evaluate_conversation_quality(
            conversation_history,
            agent_responses
        )
        
        # 合規性檢查
        compliance_issues = await self._check_compliance(
            agent_responses
        )
        
        # 性能分析
        performance_analysis = await self._analyze_performance(
            metrics,
            conversation_history
        )
        
        # 生成優化建議
        optimization_suggestions = await self._generate_suggestions(
            quality_scores,
            compliance_issues,
            performance_analysis,
            conversation_history
        )
        
        # 計算整體健康分數
        health_score = await self._calculate_health_score(
            quality_scores,
            compliance_issues,
            performance_analysis
        )
        
        # 生成改進行動計劃
        action_plan = await self._create_action_plan(
            optimization_suggestions,
            health_score
        )
        
        return {
            'quality_scores': quality_scores,
            'compliance_issues': compliance_issues,
            'performance_analysis': performance_analysis,
            'optimization_suggestions': optimization_suggestions,
            'health_score': health_score,
            'action_plan': action_plan,
            'monitoring_time': datetime.now().isoformat(),
            'alerts': self._generate_alerts(compliance_issues, health_score),
            'context': {
                **context,
                'quality_check_completed': True
            }
        }
    
    async def _evaluate_conversation_quality(
        self,
        history: List[Dict[str, Any]],
        agent_responses: List[Dict[str, Any]]
    ) -> Dict[str, float]:
        """評估對話質量"""
        scores = {}
        
        # 響應質量評分
        response_scores = {
            'accuracy': await self._score_accuracy(agent_responses),
            'relevance': await self._score_relevance(history, agent_responses),
            'completeness': await self._score_completeness(agent_responses),
            'tone': await self._score_tone(agent_responses),
            'grammar': await self._score_grammar(agent_responses)
        }
        
        # 加權計算響應質量總分
        response_quality = 0
        for metric, score in response_scores.items():
            weight = self.quality_metrics['response_quality'][metric]['weight']
            response_quality += score * weight
        
        scores['response_quality'] = response_quality
        scores['response_details'] = response_scores
        
        # 對話流程評分
        flow_scores = {
            'coherence': await self._score_coherence(history),
            'progression': await self._score_progression(history),
            'engagement': await self._score_engagement(history)
        }
        
        # 加權計算流程質量總分
        conversation_flow = 0
        for metric, score in flow_scores.items():
            weight = self.quality_metrics['conversation_flow'][metric]['weight']
            conversation_flow += score * weight
        
        scores['conversation_flow'] = conversation_flow
        scores['flow_details'] = flow_scores
        
        # 業務影響評分
        impact_scores = {
            'conversion_potential': await self._score_conversion_potential(history),
            'customer_satisfaction': await self._score_satisfaction(history),
            'efficiency': await self._score_efficiency(history)
        }
        
        # 加權計算業務影響總分
        business_impact = 0
        for metric, score in impact_scores.items():
            weight = self.quality_metrics['business_impact'][metric]['weight']
            business_impact += score * weight
        
        scores['business_impact'] = business_impact
        scores['impact_details'] = impact_scores
        
        # 整體質量分數
        scores['overall'] = (response_quality + conversation_flow + business_impact) / 3
        
        return scores
    
    async def _score_accuracy(self, responses: List[Dict[str, Any]]) -> float:
        """評估準確性"""
        if not responses:
            return 0.0
        
        accurate_count = 0
        for response in responses:
            # 檢查價格計算準確性
            if 'quoted_price' in response:
                # 這裡應該驗證價格計算邏輯
                accurate_count += 1
            # 檢查信息準確性
            elif not self._contains_inaccurate_info(response.get('content', '')):
                accurate_count += 1
        
        return accurate_count / len(responses)
    
    async def _score_relevance(self, history: List[Dict[str, Any]], responses: List[Dict[str, Any]]) -> float:
        """評估相關性"""
        if not responses or not history:
            return 0.0
        
        relevant_count = 0
        for i, response in enumerate(responses):
            if i < len(history):
                question = history[i].get('content', '')
                answer = response.get('content', '')
                if self._is_relevant_response(question, answer):
                    relevant_count += 1
        
        return relevant_count / len(responses) if responses else 0.0
    
    async def _score_completeness(self, responses: List[Dict[str, Any]]) -> float:
        """評估完整性"""
        complete_count = sum(1 for r in responses 
                           if len(r.get('content', '')) > 20 
                           and not r.get('content', '').endswith('...'))
        return complete_count / len(responses) if responses else 0.0
    
    async def _score_tone(self, responses: List[Dict[str, Any]]) -> float:
        """評估語氣適當性"""
        positive_patterns = ['您好', '謝謝', '請', '歡迎', '😊', '👍']
        negative_patterns = ['不行', '錯誤', '失敗', '糟糕']
        
        tone_score = 0
        for response in responses:
            content = response.get('content', '')
            positive = sum(1 for p in positive_patterns if p in content)
            negative = sum(1 for p in negative_patterns if p in content)
            if positive > negative:
                tone_score += 1
        
        return tone_score / len(responses) if responses else 0.0
    
    async def _score_grammar(self, responses: List[Dict[str, Any]]) -> float:
        """評估語法正確性"""
        # 簡化的語法檢查
        grammar_score = sum(1 for r in responses 
                          if not self._has_grammar_issues(r.get('content', '')))
        return grammar_score / len(responses) if responses else 0.0
    
    async def _score_coherence(self, history: List[Dict[str, Any]]) -> float:
        """評估對話連貫性"""
        if len(history) < 2:
            return 1.0
        
        coherent_transitions = 0
        for i in range(1, len(history)):
            if self._is_coherent_transition(history[i-1], history[i]):
                coherent_transitions += 1
        
        return coherent_transitions / (len(history) - 1)
    
    async def _score_progression(self, history: List[Dict[str, Any]]) -> float:
        """評估對話進展"""
        stages = ['greeting', 'discovery', 'presentation', 'handling_objections', 'closing']
        current_stage = 0
        
        for message in history:
            detected_stage = self._detect_conversation_stage(message)
            if detected_stage > current_stage:
                current_stage = detected_stage
        
        return current_stage / len(stages)
    
    async def _score_engagement(self, history: List[Dict[str, Any]]) -> float:
        """評估客戶參與度"""
        if not history:
            return 0.0
        
        customer_messages = [m for m in history if m.get('sender') == 'customer']
        if not customer_messages:
            return 0.0
        
        engaged_messages = sum(1 for m in customer_messages 
                             if len(m.get('content', '')) > 10 
                             and '?' in m.get('content', ''))
        
        return engaged_messages / len(customer_messages)
    
    async def _score_conversion_potential(self, history: List[Dict[str, Any]]) -> float:
        """評估轉化潛力"""
        buying_signals = ['想要', '預約', '什麼時候', '可以', '價格合適', '優惠']
        objections = ['太貴', '再想想', '不急', '以後']
        
        positive_signals = sum(1 for m in history 
                             if any(signal in m.get('content', '') for signal in buying_signals))
        negative_signals = sum(1 for m in history 
                             if any(obj in m.get('content', '') for obj in objections))
        
        if positive_signals + negative_signals == 0:
            return 0.5
        
        return positive_signals / (positive_signals + negative_signals)
    
    async def _score_satisfaction(self, history: List[Dict[str, Any]]) -> float:
        """評估客戶滿意度"""
        positive_indicators = ['謝謝', '很好', '明白', '清楚', '👍', '😊']
        negative_indicators = ['不懂', '什麼意思', '太複雜', '算了', '😕', '😞']
        
        positive_count = sum(1 for m in history 
                           if any(ind in m.get('content', '') for ind in positive_indicators))
        negative_count = sum(1 for m in history 
                           if any(ind in m.get('content', '') for ind in negative_indicators))
        
        if positive_count + negative_count == 0:
            return 0.7  # 預設中等滿意度
        
        return positive_count / (positive_count + negative_count)
    
    async def _score_efficiency(self, history: List[Dict[str, Any]]) -> float:
        """評估效率"""
        if not history:
            return 0.0
        
        # 理想情況下，5-10輪對話完成銷售
        message_count = len(history)
        if message_count <= 5:
            return 1.0
        elif message_count <= 10:
            return 0.8
        elif message_count <= 15:
            return 0.6
        elif message_count <= 20:
            return 0.4
        else:
            return 0.2
    
    async def _check_compliance(self, responses: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """檢查合規性"""
        issues = []
        
        for response in responses:
            content = response.get('content', '')
            
            # 檢查醫療合規
            for rule in self.compliance_rules['medical_compliance']:
                if 'pattern' in rule and re.search(rule['pattern'], content):
                    issues.append({
                        'type': 'medical_compliance',
                        'rule': rule['rule'],
                        'severity': rule['severity'],
                        'message': rule['message'],
                        'response_id': response.get('id'),
                        'timestamp': response.get('timestamp')
                    })
            
            # 檢查業務合規
            for rule in self.compliance_rules['business_compliance']:
                if 'pattern' in rule and re.search(rule['pattern'], content):
                    issues.append({
                        'type': 'business_compliance',
                        'rule': rule['rule'],
                        'severity': rule['severity'],
                        'message': rule['message'],
                        'response_id': response.get('id'),
                        'timestamp': response.get('timestamp')
                    })
            
            # 檢查數據隱私
            for rule in self.compliance_rules['data_privacy']:
                if 'pattern' in rule and re.search(rule['pattern'], content):
                    issues.append({
                        'type': 'data_privacy',
                        'rule': rule['rule'],
                        'severity': rule['severity'],
                        'message': rule['message'],
                        'response_id': response.get('id'),
                        'timestamp': response.get('timestamp')
                    })
        
        return issues
    
    async def _analyze_performance(
        self,
        metrics: Dict[str, Any],
        history: List[Dict[str, Any]]
    ) -> Dict[str, Any]:
        """分析性能指標"""
        analysis = {
            'response_time': self._analyze_response_time(metrics.get('response_times', [])),
            'conversion_rate': self._analyze_conversion_rate(metrics.get('conversions', {})),
            'satisfaction_score': self._analyze_satisfaction(metrics.get('satisfaction_scores', [])),
            'engagement_metrics': self._analyze_engagement(history)
        }
        
        # 與基準對比
        analysis['benchmark_comparison'] = {}
        for metric, value in analysis.items():
            if isinstance(value, dict) and 'average' in value:
                avg = value['average']
                benchmark = self._get_benchmark_rating(metric, avg)
                analysis['benchmark_comparison'][metric] = benchmark
        
        return analysis
    
    def _analyze_response_time(self, response_times: List[float]) -> Dict[str, Any]:
        """分析響應時間"""
        if not response_times:
            return {'average': 0, 'min': 0, 'max': 0, 'status': 'no_data'}
        
        avg_time = sum(response_times) / len(response_times)
        return {
            'average': avg_time,
            'min': min(response_times),
            'max': max(response_times),
            'status': self._get_benchmark_rating('response_time', avg_time)
        }
    
    def _analyze_conversion_rate(self, conversions: Dict[str, Any]) -> Dict[str, Any]:
        """分析轉化率"""
        total_conversations = conversions.get('total', 0)
        converted = conversions.get('converted', 0)
        
        if total_conversations == 0:
            return {'rate': 0, 'status': 'no_data'}
        
        rate = converted / total_conversations
        return {
            'rate': rate,
            'converted': converted,
            'total': total_conversations,
            'status': self._get_benchmark_rating('conversion_rate', rate)
        }
    
    def _analyze_satisfaction(self, scores: List[float]) -> Dict[str, Any]:
        """分析滿意度"""
        if not scores:
            return {'average': 0, 'status': 'no_data'}
        
        avg_score = sum(scores) / len(scores)
        return {
            'average': avg_score,
            'count': len(scores),
            'status': self._get_benchmark_rating('customer_satisfaction', avg_score)
        }
    
    def _analyze_engagement(self, history: List[Dict[str, Any]]) -> Dict[str, Any]:
        """分析參與度指標"""
        if not history:
            return {'rate': 0, 'status': 'no_data'}
        
        customer_messages = [m for m in history if m.get('sender') == 'customer']
        total_messages = len(history)
        
        if total_messages == 0:
            return {'rate': 0, 'status': 'no_data'}
        
        engagement_rate = len(customer_messages) / total_messages
        return {
            'rate': engagement_rate,
            'customer_messages': len(customer_messages),
            'total_messages': total_messages,
            'status': self._get_benchmark_rating('engagement_rate', engagement_rate)
        }
    
    def _get_benchmark_rating(self, metric: str, value: float) -> str:
        """獲取基準評級"""
        benchmarks = self.performance_benchmarks.get(metric, {})
        
        if value <= benchmarks.get('excellent', float('inf')):
            return 'excellent'
        elif value <= benchmarks.get('good', float('inf')):
            return 'good'
        elif value <= benchmarks.get('acceptable', float('inf')):
            return 'acceptable'
        else:
            return 'poor'
    
    async def _generate_suggestions(
        self,
        quality_scores: Dict[str, float],
        compliance_issues: List[Dict[str, Any]],
        performance_analysis: Dict[str, Any],
        history: List[Dict[str, Any]]
    ) -> List[Dict[str, Any]]:
        """生成優化建議"""
        suggestions = []
        
        # 基於質量分數的建議
        if quality_scores['response_quality'] < 0.8:
            suggestions.append({
                'category': 'response_quality',
                'priority': 'high',
                'suggestion': '提升回應質量：確保準確性、相關性和完整性',
                'specific_actions': [
                    '加強產品知識培訓',
                    '優化回應模板',
                    '實施質量檢查流程'
                ]
            })
        
        # 基於合規問題的建議
        if compliance_issues:
            critical_issues = [i for i in compliance_issues if i['severity'] == 'critical']
            if critical_issues:
                suggestions.append({
                    'category': 'compliance',
                    'priority': 'critical',
                    'suggestion': f'立即處理{len(critical_issues)}個嚴重合規問題',
                    'specific_actions': [
                        f"修正{issue['rule']}相關回應" for issue in critical_issues[:3]
                    ]
                })
        
        # 基於性能分析的建議
        for metric, analysis in performance_analysis.items():
            if isinstance(analysis, dict) and analysis.get('status') == 'poor':
                suggestions.append({
                    'category': 'performance',
                    'priority': 'high',
                    'suggestion': f'改善{metric}表現',
                    'specific_actions': self._get_performance_improvement_actions(metric)
                })
        
        # 基於對話分析的建議
        if len(history) > 15:
            suggestions.append({
                'category': 'efficiency',
                'priority': 'medium',
                'suggestion': '縮短銷售週期',
                'specific_actions': [
                    '在前3輪對話中識別客戶需求',
                    '更快提供價格信息',
                    '使用更有效的成交技巧'
                ]
            })
        
        return sorted(suggestions, key=lambda x: 
                     {'critical': 0, 'high': 1, 'medium': 2, 'low': 3}[x['priority']])
    
    def _get_performance_improvement_actions(self, metric: str) -> List[str]:
        """獲取性能改進行動"""
        actions_map = {
            'response_time': [
                '優化系統架構減少延遲',
                '實施緩存策略',
                '升級服務器配置'
            ],
            'conversion_rate': [
                '優化銷售話術',
                '加強異議處理培訓',
                '實施A/B測試找出最佳策略'
            ],
            'customer_satisfaction': [
                '提升服務態度',
                '加快問題解決速度',
                '提供更個性化的服務'
            ],
            'engagement_rate': [
                '使用更吸引的開場白',
                '提供更有價值的信息',
                '適時使用互動元素'
            ]
        }
        return actions_map.get(metric, ['分析具體問題並制定改進計劃'])
    
    async def _calculate_health_score(
        self,
        quality_scores: Dict[str, float],
        compliance_issues: List[Dict[str, Any]],
        performance_analysis: Dict[str, Any]
    ) -> Dict[str, Any]:
        """計算整體健康分數"""
        # 質量分數權重 40%
        quality_component = quality_scores['overall'] * 0.4
        
        # 合規分數權重 30%
        critical_issues = len([i for i in compliance_issues if i['severity'] == 'critical'])
        high_issues = len([i for i in compliance_issues if i['severity'] == 'high'])
        compliance_score = max(0, 1 - (critical_issues * 0.2 + high_issues * 0.1))
        compliance_component = compliance_score * 0.3
        
        # 性能分數權重 30%
        performance_scores = []
        for metric, analysis in performance_analysis.items():
            if isinstance(analysis, dict) and 'status' in analysis:
                status_score = {
                    'excellent': 1.0,
                    'good': 0.8,
                    'acceptable': 0.6,
                    'poor': 0.3,
                    'no_data': 0.5
                }
                performance_scores.append(status_score.get(analysis['status'], 0.5))
        
        performance_component = (sum(performance_scores) / len(performance_scores) * 0.3) if performance_scores else 0.15
        
        # 計算總分
        total_score = quality_component + compliance_component + performance_component
        
        # 確定健康狀態
        if total_score >= 0.85:
            status = 'excellent'
            color = 'green'
        elif total_score >= 0.70:
            status = 'good'
            color = 'yellow'
        elif total_score >= 0.50:
            status = 'needs_improvement'
            color = 'orange'
        else:
            status = 'critical'
            color = 'red'
        
        return {
            'score': round(total_score * 100, 2),
            'status': status,
            'color': color,
            'components': {
                'quality': round(quality_component * 100 / 0.4, 2),
                'compliance': round(compliance_component * 100 / 0.3, 2),
                'performance': round(performance_component * 100 / 0.3, 2)
            },
            'timestamp': datetime.now().isoformat()
        }
    
    async def _create_action_plan(
        self,
        suggestions: List[Dict[str, Any]],
        health_score: Dict[str, Any]
    ) -> List[Dict[str, Any]]:
        """創建改進行動計劃"""
        action_plan = []
        
        # 根據健康分數確定緊急程度
        urgency_multiplier = {
            'critical': 0.5,  # 加快50%
            'needs_improvement': 0.8,
            'good': 1.0,
            'excellent': 1.2
        }.get(health_score['status'], 1.0)
        
        # 將建議轉換為具體行動
        for i, suggestion in enumerate(suggestions[:5]):  # 最多5個行動
            deadline_hours = {
                'critical': 24,
                'high': 72,
                'medium': 168,  # 1週
                'low': 336  # 2週
            }.get(suggestion['priority'], 168)
            
            action_plan.append({
                'id': f"action_{i+1}",
                'title': suggestion['suggestion'],
                'category': suggestion['category'],
                'priority': suggestion['priority'],
                'actions': suggestion['specific_actions'],
                'deadline': (datetime.now() + timedelta(
                    hours=deadline_hours * urgency_multiplier
                )).isoformat(),
                'status': 'pending',
                'assigned_to': 'system_admin',
                'impact': self._estimate_impact(suggestion)
            })
        
        return action_plan
    
    def _estimate_impact(self, suggestion: Dict[str, Any]) -> str:
        """估算改進影響"""
        impact_map = {
            'compliance': 'high',
            'response_quality': 'high',
            'performance': 'medium',
            'efficiency': 'medium'
        }
        return impact_map.get(suggestion['category'], 'low')
    
    def _generate_alerts(
        self,
        compliance_issues: List[Dict[str, Any]],
        health_score: Dict[str, Any]
    ) -> List[Dict[str, Any]]:
        """生成警報"""
        alerts = []
        
        # 合規警報
        critical_compliance = [i for i in compliance_issues if i['severity'] == 'critical']
        if critical_compliance:
            alerts.append({
                'type': 'compliance_violation',
                'severity': 'critical',
                'message': f'發現{len(critical_compliance)}個嚴重合規違規',
                'action_required': True,
                'timestamp': datetime.now().isoformat()
            })
        
        # 健康分數警報
        if health_score['status'] == 'critical':
            alerts.append({
                'type': 'health_score_critical',
                'severity': 'high',
                'message': f"系統健康分數過低: {health_score['score']}%",
                'action_required': True,
                'timestamp': datetime.now().isoformat()
            })
        
        # 性能警報
        if health_score['components']['performance'] < 50:
            alerts.append({
                'type': 'performance_degradation',
                'severity': 'medium',
                'message': '系統性能顯著下降',
                'action_required': True,
                'timestamp': datetime.now().isoformat()
            })
        
        return alerts
    
    # 輔助方法
    def _contains_inaccurate_info(self, content: str) -> bool:
        """檢查是否包含不準確信息"""
        # 簡化實現
        inaccurate_patterns = ['免費', '0元', '無需付款']
        return any(pattern in content for pattern in inaccurate_patterns)
    
    def _is_relevant_response(self, question: str, answer: str) -> bool:
        """檢查回應是否相關"""
        # 簡化實現：檢查是否有共同關鍵詞
        question_words = set(question.split())
        answer_words = set(answer.split())
        common_words = question_words.intersection(answer_words)
        return len(common_words) > 0
    
    def _has_grammar_issues(self, content: str) -> bool:
        """檢查語法問題"""
        # 簡化實現
        issues = ['。。', '，，', '！！', '？？']
        return any(issue in content for issue in issues)
    
    def _is_coherent_transition(self, msg1: Dict[str, Any], msg2: Dict[str, Any]) -> bool:
        """檢查對話轉換是否連貫"""
        # 簡化實現
        return True  # 實際應該檢查主題連續性
    
    def _detect_conversation_stage(self, message: Dict[str, Any]) -> int:
        """檢測對話階段"""
        content = message.get('content', '').lower()
        if any(word in content for word in ['你好', 'hi', 'hello']):
            return 0  # greeting
        elif any(word in content for word in ['了解', '想要', '需要']):
            return 1  # discovery
        elif any(word in content for word in ['套餐', '價格', '優惠']):
            return 2  # presentation
        elif any(word in content for word in ['太貴', '考慮', '不確定']):
            return 3  # handling_objections
        elif any(word in content for word in ['預約', '確認', '什麼時候']):
            return 4  # closing
        return 0