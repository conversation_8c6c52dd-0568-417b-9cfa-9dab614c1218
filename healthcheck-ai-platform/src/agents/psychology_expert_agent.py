from typing import Dict, Any, List, Optional
from datetime import datetime
from crewai import Agent, Task
from .base_agent import BaseHealthCheckAgent
import json

class PsychologyExpertAgent(BaseHealthCheckAgent):
    """心理專家 Agent - 深度分析客戶心理狀態和購買動機"""
    
    def __init__(self):
        super().__init__(
            name="Psychology Expert Agent",
            config_path="config/agents/psychology_expert.yaml"
        )
        self.psychological_patterns = self._load_psychological_patterns()
        self.personality_types = self._load_personality_types()
        self.emotional_triggers = self._load_emotional_triggers()
        
    def _create_agent(self) -> Agent:
        return Agent(
            role=self.config['role'],
            goal=self.config['goal'],
            backstory=self.config['backstory'],
            tools=[],
            verbose=True,
            max_iter=5
        )
    
    def _load_psychological_patterns(self) -> Dict[str, Any]:
        """加載心理行為模式"""
        return {
            "decision_making_styles": {
                "analytical": {
                    "characteristics": ["需要詳細數據", "重視邏輯", "決策慢"],
                    "approach": "provide_detailed_comparisons",
                    "triggers": ["科學依據", "數據支持", "專業認證"]
                },
                "driver": {
                    "characteristics": ["快速決策", "結果導向", "重視效率"],
                    "approach": "focus_on_benefits_and_roi",
                    "triggers": ["節省時間", "快速見效", "VIP服務"]
                },
                "amiable": {
                    "characteristics": ["重視關係", "需要信任", "避免衝突"],
                    "approach": "build_trust_and_rapport",
                    "triggers": ["朋友推薦", "貼心服務", "安全保障"]
                },
                "expressive": {
                    "characteristics": ["情感驅動", "重視體驗", "社交需求"],
                    "approach": "create_emotional_connection",
                    "triggers": ["獨特體驗", "身份認同", "社交價值"]
                }
            },
            "fear_patterns": {
                "health_anxiety": ["疾病恐懼", "家族病史擔憂", "症狀過度解讀"],
                "financial_concern": ["價格敏感", "投資回報疑慮", "隱藏費用擔心"],
                "time_pressure": ["工作繁忙", "家庭責任", "時間安排困難"],
                "decision_regret": ["選擇困難", "後悔避免", "比較焦慮"]
            },
            "motivation_drivers": {
                "health_preservation": {"weight": 0.8, "keywords": ["健康", "預防", "長壽"]},
                "family_care": {"weight": 0.9, "keywords": ["家人", "孩子", "父母"]},
                "career_success": {"weight": 0.7, "keywords": ["工作", "事業", "表現"]},
                "social_image": {"weight": 0.6, "keywords": ["形象", "面子", "朋友"]},
                "peace_of_mind": {"weight": 0.85, "keywords": ["安心", "放心", "保障"]}
            }
        }
    
    def _load_personality_types(self) -> Dict[str, Any]:
        """加載人格類型分析"""
        return {
            "cautious_optimizer": {
                "traits": ["謹慎", "追求最優", "比較傾向"],
                "sales_approach": "comparison_with_value_emphasis",
                "objection_probability": 0.8
            },
            "spontaneous_buyer": {
                "traits": ["衝動", "情感驅動", "快速決策"],
                "sales_approach": "create_urgency_with_emotion",
                "objection_probability": 0.3
            },
            "relationship_oriented": {
                "traits": ["重視服務", "需要認同", "長期關係"],
                "sales_approach": "personal_connection_building",
                "objection_probability": 0.5
            },
            "value_seeker": {
                "traits": ["精打細算", "追求CP值", "理性分析"],
                "sales_approach": "roi_demonstration",
                "objection_probability": 0.7
            }
        }
    
    def _load_emotional_triggers(self) -> Dict[str, List[str]]:
        """加載情緒觸發點"""
        return {
            "trust_builders": [
                "醫院合作認證", "真實客戶見證", "透明價格", 
                "專業醫療團隊", "退款保障", "隱私保護"
            ],
            "urgency_creators": [
                "限時優惠", "名額有限", "價格即將上漲", 
                "季節性提醒", "年齡里程碑", "健康新聞事件"
            ],
            "value_enhancers": [
                "額外贈品", "家庭優惠", "會員特權", 
                "積分獎勵", "轉介紹優惠", "套餐升級"
            ],
            "fear_reducers": [
                "無痛檢查", "隱私保護", "專人陪同", 
                "彈性時間", "分期付款", "滿意保證"
            ]
        }
    
    async def process(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """深度心理分析處理"""
        conversation_history = context.get('conversation_history', [])
        customer_profile = context.get('customer_profile', {})
        current_state = context.get('current_state', {})
        
        # 執行多維度心理分析
        psychological_profile = await self._analyze_psychological_profile(
            conversation_history, customer_profile
        )
        
        # 識別決策風格
        decision_style = await self._identify_decision_style(conversation_history)
        
        # 分析情緒狀態
        emotional_state = await self._analyze_emotional_state(conversation_history)
        
        # 預測購買障礙
        purchase_barriers = await self._predict_purchase_barriers(
            psychological_profile, emotional_state
        )
        
        # 生成個性化策略
        personalized_strategy = await self._generate_personalized_strategy(
            psychological_profile, decision_style, emotional_state, purchase_barriers
        )
        
        # 計算心理準備度分數
        psychological_readiness = await self._calculate_psychological_readiness(
            psychological_profile, emotional_state, context
        )
        
        return {
            'psychological_profile': psychological_profile,
            'decision_style': decision_style,
            'emotional_state': emotional_state,
            'purchase_barriers': purchase_barriers,
            'personalized_strategy': personalized_strategy,
            'psychological_readiness_score': psychological_readiness,
            'recommended_approach': personalized_strategy['primary_approach'],
            'key_triggers': personalized_strategy['triggers'],
            'avoid_topics': personalized_strategy['avoid'],
            'optimal_message_tone': personalized_strategy['tone']
        }
    
    async def _analyze_psychological_profile(
        self, 
        conversation_history: List[Dict], 
        customer_profile: Dict
    ) -> Dict[str, Any]:
        """分析客戶心理檔案"""
        profile = {
            'personality_type': 'unknown',
            'primary_motivations': [],
            'core_values': [],
            'fear_factors': [],
            'trust_level': 0,
            'openness_score': 0
        }
        
        # 分析對話內容提取心理特徵
        if conversation_history:
            text_analysis = ' '.join([msg.get('content', '') for msg in conversation_history])
            
            # 識別主要動機
            for motivation, data in self.psychological_patterns['motivation_drivers'].items():
                if any(keyword in text_analysis for keyword in data['keywords']):
                    profile['primary_motivations'].append({
                        'type': motivation,
                        'strength': data['weight']
                    })
            
            # 識別恐懼因素
            for fear_type, patterns in self.psychological_patterns['fear_patterns'].items():
                if any(pattern in text_analysis for pattern in patterns):
                    profile['fear_factors'].append(fear_type)
            
            # 計算信任度
            trust_indicators = ['相信', '真的嗎', '確定', '保證', '承諾']
            profile['trust_level'] = sum(1 for indicator in trust_indicators 
                                       if indicator in text_analysis) / len(trust_indicators)
            
            # 評估開放度
            question_count = text_analysis.count('?') + text_analysis.count('？')
            profile['openness_score'] = min(question_count / max(len(conversation_history), 1), 1.0)
        
        # 基於特徵確定人格類型
        if profile['trust_level'] < 0.3 and profile['openness_score'] > 0.5:
            profile['personality_type'] = 'cautious_optimizer'
        elif len(profile['primary_motivations']) > 2 and profile['openness_score'] < 0.3:
            profile['personality_type'] = 'spontaneous_buyer'
        elif profile['trust_level'] > 0.5:
            profile['personality_type'] = 'relationship_oriented'
        else:
            profile['personality_type'] = 'value_seeker'
            
        return profile
    
    async def _identify_decision_style(self, conversation_history: List[Dict]) -> Dict[str, Any]:
        """識別決策風格"""
        if not conversation_history:
            return {'style': 'unknown', 'confidence': 0}
        
        text_content = ' '.join([msg.get('content', '') for msg in conversation_history])
        
        # 分析決策風格指標
        analytical_keywords = ['數據', '比較', '分析', '證據', '研究', '科學']
        driver_keywords = ['快', '馬上', '立即', '效率', '結果', '時間']
        amiable_keywords = ['感覺', '舒服', '信任', '朋友', '推薦', '安心']
        expressive_keywords = ['喜歡', '感動', '特別', '獨特', '體驗', '分享']
        
        scores = {
            'analytical': sum(1 for kw in analytical_keywords if kw in text_content),
            'driver': sum(1 for kw in driver_keywords if kw in text_content),
            'amiable': sum(1 for kw in amiable_keywords if kw in text_content),
            'expressive': sum(1 for kw in expressive_keywords if kw in text_content)
        }
        
        # 確定主要風格
        primary_style = max(scores, key=scores.get)
        confidence = scores[primary_style] / max(sum(scores.values()), 1)
        
        return {
            'style': primary_style,
            'confidence': confidence,
            'characteristics': self.psychological_patterns['decision_making_styles'][primary_style],
            'scores': scores
        }
    
    async def _analyze_emotional_state(self, conversation_history: List[Dict]) -> Dict[str, Any]:
        """分析當前情緒狀態"""
        if not conversation_history:
            return {'primary_emotion': 'neutral', 'intensity': 0}
        
        # 獲取最近的對話
        recent_messages = conversation_history[-3:] if len(conversation_history) >= 3 else conversation_history
        recent_text = ' '.join([msg.get('content', '') for msg in recent_messages])
        
        # 情緒指標
        emotions = {
            'interested': ['想了解', '告訴我', '怎麼樣', '可以嗎', '詳細'],
            'hesitant': ['不確定', '考慮', '想想', '可能', '或許'],
            'excited': ['太好了', '真的', '馬上', '！', '期待'],
            'concerned': ['擔心', '害怕', '會不會', '萬一', '風險'],
            'skeptical': ['真的嗎', '確定', '別人', '聽說', '是不是'],
            'price_sensitive': ['貴', '便宜', '優惠', '折扣', '預算']
        }
        
        emotion_scores = {}
        for emotion, keywords in emotions.items():
            emotion_scores[emotion] = sum(1 for kw in keywords if kw in recent_text)
        
        # 確定主要情緒
        primary_emotion = max(emotion_scores, key=emotion_scores.get) if any(emotion_scores.values()) else 'neutral'
        intensity = emotion_scores.get(primary_emotion, 0) / max(len(recent_text.split()), 1)
        
        return {
            'primary_emotion': primary_emotion,
            'intensity': min(intensity * 10, 1.0),
            'emotion_mix': emotion_scores,
            'trend': self._analyze_emotion_trend(conversation_history)
        }
    
    def _analyze_emotion_trend(self, conversation_history: List[Dict]) -> str:
        """分析情緒趨勢"""
        if len(conversation_history) < 2:
            return 'stable'
        
        # 簡化的情緒趨勢分析
        early_positive = sum(1 for msg in conversation_history[:len(conversation_history)//2] 
                           if any(word in msg.get('content', '') for word in ['好', '可以', '想']))
        late_positive = sum(1 for msg in conversation_history[len(conversation_history)//2:] 
                          if any(word in msg.get('content', '') for word in ['好', '可以', '想']))
        
        if late_positive > early_positive:
            return 'improving'
        elif late_positive < early_positive:
            return 'declining'
        return 'stable'
    
    async def _predict_purchase_barriers(
        self, 
        psychological_profile: Dict,
        emotional_state: Dict
    ) -> List[Dict[str, Any]]:
        """預測購買障礙"""
        barriers = []
        
        # 基於人格類型預測障礙
        personality_type = psychological_profile.get('personality_type', 'unknown')
        if personality_type in self.personality_types:
            objection_prob = self.personality_types[personality_type]['objection_probability']
            if objection_prob > 0.6:
                barriers.append({
                    'type': 'high_objection_tendency',
                    'probability': objection_prob,
                    'reason': f'{personality_type} 類型客戶通常需要更多說服'
                })
        
        # 基於恐懼因素預測障礙
        for fear in psychological_profile.get('fear_factors', []):
            barriers.append({
                'type': f'fear_{fear}',
                'probability': 0.7,
                'reason': f'客戶表現出 {fear} 相關的擔憂'
            })
        
        # 基於情緒狀態預測障礙
        if emotional_state.get('primary_emotion') in ['hesitant', 'concerned', 'skeptical']:
            barriers.append({
                'type': 'emotional_resistance',
                'probability': 0.8,
                'reason': f'客戶當前情緒狀態 ({emotional_state["primary_emotion"]}) 可能影響決策'
            })
        
        # 基於信任度預測障礙
        if psychological_profile.get('trust_level', 0) < 0.4:
            barriers.append({
                'type': 'low_trust',
                'probability': 0.9,
                'reason': '信任度較低，需要建立更多信任'
            })
        
        return sorted(barriers, key=lambda x: x['probability'], reverse=True)
    
    async def _generate_personalized_strategy(
        self,
        psychological_profile: Dict,
        decision_style: Dict,
        emotional_state: Dict,
        purchase_barriers: List[Dict]
    ) -> Dict[str, Any]:
        """生成個性化銷售策略"""
        strategy = {
            'primary_approach': '',
            'secondary_approach': '',
            'triggers': [],
            'avoid': [],
            'tone': 'professional',
            'pace': 'moderate',
            'content_focus': [],
            'objection_handlers': {}
        }
        
        # 基於決策風格設定主要方法
        style = decision_style.get('style', 'unknown')
        if style == 'analytical':
            strategy['primary_approach'] = 'data_driven_presentation'
            strategy['content_focus'] = ['詳細數據', '對比分析', '科學依據']
            strategy['tone'] = 'professional_detailed'
            strategy['pace'] = 'slow'
        elif style == 'driver':
            strategy['primary_approach'] = 'benefit_focused_pitch'
            strategy['content_focus'] = ['核心利益', '時間效率', 'ROI']
            strategy['tone'] = 'direct_confident'
            strategy['pace'] = 'fast'
        elif style == 'amiable':
            strategy['primary_approach'] = 'relationship_building'
            strategy['content_focus'] = ['服務品質', '客戶關懷', '安全保障']
            strategy['tone'] = 'warm_supportive'
            strategy['pace'] = 'gentle'
        elif style == 'expressive':
            strategy['primary_approach'] = 'experience_storytelling'
            strategy['content_focus'] = ['成功案例', '獨特體驗', '情感價值']
            strategy['tone'] = 'enthusiastic_engaging'
            strategy['pace'] = 'dynamic'
        
        # 基於心理檔案選擇觸發點
        if 'family_care' in [m['type'] for m in psychological_profile.get('primary_motivations', [])]:
            strategy['triggers'].extend(['家庭健康套餐', '守護家人健康', '代際關懷'])
        
        if 'health_preservation' in [m['type'] for m in psychological_profile.get('primary_motivations', [])]:
            strategy['triggers'].extend(['預防勝於治療', '早期發現優勢', '健康投資回報'])
        
        # 基於情緒狀態調整策略
        if emotional_state.get('primary_emotion') == 'price_sensitive':
            strategy['triggers'].extend(self.emotional_triggers['value_enhancers'])
            strategy['secondary_approach'] = 'value_demonstration'
        elif emotional_state.get('primary_emotion') == 'concerned':
            strategy['triggers'].extend(self.emotional_triggers['fear_reducers'])
            strategy['secondary_approach'] = 'reassurance_focus'
        
        # 設定需要避免的話題
        if 'health_anxiety' in psychological_profile.get('fear_factors', []):
            strategy['avoid'].extend(['嚴重疾病案例', '恐怖統計數據', '最壞情況'])
        
        if 'financial_concern' in psychological_profile.get('fear_factors', []):
            strategy['avoid'].extend(['額外費用', '價格上漲', '隱藏成本'])
        
        # 準備異議處理策略
        for barrier in purchase_barriers[:3]:  # 處理前3個最可能的障礙
            if barrier['type'] == 'high_objection_tendency':
                strategy['objection_handlers']['general'] = '理解您的謹慎，這確實是重要決定。讓我為您提供更多信息...'
            elif 'fear_' in barrier['type']:
                strategy['objection_handlers'][barrier['type']] = f'完全理解您的擔心。其實我們有專門的措施來解決這個問題...'
            elif barrier['type'] == 'low_trust':
                strategy['objection_handlers']['trust'] = '我們是政府認證的醫療機構，這裡有我們的資質證明...'
        
        return strategy
    
    async def _calculate_psychological_readiness(
        self,
        psychological_profile: Dict,
        emotional_state: Dict,
        context: Dict
    ) -> float:
        """計算心理準備度分數 (0-100)"""
        score = 50.0  # 基礎分數
        
        # 動機強度加分 (最高 +20)
        motivations = psychological_profile.get('primary_motivations', [])
        if motivations:
            avg_motivation_strength = sum(m['strength'] for m in motivations) / len(motivations)
            score += avg_motivation_strength * 20
        
        # 信任度加分 (最高 +15)
        trust_level = psychological_profile.get('trust_level', 0)
        score += trust_level * 15
        
        # 開放度加分 (最高 +10)
        openness = psychological_profile.get('openness_score', 0)
        score += openness * 10
        
        # 情緒狀態影響 (-10 到 +10)
        emotion = emotional_state.get('primary_emotion', 'neutral')
        emotion_scores = {
            'interested': 10,
            'excited': 10,
            'neutral': 0,
            'hesitant': -5,
            'concerned': -5,
            'skeptical': -10,
            'price_sensitive': -5
        }
        score += emotion_scores.get(emotion, 0)
        
        # 情緒趨勢影響 (-5 到 +5)
        trend = emotional_state.get('trend', 'stable')
        trend_scores = {'improving': 5, 'stable': 0, 'declining': -5}
        score += trend_scores.get(trend, 0)
        
        # 恐懼因素扣分 (每個 -5)
        fear_count = len(psychological_profile.get('fear_factors', []))
        score -= fear_count * 5
        
        # 互動深度加分 (最高 +10)
        interaction_count = len(context.get('conversation_history', []))
        if interaction_count > 10:
            score += 10
        elif interaction_count > 5:
            score += 5
        
        # 確保分數在 0-100 範圍內
        return max(0, min(100, score))