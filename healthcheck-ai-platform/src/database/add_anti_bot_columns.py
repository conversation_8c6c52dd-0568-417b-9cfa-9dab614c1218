"""
添加防機器人偵測相關的資料庫欄位
"""
from sqlalchemy import Column, String, Integer, Float, DateTime, JSON, Bo<PERSON><PERSON>, create_engine
from sqlalchemy.sql import text
from datetime import datetime
import logging

logger = logging.getLogger(__name__)

def add_anti_bot_columns(database_url: str):
    """添加防機器人偵測相關的欄位"""
    engine = create_engine(database_url)
    
    with engine.connect() as conn:
        # 為 messages 表添加防偵測欄位
        try:
            conn.execute(text("""
                ALTER TABLE messages ADD COLUMN IF NOT EXISTS 
                user_agent VARCHAR(500);
            """))
            
            conn.execute(text("""
                ALTER TABLE messages ADD COLUMN IF NOT EXISTS 
                ip_address VARCHAR(45);
            """))
            
            conn.execute(text("""
                ALTER TABLE messages ADD COLUMN IF NOT EXISTS 
                session_id VARCHAR(100);
            """))
            
            conn.execute(text("""
                ALTER TABLE messages ADD COLUMN IF NOT EXISTS 
                device_fingerprint VARCHAR(200);
            """))
            
            conn.execute(text("""
                ALTER TABLE messages ADD COLUMN IF NOT EXISTS 
                typing_pattern JSON;
            """))
            
            logger.info("已成功添加 messages 表的防偵測欄位")
        except Exception as e:
            logger.error(f"添加 messages 表欄位時出錯: {e}")
        
        # 為 conversations 表添加行為分析欄位
        try:
            conn.execute(text("""
                ALTER TABLE conversations ADD COLUMN IF NOT EXISTS 
                behavior_score FLOAT DEFAULT 1.0;
            """))
            
            conn.execute(text("""
                ALTER TABLE conversations ADD COLUMN IF NOT EXISTS 
                interaction_patterns JSON;
            """))
            
            conn.execute(text("""
                ALTER TABLE conversations ADD COLUMN IF NOT EXISTS 
                suspicious_activities JSON;
            """))
            
            logger.info("已成功添加 conversations 表的行為分析欄位")
        except Exception as e:
            logger.error(f"添加 conversations 表欄位時出錯: {e}")
        
        # 創建新的防機器人偵測記錄表
        try:
            conn.execute(text("""
                CREATE TABLE IF NOT EXISTS anti_bot_logs (
                    id VARCHAR(36) PRIMARY KEY,
                    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    event_type VARCHAR(50) NOT NULL,
                    customer_id VARCHAR(36),
                    conversation_id VARCHAR(36),
                    risk_score FLOAT DEFAULT 0.0,
                    detection_reason JSON,
                    action_taken VARCHAR(100),
                    metadata JSON,
                    FOREIGN KEY (customer_id) REFERENCES customers(id),
                    FOREIGN KEY (conversation_id) REFERENCES conversations(id),
                    INDEX idx_anti_bot_timestamp (timestamp),
                    INDEX idx_anti_bot_risk (risk_score),
                    INDEX idx_anti_bot_customer (customer_id)
                );
            """))
            
            logger.info("已成功創建 anti_bot_logs 表")
        except Exception as e:
            logger.error(f"創建 anti_bot_logs 表時出錯: {e}")
        
        # 創建會話追蹤表
        try:
            conn.execute(text("""
                CREATE TABLE IF NOT EXISTS session_tracking (
                    id VARCHAR(36) PRIMARY KEY,
                    session_id VARCHAR(100) UNIQUE NOT NULL,
                    customer_id VARCHAR(36),
                    started_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    last_activity TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                    page_views INTEGER DEFAULT 0,
                    mouse_movements JSON,
                    click_patterns JSON,
                    scroll_behavior JSON,
                    time_spent_seconds INTEGER DEFAULT 0,
                    is_bot_suspected BOOLEAN DEFAULT FALSE,
                    FOREIGN KEY (customer_id) REFERENCES customers(id),
                    INDEX idx_session_id (session_id),
                    INDEX idx_session_customer (customer_id),
                    INDEX idx_session_activity (last_activity)
                );
            """))
            
            logger.info("已成功創建 session_tracking 表")
        except Exception as e:
            logger.error(f"創建 session_tracking 表時出錯: {e}")
        
        conn.commit()

if __name__ == "__main__":
    # 測試腳本
    import os
    from dotenv import load_dotenv
    
    load_dotenv()
    database_url = os.getenv("DATABASE_URL", "mysql+pymysql://root:password@localhost/healthcheck_db")
    
    add_anti_bot_columns(database_url)