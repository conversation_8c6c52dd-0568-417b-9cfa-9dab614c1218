from sqlalchemy import Column, String, Integer, Float, DateTime, Text, JSON, Boolean, ForeignKey, Enum, Index
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import relationship
from datetime import datetime
import enum
import uuid

Base = declarative_base()

class ConversationStatus(enum.Enum):
    ACTIVE = "active"
    COMPLETED = "completed"
    ABANDONED = "abandoned"
    FOLLOW_UP = "follow_up"

class CustomerSegment(enum.Enum):
    PRICE_SENSITIVE = "price_sensitive"
    BUSY_PROFESSIONAL = "busy_professional"
    HEALTH_CONSCIOUS = "health_conscious"
    ABANDONED_CART = "abandoned_cart"
    GENERAL = "general"

class MessageType(enum.Enum):
    CUSTOMER = "customer"
    AGENT = "agent"
    SYSTEM = "system"

class Customer(Base):
    __tablename__ = 'customers'
    
    id = Column(String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    phone_number = Column(String(20), unique=True, nullable=False, index=True)
    name = Column(String(100))
    segment = Column(Enum(CustomerSegment), default=CustomerSegment.GENERAL)
    preferred_language = Column(String(10), default='zh-TW')
    timezone = Column(String(50), default='Asia/Hong_Kong')
    
    # 客戶畫像
    age_group = Column(String(20))  # 20-30, 30-40, etc
    occupation = Column(String(100))
    health_concerns = Column(JSON)  # List of health concerns
    budget_range = Column(String(50))
    
    # 互動統計
    total_conversations = Column(Integer, default=0)
    completed_purchases = Column(Integer, default=0)
    total_spent = Column(Float, default=0.0)
    last_interaction = Column(DateTime)
    engagement_score = Column(Float, default=0.0)
    
    # 防機器人檢測
    behavior_score = Column(Float, default=1.0)  # 1.0 = 正常人類行為
    suspicious_activities = Column(Integer, default=0)
    
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # 偏好設置
    best_contact_times = Column(JSON)  # List of preferred hours
    communication_preferences = Column(JSON)
    
    # Relationships
    conversations = relationship("Conversation", back_populates="customer")
    purchases = relationship("Purchase", back_populates="customer")
    
    __table_args__ = (
        Index('idx_customer_segment', 'segment'),
        Index('idx_customer_last_interaction', 'last_interaction'),
    )


class KnowledgeBaseItem(Base):
    """知識庫項目"""
    __tablename__ = 'knowledge_base_items'
    
    id = Column(Integer, primary_key=True)
    item_id = Column(String(100), unique=True, nullable=False, index=True)
    category = Column(String(50), nullable=False, index=True)
    topic = Column(String(200), nullable=False, index=True)
    content = Column(JSON, nullable=False)
    metadata = Column(JSON)
    
    # 版本控制
    version = Column(String(50), nullable=False)
    confidence = Column(Float, default=1.0)
    
    # 向量嵌入（用於語義搜索）
    embedding = Column(JSON)  # 存儲向量數據
    embedding_model = Column(String(50))  # 使用的嵌入模型
    
    # 審計字段
    created_at = Column(DateTime, default=datetime.utcnow)
    created_by = Column(String(100), nullable=False)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    updated_by = Column(String(100))
    is_active = Column(Boolean, default=True)
    
    # 索引
    __table_args__ = (
        Index('idx_kb_category_topic', 'category', 'topic'),
        Index('idx_kb_version_active', 'version', 'is_active'),
    )


class KnowledgeVersion(Base):
    """知識庫版本"""
    __tablename__ = 'knowledge_versions'
    
    id = Column(Integer, primary_key=True)
    version = Column(String(50), unique=True, nullable=False)
    description = Column(Text)
    changes = Column(JSON)  # 變更記錄
    
    created_at = Column(DateTime, default=datetime.utcnow)
    created_by = Column(String(100), nullable=False)
    is_active = Column(Boolean, default=True)
    
    # 統計
    total_items = Column(Integer, default=0)
    categories = Column(JSON)  # 各類別項目數


class Conversation(Base):
    __tablename__ = 'conversations'
    
    id = Column(String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    customer_id = Column(String(36), ForeignKey('customers.id'), nullable=False)
    status = Column(Enum(ConversationStatus), default=ConversationStatus.ACTIVE)
    
    # 對話元數據
    started_at = Column(DateTime, default=datetime.utcnow)
    ended_at = Column(DateTime)
    last_message_at = Column(DateTime)
    message_count = Column(Integer, default=0)
    
    # 業務數據
    current_stage = Column(String(50), default='greeting')
    quoted_packages = Column(JSON)  # List of quoted package IDs
    final_quoted_price = Column(Float)
    conversion_probability = Column(Float)
    
    # AI 分析
    customer_intent = Column(JSON)  # Detected intents
    identified_needs = Column(JSON)  # Identified customer needs
    objections_raised = Column(JSON)  # List of objections
    
    # 質量指標
    quality_score = Column(Float)
    response_accuracy = Column(Float)
    customer_satisfaction = Column(Float)
    
    # Relationships
    customer = relationship("Customer", back_populates="conversations")
    messages = relationship("Message", back_populates="conversation", order_by="Message.timestamp")
    
    __table_args__ = (
        Index('idx_conversation_status', 'status'),
        Index('idx_conversation_customer', 'customer_id'),
        Index('idx_conversation_started', 'started_at'),
    )

class Message(Base):
    __tablename__ = 'messages'
    
    id = Column(String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    conversation_id = Column(String(36), ForeignKey('conversations.id'), nullable=False)
    message_type = Column(Enum(MessageType), nullable=False)
    
    # 消息內容
    content = Column(Text, nullable=False)
    whatsapp_message_id = Column(String(100), unique=True)
    
    # 元數據
    timestamp = Column(DateTime, default=datetime.utcnow, index=True)
    response_time_ms = Column(Integer)  # Response time in milliseconds
    
    # AI 處理
    processed_by_agent = Column(String(50))  # Which agent processed this
    detected_intent = Column(String(100))
    sentiment_score = Column(Float)  # -1 to 1
    
    # 質量檢查
    compliance_checked = Column(Boolean, default=False)
    compliance_issues = Column(JSON)  # List of compliance violations
    
    # Relationships
    conversation = relationship("Conversation", back_populates="messages")
    
    __table_args__ = (
        Index('idx_message_conversation', 'conversation_id'),
        Index('idx_message_timestamp', 'timestamp'),
    )

class HealthPackage(Base):
    __tablename__ = 'health_packages'
    
    id = Column(String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    code = Column(String(20), unique=True, nullable=False)
    name = Column(String(200), nullable=False)
    description = Column(Text)
    
    # 價格信息
    base_price = Column(Float, nullable=False)
    special_price = Column(Float)
    is_promotion = Column(Boolean, default=False)
    promotion_end_date = Column(DateTime)
    
    # 套餐內容
    items = Column(JSON)  # List of included items
    total_items = Column(Integer)
    duration_hours = Column(Float)  # Expected duration
    
    # 適用條件
    gender_specific = Column(String(10))  # M/F/Both
    min_age = Column(Integer)
    max_age = Column(Integer)
    
    # 統計
    popularity_score = Column(Float, default=0.0)
    conversion_rate = Column(Float, default=0.0)
    
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    is_active = Column(Boolean, default=True)
    
    # Relationships
    purchases = relationship("Purchase", back_populates="package")
    
    __table_args__ = (
        Index('idx_package_active', 'is_active'),
        Index('idx_package_price', 'base_price'),
    )

class Purchase(Base):
    __tablename__ = 'purchases'
    
    id = Column(String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    customer_id = Column(String(36), ForeignKey('customers.id'), nullable=False)
    package_id = Column(String(36), ForeignKey('health_packages.id'), nullable=False)
    conversation_id = Column(String(36), ForeignKey('conversations.id'))
    
    # 交易信息
    purchase_date = Column(DateTime, default=datetime.utcnow)
    original_price = Column(Float, nullable=False)
    discount_amount = Column(Float, default=0.0)
    final_price = Column(Float, nullable=False)
    
    # 優惠信息
    promotions_applied = Column(JSON)  # List of applied promotions
    referral_code = Column(String(50))
    
    # 預約信息
    appointment_date = Column(DateTime)
    appointment_location = Column(String(200))
    appointment_status = Column(String(50), default='pending')
    
    # 支付信息
    payment_method = Column(String(50))
    payment_status = Column(String(50), default='pending')
    payment_reference = Column(String(100))
    
    # Relationships
    customer = relationship("Customer", back_populates="purchases")
    package = relationship("HealthPackage", back_populates="purchases")
    
    __table_args__ = (
        Index('idx_purchase_customer', 'customer_id'),
        Index('idx_purchase_date', 'purchase_date'),
        Index('idx_purchase_appointment', 'appointment_date'),
    )

class AgentPerformance(Base):
    __tablename__ = 'agent_performance'
    
    id = Column(String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    agent_name = Column(String(50), nullable=False)
    date = Column(DateTime, nullable=False)
    
    # 性能指標
    total_interactions = Column(Integer, default=0)
    successful_interactions = Column(Integer, default=0)
    avg_response_time_ms = Column(Float)
    accuracy_score = Column(Float)
    
    # 業務指標
    conversions_influenced = Column(Integer, default=0)
    revenue_influenced = Column(Float, default=0.0)
    customer_satisfaction = Column(Float)
    
    # 質量指標
    compliance_score = Column(Float)
    error_count = Column(Integer, default=0)
    quality_issues = Column(JSON)
    
    # 改進建議
    optimization_suggestions = Column(JSON)
    
    created_at = Column(DateTime, default=datetime.utcnow)
    
    __table_args__ = (
        Index('idx_agent_performance_name_date', 'agent_name', 'date'),
    )

class SystemMetrics(Base):
    __tablename__ = 'system_metrics'
    
    id = Column(String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    timestamp = Column(DateTime, default=datetime.utcnow, index=True)
    
    # 系統性能
    cpu_usage = Column(Float)
    memory_usage = Column(Float)
    active_conversations = Column(Integer)
    queue_length = Column(Integer)
    
    # API 性能
    whatsapp_api_latency = Column(Float)
    llm_api_latency = Column(Float)
    database_latency = Column(Float)
    
    # 業務指標
    hourly_messages = Column(Integer)
    hourly_conversions = Column(Integer)
    hourly_revenue = Column(Float)
    
    # 錯誤統計
    error_count = Column(Integer, default=0)
    error_types = Column(JSON)
    
    __table_args__ = (
        Index('idx_system_metrics_timestamp', 'timestamp'),
    )

class PromotionRule(Base):
    __tablename__ = 'promotion_rules'
    
    id = Column(String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    name = Column(String(100), nullable=False)
    code = Column(String(50), unique=True)
    
    # 規則條件
    rule_type = Column(String(50))  # percentage, fixed, bundle, etc
    conditions = Column(JSON)  # Complex conditions
    discount_value = Column(Float)  # Percentage or fixed amount
    
    # 有效期
    start_date = Column(DateTime)
    end_date = Column(DateTime)
    is_active = Column(Boolean, default=True)
    
    # 使用限制
    max_uses_total = Column(Integer)
    max_uses_per_customer = Column(Integer)
    current_uses = Column(Integer, default=0)
    
    # 適用範圍
    applicable_packages = Column(JSON)  # List of package IDs
    customer_segments = Column(JSON)  # List of eligible segments
    
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    __table_args__ = (
        Index('idx_promotion_active', 'is_active'),
        Index('idx_promotion_dates', 'start_date', 'end_date'),
    )

class FollowUpTask(Base):
    __tablename__ = 'follow_up_tasks'
    
    id = Column(String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    customer_id = Column(String(36), ForeignKey('customers.id'), nullable=False)
    conversation_id = Column(String(36), ForeignKey('conversations.id'))
    
    # 任務詳情
    task_type = Column(String(50))  # follow_up, reminder, etc
    scheduled_time = Column(DateTime, nullable=False, index=True)
    channel = Column(String(20), default='whatsapp')
    
    # 消息內容
    message_template = Column(Text)
    personalization_data = Column(JSON)
    
    # 狀態追蹤
    status = Column(String(20), default='pending')  # pending, sent, failed, cancelled
    sent_at = Column(DateTime)
    response_received = Column(Boolean, default=False)
    
    # 策略信息
    urgency_level = Column(String(20))
    strategy_used = Column(String(50))
    
    created_at = Column(DateTime, default=datetime.utcnow)
    
    __table_args__ = (
        Index('idx_follow_up_scheduled', 'scheduled_time'),
        Index('idx_follow_up_status', 'status'),
        Index('idx_follow_up_customer', 'customer_id'),
    )