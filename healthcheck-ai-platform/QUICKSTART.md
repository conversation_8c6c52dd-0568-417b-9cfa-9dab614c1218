# 🚀 HealthCheck AI Platform - 快速開始指南

## 項目已準備就緒！

所有核心代碼已經創建在 `healthcheck-ai-platform` 目錄中。

### 📁 項目結構

```
healthcheck-ai-platform/
├── src/                    # 源代碼
│   ├── agents/            # AI Agents（已實現 2 個）
│   ├── core/              # 核心功能（對話管理）
│   ├── integrations/      # 集成（模擬 WhatsApp）
│   └── main.py            # 主應用入口
├── config/                # 配置文件
├── testing/               # 測試文件
├── deployment/            # 部署配置
├── requirements.txt       # Python 依賴
├── docker-compose.yml     # Docker 配置
└── README.md             # 項目說明
```

### ✅ 已完成的功能

1. **Traffic Reception Agent** - 流量接待和客戶分類
2. **Pricing Calculator Agent** - 精確價格計算
3. **模擬 WhatsApp 接口** - 用於開發測試
4. **對話管理框架** - 處理對話流程
5. **FastAPI 應用** - RESTful API
6. **Docker 支持** - 容器化部署

### 🎯 快速運行

1. **進入項目目錄**
   ```bash
   cd healthcheck-ai-platform
   ```

2. **創建虛擬環境**
   ```bash
   python3 -m venv venv
   source venv/bin/activate  # Mac/Linux
   ```

3. **安裝依賴**
   ```bash
   pip install -r requirements.txt
   ```

4. **設置環境變量**
   ```bash
   cp .env.example .env
   # 編輯 .env 文件
   ```

5. **運行應用**
   ```bash
   # 方式 1：直接運行
   python src/main.py
   
   # 方式 2：使用 Docker
   docker-compose up
   ```

### 🧪 測試對話

```bash
# 發送測試消息
curl -X POST http://localhost:8000/test/send_message \
  -H "Content-Type: application/json" \
  -d '{"sender_id": "test_user", "content": "我想了解體檢套餐"}'

# 運行測試場景
python testing/test_conversation_flow.py
```

### 📝 下一步

1. **完成剩餘的 Agents**
   - Sales Progression Agent
   - Follow-up Strategy Agent
   - Quality Monitor Agent

2. **添加真實功能**
   - 數據庫持久化
   - LLM 集成（需要 API key）
   - 真實 WhatsApp API

3. **部署到生產環境**
   - 配置真實的環境變量
   - 設置監控和日誌
   - 配置 HTTPS

### 🔑 重要提醒

- 記得在 `.env` 文件中配置你的 API 密鑰
- 默認使用模擬的 WhatsApp 接口
- 價格計算已包含多種優惠邏輯
- 所有文本都是繁體中文

需要幫助？查看 `README.md` 獲取更多詳情！