#!/usr/bin/env python3
"""
測試客服監察系統
"""
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.analysis.service_performance_analyzer import ServicePerformanceAnalyzer
from src.analysis.follow_up_tracker import Follow<PERSON>p<PERSON>racker
from src.analysis.periodic_report_generator import PeriodicReportGenerator
from datetime import datetime

def test_performance_analyzer():
    """測試績效分析器"""
    print("=== 測試客服績效分析 ===\n")
    
    analyzer = ServicePerformanceAnalyzer("/Users/<USER>/Desktop/Claude/whatsapp-mcp/whatsapp-bridge/store/messages.db")
    
    # 生成4小時績效報告
    print("生成最近4小時績效報告...")
    report = analyzer.generate_performance_report(4)
    
    print(f"\n📊 整體概況:")
    print(f"- 活躍對話數: {report['summary']['total_conversations']}")
    print(f"- 已轉化: {report['summary']['converted']}")
    print(f"- 待處理: {report['summary']['pending']}")
    print(f"- 需跟進: {report['summary']['need_follow_up']}")
    print(f"- 平均響應時間: {report['summary']['avg_response_time']}")
    print(f"- 平均質量分數: {report['summary']['avg_quality_score']:.1f}/100")
    
    if report['best_conversations']:
        print(f"\n⭐ 優秀案例 (前3個):")
        for i, conv in enumerate(report['best_conversations'][:3], 1):
            print(f"\n{i}. 客戶: {conv['customer']}")
            print(f"   質量分數: {conv['quality_score']}")
            print(f"   響應時間: {conv['response_time']}")
            print(f"   成功因素: {', '.join(conv['key_success_factors'])}")
    
    if report['areas_for_improvement']:
        print(f"\n💡 改進建議:")
        for area in report['areas_for_improvement']:
            print(f"- {area}")

def test_follow_up_tracker():
    """測試跟進追蹤系統"""
    print("\n\n=== 測試客戶跟進追蹤 ===\n")
    
    tracker = FollowUpTracker("/Users/<USER>/Desktop/Claude/whatsapp-mcp/whatsapp-bridge/store/messages.db")
    
    # 識別需要跟進的客戶
    print("識別需要跟進的客戶...")
    follow_ups = tracker.identify_follow_up_customers(168)  # 過去7天
    
    print(f"\n找到 {len(follow_ups)} 位需要跟進的客戶\n")
    
    # 按優先級顯示
    for priority in ['urgent', 'high', 'medium', 'low']:
        priority_customers = [f for f in follow_ups if f.priority.value == priority]
        if priority_customers:
            print(f"\n【{priority.upper()} 優先級】({len(priority_customers)}位)")
            for customer in priority_customers[:3]:  # 每個優先級顯示前3個
                print(f"\n客戶: {customer.customer_name}")
                print(f"電話: {customer.phone_number}")
                print(f"最後聯繫: {customer.last_contact.strftime('%m/%d %H:%M')}")
                print(f"跟進原因: {customer.follow_up_reason}")
                print(f"建議消息: {customer.suggested_message}")
                print(f"標籤: {', '.join(customer.tags)}")
                print("-" * 50)

def test_periodic_reports():
    """測試定期報告生成"""
    print("\n\n=== 測試定期報告生成 ===\n")
    
    generator = PeriodicReportGenerator(
        "/Users/<USER>/Desktop/Claude/whatsapp-mcp/whatsapp-bridge/store/messages.db",
        "test_reports"
    )
    
    # 生成4小時監察報告
    print("生成4小時監察報告...")
    hourly_report = generator.generate_4hour_report()
    print("\n4小時報告已生成:")
    print(generator.format_report_for_display(hourly_report))
    
    # 生成每日跟進清單
    print("\n\n生成明日跟進清單...")
    daily_report = generator.generate_daily_follow_up_list()
    print("\n每日跟進清單已生成:")
    print(generator.format_report_for_display(daily_report))
    
    print(f"\n\n報告已保存到 test_reports 目錄")

def main():
    """主測試函數"""
    print(f"開始測試客服監察系統")
    print(f"當前時間: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 80)
    
    # 執行各項測試
    test_performance_analyzer()
    test_follow_up_tracker()
    test_periodic_reports()
    
    print("\n\n=== 測試完成 ===")
    print("\n系統功能總結:")
    print("1. ✅ 客服績效分析 - 評估回覆質量、轉化率、響應時間")
    print("2. ✅ 客戶跟進追蹤 - 智能識別需要跟進的客戶並提供建議")
    print("3. ✅ 4小時監察報告 - 實時掌握服務狀況和關鍵指標")
    print("4. ✅ 每日跟進清單 - 第二天待辦事項，按優先級排序")
    print("5. ✅ 優秀案例識別 - 學習最佳實踐，持續改進")

if __name__ == "__main__":
    main()