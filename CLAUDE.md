# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Repository Overview

This is a multi-project repository containing AI-powered tools and platforms:

1. **HealthCheck AI Platform** (`/healthcheck-ai-platform/`) - WhatsApp-based AI sales/service system for healthcare
2. **Coding Agents** (`/coding_agents/`) - AI assistants for code review, testing, and documentation
3. **WhatsApp MCP** (`/whatsapp-mcp/`) - Model Context Protocol server for WhatsApp integration
4. **Obsidian MCP** (`/obsidian-mcp/`) - MCP server for Obsidian note-taking app
5. **Claude Code Documentation** (root) - Setup guides and examples for Claude Code CLI

## Common Development Commands

### HealthCheck AI Platform
```bash
# Setup and start
./setup.sh                           # Initial setup
./scripts/start.sh                   # Start with Docker
docker-compose up -d                 # Start all services
python src/main.py                   # Run without Docker (activate venv first)

# Testing
python run_test.py                   # Quick mock conversation tests
python testing/test_conversation_flow.py  # Test specific conversation flows
pytest                               # Full test suite
pytest --cov                         # With coverage
pytest -k "test_name"                # Run specific test

# Code quality
black .                              # Format code
flake8                              # Lint
mypy .                              # Type check

# Database
alembic upgrade head                # Run migrations

# Docker management
docker-compose logs -f [service]     # View logs (e.g., api, postgres, redis)
docker-compose restart [service]     # Restart specific service

# Monitoring
# Prometheus: http://localhost:9090
# Grafana: http://localhost:3000 (admin/admin)
```

### WhatsApp MCP
```bash
# WhatsApp Bridge (Go)
cd whatsapp-mcp/whatsapp-bridge
go run main.go                       # Run the bridge
# Windows: set CGO_ENABLED=0 before running

# MCP Server (Python)
cd whatsapp-mcp
uv sync                             # Install dependencies
uv run mcp-whatsapp                 # Run the server
# OR after installation:
uv pip install -e .
mcp-whatsapp
```

### Obsidian MCP
```bash
cd obsidian-mcp
uv sync                             # Install dependencies
uvx mcp-obsidian                    # Run directly
# OR:
uv pip install -e .
mcp-obsidian

# Requires Obsidian REST API plugin running on port 27124
```

### Coding Agents
```bash
cd coding_agents
python src/main.py                   # Run orchestrator
# Example usage:
python src/examples/test_agents.py   # Test with sample code
```

### Python Projects (General)
```bash
# Virtual environment
python3 -m venv venv
source venv/bin/activate            # Linux/Mac
venv\Scripts\activate               # Windows

# Dependencies
pip install -r requirements.txt      # From requirements file
pip install -e .                    # Install package in editable mode
```

## Architecture Patterns

### Multi-Agent Systems
Both HealthCheck AI Platform and Coding Agents use specialized AI agents:
- **HealthCheck**: 5 agents (Traffic Reception, Pricing Calculator, Sales Progression, Follow-up Strategy, Quality Monitor)
- **Coding Agents**: 3 agents (Code Review, Testing, Documentation) with an orchestrator

### HealthCheck AI Platform Architecture
- **API Layer**: FastAPI with async endpoints at `/api/v1/`
- **Agent Orchestration**: ConversationManager coordinates agent execution
- **Message Flow**: WhatsApp → Webhook → Validation → Agent Processing → Response
- **Background Tasks**: Async processing for non-blocking operations
- **Caching**: Redis for conversation state and temporary data
- **Database**: PostgreSQL with SQLAlchemy ORM for persistence

### MCP Server Architecture
- **Protocol**: Model Context Protocol for tool exposure
- **WhatsApp MCP**: Go bridge (whatsmeow) + Python MCP server
- **Obsidian MCP**: Direct REST API integration with Obsidian
- **Tool Registration**: Decorators for exposing functions as MCP tools

### Technology Stack
- **Languages**: Python (primary), Go (WhatsApp bridge)
- **Frameworks**: FastAPI, asyncio, CrewAI, LangChain
- **Databases**: PostgreSQL, Redis, SQLite
- **AI Integration**: OpenAI API, Claude, LangChain
- **Containerization**: Docker, Docker Compose
- **Monitoring**: Prometheus, Grafana, OpenTelemetry

### Key Directories
- Config files: `config/agents/`, `config/business_rules/`
- Agent implementations: `src/agents/`
- Core services: `src/core/` (validators, managers, engines)
- API integrations: `src/integrations/`
- Test scenarios: `testing/scenarios/`
- Deployment scripts: `scripts/`

## Development Guidelines

### When Working with Agents
- Agents inherit from base classes (`base_agent.py`, `base_coding_agent.py`)
- Each agent has specific responsibilities - maintain separation of concerns
- Use asyncio for concurrent operations
- Agent configurations are in YAML files under `config/agents/`

### When Working with MCP Servers
- Follow the MCP protocol specifications
- Use the provided tool decorators for exposing functionality
- Test with the MCP CLI tools
- UV is the preferred package manager for MCP projects

### API Endpoints (HealthCheck AI Platform)
- `POST /api/v1/webhooks/whatsapp` - Receive WhatsApp messages
- `GET /api/v1/conversations/{id}` - Get conversation details
- `GET /api/v1/metrics` - Performance metrics
- `GET /health` - Health check endpoint

### Testing Approach
- Unit tests in `testing/unit-tests/`
- Integration tests in `testing/scenarios/`
- Use pytest fixtures for common test data
- Mock external services (WhatsApp, LLMs) for testing
- Use `run_test.py` for quick mock conversation testing

### Configuration Management
- Environment variables in `.env` files
- Agent behaviors in YAML configuration files
- Business rules in `config/business_rules/`
- Prompt templates in `config/prompts/`

### Dependencies by Project
- **HealthCheck AI**: FastAPI, CrewAI, LangChain, SQLAlchemy, Redis, Twilio
- **WhatsApp MCP**: whatsmeow (Go), mcp (Python)
- **Obsidian MCP**: mcp, httpx
- **Coding Agents**: asyncio, OpenAI/Anthropic SDK