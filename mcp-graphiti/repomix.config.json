{"output": {"filePath": "repomix-output.md", "style": "markdown", "parsableStyle": false, "fileSummary": true, "directoryStructure": true, "removeComments": false, "removeEmptyLines": false, "compress": false, "topFilesLength": 5, "showLineNumbers": false, "copyToClipboard": false, "git": {"sortByChanges": false, "sortByChangesMaxCommits": 100}}, "include": [], "ignore": {"useGitignore": true, "useDefaultPatterns": true, "customPatterns": ["tmp/", ".venv/", "uv.lock", "dist/", ".ai/", "llm_cache/", "docs/", "*.egg-info/", "__pycache__/", "*.pyc", "*.pyo", ".python-version", ".env", "*.log", "repomix.config.json", ".repomixignore"]}, "security": {"enableSecurityCheck": true}, "tokenCount": {"encoding": "o200k_base"}}