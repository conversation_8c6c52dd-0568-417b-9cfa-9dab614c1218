{"$schema": "http://json-schema.org/draft-07/schema#", "title": "Graphiti MCP Projects Registry", "description": "Registry of Graphiti MCP projects with their configurations and enabled status", "type": "object", "required": ["projects"], "properties": {"projects": {"type": "object", "description": "Map of project names to their configurations", "additionalProperties": {"type": "object", "required": ["root_dir", "config_file", "enabled"], "properties": {"root_dir": {"type": "string", "description": "Absolute path to the project root directory"}, "config_file": {"type": "string", "description": "Absolute path to the project's mcp-config.yaml file"}, "enabled": {"type": "boolean", "description": "Whether this project should be included in docker-compose.yml generation"}}}}}}