{"$schema": "http://json-schema.org/draft-07/schema#", "title": "Graphiti MCP Project Configuration", "description": "Configuration for a Graphiti MCP server project", "type": "object", "required": ["services"], "properties": {"services": {"type": "array", "description": "List of MCP services defined for this project", "items": {"type": "object", "required": ["id", "group_id", "entities_dir"], "properties": {"id": {"type": "string", "description": "Service ID (used for default naming)"}, "container_name": {"type": "string", "description": "Optional: Specify custom container name"}, "port_default": {"type": ["integer", "string"], "description": "Optional: Specify custom host port"}, "group_id": {"type": "string", "description": "Graph group ID for namespacing"}, "entities_dir": {"type": "string", "description": "Relative path to entity definitions within ai/graph"}, "environment": {"type": "object", "description": "Optional: Additional environment variables", "additionalProperties": {"type": ["string", "boolean", "number"]}}, "sync_cursor_mcp_config": {"type": "boolean", "description": "Whether to automatically update .cursor/mcp.json during compose", "default": true}}}}}}