# Graphiti MCP Server Environment Configuration

# --- Required Secrets ---
# Neo4j Database Configuration
# These settings are used to connect to your Neo4j database
NEO4J_USER=neo4j
NEO4J_PASSWORD=your_strong_neo4j_password_here

# Set Graphiti env to dev to allow weak password
# GRAPHITI_ENV=dev

# OpenAI API Configuration
# Required for LLM operations
OPENAI_API_KEY=your_openai_api_key_here
MODEL_NAME=gpt-4o

# --- Optional Configuration ---
# OpenAI Base URL (if not using the standard OpenAI API endpoint)
OPENAI_BASE_URL=https://api.openai.com/v1

# --- Neo4j Connection Configuration ---
# Neo4j URI (default is bolt://neo4j:7687 from docker-compose.yml)
NEO4J_URI=bolt://neo4j:7687

# Host ports - ports exposed on your local machine
NEO4J_HOST_HTTP_PORT=7474
NEO4J_HOST_BOLT_PORT=7687

# Container ports - ports used inside the container (rarely need to change)
NEO4J_CONTAINER_HTTP_PORT=7474
NEO4J_CONTAINER_BOLT_PORT=7687

# Neo4j Memory Settings
NEO4J_HEAP_INITIAL=512m # Initial heap size for Neo4j
NEO4J_HEAP_MAX=1G # Maximum heap size for Neo4j
NEO4J_PAGECACHE=512m # Page cache size for Neo4j

# --- MCP Server Configuration ---
# Default internal port used by all MCP servers
MCP_ROOT_CONTAINER_PORT=8000
MCP_PORT=8000 # Default port for service containers

# Root MCP Server (Required)
MCP_ROOT_CONTAINER_NAME=mcp-root
MCP_ROOT_HOST_PORT=8000

# --- MCP Root Server Configuration (via base-compose.yaml) ---
MCP_ROOT_GROUP_ID=root
MCP_ROOT_USE_CUSTOM_ENTITIES=true
MCP_ROOT_ENTITIES_DIR=entities
MCP_ROOT_ENTITIES= # Default: empty (use all in dir), set e.g. "Requirement Procedure" to restrict

# --- MCP Root Server Configuration ---
# These variables are used by entrypoint.sh and graphiti_mcp_server.py
# MCP_GROUP_ID=root # Default namespace for graph data - REMOVED - Configure via MCP_ROOT_GROUP_ID
# MCP_USE_CUSTOM_ENTITIES=true # Whether to use custom entities - REMOVED - Configure via MCP_ROOT_USE_CUSTOM_ENTITIES
# MCP_ENTITIES_DIR=entities # Directory for entity definitions - REMOVED - Configure via MCP_ROOT_ENTITIES_DIR
# MCP_ENTITIES= # Optional space-separated list of entities to use - REMOVED - Configure via MCP_ROOT_ENTITIES

# --- Neo4j Container Name ---
NEO4J_CONTAINER_NAME=neo4j

# --- Logging Configuration ---
GRAPHITI_LOG_LEVEL=info

# --- DANGER ZONE ---
# !!! WARNING !!! UNCOMMENTING AND SETTING THE FOLLOWING VARIABLE TO "true" WILL:
# - PERMANENTLY DELETE ALL DATA in the Neo4j database
# - Affect ALL knowledge graphs, not just a specific group
# - Cannot be undone once executed
# Only uncomment and set to "true" when you specifically need to clear all data
# Always comment out or set back to "false" immediately after use
# NEO4J_DESTROY_ENTIRE_GRAPH=false
