# WARNING: For PRIVATE projects, remove 'mcp-projects.yaml' from this .gitignore file so it is tracked by Git.
# For public/shared projects, keep 'mcp-projects.yaml' ignored to avoid leaking sensitive paths or configuration.

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST


# Environments
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# Docker
docker-compose.yml

# IDE files
.idea/
.vscode/
*.swp
*.swo

# Logs
logs/
*.log

# Local development
.DS_Store
.coverage
htmlcov/

# Repomix
repomix-output.md

# Config
mcp-projects.yaml

# Cursor
.cursor/**
.cursor/*