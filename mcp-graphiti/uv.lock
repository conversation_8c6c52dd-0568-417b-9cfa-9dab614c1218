# This file was autogenerated by uv via the following command:
#    uv pip compile pyproject.toml --output-file uv.lock
annotated-types==0.7.0
    # via pydantic
anthropic==0.49.0
    # via graphiti-core
anyio==4.9.0
    # via
    #   anthropic
    #   httpx
    #   mcp
    #   openai
    #   sse-starlette
    #   starlette
certifi==2025.1.31
    # via
    #   httpcore
    #   httpx
click==8.1.8
    # via
    #   typer
    #   uvicorn
diskcache==5.6.3
    # via graphiti-core
distro==1.9.0
    # via
    #   anthropic
    #   openai
graphiti-core==0.8.5
    # via rawr-mcp-graphiti (pyproject.toml)
h11==0.14.0
    # via
    #   httpcore
    #   uvicorn
httpcore==1.0.7
    # via httpx
httpx==0.28.1
    # via
    #   anthropic
    #   mcp
    #   openai
httpx-sse==0.4.0
    # via mcp
idna==3.10
    # via
    #   anyio
    #   httpx
jiter==0.9.0
    # via
    #   anthropic
    #   openai
markdown-it-py==3.0.0
    # via rich
mcp==1.6.0
    # via rawr-mcp-graphiti (pyproject.toml)
mdurl==0.1.2
    # via markdown-it-py
neo4j==5.28.1
    # via graphiti-core
numpy==2.2.4
    # via graphiti-core
openai==1.70.0
    # via
    #   rawr-mcp-graphiti (pyproject.toml)
    #   graphiti-core
pydantic==2.11.1
    # via
    #   anthropic
    #   graphiti-core
    #   mcp
    #   openai
    #   pydantic-settings
pydantic-core==2.33.0
    # via pydantic
pydantic-settings==2.8.1
    # via mcp
pygments==2.19.1
    # via rich
python-dotenv==1.1.0
    # via
    #   rawr-mcp-graphiti (pyproject.toml)
    #   graphiti-core
    #   pydantic-settings
pytz==2025.2
    # via neo4j
rich==14.0.0
    # via typer
ruamel-yaml==0.18.10
    # via rawr-mcp-graphiti (pyproject.toml)
ruamel-yaml-clib==0.2.12
    # via ruamel-yaml
shellingham==1.5.4
    # via typer
sniffio==1.3.1
    # via
    #   anthropic
    #   anyio
    #   openai
sse-starlette==2.2.1
    # via mcp
starlette==0.46.1
    # via
    #   mcp
    #   sse-starlette
tenacity==9.0.0
    # via graphiti-core
tqdm==4.67.1
    # via openai
typer==0.15.2
    # via rawr-mcp-graphiti (pyproject.toml)
typing-extensions==4.13.0
    # via
    #   anthropic
    #   anyio
    #   openai
    #   pydantic
    #   pydantic-core
    #   typer
    #   typing-inspection
typing-inspection==0.4.0
    # via pydantic
uvicorn==0.34.0
    # via mcp
