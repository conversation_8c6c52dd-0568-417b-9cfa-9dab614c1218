from typing import Dict, Any, List, Optional
import re
import ast
from .base_coding_agent import BaseCodingAgent, CodeContext, AgentResult, AgentType


class TestingAgent(BaseCodingAgent):
    def __init__(self, config: Dict[str, Any]):
        super().__init__(config)
        self.test_frameworks = config.get("test_frameworks", ["pytest", "unittest", "jest", "mocha"])
        self.coverage_threshold = config.get("coverage_threshold", 80)
        
    async def analyze(self, context: CodeContext) -> AgentResult:
        findings = []
        suggestions = []
        
        # Detect if this is a test file
        is_test_file = self._is_test_file(context)
        
        if is_test_file:
            # Analyze test quality
            test_analysis = self._analyze_test_quality(context)
            findings.extend(test_analysis["findings"])
            suggestions.extend(test_analysis["suggestions"])
        else:
            # Suggest tests for code
            test_suggestions = self._suggest_tests(context)
            suggestions.extend(test_suggestions)
            
            # Check for testability issues
            testability_issues = self._check_testability(context)
            findings.extend(testability_issues)
        
        # Calculate test coverage score (simplified)
        score = self._estimate_test_coverage(context, is_test_file)
        
        return AgentResult(
            agent_type=AgentType.TESTING,
            status="completed",
            findings=findings,
            suggestions=suggestions,
            score=score
        )
    
    def _is_test_file(self, context: CodeContext) -> bool:
        file_path = context.file_path.lower()
        test_patterns = ["test_", "_test.", "spec.", ".test.", "tests/", "test/"]
        return any(pattern in file_path for pattern in test_patterns)
    
    def _analyze_test_quality(self, context: CodeContext) -> Dict[str, Any]:
        findings = []
        suggestions = []
        
        # Check for test patterns
        test_patterns = {
            "assertions": r'(assert|expect|should|toBe|toEqual)',
            "setup_teardown": r'(setUp|tearDown|beforeEach|afterEach|before|after)',
            "mocking": r'(mock|patch|spy|stub)',
            "parametrized": r'(@pytest\.mark\.parametrize|@parameterized|test\.each)'
        }
        
        pattern_counts = {}
        for name, pattern in test_patterns.items():
            pattern_counts[name] = len(re.findall(pattern, context.content, re.IGNORECASE))
        
        # Check for missing assertions
        if pattern_counts["assertions"] == 0:
            findings.append({
                "type": "test_quality",
                "severity": "high",
                "message": "No assertions found in test file",
                "line": None
            })
        
        # Check test naming
        test_functions = re.findall(r'def\s+(test_\w+)', context.content)
        for test_func in test_functions:
            if len(test_func) < 10:
                findings.append({
                    "type": "test_quality",
                    "severity": "minor",
                    "message": f"Test name '{test_func}' is too short and not descriptive",
                    "line": None
                })
        
        # Suggestions based on patterns
        if pattern_counts["mocking"] == 0:
            suggestions.append("Consider using mocks to isolate units under test")
        
        if pattern_counts["parametrized"] == 0 and len(test_functions) > 5:
            suggestions.append("Consider using parametrized tests to reduce duplication")
        
        return {"findings": findings, "suggestions": suggestions}
    
    def _suggest_tests(self, context: CodeContext) -> List[str]:
        suggestions = []
        
        # Extract functions/methods from code
        if context.language == "python":
            functions = self._extract_python_functions(context.content)
        elif context.language in ["javascript", "typescript"]:
            functions = self._extract_js_functions(context.content)
        else:
            functions = []
        
        # Generate test suggestions for each function
        for func_name in functions:
            suggestions.append(f"Add unit test for function: {func_name}")
            
            # Suggest edge case tests
            if any(keyword in func_name.lower() for keyword in ["validate", "check", "verify"]):
                suggestions.append(f"Add edge case tests for {func_name} (null, empty, invalid inputs)")
            
            if any(keyword in func_name.lower() for keyword in ["parse", "convert", "transform"]):
                suggestions.append(f"Add format/conversion tests for {func_name}")
        
        # General suggestions
        if "class" in context.content:
            suggestions.append("Add integration tests for class interactions")
        
        if re.search(r'(async|await|Promise)', context.content):
            suggestions.append("Add tests for async/concurrent behavior")
        
        if re.search(r'(try|catch|except)', context.content):
            suggestions.append("Add tests for error handling paths")
        
        return suggestions
    
    def _check_testability(self, context: CodeContext) -> List[Dict[str, Any]]:
        findings = []
        
        # Check for global state
        if re.search(r'global\s+\w+', context.content):
            findings.append({
                "type": "testability",
                "severity": "warning",
                "message": "Global state usage detected - may complicate testing",
                "line": None
            })
        
        # Check for hardcoded values
        hardcoded_patterns = [
            (r'(localhost|127\.0\.0\.1|192\.168\.)', "Hardcoded IP addresses"),
            (r'(http://|https://)[^\s]+', "Hardcoded URLs"),
            (r'/[a-zA-Z0-9/_]+\.(txt|log|csv|json)', "Hardcoded file paths")
        ]
        
        for pattern, message in hardcoded_patterns:
            if re.search(pattern, context.content):
                findings.append({
                    "type": "testability",
                    "severity": "warning",
                    "message": f"{message} - consider using configuration",
                    "line": None
                })
        
        # Check for tight coupling
        import_count = len(re.findall(r'(import|from|require)', context.content))
        if import_count > 15:
            findings.append({
                "type": "testability",
                "severity": "warning",
                "message": "High number of dependencies - consider reducing coupling",
                "line": None
            })
        
        return findings
    
    def _extract_python_functions(self, content: str) -> List[str]:
        functions = []
        try:
            tree = ast.parse(content)
            for node in ast.walk(tree):
                if isinstance(node, ast.FunctionDef):
                    if not node.name.startswith('_'):
                        functions.append(node.name)
        except:
            # Fallback to regex if AST parsing fails
            functions = re.findall(r'def\s+(\w+)\s*\(', content)
        return functions
    
    def _extract_js_functions(self, content: str) -> List[str]:
        patterns = [
            r'function\s+(\w+)\s*\(',
            r'const\s+(\w+)\s*=\s*\([^)]*\)\s*=>',
            r'(\w+)\s*:\s*function\s*\(',
            r'(\w+)\s*\([^)]*\)\s*\{'
        ]
        
        functions = []
        for pattern in patterns:
            functions.extend(re.findall(pattern, content))
        
        return list(set(functions))
    
    def _estimate_test_coverage(self, context: CodeContext, is_test_file: bool) -> float:
        if is_test_file:
            # For test files, score based on test quality
            test_count = len(re.findall(r'(def\s+test_|it\s*\(|test\s*\()', context.content))
            assertion_count = len(re.findall(r'(assert|expect)', context.content))
            
            if test_count == 0:
                return 0.0
            
            quality_score = min(100, (assertion_count / test_count) * 50 + 50)
            return quality_score
        else:
            # For source files, estimate based on complexity
            function_count = len(self._extract_python_functions(context.content) or 
                               self._extract_js_functions(context.content))
            
            if function_count == 0:
                return 100.0
            
            # Assume 50% coverage as baseline for files without tests
            return 50.0
    
    def get_capabilities(self) -> List[str]:
        return [
            "Test quality analysis",
            "Test coverage estimation",
            "Test suggestion generation",
            "Testability assessment",
            "Test pattern detection",
            "Edge case identification"
        ]