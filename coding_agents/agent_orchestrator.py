from typing import Dict, Any, List, Optional
import asyncio
from pathlib import Path
import json
from .base_coding_agent import BaseCodingAgent, CodeContext, AgentResult, AgentType
from .code_review_agent import CodeReviewAgent
from .testing_agent import TestingAgent
from .documentation_agent import DocumentationAgent


class AgentOrchestrator:
    def __init__(self, config_path: Optional[str] = None):
        self.agents: Dict[AgentType, BaseCodingAgent] = {}
        self.config = self._load_config(config_path)
        self._initialize_agents()
    
    def _load_config(self, config_path: Optional[str]) -> Dict[str, Any]:
        if config_path and Path(config_path).exists():
            with open(config_path, 'r') as f:
                return json.load(f)
        return self._get_default_config()
    
    def _get_default_config(self) -> Dict[str, Any]:
        return {
            "agents": {
                "code_review": {
                    "enabled": True,
                    "config": {
                        "name": "Code Review Agent",
                        "description": "Analyzes code quality and best practices"
                    }
                },
                "testing": {
                    "enabled": True,
                    "config": {
                        "name": "Testing Agent",
                        "description": "Analyzes test coverage and suggests tests",
                        "test_frameworks": ["pytest", "unittest", "jest", "mocha"],
                        "coverage_threshold": 80
                    }
                },
                "documentation": {
                    "enabled": True,
                    "config": {
                        "name": "Documentation Agent",
                        "description": "Checks documentation quality and completeness",
                        "doc_standards": "google",
                        "require_examples": True
                    }
                }
            },
            "orchestration": {
                "parallel_execution": True,
                "fail_fast": False,
                "output_format": "detailed"
            }
        }
    
    def _initialize_agents(self):
        agent_classes = {
            AgentType.CODE_REVIEW: CodeReviewAgent,
            AgentType.TESTING: TestingAgent,
            AgentType.DOCUMENTATION: DocumentationAgent
        }
        
        for agent_type, agent_class in agent_classes.items():
            agent_key = agent_type.value
            if self.config["agents"].get(agent_key, {}).get("enabled", True):
                agent_config = self.config["agents"][agent_key].get("config", {})
                self.agents[agent_type] = agent_class(agent_config)
    
    async def analyze_file(self, file_path: str, content: Optional[str] = None) -> Dict[str, Any]:
        # Read file content if not provided
        if content is None:
            with open(file_path, 'r') as f:
                content = f.read()
        
        # Detect language
        language = self._detect_language(file_path)
        
        # Create context
        context = CodeContext(
            file_path=file_path,
            content=content,
            language=language,
            metadata={}
        )
        
        # Run agents
        if self.config["orchestration"]["parallel_execution"]:
            results = await self._run_agents_parallel(context)
        else:
            results = await self._run_agents_sequential(context)
        
        # Aggregate results
        return self._aggregate_results(results, file_path)
    
    async def analyze_directory(self, directory: str, patterns: List[str] = None) -> Dict[str, Any]:
        if patterns is None:
            patterns = ["*.py", "*.js", "*.ts", "*.jsx", "*.tsx"]
        
        path = Path(directory)
        files_to_analyze = []
        
        for pattern in patterns:
            files_to_analyze.extend(path.rglob(pattern))
        
        results = {}
        for file_path in files_to_analyze:
            if not self._should_skip_file(str(file_path)):
                try:
                    file_results = await self.analyze_file(str(file_path))
                    results[str(file_path)] = file_results
                except Exception as e:
                    results[str(file_path)] = {
                        "error": str(e),
                        "status": "failed"
                    }
        
        return {
            "directory": directory,
            "files_analyzed": len(results),
            "results": results,
            "summary": self._generate_summary(results)
        }
    
    def _detect_language(self, file_path: str) -> str:
        extension = Path(file_path).suffix.lower()
        language_map = {
            ".py": "python",
            ".js": "javascript",
            ".ts": "typescript",
            ".jsx": "javascript",
            ".tsx": "typescript",
            ".java": "java",
            ".cpp": "cpp",
            ".c": "c",
            ".go": "go",
            ".rs": "rust"
        }
        return language_map.get(extension, "unknown")
    
    def _should_skip_file(self, file_path: str) -> bool:
        skip_patterns = [
            "__pycache__", "node_modules", ".git", ".pytest_cache",
            "dist", "build", ".venv", "venv"
        ]
        return any(pattern in file_path for pattern in skip_patterns)
    
    async def _run_agents_parallel(self, context: CodeContext) -> List[AgentResult]:
        tasks = []
        for agent_type, agent in self.agents.items():
            tasks.append(agent.analyze(context))
        
        return await asyncio.gather(*tasks, return_exceptions=True)
    
    async def _run_agents_sequential(self, context: CodeContext) -> List[AgentResult]:
        results = []
        for agent_type, agent in self.agents.items():
            try:
                result = await agent.analyze(context)
                results.append(result)
                
                if self.config["orchestration"]["fail_fast"] and result.findings:
                    severe_findings = [f for f in result.findings if f.get("severity") in ["critical", "high"]]
                    if severe_findings:
                        break
            except Exception as e:
                results.append(e)
        
        return results
    
    def _aggregate_results(self, results: List[AgentResult], file_path: str) -> Dict[str, Any]:
        aggregated = {
            "file_path": file_path,
            "timestamp": None,
            "agents_run": len(results),
            "overall_score": 0,
            "findings": [],
            "suggestions": [],
            "errors": []
        }
        
        valid_results = []
        for result in results:
            if isinstance(result, Exception):
                aggregated["errors"].append(str(result))
            else:
                valid_results.append(result)
                aggregated["findings"].extend(result.findings)
                aggregated["suggestions"].extend(result.suggestions)
                if result.timestamp and not aggregated["timestamp"]:
                    aggregated["timestamp"] = result.timestamp.isoformat()
        
        # Calculate overall score
        if valid_results:
            scores = [r.score for r in valid_results if r.score is not None]
            if scores:
                aggregated["overall_score"] = sum(scores) / len(scores)
        
        # Group findings by severity
        aggregated["findings_by_severity"] = self._group_findings_by_severity(aggregated["findings"])
        
        # Remove duplicates from suggestions
        aggregated["suggestions"] = list(set(aggregated["suggestions"]))
        
        return aggregated
    
    def _group_findings_by_severity(self, findings: List[Dict[str, Any]]) -> Dict[str, int]:
        severity_counts = {
            "critical": 0,
            "high": 0,
            "warning": 0,
            "minor": 0
        }
        
        for finding in findings:
            severity = finding.get("severity", "minor")
            if severity in severity_counts:
                severity_counts[severity] += 1
        
        return severity_counts
    
    def _generate_summary(self, results: Dict[str, Any]) -> Dict[str, Any]:
        total_files = len(results)
        failed_files = sum(1 for r in results.values() if "error" in r)
        
        all_findings = []
        all_scores = []
        
        for file_results in results.values():
            if "error" not in file_results:
                all_findings.extend(file_results.get("findings", []))
                if file_results.get("overall_score") is not None:
                    all_scores.append(file_results["overall_score"])
        
        summary = {
            "total_files": total_files,
            "successful_analyses": total_files - failed_files,
            "failed_analyses": failed_files,
            "average_score": sum(all_scores) / len(all_scores) if all_scores else 0,
            "total_findings": len(all_findings),
            "findings_by_severity": self._group_findings_by_severity(all_findings)
        }
        
        return summary
    
    def get_agent_capabilities(self) -> Dict[AgentType, List[str]]:
        capabilities = {}
        for agent_type, agent in self.agents.items():
            capabilities[agent_type] = agent.get_capabilities()
        return capabilities