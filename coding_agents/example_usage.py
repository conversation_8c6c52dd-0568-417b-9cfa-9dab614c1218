import asyncio
from pathlib import Path
from coding_agents import AgentOrchestrator, CodeContext, AgentType


async def example_single_file_analysis():
    print("=== Single File Analysis Example ===\n")
    
    # Create orchestrator
    orchestrator = AgentOrchestrator()
    
    # Example Python code
    example_code = '''
def calculate_total(items):
    total = 0
    for item in items:
        total += item["price"] * item["quantity"]
    return total

class ShoppingCart:
    def __init__(self):
        self.items = []
    
    def add_item(self, item):
        self.items.append(item)
    
    def get_total(self):
        return calculate_total(self.items)
'''
    
    # Analyze the code
    results = await orchestrator.analyze_file(
        file_path="example_shopping_cart.py",
        content=example_code
    )
    
    # Print results
    print(f"File: {results['file_path']}")
    print(f"Overall Score: {results['overall_score']:.1f}/100")
    print(f"\nFindings by Severity:")
    for severity, count in results['findings_by_severity'].items():
        if count > 0:
            print(f"  {severity}: {count}")
    
    print(f"\nDetailed Findings:")
    for finding in results['findings']:
        print(f"  - [{finding['severity']}] {finding['message']}")
        if finding.get('line'):
            print(f"    Line: {finding['line']}")
    
    print(f"\nSuggestions:")
    for suggestion in results['suggestions']:
        print(f"  - {suggestion}")


async def example_directory_analysis():
    print("\n\n=== Directory Analysis Example ===\n")
    
    # Create orchestrator with custom config
    config = {
        "agents": {
            "code_review": {"enabled": True},
            "testing": {"enabled": True},
            "documentation": {"enabled": True}
        },
        "orchestration": {
            "parallel_execution": True,
            "fail_fast": False
        }
    }
    
    orchestrator = AgentOrchestrator()
    orchestrator.config = config
    orchestrator._initialize_agents()
    
    # Analyze current directory (example)
    # In real usage, replace with actual directory path
    current_dir = Path(__file__).parent
    
    results = await orchestrator.analyze_directory(
        directory=str(current_dir),
        patterns=["*.py"]
    )
    
    print(f"Directory: {results['directory']}")
    print(f"Files Analyzed: {results['files_analyzed']}")
    print(f"\nSummary:")
    summary = results['summary']
    print(f"  Average Score: {summary['average_score']:.1f}/100")
    print(f"  Total Findings: {summary['total_findings']}")
    print(f"  Findings by Severity:")
    for severity, count in summary['findings_by_severity'].items():
        if count > 0:
            print(f"    {severity}: {count}")


async def example_custom_agent_usage():
    print("\n\n=== Custom Agent Usage Example ===\n")
    
    from coding_agents import CodeReviewAgent, CodeContext
    
    # Create individual agent
    review_agent = CodeReviewAgent({
        "name": "Custom Review Agent",
        "review_criteria": {
            "code_quality": {
                "naming_conventions": True,
                "code_complexity": True
            }
        }
    })
    
    # Create context
    context = CodeContext(
        file_path="test.py",
        content="""
def processData(d):
    x = []
    for i in d:
        if i > 0:
            x.append(i * 2)
    return x
""",
        language="python",
        metadata={}
    )
    
    # Run analysis
    result = await review_agent.analyze(context)
    
    print(f"Agent: {review_agent.name}")
    print(f"Score: {result.score:.1f}/100")
    print(f"Findings: {len(result.findings)}")
    for finding in result.findings:
        print(f"  - {finding['message']}")


async def example_agent_capabilities():
    print("\n\n=== Agent Capabilities ===\n")
    
    orchestrator = AgentOrchestrator()
    capabilities = orchestrator.get_agent_capabilities()
    
    for agent_type, caps in capabilities.items():
        print(f"\n{agent_type.value.replace('_', ' ').title()} Agent:")
        for cap in caps:
            print(f"  - {cap}")


async def main():
    # Run all examples
    await example_single_file_analysis()
    await example_directory_analysis()
    await example_custom_agent_usage()
    await example_agent_capabilities()


if __name__ == "__main__":
    asyncio.run(main())