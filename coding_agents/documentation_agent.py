from typing import Dict, Any, List, Optional, Tuple
import re
import ast
from .base_coding_agent import BaseCodingAgent, CodeContext, AgentResult, AgentType


class DocumentationAgent(BaseCodingAgent):
    def __init__(self, config: Dict[str, Any]):
        super().__init__(config)
        self.doc_standards = config.get("doc_standards", "google")
        self.require_examples = config.get("require_examples", True)
        
    async def analyze(self, context: CodeContext) -> AgentResult:
        findings = []
        suggestions = []
        
        # Analyze existing documentation
        doc_analysis = self._analyze_documentation(context)
        findings.extend(doc_analysis["findings"])
        
        # Generate documentation suggestions
        doc_suggestions = self._generate_doc_suggestions(context)
        suggestions.extend(doc_suggestions)
        
        # Check README and high-level docs
        if "readme" in context.file_path.lower():
            readme_analysis = self._analyze_readme(context)
            findings.extend(readme_analysis)
        
        # Calculate documentation score
        score = self._calculate_doc_score(context)
        
        return AgentResult(
            agent_type=AgentType.DOCUMENTATION,
            status="completed",
            findings=findings,
            suggestions=suggestions,
            score=score
        )
    
    def _analyze_documentation(self, context: CodeContext) -> Dict[str, Any]:
        findings = []
        
        if context.language == "python":
            findings.extend(self._analyze_python_docs(context))
        elif context.language in ["javascript", "typescript"]:
            findings.extend(self._analyze_js_docs(context))
        
        # Common documentation issues
        common_issues = self._check_common_doc_issues(context)
        findings.extend(common_issues)
        
        return {"findings": findings}
    
    def _analyze_python_docs(self, context: CodeContext) -> List[Dict[str, Any]]:
        findings = []
        
        try:
            tree = ast.parse(context.content)
            
            # Check module docstring
            module_doc = ast.get_docstring(tree)
            if not module_doc:
                findings.append({
                    "type": "documentation",
                    "severity": "warning",
                    "message": "Missing module-level docstring",
                    "line": 1
                })
            
            # Check class and function docstrings
            for node in ast.walk(tree):
                if isinstance(node, (ast.FunctionDef, ast.AsyncFunctionDef, ast.ClassDef)):
                    docstring = ast.get_docstring(node)
                    
                    if not docstring:
                        findings.append({
                            "type": "documentation",
                            "severity": "high",
                            "message": f"Missing docstring for {node.name}",
                            "line": node.lineno
                        })
                    else:
                        # Check docstring quality
                        doc_issues = self._check_docstring_quality(
                            docstring, node, context.content
                        )
                        findings.extend(doc_issues)
        except:
            # Fallback to regex-based analysis
            findings.extend(self._regex_based_doc_analysis(context))
        
        return findings
    
    def _analyze_js_docs(self, context: CodeContext) -> List[Dict[str, Any]]:
        findings = []
        content = context.content
        
        # Check for JSDoc comments
        functions = re.findall(
            r'(?:function\s+(\w+)|const\s+(\w+)\s*=.*?=>|(\w+)\s*\([^)]*\)\s*\{)',
            content
        )
        
        for match in functions:
            func_name = next(name for name in match if name)
            func_pattern = re.escape(func_name)
            
            # Look for JSDoc before function
            jsdoc_pattern = rf'/\*\*[\s\S]*?\*/\s*(?:export\s+)?(?:async\s+)?(?:function\s+{func_pattern}|const\s+{func_pattern})'
            
            if not re.search(jsdoc_pattern, content):
                findings.append({
                    "type": "documentation",
                    "severity": "high",
                    "message": f"Missing JSDoc for function {func_name}",
                    "line": None
                })
        
        return findings
    
    def _check_common_doc_issues(self, context: CodeContext) -> List[Dict[str, Any]]:
        findings = []
        content = context.content
        
        # Check for outdated documentation markers
        outdated_markers = ["TODO", "FIXME", "DEPRECATED", "HACK"]
        for marker in outdated_markers:
            matches = re.finditer(rf'{marker}:?\s*(.+)', content, re.IGNORECASE)
            for match in matches:
                line_num = content[:match.start()].count('\n') + 1
                findings.append({
                    "type": "documentation",
                    "severity": "minor",
                    "message": f"{marker} comment found: {match.group(1)[:50]}...",
                    "line": line_num
                })
        
        # Check for missing type hints (Python)
        if context.language == "python":
            functions_without_types = re.findall(
                r'def\s+(\w+)\s*\([^)]*\)\s*(?!->)',
                content
            )
            for func in functions_without_types:
                if not func.startswith('_'):
                    findings.append({
                        "type": "documentation",
                        "severity": "minor",
                        "message": f"Function {func} missing return type annotation",
                        "line": None
                    })
        
        return findings
    
    def _check_docstring_quality(self, docstring: str, node: Any, content: str) -> List[Dict[str, Any]]:
        findings = []
        
        # Check for standard sections
        expected_sections = []
        
        if isinstance(node, (ast.FunctionDef, ast.AsyncFunctionDef)):
            if node.args.args:
                expected_sections.append("Args" if self.doc_standards == "google" else "Parameters")
            expected_sections.append("Returns")
            
            if self.require_examples:
                expected_sections.append("Example")
        
        for section in expected_sections:
            if section not in docstring:
                findings.append({
                    "type": "documentation",
                    "severity": "warning",
                    "message": f"Docstring for {node.name} missing {section} section",
                    "line": node.lineno
                })
        
        # Check docstring length
        if len(docstring.split()) < 5:
            findings.append({
                "type": "documentation",
                "severity": "minor",
                "message": f"Docstring for {node.name} is too brief",
                "line": node.lineno
            })
        
        return findings
    
    def _generate_doc_suggestions(self, context: CodeContext) -> List[str]:
        suggestions = []
        
        # Suggest documentation templates
        if context.language == "python":
            if "class" in context.content:
                suggestions.append("Add class-level documentation explaining the purpose and usage")
            
            if re.search(r'__init__', context.content):
                suggestions.append("Document class initialization parameters and their types")
        
        # Suggest code examples
        if re.search(r'(API|api|endpoint|route)', context.content, re.IGNORECASE):
            suggestions.append("Add API documentation with request/response examples")
        
        # Suggest diagrams or visual docs
        if re.search(r'(workflow|pipeline|process|state)', context.content, re.IGNORECASE):
            suggestions.append("Consider adding a diagram to illustrate the workflow/process")
        
        # Suggest configuration documentation
        if re.search(r'(config|settings|options)', context.content, re.IGNORECASE):
            suggestions.append("Document all configuration options and their default values")
        
        return suggestions
    
    def _analyze_readme(self, context: CodeContext) -> List[Dict[str, Any]]:
        findings = []
        content = context.content.lower()
        
        # Check for essential README sections
        essential_sections = [
            ("installation", "Installation instructions"),
            ("usage", "Usage examples"),
            ("requirements", "Requirements or dependencies"),
            ("license", "License information")
        ]
        
        for section, description in essential_sections:
            if section not in content:
                findings.append({
                    "type": "documentation",
                    "severity": "warning",
                    "message": f"README missing {description}",
                    "line": None
                })
        
        # Check for code blocks
        if "```" not in context.content and "~~~" not in context.content:
            findings.append({
                "type": "documentation",
                "severity": "minor",
                "message": "README contains no code examples",
                "line": None
            })
        
        return findings
    
    def _regex_based_doc_analysis(self, context: CodeContext) -> List[Dict[str, Any]]:
        findings = []
        
        # Simple regex patterns for function detection
        func_pattern = r'(?:async\s+)?def\s+(\w+)\s*\([^)]*\):'
        functions = re.finditer(func_pattern, context.content)
        
        for match in functions:
            func_name = match.group(1)
            func_start = match.start()
            
            # Check if there's a docstring after the function definition
            after_func = context.content[func_start:func_start + 200]
            if not re.search(r':\s*["\']', after_func):
                line_num = context.content[:func_start].count('\n') + 1
                findings.append({
                    "type": "documentation",
                    "severity": "high",
                    "message": f"Missing docstring for function {func_name}",
                    "line": line_num
                })
        
        return findings
    
    def _calculate_doc_score(self, context: CodeContext) -> float:
        # Count documented vs undocumented entities
        total_entities = 0
        documented_entities = 0
        
        if context.language == "python":
            # Count functions and classes
            total_entities += len(re.findall(r'(?:def|class)\s+\w+', context.content))
            # Count docstrings (simplified)
            documented_entities += len(re.findall(r'(?:def|class)\s+\w+.*?:\s*["\']', context.content, re.DOTALL))
        else:
            # Count functions
            total_entities += len(re.findall(r'function\s+\w+|const\s+\w+\s*=.*?=>', context.content))
            # Count JSDoc comments
            documented_entities += len(re.findall(r'/\*\*[\s\S]*?\*/', context.content))
        
        if total_entities == 0:
            return 100.0
        
        base_score = (documented_entities / total_entities) * 100
        
        # Penalty for TODO/FIXME comments
        todo_count = len(re.findall(r'TODO|FIXME', context.content, re.IGNORECASE))
        penalty = min(todo_count * 2, 20)
        
        return max(0, base_score - penalty)
    
    def get_capabilities(self) -> List[str]:
        return [
            "Documentation coverage analysis",
            "Docstring quality assessment",
            "API documentation validation",
            "README completeness check",
            "Documentation template generation",
            "Type annotation verification"
        ]