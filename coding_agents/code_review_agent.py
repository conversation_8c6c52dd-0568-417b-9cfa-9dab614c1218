from typing import Dict, Any, List, Optional
import re
from .base_coding_agent import BaseCodingAgent, CodeContext, AgentResult, AgentType


class CodeReviewAgent(BaseCodingAgent):
    def __init__(self, config: Dict[str, Any]):
        super().__init__(config)
        self.review_criteria = config.get("review_criteria", self._get_default_criteria())
        
    def _get_default_criteria(self) -> Dict[str, Any]:
        return {
            "code_quality": {
                "naming_conventions": True,
                "code_complexity": True,
                "duplication": True,
                "readability": True
            },
            "best_practices": {
                "error_handling": True,
                "security": True,
                "performance": True,
                "maintainability": True
            },
            "style": {
                "formatting": True,
                "comments": True,
                "documentation": True
            }
        }
    
    async def analyze(self, context: CodeContext) -> AgentResult:
        findings = []
        suggestions = []
        
        # Analyze code structure
        structure_issues = self._analyze_structure(context)
        findings.extend(structure_issues)
        
        # Check for common issues
        common_issues = self._check_common_issues(context)
        findings.extend(common_issues)
        
        # Analyze complexity
        complexity_score = self._calculate_complexity(context)
        if complexity_score > 10:
            findings.append({
                "type": "complexity",
                "severity": "warning",
                "message": f"High complexity detected (score: {complexity_score})",
                "line": None
            })
            suggestions.append("Consider breaking down complex functions into smaller, more focused ones")
        
        # Calculate overall score
        score = self._calculate_review_score(findings)
        
        return AgentResult(
            agent_type=AgentType.CODE_REVIEW,
            status="completed",
            findings=findings,
            suggestions=suggestions,
            score=score
        )
    
    def _analyze_structure(self, context: CodeContext) -> List[Dict[str, Any]]:
        findings = []
        lines = context.content.split('\n')
        
        # Check function length
        in_function = False
        function_start = 0
        function_lines = 0
        
        for i, line in enumerate(lines):
            if re.match(r'^\s*(def|async def|function|const.*=.*=>)', line):
                if in_function and function_lines > 50:
                    findings.append({
                        "type": "structure",
                        "severity": "warning",
                        "message": f"Function too long ({function_lines} lines)",
                        "line": function_start + 1
                    })
                in_function = True
                function_start = i
                function_lines = 0
            elif in_function:
                function_lines += 1
        
        # Check line length
        for i, line in enumerate(lines):
            if len(line) > 120:
                findings.append({
                    "type": "style",
                    "severity": "minor",
                    "message": f"Line too long ({len(line)} characters)",
                    "line": i + 1
                })
        
        return findings
    
    def _check_common_issues(self, context: CodeContext) -> List[Dict[str, Any]]:
        findings = []
        content = context.content
        
        # Security checks
        security_patterns = [
            (r'eval\s*\(', "Avoid using eval() - security risk"),
            (r'exec\s*\(', "Avoid using exec() - security risk"),
            (r'(password|secret|key)\s*=\s*["\'].*["\']', "Hardcoded secrets detected"),
            (r'TODO|FIXME|XXX', "Unresolved TODO/FIXME found")
        ]
        
        for pattern, message in security_patterns:
            matches = re.finditer(pattern, content, re.IGNORECASE)
            for match in matches:
                line_num = content[:match.start()].count('\n') + 1
                findings.append({
                    "type": "security" if "security" in message else "maintenance",
                    "severity": "high" if "security" in message else "minor",
                    "message": message,
                    "line": line_num
                })
        
        # Error handling checks
        if context.language in ["python", "javascript", "typescript"]:
            if "try" in content and "except" not in content and "catch" not in content:
                findings.append({
                    "type": "error_handling",
                    "severity": "warning",
                    "message": "Try block without exception handling",
                    "line": None
                })
        
        return findings
    
    def _calculate_complexity(self, context: CodeContext) -> int:
        complexity = 1
        
        # Count control flow statements
        control_flow_keywords = [
            r'\bif\b', r'\belif\b', r'\belse\b', r'\bfor\b', r'\bwhile\b',
            r'\btry\b', r'\bcatch\b', r'\bexcept\b', r'\bswitch\b', r'\bcase\b'
        ]
        
        for keyword in control_flow_keywords:
            complexity += len(re.findall(keyword, context.content))
        
        # Add complexity for nested structures
        lines = context.content.split('\n')
        max_indentation = 0
        for line in lines:
            indentation = len(line) - len(line.lstrip())
            max_indentation = max(max_indentation, indentation // 4)
        
        complexity += max_indentation * 2
        
        return complexity
    
    def _calculate_review_score(self, findings: List[Dict[str, Any]]) -> float:
        if not findings:
            return 100.0
        
        severity_weights = {
            "critical": 10,
            "high": 5,
            "warning": 3,
            "minor": 1
        }
        
        total_penalty = sum(
            severity_weights.get(finding.get("severity", "minor"), 1)
            for finding in findings
        )
        
        score = max(0, 100 - total_penalty)
        return score
    
    def get_capabilities(self) -> List[str]:
        return [
            "Code quality analysis",
            "Security vulnerability detection",
            "Complexity assessment",
            "Style and formatting checks",
            "Best practices validation",
            "Performance suggestions"
        ]