from abc import ABC, abstractmethod
from typing import Dict, Any, List, Optional
from dataclasses import dataclass
from enum import Enum
import asyncio
from datetime import datetime


class AgentType(Enum):
    CODE_REVIEW = "code_review"
    TESTING = "testing"
    DOCUMENTATION = "documentation"


@dataclass
class CodeContext:
    file_path: str
    content: str
    language: str
    metadata: Dict[str, Any]


@dataclass
class AgentResult:
    agent_type: AgentType
    status: str
    findings: List[Dict[str, Any]]
    suggestions: List[str]
    score: Optional[float] = None
    timestamp: datetime = None

    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = datetime.now()


class BaseCodingAgent(ABC):
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.name = config.get("name", self.__class__.__name__)
        self.description = config.get("description", "")
        
    @abstractmethod
    async def analyze(self, context: CodeContext) -> AgentResult:
        pass
    
    @abstractmethod
    def get_capabilities(self) -> List[str]:
        pass
    
    async def process_batch(self, contexts: List[CodeContext]) -> List[AgentResult]:
        tasks = [self.analyze(context) for context in contexts]
        return await asyncio.gather(*tasks)