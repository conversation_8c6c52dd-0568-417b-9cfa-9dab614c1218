#!/usr/bin/env python3
"""Test script for Coding Agents functionality"""

import asyncio
import sys
from pathlib import Path

# Add current directory to path
sys.path.insert(0, str(Path(__file__).parent))

# Import modules directly
from agent_orchestrator import AgentOrchestrator
from base_coding_agent import CodeContext, AgentType


async def test_basic_functionality():
    print("=== Testing Coding Agents Basic Functionality ===\n")
    
    try:
        # Create orchestrator
        orchestrator = AgentOrchestrator()
        print("✓ AgentOrchestrator created successfully")
        
        # Test simple code analysis
        test_code = '''
def add_numbers(a, b):
    """Add two numbers"""
    return a + b

def multiply(x, y):
    # Missing docstring
    result = x * y
    return result
'''
        
        # Create context
        context = CodeContext(
            file_path="test_file.py",
            content=test_code,
            language="python",
            metadata={}
        )
        print("✓ CodeContext created successfully")
        
        # Test agent capabilities
        capabilities = orchestrator.get_agent_capabilities()
        print(f"✓ Found {len(capabilities)} agent types:")
        for agent_type in capabilities:
            print(f"  - {agent_type.value}")
        
        # Analyze the code
        print("\nAnalyzing test code...")
        results = await orchestrator.analyze_file(
            file_path="test_file.py",
            content=test_code
        )
        
        print(f"\n✓ Analysis completed!")
        print(f"  Overall Score: {results.get('overall_score', 'N/A')}/100")
        print(f"  Findings: {len(results.get('findings', []))}")
        print(f"  Suggestions: {len(results.get('suggestions', []))}")
        
        # Show some findings
        if results.get('findings'):
            print("\nSample findings:")
            for finding in results['findings'][:3]:
                print(f"  - [{finding.get('severity', 'INFO')}] {finding.get('message', 'No message')}")
        
        return True
        
    except Exception as e:
        print(f"✗ Error: {type(e).__name__}: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


async def test_individual_agents():
    print("\n\n=== Testing Individual Agents ===\n")
    
    try:
        from code_review_agent import CodeReviewAgent
        from testing_agent import TestingAgent
        from documentation_agent import DocumentationAgent
        
        # Test each agent type
        agents = [
            ("Code Review Agent", CodeReviewAgent),
            ("Testing Agent", TestingAgent),
            ("Documentation Agent", DocumentationAgent)
        ]
        
        test_context = CodeContext(
            file_path="sample.py",
            content="def hello(): pass",
            language="python",
            metadata={}
        )
        
        for agent_name, agent_class in agents:
            try:
                agent = agent_class({"name": agent_name})
                result = await agent.analyze(test_context)
                print(f"✓ {agent_name}: Score = {result.score}/100")
            except Exception as e:
                print(f"✗ {agent_name}: {type(e).__name__}")
        
        return True
        
    except Exception as e:
        print(f"✗ Error testing individual agents: {str(e)}")
        return False


async def main():
    print("Coding Agents Test Suite\n")
    
    # Run tests
    test_results = []
    
    # Test 1: Basic functionality
    result1 = await test_basic_functionality()
    test_results.append(("Basic Functionality", result1))
    
    # Test 2: Individual agents
    result2 = await test_individual_agents()
    test_results.append(("Individual Agents", result2))
    
    # Summary
    print("\n\n=== Test Summary ===")
    passed = sum(1 for _, result in test_results if result)
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "PASSED" if result else "FAILED"
        print(f"{test_name}: {status}")
    
    print(f"\nTotal: {passed}/{total} tests passed")
    
    return passed == total


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)