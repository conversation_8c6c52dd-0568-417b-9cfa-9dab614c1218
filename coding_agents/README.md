# Coding Agents

A collection of AI-powered agents to assist with code review, testing, and documentation during your development workflow.

## Features

- **Code Review Agent**: Analyzes code quality, security issues, and best practices
- **Testing Agent**: Suggests tests, analyzes test coverage, and identifies testability issues
- **Documentation Agent**: Checks documentation completeness and suggests improvements

## Installation

```bash
pip install -r requirements.txt
```

## Quick Start

```python
import asyncio
from coding_agents import AgentOrchestrator

async def analyze_my_code():
    orchestrator = AgentOrchestrator()
    
    # Analyze a single file
    results = await orchestrator.analyze_file("my_code.py")
    print(f"Score: {results['overall_score']}/100")
    
    # Analyze a directory
    dir_results = await orchestrator.analyze_directory("./src")
    print(f"Analyzed {dir_results['files_analyzed']} files")

asyncio.run(analyze_my_code())
```

## Agent Types

### Code Review Agent
- Checks naming conventions and code style
- Analyzes code complexity
- Detects security issues
- Identifies code duplication
- Suggests refactoring opportunities

### Testing Agent
- Analyzes test quality
- Suggests missing tests
- Estimates test coverage
- Identifies testability issues
- Recommends test patterns

### Documentation Agent
- Checks for missing docstrings
- Validates documentation format
- Ensures README completeness
- Verifies type annotations
- Suggests documentation improvements

## Configuration

Create a JSON configuration file to customize agent behavior:

```json
{
  "agents": {
    "code_review": {
      "enabled": true,
      "config": {
        "review_criteria": {
          "code_quality": {"naming_conventions": true},
          "security": {"check_secrets": true}
        }
      }
    },
    "testing": {
      "enabled": true,
      "config": {
        "coverage_threshold": 80,
        "test_frameworks": ["pytest", "unittest"]
      }
    },
    "documentation": {
      "enabled": true,
      "config": {
        "doc_standards": "google",
        "require_examples": true
      }
    }
  }
}
```

## Advanced Usage

### Running Individual Agents

```python
from coding_agents import CodeReviewAgent, CodeContext

agent = CodeReviewAgent({"name": "My Review Agent"})
context = CodeContext(
    file_path="example.py",
    content="def foo(): pass",
    language="python",
    metadata={}
)

result = await agent.analyze(context)
```

### Batch Processing

```python
contexts = [CodeContext(...) for file in files]
results = await agent.process_batch(contexts)
```

## Output Format

Analysis results include:
- Overall score (0-100)
- Findings grouped by severity (critical, high, warning, minor)
- Actionable suggestions
- Per-agent results

## License

MIT