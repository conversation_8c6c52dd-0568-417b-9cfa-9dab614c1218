import csv
import re
from collections import defaultdict

# Read the CSV file
messages = []
with open('/tmp/whatsapp_messages.csv', 'r', encoding='utf-8') as f:
    reader = csv.reader(f)
    for row in reader:
        if len(row) >= 2 and len(row[1]) > 50:  # Only messages with substantial content
            messages.append(row[1])

# Categorize templates
templates = {
    '開場白模板': [],
    '價格說明模板': [],
    '優惠介紹模板': [],
    '預約確認模板': [],
    '跟進消息模板': [],
    '提醒通知模板': [],
    '問候關心模板': []
}

for msg in messages:
    # Opening greetings
    if ('你好' in msg[:50] or '歡迎查詢' in msg[:50]) and len(msg) > 200:
        templates['開場白模板'].append(msg)
    
    # Price explanations
    elif re.search(r'\$\d+|價錢|折後|原價', msg):
        templates['價格說明模板'].append(msg)
    
    # Promotions
    elif re.search(r'優惠|折扣|免費|贈送|快閃', msg):
        templates['優惠介紹模板'].append(msg)
    
    # Appointment confirmations
    elif re.search(r'預約|確認|安排|時間|日期', msg) and '？' not in msg:
        templates['預約確認模板'].append(msg)
    
    # Follow-ups
    elif re.search(r'跟進|提醒|通知|記得', msg):
        templates['跟進消息模板'].append(msg)
    
    # Reminders
    elif re.search(r'注意事項|準備|禁食|樣本', msg):
        templates['提醒通知模板'].append(msg)
    
    # Care messages
    elif re.search(r'謝謝|感謝|辛苦|身體健康', msg):
        templates['問候關心模板'].append(msg)

# Print unique templates for each category
for category, msgs in templates.items():
    print(f"\n{'='*50}")
    print(f"{category} (共{len(msgs)}個)")
    print('='*50)
    
    # Get unique templates (deduplicate similar ones)
    unique_msgs = []
    seen_patterns = set()
    
    for msg in msgs:
        # Create a simplified pattern to detect similar messages
        pattern = re.sub(r'\d+', 'NUM', msg[:100])
        pattern = re.sub(r'[A-Za-z]+', 'NAME', pattern)
        
        if pattern not in seen_patterns:
            seen_patterns.add(pattern)
            unique_msgs.append(msg)
    
    # Show up to 3 unique templates
    for i, msg in enumerate(unique_msgs[:3], 1):
        print(f"\n--- 模板 {i} ---")
        print(msg)
        if i < len(unique_msgs[:3]):
            print()

# Extract common emoji usage patterns
print(f"\n{'='*50}")
print("表情符號使用習慣")
print('='*50)

emoji_positions = defaultdict(list)
for msg in messages:
    # Find emojis and their positions
    for match in re.finditer(r'[👋🎯🎁✅📋🔬🩻🧪📄📍🕐📆💰🧑‍🧑‍🧒💲🙇🏻‍♀️⸻❤️😊🙏👍💪🎉✨🌟🔥🔸🔹👑🧬🕒]', msg):
        emoji = match.group()
        position = 'start' if match.start() < 20 else 'middle' if match.start() < len(msg)-20 else 'end'
        context = msg[max(0, match.start()-10):min(len(msg), match.end()+10)]
        emoji_positions[emoji].append((position, context))

print("\n常用表情符號及其使用位置：")
for emoji, positions in sorted(emoji_positions.items(), key=lambda x: len(x[1]), reverse=True)[:10]:
    position_counts = defaultdict(int)
    for pos, _ in positions:
        position_counts[pos] += 1
    
    print(f"\n{emoji} (使用{len(positions)}次)")
    for pos, count in position_counts.items():
        print(f"  - {pos}: {count}次")
    
    # Show example usage
    if positions:
        _, context = positions[0]
        print(f"  例子: ...{context}...")

# Extract common closing phrases
print(f"\n{'='*50}")
print("常用結尾語")
print('='*50)

closing_phrases = []
for msg in messages:
    if len(msg) > 50:
        # Get last 50 characters
        ending = msg[-100:].strip()
        if any(phrase in ending for phrase in ['想預約', '幫你', '即刻', '回覆', '告訴我', '請']):
            closing_phrases.append(ending)

unique_closings = list(set(closing_phrases))[:5]
for i, closing in enumerate(unique_closings, 1):
    print(f"\n{i}. ...{closing}")