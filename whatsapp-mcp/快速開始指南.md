# WhatsApp MCP 快速開始指南 🚀

## 5 分鐘快速上手

### 第 1 步：連接 WhatsApp（2分鐘）

1. **打開終端**
   - 按 `Command + 空白鍵`
   - 輸入 "Terminal" 並按 Enter

2. **複製貼上這段命令**
   ```bash
   cd /Users/<USER>/Desktop/Claude/whatsapp-mcp/whatsapp-bridge && go run main.go
   ```

3. **掃描 QR 碼**
   - 打開手機 WhatsApp
   - 設定 → 連結裝置 → 掃描 QR 碼

4. **看到這個就成功了**
   ```
   ✓ Connected to WhatsApp!
   ```

---

### 第 2 步：開始觀察模式（2分鐘）

1. **開新終端視窗**
   - 按 `Command + N`

2. **複製貼上這段命令**
   ```bash
   cd /Users/<USER>/Desktop/Claude/whatsapp-mcp/observer-mode && ./start_observer.sh
   ```

3. **選擇 1 並按 Enter**
   ```
   請選擇 (1/2/3): 1
   ```

✅ **完成！系統現在正在：**
- 📥 自動下載所有對話
- 🔍 監控重要關鍵詞
- 🚫 不會發送任何消息

---

### 第 3 步：查看分析報告（1分鐘）

**隨時按 `Ctrl + C` 停止觀察**，系統會自動生成報告

報告位置：
```
/Users/<USER>/Desktop/Claude/whatsapp-mcp/observer-mode/observation_report.json
```

---

## 常用操作速查表

### 🔄 更換 WhatsApp 號碼
```bash
# 1. 停止程序 (Ctrl + C)
# 2. 清除舊資料
rm -rf /Users/<USER>/Desktop/Claude/whatsapp-mcp/whatsapp-bridge/store/whatsapp.db
# 3. 重新執行第 1 步
```

### 📊 生成詳細分析報告
```bash
cd /Users/<USER>/Desktop/Claude/whatsapp-mcp/observer-mode
python3 conversation_analyzer.py
```

### 🛑 停止所有服務
- WhatsApp Bridge: `Ctrl + C`
- 觀察模式: `Ctrl + C`

### 📱 在 Claude 中使用
只要 WhatsApp Bridge 在運行，Claude 就能自動使用 WhatsApp 功能

---

## 三個最重要的提醒

1. **保持 WhatsApp Bridge 運行**
   - 第一個終端不要關閉
   - 看到 "REST server is running" 就對了

2. **觀察模式不發消息**
   - 純粹觀察和記錄
   - 適合了解客戶行為

3. **定期查看報告**
   - 了解客戶活躍時間
   - 發現業務機會
   - 及時處理問題

---

## 遇到問題？

### ❌ 看不到 QR 碼
→ 放大終端視窗

### ❌ 無法連接
→ 確保手機和電腦在同一網路

### ❌ 觀察模式沒反應
→ 確認第一個終端的 WhatsApp Bridge 還在運行

### ❌ 其他問題
→ 查看完整使用手冊：`完整使用手冊.md`

---

## 下一步

恭喜！您已經開始使用 WhatsApp MCP 了 🎉

建議您：
1. 觀察 1-2 天收集數據
2. 查看自動生成的分析報告
3. 根據洞察優化業務流程

有任何問題隨時查看完整手冊或聯繫支持！