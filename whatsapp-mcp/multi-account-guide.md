# WhatsApp 多帳號設置指南

本指南幫助您同時運行多個 WhatsApp 帳號。

## 快速開始

### 1. 自動設置（推薦）

```bash
# 運行設置腳本
./multi-account-setup.sh

# 按提示輸入要設置的帳號數量（1-5）
```

### 2. 修改端口配置

每個帳號需要使用不同的端口。腳本已為您創建了基本結構，但需要手動修改一些文件：

#### 方法 A：使用提供的 main_multi.go

```bash
# 為每個帳號複製修改版的 main.go
cp whatsapp-bridge/main_multi.go whatsapp-account-1/whatsapp-bridge/main.go
cp whatsapp-bridge/main_multi.go whatsapp-account-2/whatsapp-bridge/main.go
# ... 以此類推
```

#### 方法 B：手動修改原始 main.go

在每個帳號的 `whatsapp-bridge/main.go` 中，修改端口號：

```go
// 帳號 1：端口 8080（默認）
fmt.Println("Starting REST API server on :8080...")
http.ListenAndServe(":8080", nil)

// 帳號 2：端口 8081
fmt.Println("Starting REST API server on :8081...")
http.ListenAndServe(":8081", nil)

// 帳號 3：端口 8082
// ... 以此類推
```

### 3. 啟動帳號

#### 啟動所有帳號
```bash
./start-all-accounts.sh
```

#### 或手動啟動單個帳號
```bash
# 帳號 1
cd whatsapp-account-1/whatsapp-bridge
go run main.go -port 8080 -db-dir store -name "WhatsApp-1"

# 帳號 2（新終端）
cd whatsapp-account-2/whatsapp-bridge
go run main.go -port 8081 -db-dir store -name "WhatsApp-2"
```

### 4. 配置 Claude Desktop

將生成的 `claude-multi-whatsapp-config.json` 內容添加到您的 Claude Desktop 配置文件：

```bash
# 查看生成的配置
cat claude-multi-whatsapp-config.json

# 編輯 Claude Desktop 配置
open ~/Library/Application\ Support/Claude/claude_desktop_config.json
```

合併配置示例：
```json
{
  "mcpServers": {
    "whatsapp-account-1": {
      "command": "/opt/homebrew/bin/uv",
      "args": [
        "--directory",
        "/path/to/whatsapp-account-1/whatsapp-mcp-server",
        "run",
        "main.py"
      ],
      "env": {
        "WHATSAPP_API_URL": "http://localhost:8080/api",
        "WHATSAPP_DB_PATH": "/path/to/whatsapp-account-1/whatsapp-bridge/store/messages.db"
      }
    },
    "whatsapp-account-2": {
      "command": "/opt/homebrew/bin/uv",
      "args": [
        "--directory",
        "/path/to/whatsapp-account-2/whatsapp-mcp-server",
        "run",
        "main.py"
      ],
      "env": {
        "WHATSAPP_API_URL": "http://localhost:8081/api",
        "WHATSAPP_DB_PATH": "/path/to/whatsapp-account-2/whatsapp-bridge/store/messages.db"
      }
    }
  }
}
```

## 管理多個帳號

### 查看運行狀態
```bash
# 查看所有 WhatsApp 進程
ps aux | grep whatsapp

# 查看特定端口
lsof -i :8080
lsof -i :8081
```

### 停止所有帳號
```bash
./stop-all-accounts.sh
```

### 查看日誌
```bash
# 如果使用後台運行
tail -f whatsapp-account-1/whatsapp-1.log
tail -f whatsapp-account-2/whatsapp-2.log
```

## 客戶檔案管理

每個帳號都有獨立的客戶檔案系統：

```bash
# 帳號 1 的客戶數據
whatsapp-account-1/whatsapp-bridge/store/messages.db
whatsapp-account-1/whatsapp-bridge/store/customer_profiles.db

# 帳號 2 的客戶數據
whatsapp-account-2/whatsapp-bridge/store/messages.db
whatsapp-account-2/whatsapp-bridge/store/customer_profiles.db
```

### 統一管理多帳號客戶

如需統一管理所有帳號的客戶資料，可以：

1. **創建中央數據庫**
   ```python
   # 修改 customer_manager.py
   central_db = "/path/to/central/customer_profiles.db"
   ```

2. **添加帳號標識**
   ```python
   # 在客戶檔案中添加 account_id 字段
   account_id = os.getenv("WHATSAPP_ACCOUNT_ID", "default")
   ```

## 注意事項

1. **資源消耗**：每個帳號會佔用一定的內存和 CPU
2. **QR 碼**：每個帳號需要單獨掃描 QR 碼登錄
3. **端口衝突**：確保每個帳號使用不同的端口
4. **數據隔離**：每個帳號的數據是獨立的

## 故障排除

### 端口已被佔用
```bash
# 查找佔用端口的進程
lsof -i :8080
# 結束進程
kill -9 [PID]
```

### 無法連接到 WhatsApp
- 檢查網絡連接
- 重新掃描 QR 碼
- 刪除 `store/whatsapp.db` 重新登錄

### Claude Desktop 無法連接
- 確保 WhatsApp Bridge 正在運行
- 檢查配置文件中的端口號是否正確
- 重啟 Claude Desktop

## 進階配置

### 使用 Docker 運行多帳號

創建 `docker-compose.yml`：
```yaml
version: '3.8'
services:
  whatsapp1:
    build: .
    ports:
      - "8080:8080"
    volumes:
      - ./data/account1:/app/store
    environment:
      - ACCOUNT_NAME=WhatsApp-1
      
  whatsapp2:
    build: .
    ports:
      - "8081:8080"
    volumes:
      - ./data/account2:/app/store
    environment:
      - ACCOUNT_NAME=WhatsApp-2
```

### 負載均衡

如果有大量消息，可以設置負載均衡：
```nginx
upstream whatsapp_backends {
    server localhost:8080;
    server localhost:8081;
    server localhost:8082;
}
```