#!/usr/bin/env python3
"""
數據庫初始化和升級腳本
用於創建和更新 WhatsApp MCP 系統所需的數據庫結構
"""

import sqlite3
import os
from pathlib import Path
import logging

# 設置日誌
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 數據庫路徑
DB_PATH = Path(__file__).parent.parent / "whatsapp-bridge" / "store" / "messages.db"
SCHEMA_PATH = Path(__file__).parent.parent / "whatsapp-bridge" / "store" / "schema_extension.sql"

def execute_schema():
    """執行數據庫架構擴展"""
    
    # 確保數據庫文件存在
    if not DB_PATH.exists():
        logger.error(f"Database not found at {DB_PATH}")
        return False
    
    # 讀取 SQL 架構文件
    if not SCHEMA_PATH.exists():
        logger.error(f"Schema file not found at {SCHEMA_PATH}")
        return False
    
    try:
        # 連接數據庫
        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()
        
        # 讀取並執行 SQL
        with open(SCHEMA_PATH, 'r', encoding='utf-8') as f:
            schema_sql = f.read()
        
        # 執行架構擴展
        cursor.executescript(schema_sql)
        conn.commit()
        
        # 驗證新表是否創建成功
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = [row[0] for row in cursor.fetchall()]
        
        new_tables = [
            'media_content',
            'customer_profiles', 
            'customer_health_info',
            'examination_records',
            'customer_preferences',
            'collection_progress',
            'conversation_templates',
            'auto_reminders'
        ]
        
        for table in new_tables:
            if table in tables:
                logger.info(f"✓ Table '{table}' created successfully")
            else:
                logger.warning(f"✗ Table '{table}' was not created")
        
        # 檢查對話模板是否插入
        cursor.execute("SELECT COUNT(*) FROM conversation_templates")
        template_count = cursor.fetchone()[0]
        logger.info(f"✓ Inserted {template_count} conversation templates")
        
        conn.close()
        logger.info("Database schema extension completed successfully!")
        return True
        
    except Exception as e:
        logger.error(f"Error executing schema: {e}")
        return False

def check_database_status():
    """檢查數據庫狀態和統計信息"""
    
    try:
        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()
        
        # 獲取統計信息
        cursor.execute("SELECT COUNT(*) FROM messages")
        message_count = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM chats")
        chat_count = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM messages WHERE media_type IS NOT NULL AND media_type != ''")
        media_count = cursor.fetchone()[0]
        
        logger.info("\n=== Database Statistics ===")
        logger.info(f"Total messages: {message_count}")
        logger.info(f"Total chats: {chat_count}")
        logger.info(f"Media messages: {media_count}")
        
        conn.close()
        
    except Exception as e:
        logger.error(f"Error checking database status: {e}")

if __name__ == "__main__":
    logger.info("Starting database setup...")
    
    # 執行架構擴展
    if execute_schema():
        # 檢查數據庫狀態
        check_database_status()
    else:
        logger.error("Database setup failed!")