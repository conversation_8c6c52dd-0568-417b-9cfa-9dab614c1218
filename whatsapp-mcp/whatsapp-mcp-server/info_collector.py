#!/usr/bin/env python3
"""
自動化信息收集系統
智能收集客戶健康資料
"""

import sqlite3
import json
import re
import logging
from datetime import datetime, timedelta
from pathlib import Path
from typing import Optional, Dict, Any, List, Tuple
from enum import Enum
from customer_profile_manager import CustomerProfileManager, CustomerProfile

logger = logging.getLogger(__name__)

DB_PATH = Path(__file__).parent.parent / "whatsapp-bridge" / "store" / "messages.db"

class CollectionState(Enum):
    """信息收集狀態"""
    NEW = "NEW"
    GREETING_SENT = "GREETING_SENT"
    COLLECTING_NAME = "COLLECTING_NAME"
    COLLECTING_BASIC = "COLLECTING_BASIC"
    COLLECTING_HEALTH = "COLLECTING_HEALTH"
    COLLECTING_PREFERENCES = "COLLECTING_PREFERENCES"
    REVIEW = "REVIEW"
    COMPLETE = "COMPLETE"
    PAUSED = "PAUSED"

class InfoCollector:
    """信息收集器"""
    
    def __init__(self):
        self.db_path = DB_PATH
        self.profile_manager = CustomerProfileManager()
        self.current_states = {}  # 緩存當前狀態
        self._load_templates()
    
    def _load_templates(self):
        """加載對話模板"""
        self.templates = {}
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute("SELECT template_key, content, next_template FROM conversation_templates")
            for key, content, next_template in cursor.fetchall():
                self.templates[key] = {
                    'content': content,
                    'next': next_template
                }
            
            conn.close()
        except Exception as e:
            logger.error(f"Error loading templates: {e}")
    
    def get_or_create_collection_state(self, whatsapp_id: str) -> Dict[str, Any]:
        """獲取或創建收集狀態"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # 先嘗試從客戶檔案獲取
            cursor.execute("SELECT id FROM customer_profiles WHERE whatsapp_id = ?", (whatsapp_id,))
            profile = cursor.fetchone()
            
            if not profile:
                # 創建新檔案
                profile_id = self.profile_manager.create_or_update_profile(whatsapp_id, {
                    'phone_number': whatsapp_id.split('@')[0]
                })
            else:
                profile_id = profile[0]
            
            # 獲取收集進度
            cursor.execute("""
                SELECT current_state, fields_collected, missing_fields, 
                       last_collection_attempt, next_follow_up
                FROM collection_progress
                WHERE customer_id = ?
            """, (profile_id,))
            
            progress = cursor.fetchone()
            
            if not progress:
                # 創建新的收集進度
                cursor.execute("""
                    INSERT INTO collection_progress 
                    (customer_id, whatsapp_id, current_state, fields_collected, missing_fields)
                    VALUES (?, ?, ?, ?, ?)
                """, (
                    profile_id,
                    whatsapp_id,
                    CollectionState.NEW.value,
                    json.dumps({}),
                    json.dumps(self._get_all_required_fields())
                ))
                conn.commit()
                
                state = {
                    'customer_id': profile_id,
                    'current_state': CollectionState.NEW.value,
                    'fields_collected': {},
                    'missing_fields': self._get_all_required_fields()
                }
            else:
                state = {
                    'customer_id': profile_id,
                    'current_state': progress[0],
                    'fields_collected': json.loads(progress[1] or '{}'),
                    'missing_fields': json.loads(progress[2] or '[]'),
                    'last_attempt': progress[3],
                    'next_follow_up': progress[4]
                }
            
            conn.close()
            return state
            
        except Exception as e:
            logger.error(f"Error getting collection state: {e}")
            return None
    
    def _get_all_required_fields(self) -> List[str]:
        """獲取所有必填字段"""
        return [
            'name_chinese',
            'name_english',
            'gender',
            'birth_date',
            'id_number',
            'emergency_contact_name',
            'emergency_contact_phone',
            'chronic_diseases',
            'current_medications',
            'allergies'
        ]
    
    def process_message(self, whatsapp_id: str, message_content: str) -> Tuple[str, str]:
        """
        處理用戶消息並返回回覆
        返回: (回覆內容, 新狀態)
        """
        # 獲取當前狀態
        state = self.get_or_create_collection_state(whatsapp_id)
        if not state:
            return "系統暫時無法處理您的消息，請稍後再試。", CollectionState.NEW.value
        
        current_state = CollectionState(state['current_state'])
        
        # 根據狀態處理消息
        if current_state == CollectionState.NEW:
            return self._handle_new_customer(whatsapp_id, state)
        
        elif current_state == CollectionState.COLLECTING_NAME:
            return self._handle_name_collection(whatsapp_id, message_content, state)
        
        elif current_state == CollectionState.COLLECTING_BASIC:
            return self._handle_basic_info_collection(whatsapp_id, message_content, state)
        
        elif current_state == CollectionState.COLLECTING_HEALTH:
            return self._handle_health_info_collection(whatsapp_id, message_content, state)
        
        else:
            return "感謝您的訊息。如需協助，請聯絡我們的客服人員。", current_state.value
    
    def _handle_new_customer(self, whatsapp_id: str, state: Dict) -> Tuple[str, str]:
        """處理新客戶"""
        # 發送歡迎消息
        greeting = self.templates.get('greeting_new_customer_zh', {}).get('content', 
            "你好！歡迎嚟到森仁醫健中心！為咗提供更好嘅服務，我需要收集一啲基本資料。請問可以知道你嘅中文全名嗎？")
        
        # 更新狀態
        self._update_collection_state(whatsapp_id, state['customer_id'], 
                                    CollectionState.COLLECTING_NAME.value)
        
        return greeting, CollectionState.COLLECTING_NAME.value
    
    def _handle_name_collection(self, whatsapp_id: str, message: str, state: Dict) -> Tuple[str, str]:
        """處理姓名收集"""
        # 提取姓名（簡單處理，假設整個消息就是姓名）
        name = message.strip()
        
        if len(name) < 2 or len(name) > 20:
            return "請提供您的完整中文姓名（2-20個字）。", CollectionState.COLLECTING_NAME.value
        
        # 更新檔案
        self.profile_manager.create_or_update_profile(whatsapp_id, {
            'name_chinese': name
        })
        
        # 更新已收集字段
        state['fields_collected']['name_chinese'] = name
        self._update_fields_collected(state['customer_id'], state['fields_collected'])
        
        # 詢問英文名
        response = f"收到，{name}你好！請問你嘅英文名係？（如果冇可以輸入'無'）"
        
        # 更新狀態
        self._update_collection_state(whatsapp_id, state['customer_id'], 
                                    CollectionState.COLLECTING_BASIC.value)
        
        return response, CollectionState.COLLECTING_BASIC.value
    
    def _handle_basic_info_collection(self, whatsapp_id: str, message: str, state: Dict) -> Tuple[str, str]:
        """處理基本信息收集"""
        fields_collected = state['fields_collected']
        
        # 根據已收集的字段決定下一步
        if 'name_english' not in fields_collected:
            # 處理英文名
            if message.strip() == '無':
                english_name = None
            else:
                english_name = message.strip().upper()
            
            if english_name:
                self.profile_manager.create_or_update_profile(whatsapp_id, {
                    'name_english': english_name
                })
                fields_collected['name_english'] = english_name
            
            # 詢問性別
            response = "請問你嘅性別係？（男/女）"
            
        elif 'gender' not in fields_collected:
            # 處理性別
            gender_map = {'男': '男', '女': '女', 'M': '男', 'F': '女'}
            gender = gender_map.get(message.strip().upper())
            
            if not gender:
                return "請輸入'男'或'女'。", CollectionState.COLLECTING_BASIC.value
            
            self.profile_manager.create_or_update_profile(whatsapp_id, {
                'gender': gender
            })
            fields_collected['gender'] = gender
            
            # 詢問出生日期
            response = "請問你嘅出生日期係幾時？（格式：YYYY-MM-DD，例如：1980-05-15）"
            
        elif 'birth_date' not in fields_collected:
            # 處理出生日期
            date_match = re.match(r'(\d{4})-(\d{1,2})-(\d{1,2})', message.strip())
            if not date_match:
                return "請用正確格式輸入日期（YYYY-MM-DD）。", CollectionState.COLLECTING_BASIC.value
            
            birth_date = message.strip()
            # 計算年齡
            birth = datetime.strptime(birth_date, '%Y-%m-%d')
            age = (datetime.now() - birth).days // 365
            
            self.profile_manager.create_or_update_profile(whatsapp_id, {
                'birth_date': birth_date,
                'age': age
            })
            fields_collected['birth_date'] = birth_date
            
            # 轉到健康信息收集
            response = "多謝你嘅基本資料！而家想了解下你嘅健康狀況。請問你有冇任何長期病患或者慢性疾病？（如果冇請輸入'無'）"
            self._update_collection_state(whatsapp_id, state['customer_id'], 
                                        CollectionState.COLLECTING_HEALTH.value)
            
        # 更新已收集字段
        self._update_fields_collected(state['customer_id'], fields_collected)
        
        return response, state['current_state']
    
    def _handle_health_info_collection(self, whatsapp_id: str, message: str, state: Dict) -> Tuple[str, str]:
        """處理健康信息收集"""
        fields_collected = state['fields_collected']
        
        if 'chronic_diseases' not in fields_collected:
            # 處理慢性疾病
            if message.strip() == '無':
                diseases = []
            else:
                # 簡單分割疾病列表
                diseases = [d.strip() for d in re.split('[,，、]', message) if d.strip()]
            
            fields_collected['chronic_diseases'] = diseases
            
            # 詢問藥物
            response = "請問你而家有冇定期服用任何藥物？如果有，請列出藥物名稱。（如果冇請輸入'無'）"
            
        elif 'current_medications' not in fields_collected:
            # 處理藥物
            if message.strip() == '無':
                medications = []
            else:
                medications = [m.strip() for m in re.split('[,，、]', message) if m.strip()]
            
            fields_collected['current_medications'] = medications
            
            # 詢問過敏
            response = "請問你有冇任何藥物或者食物過敏？（如果冇請輸入'無'）"
            
        elif 'allergies' not in fields_collected:
            # 處理過敏
            if message.strip() == '無':
                allergies = []
            else:
                allergies = [a.strip() for a in re.split('[,，、]', message) if a.strip()]
            
            fields_collected['allergies'] = allergies
            
            # 基本收集完成
            response = self._generate_summary(whatsapp_id, fields_collected)
            self._update_collection_state(whatsapp_id, state['customer_id'], 
                                        CollectionState.REVIEW.value)
        
        # 更新已收集字段
        self._update_fields_collected(state['customer_id'], fields_collected)
        
        # 保存健康信息到數據庫
        if all(k in fields_collected for k in ['chronic_diseases', 'current_medications', 'allergies']):
            self.profile_manager.update_health_info(state['customer_id'], {
                'chronic_diseases': fields_collected.get('chronic_diseases', []),
                'current_medications': [{'name': med} for med in fields_collected.get('current_medications', [])],
                'drug_allergies': fields_collected.get('allergies', [])
            })
        
        return response, state['current_state']
    
    def _generate_summary(self, whatsapp_id: str, fields_collected: Dict) -> str:
        """生成收集摘要"""
        summary = "感謝你提供嘅資料！以下係我哋收集到嘅信息：\n\n"
        
        summary += f"姓名：{fields_collected.get('name_chinese', '未提供')}"
        if fields_collected.get('name_english'):
            summary += f" ({fields_collected['name_english']})"
        summary += "\n"
        
        summary += f"性別：{fields_collected.get('gender', '未提供')}\n"
        summary += f"出生日期：{fields_collected.get('birth_date', '未提供')}\n"
        
        diseases = fields_collected.get('chronic_diseases', [])
        summary += f"慢性疾病：{', '.join(diseases) if diseases else '無'}\n"
        
        medications = fields_collected.get('current_medications', [])
        summary += f"服用藥物：{', '.join(medications) if medications else '無'}\n"
        
        allergies = fields_collected.get('allergies', [])
        summary += f"過敏史：{', '.join(allergies) if allergies else '無'}\n"
        
        summary += "\n如果需要修改任何資料，請聯絡我們的客服人員。"
        
        return summary
    
    def _update_collection_state(self, whatsapp_id: str, customer_id: int, new_state: str):
        """更新收集狀態"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute("""
                UPDATE collection_progress
                SET current_state = ?, 
                    last_collection_attempt = ?,
                    updated_at = ?
                WHERE customer_id = ?
            """, (new_state, datetime.now().isoformat(), datetime.now().isoformat(), customer_id))
            
            conn.commit()
            conn.close()
            
        except Exception as e:
            logger.error(f"Error updating collection state: {e}")
    
    def _update_fields_collected(self, customer_id: int, fields_collected: Dict):
        """更新已收集字段"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # 計算缺失字段
            all_fields = set(self._get_all_required_fields())
            collected = set(fields_collected.keys())
            missing = list(all_fields - collected)
            
            cursor.execute("""
                UPDATE collection_progress
                SET fields_collected = ?,
                    missing_fields = ?,
                    updated_at = ?
                WHERE customer_id = ?
            """, (
                json.dumps(fields_collected, ensure_ascii=False),
                json.dumps(missing, ensure_ascii=False),
                datetime.now().isoformat(),
                customer_id
            ))
            
            conn.commit()
            conn.close()
            
        except Exception as e:
            logger.error(f"Error updating fields collected: {e}")

# 測試函數
def test_info_collector():
    """測試信息收集器"""
    collector = InfoCollector()
    test_whatsapp_id = "<EMAIL>"
    
    print("=== 測試信息收集流程 ===\n")
    
    # 模擬對話
    test_messages = [
        "",  # 觸發歡迎消息
        "李小明",  # 中文名
        "LEE Siu Ming",  # 英文名
        "男",  # 性別
        "1985-03-20",  # 出生日期
        "高血壓，糖尿病",  # 慢性疾病
        "二甲雙胍，阿斯匹林",  # 藥物
        "青黴素"  # 過敏
    ]
    
    for i, msg in enumerate(test_messages):
        print(f"\n--- 第 {i+1} 輪對話 ---")
        if msg:
            print(f"用戶: {msg}")
        
        response, new_state = collector.process_message(test_whatsapp_id, msg)
        print(f"系統: {response}")
        print(f"狀態: {new_state}")

if __name__ == "__main__":
    test_info_collector()