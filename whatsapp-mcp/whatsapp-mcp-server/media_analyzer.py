#!/usr/bin/env python3
"""
媒體分析器 - 整合下載和內容提取
"""

import asyncio
import logging
from media_processor import MediaProcessor, MediaAnalyzer
from ocr_processor import OCRProcessor
import sqlite3
from pathlib import Path

# 設置日誌
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

DB_PATH = Path(__file__).parent.parent / "whatsapp-bridge" / "store" / "messages.db"

class IntegratedMediaAnalyzer:
    """整合的媒體分析器"""
    
    def __init__(self):
        self.media_processor = MediaProcessor()
        self.ocr_processor = OCRProcessor()
        self.db_path = DB_PATH
    
    async def analyze_recent_media(self, limit: int = 10):
        """分析最近的媒體消息"""
        logger.info("Starting integrated media analysis...")
        
        # Step 1: 下載未處理的媒體
        logger.info("Step 1: Downloading unprocessed media...")
        unprocessed = await self.media_processor.scan_unprocessed_media()
        
        if unprocessed:
            # 處理前幾個
            to_process = unprocessed[:limit]
            download_results = await self.media_processor.process_batch(to_process)
            logger.info(f"Download results: {download_results}")
        
        # Step 2: OCR 處理已下載的圖片
        logger.info("Step 2: Processing images with OCR...")
        ocr_results = await self.ocr_processor.process_pending_images()
        
        # 顯示 OCR 結果
        for result in ocr_results:
            if result['success']:
                logger.info(f"OCR Success: {result['message_id']} - "
                          f"Extracted {result['text_length']} chars, "
                          f"Type: {result['report_type']}, "
                          f"Confidence: {result['confidence']:.2f}")
            else:
                logger.warning(f"OCR Failed: {result['message_id']} - {result.get('error', 'Unknown error')}")
        
        return {
            'downloads': download_results if 'download_results' in locals() else None,
            'ocr_results': ocr_results
        }
    
    def get_analysis_summary(self):
        """獲取分析摘要"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # 統計各類媒體處理情況
            cursor.execute("""
                SELECT 
                    media_type,
                    extraction_method,
                    COUNT(*) as count
                FROM media_content
                GROUP BY media_type, extraction_method
            """)
            
            summary = {}
            for media_type, method, count in cursor.fetchall():
                if media_type not in summary:
                    summary[media_type] = {}
                summary[media_type][method or 'unprocessed'] = count
            
            # 獲取最近提取的內容樣本
            cursor.execute("""
                SELECT 
                    message_id,
                    media_type,
                    extracted_text,
                    extraction_confidence,
                    processed_at
                FROM media_content
                WHERE extracted_text IS NOT NULL
                ORDER BY processed_at DESC
                LIMIT 5
            """)
            
            recent_extractions = []
            for row in cursor.fetchall():
                recent_extractions.append({
                    'message_id': row[0],
                    'media_type': row[1],
                    'text_preview': row[2][:100] + '...' if row[2] and len(row[2]) > 100 else row[2],
                    'confidence': row[3],
                    'processed_at': row[4]
                })
            
            conn.close()
            
            return {
                'processing_summary': summary,
                'recent_extractions': recent_extractions
            }
            
        except Exception as e:
            logger.error(f"Error getting summary: {e}")
            return {}
    
    async def search_medical_reports(self, keywords: List[str]):
        """搜索醫療報告內容"""
        results = []
        
        for keyword in keywords:
            logger.info(f"Searching for: {keyword}")
            search_results = self.ocr_processor.search_extracted_content(keyword)
            
            for result in search_results:
                result['search_keyword'] = keyword
                results.append(result)
        
        return results

async def main():
    """主函數"""
    analyzer = IntegratedMediaAnalyzer()
    
    # 分析最近的媒體
    print("\n=== 開始整合媒體分析 ===\n")
    
    results = await analyzer.analyze_recent_media(limit=5)
    
    # 顯示摘要
    print("\n=== 分析摘要 ===")
    summary = analyzer.get_analysis_summary()
    
    print("\n處理統計:")
    for media_type, methods in summary['processing_summary'].items():
        print(f"\n{media_type}:")
        for method, count in methods.items():
            print(f"  {method}: {count}")
    
    if summary['recent_extractions']:
        print("\n最近提取的內容:")
        for extraction in summary['recent_extractions']:
            print(f"\n- {extraction['message_id']}")
            print(f"  類型: {extraction['media_type']}")
            print(f"  置信度: {extraction['confidence']:.2f}" if extraction['confidence'] else "  置信度: N/A")
            print(f"  內容預覽: {extraction['text_preview']}")
    
    # 搜索醫療關鍵詞
    print("\n=== 搜索醫療報告 ===")
    medical_keywords = ['血液', '檢查', 'blood', 'test', '報告']
    search_results = await analyzer.search_medical_reports(medical_keywords)
    
    if search_results:
        print(f"\n找到 {len(search_results)} 個相關結果:")
        for result in search_results[:5]:
            print(f"\n- 關鍵詞: {result['search_keyword']}")
            print(f"  聊天: {result['chat_name']}")
            print(f"  內容: {result['text_preview']}")

if __name__ == "__main__":
    asyncio.run(main())