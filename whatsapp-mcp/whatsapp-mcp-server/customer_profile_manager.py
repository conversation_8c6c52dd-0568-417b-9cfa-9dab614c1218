#!/usr/bin/env python3
"""
客戶檔案管理系統
管理客戶的完整健康資料和偏好
"""

import sqlite3
import json
import logging
from datetime import datetime, date
from pathlib import Path
from typing import Optional, Dict, Any, List, Tuple
from dataclasses import dataclass, asdict
from enum import Enum

logger = logging.getLogger(__name__)

DB_PATH = Path(__file__).parent.parent / "whatsapp-bridge" / "store" / "messages.db"

class CustomerType(Enum):
    VIP = "VIP"
    REGULAR = "常客"
    POTENTIAL = "潛在"
    NEW = "新客"
    LOST = "流失"

class Gender(Enum):
    MALE = "男"
    FEMALE = "女"
    OTHER = "其他"

@dataclass
class CustomerProfile:
    """客戶檔案數據類"""
    # 基本資料
    whatsapp_id: str
    phone_number: str
    name_chinese: Optional[str] = None
    name_english: Optional[str] = None
    gender: Optional[str] = None
    birth_date: Optional[str] = None
    age: Optional[int] = None
    id_number: Optional[str] = None
    id_verified: bool = False
    
    # 聯絡資料
    email: Optional[str] = None
    backup_phone: Optional[str] = None
    preferred_contact_method: str = "WhatsApp"
    preferred_contact_time: Optional[str] = None
    address_district: Optional[str] = None
    address_full: Optional[str] = None
    
    # 緊急聯絡人
    emergency_contact_name: Optional[str] = None
    emergency_contact_relation: Optional[str] = None
    emergency_contact_phone: Optional[str] = None
    
    # 語言和分類
    language_preference: str = "廣東話"
    customer_type: str = CustomerType.POTENTIAL.value
    value_level: str = "中"
    
    # 統計
    total_spending: float = 0.0
    visit_count: int = 0
    referral_count: int = 0
    
    # 系統
    created_at: Optional[str] = None
    updated_at: Optional[str] = None
    last_contact_date: Optional[str] = None
    profile_completeness: float = 0.0

@dataclass
class HealthInfo:
    """健康資料數據類"""
    customer_id: int
    # 基本健康數據
    height_cm: Optional[float] = None
    weight_kg: Optional[float] = None
    bmi: Optional[float] = None
    blood_type: Optional[str] = None
    
    # 病史（JSON 格式存儲）
    chronic_diseases: List[str] = None
    past_diseases: List[Dict] = None
    surgeries: List[Dict] = None
    hospitalizations: List[Dict] = None
    family_history: Dict[str, List[str]] = None
    
    # 用藥記錄
    current_medications: List[Dict] = None
    stopped_medications: List[Dict] = None
    
    # 過敏史
    drug_allergies: List[str] = None
    food_allergies: List[str] = None
    other_allergies: List[str] = None
    
    # 生活習慣
    smoking_status: Optional[str] = None
    drinking_status: Optional[str] = None
    exercise_frequency: Optional[str] = None
    dietary_restrictions: Optional[str] = None
    
    # 女性專項
    pregnancy_status: Optional[str] = None
    last_menstrual_date: Optional[str] = None
    pregnancy_history: Optional[str] = None

class CustomerProfileManager:
    """客戶檔案管理器"""
    
    def __init__(self):
        self.db_path = DB_PATH
        self._init_test_data()
    
    def _init_test_data(self):
        """初始化測試數據（如果需要）"""
        pass
    
    def create_or_update_profile(self, whatsapp_id: str, profile_data: Dict[str, Any]) -> int:
        """創建或更新客戶檔案"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # 檢查是否已存在
            cursor.execute("SELECT id FROM customer_profiles WHERE whatsapp_id = ?", (whatsapp_id,))
            existing = cursor.fetchone()
            
            if existing:
                # 更新現有檔案
                profile_id = existing[0]
                update_fields = []
                update_values = []
                
                for field, value in profile_data.items():
                    if field not in ['id', 'created_at']:
                        update_fields.append(f"{field} = ?")
                        update_values.append(value)
                
                update_values.append(datetime.now().isoformat())
                update_values.append(profile_id)
                
                query = f"""
                    UPDATE customer_profiles 
                    SET {', '.join(update_fields)}, updated_at = ?
                    WHERE id = ?
                """
                cursor.execute(query, update_values)
                
            else:
                # 創建新檔案
                profile = CustomerProfile(
                    whatsapp_id=whatsapp_id,
                    phone_number=profile_data.get('phone_number', whatsapp_id.split('@')[0]),
                    created_at=datetime.now().isoformat(),
                    updated_at=datetime.now().isoformat(),
                    **{k: v for k, v in profile_data.items() if k not in ['whatsapp_id', 'phone_number']}
                )
                
                # 計算完整度
                profile.profile_completeness = self._calculate_completeness(profile)
                
                # 插入數據
                fields = [k for k in asdict(profile).keys()]
                placeholders = ['?' for _ in fields]
                values = [getattr(profile, k) for k in fields]
                
                query = f"""
                    INSERT INTO customer_profiles ({', '.join(fields)})
                    VALUES ({', '.join(placeholders)})
                """
                cursor.execute(query, values)
                profile_id = cursor.lastrowid
            
            conn.commit()
            conn.close()
            
            logger.info(f"Profile {'updated' if existing else 'created'} for {whatsapp_id}")
            return profile_id
            
        except Exception as e:
            logger.error(f"Error creating/updating profile: {e}")
            return -1
    
    def get_profile(self, whatsapp_id: str) -> Optional[CustomerProfile]:
        """獲取客戶檔案"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute("SELECT * FROM customer_profiles WHERE whatsapp_id = ?", (whatsapp_id,))
            row = cursor.fetchone()
            
            if row:
                # 獲取列名
                columns = [description[0] for description in cursor.description]
                profile_dict = dict(zip(columns, row))
                
                # 移除 id 字段
                profile_dict.pop('id', None)
                
                profile = CustomerProfile(**profile_dict)
                conn.close()
                return profile
            
            conn.close()
            return None
            
        except Exception as e:
            logger.error(f"Error getting profile: {e}")
            return None
    
    def update_health_info(self, customer_id: int, health_data: Dict[str, Any]) -> bool:
        """更新健康資料"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # 轉換列表和字典為 JSON
            json_fields = ['chronic_diseases', 'past_diseases', 'surgeries', 
                          'hospitalizations', 'family_history', 'current_medications',
                          'stopped_medications', 'drug_allergies', 'food_allergies', 
                          'other_allergies']
            
            for field in json_fields:
                if field in health_data and health_data[field] is not None:
                    health_data[field] = json.dumps(health_data[field], ensure_ascii=False)
            
            # 檢查是否已存在
            cursor.execute("SELECT id FROM customer_health_info WHERE customer_id = ?", (customer_id,))
            existing = cursor.fetchone()
            
            if existing:
                # 更新
                update_fields = []
                update_values = []
                
                for field, value in health_data.items():
                    if field != 'customer_id':
                        update_fields.append(f"{field} = ?")
                        update_values.append(value)
                
                update_values.append(datetime.now().isoformat())
                update_values.append(customer_id)
                
                query = f"""
                    UPDATE customer_health_info 
                    SET {', '.join(update_fields)}, updated_at = ?
                    WHERE customer_id = ?
                """
                cursor.execute(query, update_values)
                
            else:
                # 插入
                fields = ['customer_id'] + list(health_data.keys())
                values = [customer_id] + list(health_data.values())
                placeholders = ['?' for _ in fields]
                
                query = f"""
                    INSERT INTO customer_health_info ({', '.join(fields)})
                    VALUES ({', '.join(placeholders)})
                """
                cursor.execute(query, values)
            
            conn.commit()
            conn.close()
            return True
            
        except Exception as e:
            logger.error(f"Error updating health info: {e}")
            return False
    
    def _calculate_completeness(self, profile: CustomerProfile) -> float:
        """計算檔案完整度"""
        # 定義各字段的權重
        field_weights = {
            'name_chinese': 10,
            'name_english': 5,
            'gender': 5,
            'birth_date': 10,
            'id_number': 10,
            'email': 5,
            'address_district': 5,
            'emergency_contact_name': 10,
            'emergency_contact_phone': 10,
            # 可以添加更多字段
        }
        
        total_weight = sum(field_weights.values())
        completed_weight = 0
        
        for field, weight in field_weights.items():
            value = getattr(profile, field, None)
            if value and value != "":
                completed_weight += weight
        
        return (completed_weight / total_weight) * 100 if total_weight > 0 else 0
    
    def get_incomplete_profiles(self, threshold: float = 50.0) -> List[Dict[str, Any]]:
        """獲取不完整的客戶檔案"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute("""
                SELECT whatsapp_id, name_chinese, phone_number, 
                       profile_completeness, last_contact_date
                FROM customer_profiles
                WHERE profile_completeness < ?
                ORDER BY last_contact_date DESC
            """, (threshold,))
            
            incomplete = []
            for row in cursor.fetchall():
                incomplete.append({
                    'whatsapp_id': row[0],
                    'name': row[1] or f"Customer {row[2][-4:]}",
                    'phone_number': row[2],
                    'completeness': row[3],
                    'last_contact': row[4]
                })
            
            conn.close()
            return incomplete
            
        except Exception as e:
            logger.error(f"Error getting incomplete profiles: {e}")
            return []
    
    def get_missing_fields(self, whatsapp_id: str) -> List[str]:
        """獲取缺失的必填字段"""
        profile = self.get_profile(whatsapp_id)
        if not profile:
            return ["所有基本資料"]
        
        required_fields = {
            'name_chinese': '中文姓名',
            'gender': '性別',
            'birth_date': '出生日期',
            'id_number': '身份證號碼',
            'emergency_contact_name': '緊急聯絡人姓名',
            'emergency_contact_phone': '緊急聯絡人電話'
        }
        
        missing = []
        for field, label in required_fields.items():
            value = getattr(profile, field, None)
            if not value or value == "":
                missing.append(label)
        
        return missing

# 測試函數
def test_profile_manager():
    """測試客戶檔案管理器"""
    manager = CustomerProfileManager()
    
    # 測試創建檔案
    test_whatsapp_id = "<EMAIL>"
    
    profile_data = {
        'name_chinese': '陳大文',
        'name_english': 'CHAN Tai Man',
        'gender': Gender.MALE.value,
        'birth_date': '1980-05-15',
        'age': 45,
        'email': '<EMAIL>',
        'address_district': '中環',
        'language_preference': '廣東話',
        'customer_type': CustomerType.REGULAR.value
    }
    
    print("創建客戶檔案...")
    profile_id = manager.create_or_update_profile(test_whatsapp_id, profile_data)
    print(f"檔案 ID: {profile_id}")
    
    # 測試獲取檔案
    print("\n獲取客戶檔案...")
    profile = manager.get_profile(test_whatsapp_id)
    if profile:
        print(f"姓名: {profile.name_chinese} ({profile.name_english})")
        print(f"性別: {profile.gender}")
        print(f"完整度: {profile.profile_completeness:.1f}%")
    
    # 測試健康資料
    if profile_id > 0:
        health_data = {
            'height_cm': 175,
            'weight_kg': 70,
            'blood_type': 'O+',
            'chronic_diseases': ['高血壓', '糖尿病'],
            'current_medications': [
                {'name': '二甲雙胍', 'dosage': '500mg', 'frequency': '每日兩次'}
            ],
            'drug_allergies': ['青黴素']
        }
        
        print("\n更新健康資料...")
        success = manager.update_health_info(profile_id, health_data)
        print(f"更新{'成功' if success else '失敗'}")
    
    # 測試缺失字段
    print("\n檢查缺失字段...")
    missing = manager.get_missing_fields(test_whatsapp_id)
    if missing:
        print(f"缺失: {', '.join(missing)}")
    else:
        print("所有必填字段已完成")

if __name__ == "__main__":
    test_profile_manager()