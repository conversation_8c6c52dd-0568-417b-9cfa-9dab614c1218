#!/usr/bin/env python3
"""
OCR 模擬器 - 用於測試系統而不需要安裝大型 OCR 庫
"""

import random
import sqlite3
import json
from datetime import datetime
from pathlib import Path
import logging

logger = logging.getLogger(__name__)

DB_PATH = Path(__file__).parent.parent / "whatsapp-bridge" / "store" / "messages.db"

class MockOCRProcessor:
    """模擬 OCR 處理器"""
    
    def __init__(self):
        self.db_path = DB_PATH
        # 模擬的醫療報告文字樣本
        self.sample_texts = [
            """
森仁醫健中心
健康檢查報告
日期: 2025-07-11
姓名: 張小明
年齡: 35
性別: 男

血液檢查結果:
白血球 WBC: 7.2 x10^9/L (正常)
紅血球 RBC: 4.8 x10^12/L (正常)
血紅蛋白 Hb: 145 g/L (正常)
血小板 PLT: 220 x10^9/L (正常)
血糖 GLU: 6.8 mmol/L ↑ (偏高)

建議: 注意飲食，定期檢查血糖
            """,
            """
HK REN Healthcare Center
Medical Report
Date: 2025-07-10
Name: LEE Siu Ming
Age: 42

Liver Function Test:
ALT: 45 U/L (Normal)
AST: 38 U/L (Normal)
ALP: 110 U/L ↑ (High)
Total Bilirubin: 18 umol/L (Normal)

Recommendation: Follow up in 3 months
            """,
            """
X光檢查報告
檢查部位: 胸部正位
檢查日期: 2025-07-09

發現:
1. 心臟大小正常
2. 肺野清晰
3. 無明顯異常發現

結論: 胸部X光檢查正常
醫生簽名: Dr. Wong
            """
        ]
    
    def extract_text_from_image(self, image_path: str):
        """模擬從圖片提取文字"""
        # 隨機選擇一個樣本文字
        text = random.choice(self.sample_texts)
        # 模擬置信度
        confidence = random.uniform(0.85, 0.98)
        
        logger.info(f"Mock OCR: Extracted text from {image_path}")
        return text, confidence
    
    async def process_pending_images(self):
        """處理待 OCR 的圖片"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # 查找待處理的圖片
            cursor.execute("""
                SELECT id, message_id, chat_jid, file_path, metadata
                FROM media_content
                WHERE media_type = 'image'
                AND extraction_method = 'DOWNLOAD_ONLY'
                AND extracted_text IS NULL
                LIMIT 5
            """)
            
            pending_images = cursor.fetchall()
            results = []
            
            for row in pending_images:
                record_id, message_id, chat_jid, file_path, metadata_json = row
                
                # 模擬提取文字
                text, confidence = self.extract_text_from_image(file_path)
                
                # 分析內容（簡單版）
                report_type = 'blood_test' if '血液' in text or 'blood' in text.lower() else 'general'
                
                # 更新數據庫
                cursor.execute("""
                    UPDATE media_content
                    SET extracted_text = ?,
                        extraction_method = 'MOCK_OCR',
                        extraction_confidence = ?,
                        metadata = ?,
                        processed_at = ?
                    WHERE id = ?
                """, (
                    text,
                    confidence,
                    json.dumps({
                        **json.loads(metadata_json or '{}'),
                        'ocr_analysis': {
                            'report_type': report_type,
                            'is_mock': True
                        }
                    }),
                    datetime.now().isoformat(),
                    record_id
                ))
                
                results.append({
                    'message_id': message_id,
                    'success': True,
                    'text_length': len(text),
                    'confidence': confidence,
                    'report_type': report_type
                })
                
                logger.info(f"Mock processed image {message_id}")
            
            conn.commit()
            conn.close()
            
            return results
            
        except Exception as e:
            logger.error(f"Error in mock OCR processing: {e}")
            return []
    
    def demonstrate_extraction(self):
        """演示 OCR 提取效果"""
        print("\n=== OCR 提取演示 ===")
        print("\n以下是系統能夠從醫療報告圖片中提取的信息類型：")
        
        for i, sample in enumerate(self.sample_texts, 1):
            print(f"\n--- 樣本 {i} ---")
            print(sample.strip())
            print("-" * 50)

# 測試
async def test_mock_ocr():
    processor = MockOCRProcessor()
    
    # 演示提取
    processor.demonstrate_extraction()
    
    # 處理待處理的圖片
    print("\n處理待 OCR 的圖片...")
    results = await processor.process_pending_images()
    
    print(f"\n處理了 {len(results)} 張圖片")
    for result in results:
        if result['success']:
            print(f"- {result['message_id']}: 提取了 {result['text_length']} 個字符")

if __name__ == "__main__":
    import asyncio
    asyncio.run(test_mock_ocr())