#!/usr/bin/env python3
"""
多媒體處理器
自動下載和處理 WhatsApp 多媒體消息
"""

import asyncio
import sqlite3
import json
import os
import logging
from datetime import datetime, timedelta
from pathlib import Path
from typing import Optional, List, Dict, Any, Tuple
import hashlib
import requests

# 設置日誌
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 配置
DB_PATH = Path(__file__).parent.parent / "whatsapp-bridge" / "store" / "messages.db"
MEDIA_STORAGE_PATH = Path(__file__).parent.parent / "customer_data" / "media"
WHATSAPP_API_BASE_URL = "http://localhost:8080/api"

# 確保媒體存儲目錄存在
MEDIA_STORAGE_PATH.mkdir(parents=True, exist_ok=True)

class MediaProcessor:
    """多媒體消息處理器"""
    
    def __init__(self):
        self.db_path = DB_PATH
        self.media_storage = MEDIA_STORAGE_PATH
        self.processing_queue = asyncio.Queue()
        self.processed_messages = set()
        self._load_processed_messages()
        
    def _load_processed_messages(self):
        """從數據庫加載已處理的消息ID"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            cursor.execute("SELECT message_id FROM media_content")
            self.processed_messages = {row[0] for row in cursor.fetchall()}
            conn.close()
            logger.info(f"Loaded {len(self.processed_messages)} processed messages")
        except Exception as e:
            logger.error(f"Error loading processed messages: {e}")
    
    async def scan_unprocessed_media(self) -> List[Dict[str, Any]]:
        """掃描未處理的多媒體消息"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # 查找所有未處理的多媒體消息
            cursor.execute("""
                SELECT m.id, m.chat_jid, m.sender, m.media_type, m.filename, 
                       m.timestamp, c.name as chat_name
                FROM messages m
                JOIN chats c ON m.chat_jid = c.jid
                WHERE m.media_type IS NOT NULL 
                AND m.media_type != ''
                AND m.id NOT IN (SELECT message_id FROM media_content)
                ORDER BY m.timestamp DESC
                LIMIT 100
            """)
            
            unprocessed = []
            for row in cursor.fetchall():
                message = {
                    'id': row[0],
                    'chat_jid': row[1],
                    'sender': row[2],
                    'media_type': row[3],
                    'filename': row[4],
                    'timestamp': row[5],
                    'chat_name': row[6]
                }
                unprocessed.append(message)
            
            conn.close()
            logger.info(f"Found {len(unprocessed)} unprocessed media messages")
            return unprocessed
            
        except Exception as e:
            logger.error(f"Error scanning unprocessed media: {e}")
            return []
    
    async def download_media(self, message_id: str, chat_jid: str) -> Optional[str]:
        """下載媒體文件"""
        try:
            # 調用 WhatsApp API 下載媒體
            url = f"{WHATSAPP_API_BASE_URL}/download"
            payload = {
                "message_id": message_id,
                "chat_jid": chat_jid
            }
            
            response = requests.post(url, json=payload)
            
            if response.status_code == 200:
                result = response.json()
                if result.get("success", False):
                    original_path = result.get("path")
                    if original_path and os.path.exists(original_path):
                        # 複製到我們的存儲目錄
                        local_path = await self._organize_media_file(
                            original_path, chat_jid, message_id
                        )
                        logger.info(f"Downloaded media for message {message_id}: {local_path}")
                        return local_path
                    else:
                        logger.error(f"Downloaded file not found: {original_path}")
                else:
                    logger.error(f"Download failed: {result.get('message', 'Unknown error')}")
            else:
                logger.error(f"Download API error: HTTP {response.status_code}")
                
        except Exception as e:
            logger.error(f"Error downloading media {message_id}: {e}")
        
        return None
    
    async def _organize_media_file(self, source_path: str, chat_jid: str, message_id: str) -> str:
        """組織媒體文件到結構化目錄"""
        try:
            # 創建基於日期和聊天的目錄結構
            date_str = datetime.now().strftime("%Y-%m")
            chat_dir = self._sanitize_filename(chat_jid)
            
            target_dir = self.media_storage / date_str / chat_dir
            target_dir.mkdir(parents=True, exist_ok=True)
            
            # 生成唯一文件名
            file_ext = Path(source_path).suffix
            file_hash = hashlib.md5(f"{message_id}{chat_jid}".encode()).hexdigest()[:8]
            target_filename = f"{message_id}_{file_hash}{file_ext}"
            target_path = target_dir / target_filename
            
            # 複製文件
            import shutil
            shutil.copy2(source_path, target_path)
            
            return str(target_path)
            
        except Exception as e:
            logger.error(f"Error organizing media file: {e}")
            return source_path
    
    def _sanitize_filename(self, filename: str) -> str:
        """清理文件名，移除不安全字符"""
        # 移除或替換不安全的字符
        safe_chars = "".join(c if c.isalnum() or c in ".-_" else "_" for c in filename)
        return safe_chars[:100]  # 限制長度
    
    async def process_media_message(self, message: Dict[str, Any]) -> bool:
        """處理單個媒體消息"""
        message_id = message['id']
        chat_jid = message['chat_jid']
        
        # 檢查是否已處理
        if message_id in self.processed_messages:
            return True
        
        logger.info(f"Processing media message {message_id} from {message['chat_name']}")
        
        # 下載媒體
        media_path = await self.download_media(message_id, chat_jid)
        if not media_path:
            logger.error(f"Failed to download media for message {message_id}")
            return False
        
        # 記錄到數據庫
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute("""
                INSERT INTO media_content 
                (message_id, chat_jid, media_type, original_filename, file_path, 
                 extraction_method, metadata, processed_at)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                message_id,
                chat_jid,
                message['media_type'],
                message['filename'],
                media_path,
                'DOWNLOAD_ONLY',  # 暫時只下載
                json.dumps({
                    'sender': message['sender'],
                    'timestamp': message['timestamp'],
                    'chat_name': message['chat_name']
                }),
                datetime.now().isoformat()
            ))
            
            conn.commit()
            conn.close()
            
            self.processed_messages.add(message_id)
            logger.info(f"Successfully processed media message {message_id}")
            return True
            
        except Exception as e:
            logger.error(f"Error saving media record: {e}")
            return False
    
    async def process_batch(self, messages: List[Dict[str, Any]]) -> Dict[str, int]:
        """批量處理多媒體消息"""
        results = {
            'success': 0,
            'failed': 0,
            'skipped': 0
        }
        
        for message in messages:
            if message['id'] in self.processed_messages:
                results['skipped'] += 1
                continue
            
            success = await self.process_media_message(message)
            if success:
                results['success'] += 1
            else:
                results['failed'] += 1
            
            # 添加延遲避免過於頻繁的請求
            await asyncio.sleep(1)
        
        return results
    
    async def start_auto_processing(self):
        """開始自動處理循環"""
        logger.info("Starting automatic media processing...")
        
        while True:
            try:
                # 掃描未處理的媒體
                unprocessed = await self.scan_unprocessed_media()
                
                if unprocessed:
                    logger.info(f"Processing {len(unprocessed)} media messages...")
                    results = await self.process_batch(unprocessed)
                    logger.info(f"Batch processing complete: {results}")
                
                # 等待一段時間後再次掃描
                await asyncio.sleep(30)  # 每30秒掃描一次
                
            except Exception as e:
                logger.error(f"Error in auto processing loop: {e}")
                await asyncio.sleep(60)  # 出錯後等待更長時間

class MediaAnalyzer:
    """媒體內容分析器（為後續OCR和語音處理預留）"""
    
    def __init__(self):
        self.db_path = DB_PATH
    
    async def get_pending_analysis(self) -> List[Dict[str, Any]]:
        """獲取待分析的媒體文件"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute("""
                SELECT id, message_id, chat_jid, media_type, file_path
                FROM media_content
                WHERE extraction_method = 'DOWNLOAD_ONLY'
                AND extracted_text IS NULL
                ORDER BY processed_at DESC
                LIMIT 50
            """)
            
            pending = []
            for row in cursor.fetchall():
                pending.append({
                    'id': row[0],
                    'message_id': row[1],
                    'chat_jid': row[2],
                    'media_type': row[3],
                    'file_path': row[4]
                })
            
            conn.close()
            return pending
            
        except Exception as e:
            logger.error(f"Error getting pending analysis: {e}")
            return []

async def main():
    """主函數 - 運行媒體處理器"""
    processor = MediaProcessor()
    
    # 首先處理歷史未處理的媒體
    logger.info("Processing historical media messages...")
    unprocessed = await processor.scan_unprocessed_media()
    
    if unprocessed:
        logger.info(f"Found {len(unprocessed)} historical media messages to process")
        results = await processor.process_batch(unprocessed[:20])  # 先處理前20個
        logger.info(f"Initial batch results: {results}")
    
    # 啟動自動處理
    await processor.start_auto_processing()

if __name__ == "__main__":
    asyncio.run(main())