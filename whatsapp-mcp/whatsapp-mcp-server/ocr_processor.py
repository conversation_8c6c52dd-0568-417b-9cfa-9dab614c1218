#!/usr/bin/env python3
"""
OCR 處理器
使用 EasyOCR 提取圖片中的文字
"""

import os
import sqlite3
import json
import logging
from pathlib import Path
from typing import Optional, List, Dict, Any, Tuple
from datetime import datetime

# 延遲導入 OCR 庫，因為它們較大
easyocr = None
PIL = None

logger = logging.getLogger(__name__)

# 配置
DB_PATH = Path(__file__).parent.parent / "whatsapp-bridge" / "store" / "messages.db"

class OCRProcessor:
    """OCR 文字提取處理器"""
    
    def __init__(self):
        self.db_path = DB_PATH
        self.reader = None
        self._initialize_ocr()
    
    def _initialize_ocr(self):
        """初始化 OCR 引擎"""
        try:
            global easyocr, PIL
            import easyocr as _easyocr
            from PIL import Image as _PIL
            easyocr = _easyocr
            PIL = _PIL
            
            # 創建支持中英文的 OCR 讀取器
            logger.info("Initializing EasyOCR with Chinese and English support...")
            self.reader = easyocr.Reader(['ch_tra', 'en'], gpu=False)  # 繁體中文和英文
            logger.info("OCR engine initialized successfully")
        except ImportError as e:
            logger.error(f"Failed to import OCR libraries: {e}")
            logger.error("Please install: pip install easyocr pillow")
        except Exception as e:
            logger.error(f"Failed to initialize OCR: {e}")
    
    def extract_text_from_image(self, image_path: str) -> Tuple[str, float]:
        """
        從圖片提取文字
        返回: (提取的文字, 平均置信度)
        """
        if not self.reader:
            logger.error("OCR reader not initialized")
            return "", 0.0
        
        if not os.path.exists(image_path):
            logger.error(f"Image file not found: {image_path}")
            return "", 0.0
        
        try:
            # 使用 EasyOCR 讀取圖片
            logger.info(f"Processing image: {image_path}")
            results = self.reader.readtext(image_path)
            
            # 提取文字和計算平均置信度
            text_parts = []
            confidence_scores = []
            
            for (bbox, text, confidence) in results:
                text_parts.append(text)
                confidence_scores.append(confidence)
            
            # 合併所有文字
            full_text = "\n".join(text_parts)
            
            # 計算平均置信度
            avg_confidence = sum(confidence_scores) / len(confidence_scores) if confidence_scores else 0.0
            
            logger.info(f"Extracted {len(text_parts)} text segments with average confidence: {avg_confidence:.2f}")
            
            return full_text, avg_confidence
            
        except Exception as e:
            logger.error(f"Error extracting text from image: {e}")
            return "", 0.0
    
    def analyze_medical_content(self, text: str) -> Dict[str, Any]:
        """
        分析醫療相關內容
        識別報告類型、關鍵指標等
        """
        analysis = {
            'report_type': 'unknown',
            'key_findings': [],
            'abnormal_values': [],
            'date_found': None,
            'patient_info': {}
        }
        
        # 檢測報告類型
        if any(keyword in text for keyword in ['血液檢查', '血液報告', 'Blood Test', 'CBC']):
            analysis['report_type'] = 'blood_test'
        elif any(keyword in text for keyword in ['X光', 'X-Ray', '胸部', 'Chest']):
            analysis['report_type'] = 'xray'
        elif any(keyword in text for keyword in ['心電圖', 'ECG', 'EKG']):
            analysis['report_type'] = 'ecg'
        elif any(keyword in text for keyword in ['體檢報告', '健康檢查', 'Health Check']):
            analysis['report_type'] = 'general_checkup'
        
        # 查找日期
        import re
        date_patterns = [
            r'\d{4}[-/]\d{1,2}[-/]\d{1,2}',  # YYYY-MM-DD or YYYY/MM/DD
            r'\d{1,2}[-/]\d{1,2}[-/]\d{4}',  # DD-MM-YYYY or DD/MM/YYYY
        ]
        
        for pattern in date_patterns:
            matches = re.findall(pattern, text)
            if matches:
                analysis['date_found'] = matches[0]
                break
        
        # 查找異常值標記
        abnormal_keywords = ['異常', '偏高', '偏低', 'Abnormal', 'High', 'Low', '↑', '↓', 'H', 'L']
        lines = text.split('\n')
        
        for line in lines:
            if any(keyword in line for keyword in abnormal_keywords):
                analysis['abnormal_values'].append(line.strip())
        
        # 提取患者信息
        name_patterns = [
            r'姓名[:：]\s*([^\s]+)',
            r'Name[:：]\s*([^\s]+)',
            r'患者[:：]\s*([^\s]+)'
        ]
        
        for pattern in name_patterns:
            match = re.search(pattern, text)
            if match:
                analysis['patient_info']['name'] = match.group(1)
                break
        
        return analysis
    
    async def process_pending_images(self) -> List[Dict[str, Any]]:
        """處理待 OCR 的圖片"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # 查找待處理的圖片
            cursor.execute("""
                SELECT id, message_id, chat_jid, file_path, metadata
                FROM media_content
                WHERE media_type = 'image'
                AND extraction_method = 'DOWNLOAD_ONLY'
                AND extracted_text IS NULL
                LIMIT 10
            """)
            
            pending_images = cursor.fetchall()
            results = []
            
            for row in pending_images:
                record_id, message_id, chat_jid, file_path, metadata_json = row
                
                logger.info(f"Processing image {message_id}...")
                
                # 提取文字
                text, confidence = self.extract_text_from_image(file_path)
                
                if text:
                    # 分析內容
                    analysis = self.analyze_medical_content(text)
                    
                    # 更新數據庫
                    cursor.execute("""
                        UPDATE media_content
                        SET extracted_text = ?,
                            extraction_method = 'OCR',
                            extraction_confidence = ?,
                            metadata = ?,
                            processed_at = ?
                        WHERE id = ?
                    """, (
                        text,
                        confidence,
                        json.dumps({
                            **json.loads(metadata_json or '{}'),
                            'ocr_analysis': analysis
                        }),
                        datetime.now().isoformat(),
                        record_id
                    ))
                    
                    results.append({
                        'message_id': message_id,
                        'success': True,
                        'text_length': len(text),
                        'confidence': confidence,
                        'report_type': analysis['report_type']
                    })
                    
                    logger.info(f"Successfully extracted {len(text)} characters from {message_id}")
                else:
                    results.append({
                        'message_id': message_id,
                        'success': False,
                        'error': 'No text extracted'
                    })
            
            conn.commit()
            conn.close()
            
            return results
            
        except Exception as e:
            logger.error(f"Error processing pending images: {e}")
            return []
    
    def search_extracted_content(self, query: str) -> List[Dict[str, Any]]:
        """搜索已提取的內容"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute("""
                SELECT 
                    mc.message_id,
                    mc.chat_jid,
                    mc.extracted_text,
                    mc.metadata,
                    mc.processed_at,
                    c.name as chat_name
                FROM media_content mc
                JOIN messages m ON mc.message_id = m.id
                JOIN chats c ON m.chat_jid = c.jid
                WHERE mc.extracted_text LIKE ?
                ORDER BY mc.processed_at DESC
                LIMIT 20
            """, (f'%{query}%',))
            
            results = []
            for row in cursor.fetchall():
                results.append({
                    'message_id': row[0],
                    'chat_jid': row[1],
                    'chat_name': row[5],
                    'text_preview': row[2][:200] + '...' if len(row[2]) > 200 else row[2],
                    'metadata': json.loads(row[3] or '{}'),
                    'processed_at': row[4]
                })
            
            conn.close()
            return results
            
        except Exception as e:
            logger.error(f"Error searching content: {e}")
            return []

# 測試函數
async def test_ocr():
    """測試 OCR 功能"""
    processor = OCRProcessor()
    
    # 獲取一個已下載的圖片進行測試
    conn = sqlite3.connect(DB_PATH)
    cursor = conn.cursor()
    
    cursor.execute("""
        SELECT file_path, message_id
        FROM media_content
        WHERE media_type = 'image'
        AND file_path IS NOT NULL
        LIMIT 1
    """)
    
    result = cursor.fetchone()
    conn.close()
    
    if result:
        file_path, message_id = result
        print(f"\n測試 OCR 處理: {file_path}")
        
        text, confidence = processor.extract_text_from_image(file_path)
        
        if text:
            print(f"\n提取的文字 (置信度: {confidence:.2f}):")
            print("-" * 50)
            print(text[:500] + "..." if len(text) > 500 else text)
            print("-" * 50)
            
            # 分析內容
            analysis = processor.analyze_medical_content(text)
            print(f"\n內容分析:")
            print(f"報告類型: {analysis['report_type']}")
            print(f"發現日期: {analysis['date_found']}")
            print(f"異常值數量: {len(analysis['abnormal_values'])}")
            
            if analysis['abnormal_values']:
                print("\n發現的異常值:")
                for value in analysis['abnormal_values'][:5]:
                    print(f"  - {value}")
        else:
            print("未能提取文字")
    else:
        print("沒有找到可測試的圖片")

if __name__ == "__main__":
    import asyncio
    asyncio.run(test_ocr())