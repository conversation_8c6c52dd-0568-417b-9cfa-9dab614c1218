#!/usr/bin/env python3
"""
測試媒體處理器
"""

import asyncio
import logging
from media_processor import MediaProcessor

# 設置更詳細的日誌
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

async def test_media_processor():
    """測試媒體處理器功能"""
    
    processor = MediaProcessor()
    
    print("\n=== 測試媒體處理器 ===\n")
    
    # 1. 掃描未處理的媒體
    print("1. 掃描未處理的媒體消息...")
    unprocessed = await processor.scan_unprocessed_media()
    
    if not unprocessed:
        print("   沒有找到未處理的媒體消息")
        return
    
    print(f"   找到 {len(unprocessed)} 個未處理的媒體消息")
    
    # 顯示前5個
    print("\n2. 顯示前5個未處理的媒體消息：")
    for i, msg in enumerate(unprocessed[:5], 1):
        print(f"   {i}. {msg['chat_name']} - {msg['media_type']} - {msg['filename']}")
        print(f"      ID: {msg['id']}, 時間: {msg['timestamp']}")
    
    # 3. 測試下載第一個媒體
    if unprocessed:
        print("\n3. 測試下載第一個媒體文件...")
        first_message = unprocessed[0]
        print(f"   正在下載: {first_message['filename']}")
        
        success = await processor.process_media_message(first_message)
        
        if success:
            print("   ✓ 下載成功！")
        else:
            print("   ✗ 下載失敗")
    
    # 4. 檢查處理結果
    print("\n4. 檢查數據庫記錄...")
    import sqlite3
    from pathlib import Path
    
    db_path = Path(__file__).parent.parent / "whatsapp-bridge" / "store" / "messages.db"
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    cursor.execute("""
        SELECT COUNT(*) FROM media_content
    """)
    count = cursor.fetchone()[0]
    print(f"   數據庫中已有 {count} 條媒體記錄")
    
    # 顯示最新的幾條記錄
    cursor.execute("""
        SELECT message_id, media_type, file_path, processed_at
        FROM media_content
        ORDER BY processed_at DESC
        LIMIT 5
    """)
    
    print("\n   最新的媒體記錄：")
    for row in cursor.fetchall():
        print(f"   - {row[1]} | {row[0]} | {row[3]}")
        print(f"     文件: {row[2]}")
    
    conn.close()

if __name__ == "__main__":
    asyncio.run(test_media_processor())