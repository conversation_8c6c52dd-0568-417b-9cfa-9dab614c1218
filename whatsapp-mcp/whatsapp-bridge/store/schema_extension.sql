-- WhatsApp MCP 數據庫擴展架構
-- 用於支持完整的客戶信息收集系統

-- 1. 多媒體內容表 - 存儲從圖片、文檔、音頻中提取的內容
CREATE TABLE IF NOT EXISTS media_content (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    message_id TEXT NOT NULL,
    chat_jid TEXT NOT NULL,
    media_type TEXT NOT NULL,
    original_filename TEXT,
    file_path TEXT,
    extracted_text TEXT,
    extraction_method TEXT, -- OCR, PDF_PARSER, SPEECH_TO_TEXT
    extraction_confidence REAL,
    metadata TEXT, -- JSON 格式的額外數據
    processed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (message_id, chat_jid) REFERENCES messages(id, chat_jid)
);

-- 2. 客戶檔案主表
CREATE TABLE IF NOT EXISTS customer_profiles (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    whatsapp_id TEXT UNIQUE NOT NULL,
    phone_number TEXT NOT NULL,
    -- 基本資料
    name_chinese TEXT,
    name_english TEXT,
    gender TEXT CHECK(gender IN ('男', '女', '其他')),
    birth_date DATE,
    age INTEGER,
    id_number TEXT, -- 加密存儲
    id_verified BOOLEAN DEFAULT 0,
    -- 聯絡資料
    email TEXT,
    backup_phone TEXT,
    preferred_contact_method TEXT DEFAULT 'WhatsApp',
    preferred_contact_time TEXT,
    address_district TEXT,
    address_full TEXT,
    -- 緊急聯絡人
    emergency_contact_name TEXT,
    emergency_contact_relation TEXT,
    emergency_contact_phone TEXT,
    -- 語言和偏好
    language_preference TEXT DEFAULT '廣東話',
    -- 客戶分類
    customer_type TEXT DEFAULT '潛在' CHECK(customer_type IN ('VIP', '常客', '潛在', '新客', '流失')),
    value_level TEXT DEFAULT '中' CHECK(value_level IN ('高', '中', '低')),
    -- 統計數據
    total_spending REAL DEFAULT 0,
    visit_count INTEGER DEFAULT 0,
    referral_count INTEGER DEFAULT 0,
    -- 系統欄位
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_contact_date TIMESTAMP,
    profile_completeness REAL DEFAULT 0
);

-- 3. 健康資料表
CREATE TABLE IF NOT EXISTS customer_health_info (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    customer_id INTEGER NOT NULL,
    -- 基本健康數據
    height_cm REAL,
    weight_kg REAL,
    bmi REAL,
    blood_type TEXT,
    -- 病史
    chronic_diseases TEXT, -- JSON array
    past_diseases TEXT, -- JSON array
    surgeries TEXT, -- JSON array
    hospitalizations TEXT, -- JSON array
    family_history TEXT, -- JSON object
    -- 用藥記錄
    current_medications TEXT, -- JSON array
    stopped_medications TEXT, -- JSON array
    -- 過敏史
    drug_allergies TEXT, -- JSON array
    food_allergies TEXT, -- JSON array
    other_allergies TEXT, -- JSON array
    -- 生活習慣
    smoking_status TEXT,
    drinking_status TEXT,
    exercise_frequency TEXT,
    dietary_restrictions TEXT,
    -- 女性專項
    pregnancy_status TEXT,
    last_menstrual_date DATE,
    pregnancy_history TEXT,
    -- 更新時間
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (customer_id) REFERENCES customer_profiles(id)
);

-- 4. 檢查記錄表
CREATE TABLE IF NOT EXISTS examination_records (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    customer_id INTEGER NOT NULL,
    exam_date DATE NOT NULL,
    exam_type TEXT,
    exam_items TEXT, -- JSON array
    exam_institution TEXT,
    report_status TEXT CHECK(report_status IN ('待出', '已出', '已領取')),
    abnormal_findings TEXT, -- JSON array
    follow_up_advice TEXT,
    report_files TEXT, -- JSON array of file paths
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (customer_id) REFERENCES customer_profiles(id)
);

-- 5. 客戶偏好表
CREATE TABLE IF NOT EXISTS customer_preferences (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    customer_id INTEGER NOT NULL,
    -- 檢查偏好
    budget_range TEXT,
    preferred_exam_days TEXT, -- JSON array
    preferred_time_slot TEXT,
    doctor_gender_preference TEXT,
    special_requirements TEXT, -- JSON array
    -- 溝通偏好
    response_time_expectation TEXT,
    message_detail_level TEXT,
    accept_promotions BOOLEAN DEFAULT 1,
    -- 付款偏好
    preferred_payment_method TEXT,
    need_receipt BOOLEAN DEFAULT 1,
    company_reimbursement BOOLEAN DEFAULT 0,
    -- 更新時間
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (customer_id) REFERENCES customer_profiles(id)
);

-- 6. 信息收集進度表
CREATE TABLE IF NOT EXISTS collection_progress (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    customer_id INTEGER NOT NULL,
    whatsapp_id TEXT NOT NULL,
    current_state TEXT DEFAULT 'NEW',
    -- 各部分完成狀態
    basic_info_complete BOOLEAN DEFAULT 0,
    health_info_complete BOOLEAN DEFAULT 0,
    preferences_complete BOOLEAN DEFAULT 0,
    -- 詳細進度
    fields_collected TEXT, -- JSON object showing which fields are collected
    missing_fields TEXT, -- JSON array of missing required fields
    last_collection_attempt TIMESTAMP,
    next_follow_up TIMESTAMP,
    -- 統計
    total_messages_sent INTEGER DEFAULT 0,
    total_messages_received INTEGER DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (customer_id) REFERENCES customer_profiles(id)
);

-- 7. 對話模板表
CREATE TABLE IF NOT EXISTS conversation_templates (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    template_key TEXT UNIQUE NOT NULL,
    template_type TEXT NOT NULL, -- greeting, question, confirmation, etc.
    language TEXT DEFAULT '廣東話',
    content TEXT NOT NULL,
    variables TEXT, -- JSON array of variable names
    next_template TEXT, -- next template in the flow
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 8. 自動提醒表
CREATE TABLE IF NOT EXISTS auto_reminders (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    customer_id INTEGER NOT NULL,
    reminder_type TEXT NOT NULL,
    reminder_content TEXT,
    scheduled_time TIMESTAMP NOT NULL,
    status TEXT DEFAULT 'pending' CHECK(status IN ('pending', 'sent', 'cancelled')),
    sent_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (customer_id) REFERENCES customer_profiles(id)
);

-- 創建索引以提高查詢性能
CREATE INDEX IF NOT EXISTS idx_media_content_message ON media_content(message_id, chat_jid);
CREATE INDEX IF NOT EXISTS idx_customer_profiles_whatsapp ON customer_profiles(whatsapp_id);
CREATE INDEX IF NOT EXISTS idx_customer_profiles_phone ON customer_profiles(phone_number);
CREATE INDEX IF NOT EXISTS idx_examination_records_customer ON examination_records(customer_id);
CREATE INDEX IF NOT EXISTS idx_collection_progress_state ON collection_progress(current_state);
CREATE INDEX IF NOT EXISTS idx_auto_reminders_status ON auto_reminders(status, scheduled_time);

-- 創建觸發器自動更新 updated_at 時間戳
CREATE TRIGGER IF NOT EXISTS update_customer_profiles_timestamp 
AFTER UPDATE ON customer_profiles
BEGIN
    UPDATE customer_profiles SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
END;

CREATE TRIGGER IF NOT EXISTS update_collection_progress_timestamp 
AFTER UPDATE ON collection_progress
BEGIN
    UPDATE collection_progress SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
END;

-- 插入基本對話模板
INSERT OR IGNORE INTO conversation_templates (template_key, template_type, language, content, variables, next_template) VALUES
('greeting_new_customer_zh', 'greeting', '廣東話', '你好！歡迎嚟到森仁醫健中心！我係你嘅健康顧問。為咗提供更好嘅服務俾你，我需要了解一啲基本資料。請問可以知道你嘅姓名嗎？（中文全名）', '[]', 'collect_name_zh');

INSERT OR IGNORE INTO conversation_templates (template_key, template_type, language, content, variables, next_template) VALUES
('collect_name_zh', 'question', '廣東話', '收到，{name}你好！請問你嘅英文名係？（如果冇可以話俾我知）', '["name"]', 'collect_age_zh');

INSERT OR IGNORE INTO conversation_templates (template_key, template_type, language, content, variables, next_template) VALUES
('collect_age_zh', 'question', '廣東話', '請問你嘅出生日期係幾時？（格式：年年年年-月月-日日）', '[]', 'collect_gender_zh');

INSERT OR IGNORE INTO conversation_templates (template_key, template_type, language, content, variables, next_template) VALUES
('collect_gender_zh', 'question', '廣東話', '請問你嘅性別係？（男/女）', '[]', 'collect_phone_zh');

INSERT OR IGNORE INTO conversation_templates (template_key, template_type, language, content, variables, next_template) VALUES
('collect_phone_zh', 'question', '廣東話', '請提供一個備用聯絡電話（如果有嘅話）', '[]', 'collect_health_intro_zh');

INSERT OR IGNORE INTO conversation_templates (template_key, template_type, language, content, variables, next_template) VALUES
('collect_health_intro_zh', 'question', '廣東話', '多謝你嘅基本資料！而家想了解下你嘅健康狀況。請問你有冇任何長期病患或者慢性疾病？', '[]', 'collect_medications_zh');

INSERT OR IGNORE INTO conversation_templates (template_key, template_type, language, content, variables, next_template) VALUES
('collect_medications_zh', 'question', '廣東話', '請問你而家有冇定期服用任何藥物？如果有，請列出藥物名稱。', '[]', 'collect_allergies_zh');

INSERT OR IGNORE INTO conversation_templates (template_key, template_type, language, content, variables, next_template) VALUES
('collect_allergies_zh', 'question', '廣東話', '請問你有冇任何藥物或者食物過敏？', '[]', 'collect_preferences_intro_zh');

INSERT OR IGNORE INTO conversation_templates (template_key, template_type, language, content, variables, next_template) VALUES
('greeting_new_customer_en', 'greeting', 'English', 'Hello! Welcome to HK Ren Healthcare Center! I am your health advisor. To provide you with better service, I need to collect some basic information. May I have your name please? (Full name in Chinese)', '[]', 'collect_name_en');