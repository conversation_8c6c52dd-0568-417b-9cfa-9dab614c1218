package main

import (
	"context"
	"database/sql"
	"encoding/json"
	"flag"
	"fmt"
	"log"
	"net/http"
	"os"
	"os/signal"
	"path/filepath"
	"syscall"
	"time"

	_ "github.com/mattn/go-sqlite3"
	"go.mau.fi/whatsmeow"
	"go.mau.fi/whatsmeow/store/sqlstore"
	"go.mau.fi/whatsmeow/types"
	"go.mau.fi/whatsmeow/types/events"
	waLog "go.mau.fi/whatsmeow/util/log"
)

// 支持多帳號的修改版本
var (
	port   = flag.Int("port", 8080, "Port to run the server on")
	dbDir  = flag.String("db-dir", "store", "Directory for database files")
	name   = flag.String("name", "WhatsApp", "Instance name for logging")
)

type Server struct {
	client     *whatsmeow.Client
	messagesDB *sql.DB
	port       int
	dbDir      string
	name       string
}

func NewServer(port int, dbDir string, name string) (*Server, error) {
	// 創建數據庫目錄
	err := os.MkdirAll(dbDir, 0755)
	if err != nil {
		return nil, fmt.Errorf("failed to create db directory: %v", err)
	}

	// 初始化數據庫
	container, err := sqlstore.New("sqlite3", 
		fmt.Sprintf("file:%s?_foreign_keys=true", filepath.Join(dbDir, "whatsapp.db")), 
		waLog.Stdout("Database", "INFO", true))
	if err != nil {
		return nil, fmt.Errorf("failed to create store container: %v", err)
	}

	deviceStore, err := container.GetFirstDevice()
	if err != nil {
		return nil, fmt.Errorf("failed to get device: %v", err)
	}

	client := whatsmeow.NewClient(deviceStore, waLog.Stdout("Client", "INFO", true))

	// 初始化消息數據庫
	messagesDB, err := initMessagesDB(filepath.Join(dbDir, "messages.db"))
	if err != nil {
		return nil, fmt.Errorf("failed to init messages DB: %v", err)
	}

	return &Server{
		client:     client,
		messagesDB: messagesDB,
		port:       port,
		dbDir:      dbDir,
		name:       name,
	}, nil
}

func (s *Server) Start() error {
	// 註冊事件處理器
	s.client.AddEventHandler(s.eventHandler)

	// 連接到 WhatsApp
	if s.client.Store.ID == nil {
		qrChan, _ := s.client.GetQRChannel(context.Background())
		err := s.client.Connect()
		if err != nil {
			return fmt.Errorf("failed to connect: %v", err)
		}
		for evt := range qrChan {
			if evt.Event == "code" {
				fmt.Printf("[%s] QR code: %s\n", s.name, evt.Code)
				fmt.Printf("[%s] Scan this QR code with WhatsApp on your phone\n", s.name)
			} else {
				fmt.Printf("[%s] Login event: %s\n", s.name, evt.Event)
			}
		}
	} else {
		err := s.client.Connect()
		if err != nil {
			return fmt.Errorf("failed to connect: %v", err)
		}
	}

	fmt.Printf("[%s] Connected to WhatsApp!\n", s.name)

	// 啟動 HTTP 服務器
	http.HandleFunc("/api/chats", s.handleGetChats)
	http.HandleFunc("/api/messages", s.handleGetMessages)
	http.HandleFunc("/api/send", s.handleSendMessage)
	http.HandleFunc("/api/media/", s.handleGetMedia)
	http.HandleFunc("/health", s.handleHealth)

	fmt.Printf("[%s] Starting REST API server on :%d...\n", s.name, s.port)
	go func() {
		if err := http.ListenAndServe(fmt.Sprintf(":%d", s.port), nil); err != nil {
			log.Printf("[%s] HTTP server error: %v\n", s.name, err)
		}
	}()

	// 等待中斷信號
	c := make(chan os.Signal, 1)
	signal.Notify(c, os.Interrupt, syscall.SIGTERM)
	<-c

	fmt.Printf("[%s] Shutting down...\n", s.name)
	s.client.Disconnect()
	s.messagesDB.Close()
	return nil
}

func (s *Server) eventHandler(evt interface{}) {
	switch v := evt.(type) {
	case *events.Message:
		s.handleMessage(v)
	}
}

func (s *Server) handleMessage(msg *events.Message) {
	// 保存消息到數據庫
	_, err := s.messagesDB.Exec(`
		INSERT INTO messages (chat_id, message_id, sender, text, timestamp, from_me)
		VALUES (?, ?, ?, ?, ?, ?)
	`, msg.Info.Chat.String(), msg.Info.ID, msg.Info.Sender.String(), 
		msg.Message.GetConversation(), msg.Info.Timestamp, msg.Info.IsFromMe)
	
	if err != nil {
		log.Printf("[%s] Failed to save message: %v\n", s.name, err)
	}
}

func (s *Server) handleHealth(w http.ResponseWriter, r *http.Request) {
	response := map[string]interface{}{
		"status": "ok",
		"instance": s.name,
		"port": s.port,
		"connected": s.client.IsConnected(),
	}
	json.NewEncoder(w).Encode(response)
}

// ... 其他處理函數保持不變 ...

func initMessagesDB(dbPath string) (*sql.DB, error) {
	db, err := sql.Open("sqlite3", dbPath)
	if err != nil {
		return nil, err
	}

	// 創建表
	_, err = db.Exec(`
		CREATE TABLE IF NOT EXISTS messages (
			id INTEGER PRIMARY KEY AUTOINCREMENT,
			chat_id TEXT NOT NULL,
			message_id TEXT NOT NULL,
			sender TEXT NOT NULL,
			text TEXT,
			timestamp INTEGER,
			from_me BOOLEAN,
			media_url TEXT,
			media_type TEXT,
			UNIQUE(chat_id, message_id)
		)
	`)
	if err != nil {
		return nil, err
	}

	_, err = db.Exec(`
		CREATE TABLE IF NOT EXISTS chats (
			id TEXT PRIMARY KEY,
			name TEXT,
			last_message_time INTEGER,
			unread_count INTEGER DEFAULT 0
		)
	`)
	if err != nil {
		return nil, err
	}

	return db, nil
}

func main() {
	flag.Parse()

	server, err := NewServer(*port, *dbDir, *name)
	if err != nil {
		log.Fatalf("Failed to create server: %v", err)
	}

	if err := server.Start(); err != nil {
		log.Fatalf("Server error: %v", err)
	}
}