# WhatsApp MCP Default Configuration
# All values can be overridden by environment variables

app:
  name: "WhatsApp MCP Server"
  version: "2.0.0"
  environment: "development"
  log_level: "INFO"

database:
  # Path relative to project root
  messages_db: "whatsapp-bridge/store/messages.db"
  # Connection settings
  timeout: 30
  check_same_thread: false

whatsapp_bridge:
  api_url: "http://localhost:8080/api"
  timeout: 30
  retry_attempts: 3
  retry_delay: 1

storage:
  # Base path for all media storage
  media_root: "customer_data/media"
  # Organize by date
  use_date_folders: true
  # Maximum file name length
  max_filename_length: 100

processing:
  # Media processing settings
  media:
    batch_size: 10
    scan_interval: 30  # seconds
    download_delay: 1  # seconds between downloads
    error_retry_delay: 60  # seconds after error
  
  # OCR settings
  ocr:
    enabled: true
    languages: ["ch_tra", "en"]  # Traditional Chinese and English
    use_gpu: false
    batch_size: 5
    confidence_threshold: 0.7
  
  # Audio settings (for future implementation)
  audio:
    enabled: false
    model: "whisper-base"
    language: "zh"

customer:
  # Required fields for profile completeness
  required_fields:
    - "name_chinese"
    - "gender"
    - "birth_date"
    - "id_number"
    - "emergency_contact_name"
    - "emergency_contact_phone"
  
  # Field weights for completeness calculation
  field_weights:
    name_chinese: 10
    name_english: 5
    gender: 5
    birth_date: 10
    id_number: 10
    email: 5
    address_district: 5
    emergency_contact_name: 10
    emergency_contact_phone: 10
    chronic_diseases: 10
    current_medications: 10
    allergies: 10
  
  # Information collection settings
  collection:
    greeting_template: "greeting_new_customer_zh"
    default_language: "廣東話"
    name_min_length: 2
    name_max_length: 20
    session_timeout: 1800  # 30 minutes

mcp:
  # MCP server settings
  transport: "stdio"
  
# Feature flags
features:
  auto_download_media: true
  ocr_processing: true
  audio_transcription: false
  auto_info_collection: true
  profile_completeness_check: true

# Development settings
development:
  use_mock_ocr: false
  use_mock_api: false
  debug_sql: false
  test_mode: false