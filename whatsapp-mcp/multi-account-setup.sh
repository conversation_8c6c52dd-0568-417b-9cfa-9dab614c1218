#!/bin/bash

# WhatsApp 多帳號設置腳本
# 這個腳本幫助您快速設置多個 WhatsApp 帳號

echo "WhatsApp MCP 多帳號設置助手"
echo "=========================="

# 創建多帳號目錄結構
setup_account() {
    local account_num=$1
    local port=$((8080 + $account_num - 1))
    local account_dir="whatsapp-account-$account_num"
    
    echo "設置帳號 $account_num (端口: $port)..."
    
    # 創建帳號專用目錄
    mkdir -p "$account_dir/whatsapp-bridge/store"
    mkdir -p "$account_dir/whatsapp-mcp-server"
    
    # 複製必要文件
    cp -r whatsapp-bridge/*.go "$account_dir/whatsapp-bridge/"
    cp -r whatsapp-bridge/go.* "$account_dir/whatsapp-bridge/"
    cp -r whatsapp-mcp-server/*.py "$account_dir/whatsapp-mcp-server/"
    cp -r whatsapp-mcp-server/pyproject.toml "$account_dir/whatsapp-mcp-server/"
    
    # 創建修改後的啟動腳本
    cat > "$account_dir/start-bridge.sh" <<EOF
#!/bin/bash
cd whatsapp-bridge
echo "啟動 WhatsApp Bridge - 帳號 $account_num (端口: $port)"
go run main.go
EOF
    
    chmod +x "$account_dir/start-bridge.sh"
    
    # 創建環境變量文件
    cat > "$account_dir/.env" <<EOF
WHATSAPP_PORT=$port
WHATSAPP_DB_DIR=./whatsapp-bridge/store
ACCOUNT_NAME=WhatsApp-Account-$account_num
EOF
    
    echo "帳號 $account_num 設置完成！"
    echo "啟動命令: cd $account_dir && ./start-bridge.sh"
    echo ""
}

# 詢問要設置多少個帳號
read -p "您要設置多少個 WhatsApp 帳號？ (1-5): " num_accounts

# 驗證輸入
if [[ ! "$num_accounts" =~ ^[1-5]$ ]]; then
    echo "錯誤：請輸入 1 到 5 之間的數字"
    exit 1
fi

# 設置每個帳號
for i in $(seq 1 $num_accounts); do
    setup_account $i
done

# 生成 Claude Desktop 配置
echo "生成 Claude Desktop 配置..."
cat > claude-multi-whatsapp-config.json <<EOF
{
  "mcpServers": {
EOF

for i in $(seq 1 $num_accounts); do
    port=$((8080 + $i - 1))
    if [ $i -gt 1 ]; then
        echo "," >> claude-multi-whatsapp-config.json
    fi
    cat >> claude-multi-whatsapp-config.json <<EOF
    "whatsapp-account-$i": {
      "command": "/opt/homebrew/bin/uv",
      "args": [
        "--directory",
        "$(pwd)/whatsapp-account-$i/whatsapp-mcp-server",
        "run",
        "main.py"
      ],
      "env": {
        "WHATSAPP_API_URL": "http://localhost:$port/api",
        "WHATSAPP_DB_PATH": "$(pwd)/whatsapp-account-$i/whatsapp-bridge/store/messages.db"
      }
    }
EOF
done

cat >> claude-multi-whatsapp-config.json <<EOF
  }
}
EOF

echo ""
echo "設置完成！"
echo "=========="
echo ""
echo "下一步："
echo "1. 修改每個帳號的 main.go 文件，更改端口號"
echo "2. 將 claude-multi-whatsapp-config.json 的內容添加到您的 Claude Desktop 配置"
echo "3. 在不同終端中啟動每個帳號的 WhatsApp Bridge"
echo ""
echo "快速啟動所有帳號："
echo "./start-all-accounts.sh"