#!/bin/bash

# 啟動所有 WhatsApp 帳號的腳本

echo "啟動所有 WhatsApp 帳號..."
echo "========================"

# 檢查有多少個帳號目錄
accounts=()
for dir in whatsapp-account-*/; do
    if [ -d "$dir" ]; then
        accounts+=("$dir")
    fi
done

if [ ${#accounts[@]} -eq 0 ]; then
    echo "錯誤：沒有找到任何帳號目錄"
    echo "請先運行 ./multi-account-setup.sh 設置帳號"
    exit 1
fi

echo "找到 ${#accounts[@]} 個帳號"
echo ""

# 啟動每個帳號
for i in "${!accounts[@]}"; do
    account_dir="${accounts[$i]}"
    account_num=$((i + 1))
    port=$((8080 + i))
    
    echo "啟動帳號 $account_num (端口: $port)..."
    
    # 在新的終端中啟動（macOS）
    if [[ "$OSTYPE" == "darwin"* ]]; then
        osascript -e "
        tell application \"Terminal\"
            do script \"cd $(pwd)/$account_dir && ./start-bridge-multi.sh\"
        end tell
        "
    # Linux (使用 gnome-terminal)
    elif command -v gnome-terminal &> /dev/null; then
        gnome-terminal -- bash -c "cd $(pwd)/$account_dir && ./start-bridge-multi.sh; exec bash"
    # 其他情況，使用後台進程
    else
        (cd "$account_dir" && ./start-bridge-multi.sh > "whatsapp-$account_num.log" 2>&1 &)
        echo "帳號 $account_num 在後台啟動，日誌文件：$account_dir/whatsapp-$account_num.log"
    fi
    
    # 等待一下，避免同時啟動太多進程
    sleep 2
done

echo ""
echo "所有帳號啟動命令已發送！"
echo ""
echo "提示："
echo "- 每個帳號會在新的終端窗口中運行"
echo "- 首次運行時需要掃描各自的 QR 碼"
echo "- 可以使用 'ps aux | grep whatsapp' 查看運行狀態"
echo ""
echo "停止所有帳號："
echo "./stop-all-accounts.sh"