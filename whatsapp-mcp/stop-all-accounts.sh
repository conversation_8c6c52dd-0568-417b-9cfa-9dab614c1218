#!/bin/bash

# 停止所有 WhatsApp 帳號的腳本

echo "停止所有 WhatsApp 帳號..."
echo "========================"

# 查找所有運行中的 WhatsApp 進程
pids=$(ps aux | grep -E "go run main.*whatsapp|whatsapp-bridge" | grep -v grep | awk '{print $2}')

if [ -z "$pids" ]; then
    echo "沒有找到運行中的 WhatsApp 進程"
else
    echo "找到以下進程："
    ps aux | grep -E "go run main.*whatsapp|whatsapp-bridge" | grep -v grep
    
    echo ""
    read -p "確定要停止所有這些進程嗎？ (y/n): " confirm
    
    if [ "$confirm" = "y" ] || [ "$confirm" = "Y" ]; then
        for pid in $pids; do
            echo "停止進程 $pid..."
            kill -TERM $pid 2>/dev/null
        done
        echo "所有進程已停止"
    else
        echo "取消操作"
    fi
fi

echo ""
echo "完成！"