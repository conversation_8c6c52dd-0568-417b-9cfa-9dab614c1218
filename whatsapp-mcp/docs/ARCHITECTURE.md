# WhatsApp MCP System Architecture

## Overview

WhatsApp MCP (Model Context Protocol) is a comprehensive system for integrating WhatsApp messaging with AI-powered customer management, automated information collection, and business intelligence capabilities. The system has been refactored to provide a clean, modular architecture without hard-coding or mock implementations.

## System Components

### 1. WhatsApp Bridge (Go)
- **Location**: `/whatsapp-bridge/`
- **Technology**: Go with whatsmeow library
- **Purpose**: Direct WhatsApp connection and message handling
- **Features**:
  - QR code authentication
  - Message sending/receiving
  - Media download
  - SQLite message storage
  - REST API for MCP server communication

### 2. MCP Server (Python)
- **Location**: `/whatsapp-mcp-server/`
- **Technology**: Python with MCP protocol
- **Purpose**: Expose WhatsApp functionality as MCP tools
- **Integration**: Communicates with WhatsApp Bridge via REST API

### 3. Core Modules (`/src/core/`)
Centralized infrastructure components:

#### config.py
- Unified configuration management
- Environment variable support
- YAML configuration loading
- Type-safe config objects

#### database.py
- Database abstraction layer
- Connection pooling
- Common query patterns
- JSON serialization helpers

#### whatsapp_api.py
- WhatsApp Bridge API client
- Retry logic and error handling
- Message and media operations

### 4. Processing Modules (`/src/processors/`)
Specialized processors for different content types:

#### media.py
- Automatic media download
- File organization
- Batch processing
- Progress tracking

#### ocr.py
- Optical character recognition
- Medical report analysis
- Multi-language support (Chinese/English)
- Lazy loading for performance

### 5. Customer Management (`/src/customer/`)
Comprehensive customer data management:

#### customer_manager.py
- Profile creation and updates
- Customer classification (VIP, Regular, etc.)
- Completeness tracking
- Search and insights

#### info_collector.py
- State machine for progressive collection
- Multi-language conversation templates
- Validation and error handling
- Collection progress tracking

#### health_tracker.py
- Medical history management
- Examination records
- Medication and allergy tracking
- Automated reminders

#### analytics.py
- Conversation analytics
- Customer segmentation
- Business opportunities
- Revenue insights

## Database Schema

### Core Tables
```sql
-- WhatsApp Bridge tables
messages          -- All WhatsApp messages
chats            -- Chat/contact information
media            -- Media file references

-- Extension tables
media_content        -- Downloaded media with extracted text
customer_profiles    -- Customer information
customer_health_info -- Health records
examination_records  -- Medical examinations
customer_preferences -- Communication preferences
collection_progress  -- Information collection state
conversation_templates -- Response templates
auto_reminders      -- Scheduled reminders
```

## Architecture Patterns

### 1. Configuration-Driven Design
- All settings in `/config/default.yaml`
- No hard-coded values
- Environment variable overrides
- Feature flags for optional functionality

### 2. Singleton Pattern
- Single instances for managers
- Lazy initialization
- Resource efficiency

### 3. State Machine Pattern
- Information collection flow
- Conversation state tracking
- Progress persistence

### 4. Abstraction Layers
- Database operations abstracted
- API calls wrapped
- Business logic separated from infrastructure

## Data Flow

```
1. WhatsApp Message Received
   ↓
2. WhatsApp Bridge (Go)
   - Stores in SQLite
   - Notifies MCP Server
   ↓
3. MCP Server
   - Processes message
   - Updates customer profile
   ↓
4. Processing Pipeline
   - Media download (if applicable)
   - OCR extraction (for images)
   - Information collection
   ↓
5. Analytics & Insights
   - Real-time updates
   - Business intelligence
```

## Security Considerations

1. **Authentication**
   - WhatsApp QR code pairing
   - API endpoint protection

2. **Data Privacy**
   - Local storage only
   - No external data transmission
   - Sensitive data encryption ready

3. **Access Control**
   - Database-level permissions
   - API authentication tokens

## Performance Optimizations

1. **Lazy Loading**
   - OCR engine loaded on demand
   - Reduces startup time

2. **Batch Processing**
   - Media downloads in batches
   - Configurable batch sizes

3. **Caching**
   - Database connection pooling
   - Configuration caching

4. **Async Operations**
   - Non-blocking media processing
   - Concurrent message handling

## Deployment Architecture

```
┌─────────────────┐     ┌──────────────────┐
│  WhatsApp Web   │────▶│ WhatsApp Bridge  │
└─────────────────┘     │    (Go:8080)     │
                        └────────┬─────────┘
                                 │
                        ┌────────▼─────────┐
                        │   MCP Server     │
                        │  (Python:5000)   │
                        └────────┬─────────┘
                                 │
                        ┌────────▼─────────┐
                        │  SQLite Database │
                        └──────────────────┘
```

## Configuration Reference

### Key Configuration Sections

```yaml
whatsapp_bridge:
  api_url: "http://localhost:8080/api"
  timeout: 30

database:
  path: "./data/messages.db"
  
storage:
  media_path: "./data/media"
  use_date_folders: true

features:
  auto_download_media: true
  ocr_processing: true
  
customer:
  vip_spending_threshold: 10000
  collect_health_info: true
```

## Monitoring and Observability

1. **Logging**
   - Structured logging throughout
   - Configurable log levels
   - Separate logs per module

2. **Metrics**
   - Message processing stats
   - Customer engagement metrics
   - System performance indicators

3. **Health Checks**
   - Database connectivity
   - WhatsApp connection status
   - API availability

## Future Enhancements

1. **Scalability**
   - Redis for caching
   - Message queue integration
   - Horizontal scaling support

2. **AI Integration**
   - LLM-powered responses
   - Sentiment analysis
   - Predictive analytics

3. **Enterprise Features**
   - Multi-tenant support
   - Role-based access control
   - Audit logging

## Development Guidelines

1. **Code Organization**
   - Clear module boundaries
   - Single responsibility principle
   - Comprehensive error handling

2. **Testing Strategy**
   - Unit tests for core logic
   - Integration tests for APIs
   - Mock implementations for testing

3. **Documentation**
   - Inline code documentation
   - API documentation
   - User guides

## Troubleshooting

Common issues and solutions:

1. **WhatsApp Connection**
   - Check QR code pairing
   - Verify network connectivity
   - Review bridge logs

2. **Database Errors**
   - Check file permissions
   - Verify schema migrations
   - Review connection settings

3. **Media Processing**
   - Verify storage permissions
   - Check disk space
   - Review API connectivity

## Conclusion

The refactored WhatsApp MCP system provides a clean, maintainable architecture for building WhatsApp-based customer engagement solutions. The modular design allows for easy extension and customization while maintaining code quality and performance.