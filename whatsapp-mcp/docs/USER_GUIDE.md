# WhatsApp MCP 用戶操作指南

## 快速開始

### 1. 系統啟動

#### 啟動 WhatsApp Bridge
```bash
cd whatsapp-bridge
go run main.go
```
- 首次運行會顯示 QR Code
- 使用 WhatsApp 手機應用掃描登錄

#### 啟動 MCP Server
```bash
cd whatsapp-mcp-server
uv run mcp-whatsapp
```

### 2. 基本功能

#### 查看客戶信息
系統會自動記錄所有 WhatsApp 對話，您可以：
- 查看客戶基本資料
- 追蹤對話歷史
- 查看客戶健康記錄

#### 多媒體信息處理
- **自動下載**：系統自動下載客戶發送的圖片、文檔
- **OCR 識別**：自動識別圖片中的文字（如檢查報告）
- **分類存儲**：按日期和客戶分類保存

## 主要功能模塊

### 1. 客戶信息收集

系統會自動收集以下信息：
- 姓名（中英文）
- 性別
- 出生日期
- 聯絡電話
- 電郵地址
- 居住地區
- 健康狀況

**收集流程**：
1. 客戶發送信息時，系統檢查資料完整性
2. 自動詢問缺失的信息
3. 使用對話式收集，自然親切
4. 支持中英文雙語

### 2. 健康信息管理

#### 記錄內容
- 慢性疾病
- 正在服用藥物
- 過敏史
- 體檢記錄
- 醫生建議

#### 自動提醒
- 覆診提醒
- 體檢到期提醒
- 用藥提醒

### 3. 客戶分析

#### 客戶分類
- **VIP 客戶**：消費額達到設定門檻
- **常規客戶**：有消費記錄
- **潛在客戶**：活躍但未消費
- **非活躍客戶**：超過30天無互動

#### 商機識別
系統自動識別：
- 需要挽回的 VIP 客戶
- 體檢到期客戶
- 資料不完整的活躍客戶

## 配置管理

### 修改配置文件
編輯 `config/default.yaml`：

```yaml
# 客戶管理設置
customer:
  vip_spending_threshold: 10000  # VIP門檻
  inactive_days_threshold: 30     # 非活躍天數
  collect_health_info: true       # 是否收集健康信息

# OCR 設置
ocr:
  languages: ["ch_sim", "en"]     # 識別語言
  confidence_threshold: 0.7       # 置信度門檻

# 功能開關
features:
  auto_download_media: true       # 自動下載媒體
  ocr_processing: true           # OCR處理
```

### 環境變量覆蓋
```bash
export WHATSAPP_MCP_DATABASE_PATH=/custom/path/messages.db
export WHATSAPP_MCP_FEATURES_OCR_PROCESSING=false
```

## 常用操作

### 1. 查看系統統計
```python
from src.customer import CustomerManager

manager = CustomerManager()
stats = manager.get_statistics()
print(f"總客戶數：{stats['total_customers']}")
print(f"VIP客戶：{stats['vip_customers']}")
```

### 2. 搜索客戶
```python
# 按姓名搜索
results = manager.search_customers("陳")

# 按健康狀況搜索
from src.customer import HealthTracker
tracker = HealthTracker()
diabetes_patients = tracker.search_by_condition("糖尿病")
```

### 3. 生成業務報告
```python
from src.customer import CustomerAnalytics

analytics = CustomerAnalytics()

# 對話分析（最近30天）
conv_report = analytics.get_conversation_analytics(30)
print(f"獨立客戶：{conv_report['unique_customers']}")
print(f"熱門話題：{conv_report['conversation_topics']}")

# 商機報告
opportunities = analytics.get_business_opportunities()
for opp in opportunities:
    print(f"{opp['type']}: {opp['recommendation']}")
```

### 4. 手動觸發信息收集
```python
from src.customer import InformationCollector

collector = InformationCollector()

# 開始收集
whatsapp_id = "<EMAIL>"
response = collector.start_collection(whatsapp_id, language='zh')
# 系統會自動發送詢問信息

# 查看收集進度
progress = collector.get_progress(whatsapp_id)
print(f"完成度：{progress['profile_completeness']}%")
```

## 數據管理

### 數據位置
- **數據庫**：`./data/messages.db`
- **媒體文件**：`./data/media/`
- **配置文件**：`./config/default.yaml`

### 備份建議
```bash
# 備份數據庫
cp ./data/messages.db ./backup/messages_$(date +%Y%m%d).db

# 備份媒體文件
tar -czf ./backup/media_$(date +%Y%m%d).tar.gz ./data/media/
```

## 故障排除

### 1. WhatsApp 連接問題
**症狀**：無法接收/發送消息
**解決**：
- 檢查 WhatsApp Bridge 是否運行
- 重新掃描 QR Code
- 檢查網絡連接

### 2. OCR 識別問題
**症狀**：圖片文字無法識別
**解決**：
- 確認已安裝 easyocr：`pip install easyocr`
- 檢查圖片質量
- 調整置信度門檻

### 3. 數據庫錯誤
**症狀**：無法保存客戶信息
**解決**：
- 檢查文件權限
- 確認磁盤空間
- 查看錯誤日誌

## 最佳實踐

### 1. 客戶服務
- 及時回覆客戶消息
- 定期檢查商機報告
- 主動關懷 VIP 客戶

### 2. 數據維護
- 每週備份數據
- 定期清理過期媒體文件
- 監控系統性能

### 3. 隱私保護
- 不要分享客戶個人信息
- 定期審查訪問權限
- 遵守數據保護法規

## 進階功能

### 自定義對話模板
編輯數據庫中的 `conversation_templates` 表：
```sql
INSERT INTO conversation_templates (language, category, template_text)
VALUES ('zh', 'custom_greeting', '歡迎光臨！請問有什麼可以幫到您？');
```

### 擴展健康檢查類型
```python
# 添加新的檢查類型
exam_data = {
    'exam_type': '心臟超聲波',
    'date': '2024-01-15',
    'summary': '心臟功能正常',
    'follow_up_required': True,
    'follow_up_date': '2024-07-15'
}
tracker.add_examination_record(customer_id, exam_data)
```

### 自定義分析報告
```python
# 創建月度報告
def generate_monthly_report():
    analytics = CustomerAnalytics()
    
    # 獲取各項數據
    conv = analytics.get_conversation_analytics(30)
    segments = analytics.get_customer_segmentation()
    revenue = analytics.get_revenue_analytics(30)
    
    # 生成報告
    report = f"""
    月度業務報告
    ============
    客戶總數：{segments['by_status']}
    本月對話：{conv['total_conversations']}
    營收：${revenue['total_revenue']}
    """
    
    return report
```

## 聯繫支持

如遇到問題，請：
1. 查看系統日誌
2. 參考故障排除章節
3. 聯繫技術支持團隊

## 附錄：常用命令

```bash
# 查看系統狀態
curl http://localhost:8080/api/status

# 測試消息發送
curl -X POST http://localhost:8080/api/send \
  -H "Content-Type: application/json" \
  -d '{"recipient":"<EMAIL>","message":"測試消息"}'

# 查看數據庫內容
sqlite3 ./data/messages.db "SELECT COUNT(*) FROM messages;"

# 監控日誌
tail -f logs/whatsapp-mcp.log
```