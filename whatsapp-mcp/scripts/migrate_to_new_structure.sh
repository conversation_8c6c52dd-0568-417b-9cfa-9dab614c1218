#!/bin/bash
# Script to migrate existing WhatsApp MCP files to new structure

echo "Starting migration to new structure..."

# Create backup
echo "Creating backup..."
mkdir -p backups
tar -czf "backups/backup_$(date +%Y%m%d_%H%M%S).tar.gz" whatsapp-mcp-server/ whatsapp-bridge/ 2>/dev/null

# Move Go bridge files
echo "Moving Go bridge files..."
if [ -d "whatsapp-bridge" ]; then
    cp -r whatsapp-bridge/* go-bridge/ 2>/dev/null
    echo "✓ Go bridge files moved"
fi

# Move customer management files
echo "Moving customer management files..."
if [ -f "whatsapp-mcp-server/customer_profile_manager.py" ]; then
    cp whatsapp-mcp-server/customer_profile_manager.py src/customer_management/profile.py
    echo "✓ Customer profile manager moved"
fi

# Move info collector
if [ -f "whatsapp-mcp-server/info_collector.py" ]; then
    cp whatsapp-mcp-server/info_collector.py src/customer_management/collector.py
    echo "✓ Info collector moved"
fi

# Move media processing files
echo "Moving media processing files..."
if [ -f "whatsapp-mcp-server/media_processor.py" ]; then
    cp whatsapp-mcp-server/media_processor.py src/data_processing/media.py
    echo "✓ Media processor moved"
fi

if [ -f "whatsapp-mcp-server/media_analyzer.py" ]; then
    cp whatsapp-mcp-server/media_analyzer.py src/data_processing/analyzer.py
    echo "✓ Media analyzer moved"
fi

# Move audio processing
if [ -f "whatsapp-mcp-server/audio.py" ]; then
    cp whatsapp-mcp-server/audio.py src/data_processing/audio.py
    echo "✓ Audio processor moved"
fi

# Move documentation
echo "Moving documentation..."
mkdir -p docs/zh-CN
for file in *.md; do
    if [ -f "$file" ]; then
        cp "$file" docs/
    fi
done

# Move Chinese docs
for file in WhatsApp*.md 完整使用手冊.md 常用WhatsApp指令快查.md 快速開始指南.md 測試WhatsApp_MCP.md; do
    if [ -f "$file" ]; then
        mv "$file" docs/zh-CN/ 2>/dev/null
    fi
done

echo "✓ Documentation moved"

# Move test files
echo "Moving test files..."
if [ -f "whatsapp-mcp-server/test_media_processor.py" ]; then
    mkdir -p tests/unit
    cp whatsapp-mcp-server/test_media_processor.py tests/unit/
    echo "✓ Test files moved"
fi

# Update imports in moved files
echo "Updating imports..."
# This would need to be done manually or with a more sophisticated script

echo ""
echo "Migration complete!"
echo ""
echo "Next steps:"
echo "1. Review the migrated files in the new structure"
echo "2. Update any remaining imports manually"
echo "3. Test the system with the new structure"
echo "4. Remove old directories once confirmed working"
echo ""
echo "Old structure backed up to: backups/"