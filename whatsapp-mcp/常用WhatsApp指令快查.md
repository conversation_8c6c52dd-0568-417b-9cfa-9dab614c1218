# 常用 WhatsApp MCP 指令快查

## 基礎查詢指令

### 1. 聯絡人相關
```
搜尋 WhatsApp 聯絡人 "[名稱]"
列出所有 WhatsApp 聯絡人
查找電話號碼 [852XXXXXXXX] 的聯絡人
```

### 2. 訊息查詢
```
顯示最近的 [數量] 條 WhatsApp 訊息
顯示今天的所有 WhatsApp 訊息
顯示本週的 WhatsApp 訊息
```

### 3. 對話查詢
```
列出我的 WhatsApp 聊天
顯示與 [聯絡人] 的對話
顯示群組 [群組名稱] 的對話
```

## 進階搜尋指令

### 1. 關鍵字搜尋
```
搜尋包含 "健康檢查" 的 WhatsApp 訊息
搜尋包含 "優惠" 或 "折扣" 的訊息
在 [聯絡人] 的對話中搜尋 "[關鍵字]"
```

### 2. 時間範圍搜尋
```
顯示昨天的所有訊息
顯示過去 7 天包含 "預約" 的訊息
顯示 12 月的所有客戶諮詢
```

### 3. 特定對話搜尋
```
顯示與 [電話號碼] 最近的 20 條訊息
顯示群組 [群組名稱] 今天的所有訊息
```

## 發送訊息指令

### 1. 文字訊息
```
發送 WhatsApp 訊息給 [電話號碼]: "[訊息內容]"
發送訊息到群組 [群組名稱]: "[訊息內容]"
```

### 2. 檔案發送
```
發送圖片 [檔案路徑] 給 [電話號碼]
發送文件 [檔案路徑] 到群組 [群組名稱]
```

### 3. 媒體處理
```
下載訊息 ID [message_id] 的媒體檔案
下載與 [聯絡人] 對話中的所有圖片
```

## 資料分析指令

### 1. 統計分析
```
統計本週收到的訊息數量
分析最常聯絡的前 10 個聯絡人
統計包含 "套餐" 關鍵字的訊息數量
```

### 2. 客戶分析
```
列出所有詢問過 "優越計劃" 的客戶
找出本月新增的聯絡人
顯示最近 30 天沒有互動的客戶
```

## 實用組合指令

### 每日晨會準備
```
1. 顯示昨天的所有客戶諮詢
2. 列出今天需要跟進的客戶
3. 統計昨天的諮詢類型分布
```

### 週報生成
```
1. 統計本週總諮詢量
2. 列出本週新客戶
3. 分析本週最受歡迎的套餐
4. 導出本週的成功案例
```

### 客戶追蹤
```
1. 顯示 [客戶名] 的完整對話歷史
2. 搜尋該客戶提及的所有產品
3. 分析該客戶的諮詢模式
```

## 快速回覆模板

### 套餐諮詢回覆
```
搜尋包含 "精選計劃" 的標準回覆
複製全面計劃的詳細介紹
獲取最新優惠資訊
```

### 預約相關
```
查詢可預約時間模板
發送預約確認訊息
查詢取消預約流程
```

## 注意事項

1. **電話格式**: 使用完整國際格式 (852XXXXXXXX)
2. **群組名稱**: 使用完整群組名稱
3. **時間格式**: 可用 "今天"、"昨天"、"本週" 等
4. **檔案路徑**: 使用絕對路徑

## 故障排除

如果指令無效：
1. 檢查 WhatsApp Bridge 是否運行中
2. 確認 Claude Desktop 已重啟
3. 驗證電話號碼或群組名稱正確
4. 確保有相關權限