"""
Code quality tests to detect fake implementations, hard-coding, and hallucinations
"""

import sys
import os
import re
import ast
from pathlib import Path

# Add parent directory to path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

class CodeQualityChecker:
    """Check code for quality issues"""
    
    def __init__(self):
        self.issues = []
        self.checked_files = 0
        
        # Patterns to detect issues
        self.hardcoded_patterns = [
            (r'["\']https?://[^/]+\.com', 'Hard-coded external URL'),
            (r'["\']\/tmp\/[^"\']+', 'Hard-coded temp path'),
            (r'["\']\/home\/[^"\']+', 'Hard-coded home path'),
            (r'["\']C:\\\\[^"\']+', 'Hard-coded Windows path'),
            (r'api_key\s*=\s*["\'][^"\']+', 'Hard-coded API key'),
            (r'password\s*=\s*["\'][^"\']+', 'Hard-coded password'),
            (r'["\']192\.168\.\d+\.\d+', 'Hard-coded IP address'),
            (r'port\s*=\s*\d{4,5}(?!\))', 'Hard-coded port number'),
        ]
        
        self.mock_patterns = [
            (r'def\s+mock_', 'Mock function definition'),
            (r'return\s+["\']?MOCK', 'Mock return value'),
            (r'#\s*TODO:\s*implement', 'Unimplemented TODO'),
            (r'raise\s+NotImplementedError', 'Not implemented error'),
            (r'pass\s*#\s*stub', 'Stub implementation'),
            (r'return\s+\[\s*\].*#.*fake', 'Fake return value'),
            (r'sleep\(\d+\).*#.*simulate', 'Simulated delay'),
        ]
        
        self.hallucination_patterns = [
            (r'["\']Lorem ipsum', 'Lorem ipsum placeholder'),
            (r'["\']Example Company', 'Example placeholder'),
            (r'["\']test@example\.com', 'Example email'),
            (r'["\']John Doe', 'Placeholder name'),
            (r'#\s*This is just an example', 'Example comment'),
            (r'placeholder', 'Placeholder text', re.IGNORECASE),
        ]
    
    def check_file(self, filepath: Path):
        """Check a single Python file"""
        if not filepath.suffix == '.py':
            return
        
        self.checked_files += 1
        
        try:
            with open(filepath, 'r', encoding='utf-8') as f:
                content = f.read()
                lines = content.split('\n')
            
            # Check for hard-coded values
            for pattern, description in self.hardcoded_patterns:
                matches = re.finditer(pattern, content, re.IGNORECASE)
                for match in matches:
                    line_no = content[:match.start()].count('\n') + 1
                    # Skip if in config file
                    if 'config' not in str(filepath).lower():
                        self.issues.append({
                            'file': str(filepath),
                            'line': line_no,
                            'type': 'hard-coding',
                            'description': description,
                            'code': lines[line_no-1].strip()
                        })
            
            # Check for mock implementations
            for pattern, description in self.mock_patterns:
                matches = re.finditer(pattern, content, re.IGNORECASE)
                for match in matches:
                    line_no = content[:match.start()].count('\n') + 1
                    # Skip test files
                    if 'test' not in str(filepath).lower():
                        self.issues.append({
                            'file': str(filepath),
                            'line': line_no,
                            'type': 'mock/fake',
                            'description': description,
                            'code': lines[line_no-1].strip()
                        })
            
            # Check for hallucinations
            for pattern, description, *flags in self.hallucination_patterns:
                flag = flags[0] if flags else 0
                matches = re.finditer(pattern, content, flag)
                for match in matches:
                    line_no = content[:match.start()].count('\n') + 1
                    self.issues.append({
                        'file': str(filepath),
                        'line': line_no,
                        'type': 'hallucination',
                        'description': description,
                        'code': lines[line_no-1].strip()
                    })
            
            # Check for proper error handling
            self.check_error_handling(filepath, content, lines)
            
            # Check for configuration usage
            self.check_configuration_usage(filepath, content, lines)
            
        except Exception as e:
            print(f"Error checking {filepath}: {e}")
    
    def check_error_handling(self, filepath: Path, content: str, lines: list):
        """Check for proper error handling"""
        # Look for bare except statements
        bare_except_pattern = r'except\s*:'
        matches = re.finditer(bare_except_pattern, content)
        for match in matches:
            line_no = content[:match.start()].count('\n') + 1
            self.issues.append({
                'file': str(filepath),
                'line': line_no,
                'type': 'error-handling',
                'description': 'Bare except clause (should specify exception type)',
                'code': lines[line_no-1].strip()
            })
    
    def check_configuration_usage(self, filepath: Path, content: str, lines: list):
        """Check for proper configuration usage"""
        # Skip config files themselves
        if 'config' in str(filepath).lower():
            return
        
        # Check if file uses configuration
        uses_config = 'get_config' in content or 'self.config' in content
        
        # Look for potential configuration values
        if not uses_config:
            # Check for values that should be configurable
            config_patterns = [
                (r'timeout\s*=\s*\d+', 'Timeout value should be configurable'),
                (r'batch_size\s*=\s*\d+', 'Batch size should be configurable'),
                (r'max_\w+\s*=\s*\d+', 'Max value should be configurable'),
                (r'threshold\s*=\s*[\d.]+', 'Threshold should be configurable'),
            ]
            
            for pattern, description in config_patterns:
                matches = re.finditer(pattern, content, re.IGNORECASE)
                for match in matches:
                    line_no = content[:match.start()].count('\n') + 1
                    # Only flag if not in __init__ with self.config
                    if 'def __init__' not in lines[max(0, line_no-5):line_no]:
                        self.issues.append({
                            'file': str(filepath),
                            'line': line_no,
                            'type': 'configuration',
                            'description': description,
                            'code': lines[line_no-1].strip()
                        })
    
    def check_directory(self, directory: Path):
        """Check all Python files in directory"""
        for filepath in directory.rglob('*.py'):
            # Skip test files for some checks
            if 'test' not in str(filepath) and '__pycache__' not in str(filepath):
                self.check_file(filepath)
    
    def print_report(self):
        """Print quality check report"""
        print("\n" + "="*70)
        print("CODE QUALITY CHECK REPORT")
        print("="*70)
        print(f"Files checked: {self.checked_files}")
        print(f"Issues found: {len(self.issues)}")
        
        if not self.issues:
            print("\n✓ No quality issues found!")
        else:
            # Group by type
            by_type = {}
            for issue in self.issues:
                issue_type = issue['type']
                if issue_type not in by_type:
                    by_type[issue_type] = []
                by_type[issue_type].append(issue)
            
            # Print by type
            for issue_type, issues in by_type.items():
                print(f"\n{issue_type.upper()} ({len(issues)} issues):")
                print("-" * 60)
                
                for issue in issues[:5]:  # Show first 5 of each type
                    print(f"File: {issue['file']}:{issue['line']}")
                    print(f"Issue: {issue['description']}")
                    print(f"Code: {issue['code']}")
                    print()
                
                if len(issues) > 5:
                    print(f"... and {len(issues) - 5} more {issue_type} issues")
        
        print("="*70)
        
        # Summary recommendations
        if self.issues:
            print("\nRECOMMENDATIONS:")
            if any(i['type'] == 'hard-coding' for i in self.issues):
                print("- Move hard-coded values to configuration files")
            if any(i['type'] == 'mock/fake' for i in self.issues):
                print("- Replace mock implementations with real functionality")
            if any(i['type'] == 'hallucination' for i in self.issues):
                print("- Remove placeholder text and example data")
            if any(i['type'] == 'error-handling' for i in self.issues):
                print("- Improve error handling with specific exception types")
            if any(i['type'] == 'configuration' for i in self.issues):
                print("- Use configuration system for configurable values")

def main():
    """Run code quality checks"""
    checker = CodeQualityChecker()
    
    # Check source directory
    src_dir = Path(__file__).parent.parent / 'src'
    if src_dir.exists():
        print(f"Checking source directory: {src_dir}")
        checker.check_directory(src_dir)
    
    # Check whatsapp-mcp-server directory
    server_dir = Path(__file__).parent.parent / 'whatsapp-mcp-server'
    if server_dir.exists():
        print(f"Checking server directory: {server_dir}")
        checker.check_directory(server_dir)
    
    # Print report
    checker.print_report()

if __name__ == "__main__":
    main()