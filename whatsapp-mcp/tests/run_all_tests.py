#!/usr/bin/env python3
"""
Main test runner for WhatsApp MCP system
Executes all test suites and generates comprehensive report
"""

import sys
import os
import subprocess
import asyncio
from datetime import datetime
from pathlib import Path

# Add parent directory to path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

class TestRunner:
    """Coordinate and run all tests"""
    
    def __init__(self):
        self.test_dir = Path(__file__).parent
        self.results = {}
        self.start_time = None
        self.end_time = None
    
    def print_header(self):
        """Print test header"""
        print("\n" + "="*80)
        print("WhatsApp MCP System - Comprehensive Test Suite")
        print("="*80)
        print(f"Test Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"Test Directory: {self.test_dir}")
        print("="*80)
    
    async def run_test_suite(self, test_name, test_file):
        """Run a single test suite"""
        print(f"\n🔍 Running {test_name}...")
        print("-" * 60)
        
        try:
            # Run the test file
            if test_file == 'test_code_quality.py':
                # Run synchronous test
                result = subprocess.run(
                    [sys.executable, str(self.test_dir / test_file)],
                    capture_output=True,
                    text=True
                )
                
                print(result.stdout)
                if result.stderr:
                    print("STDERR:", result.stderr)
                
                success = result.returncode == 0
                self.results[test_name] = {
                    'status': 'PASSED' if success else 'FAILED',
                    'output': result.stdout,
                    'errors': result.stderr
                }
                
            else:
                # Import and run async tests
                module_name = test_file.replace('.py', '')
                test_module = __import__(module_name)
                
                if hasattr(test_module, 'main'):
                    await test_module.main()
                    self.results[test_name] = {'status': 'PASSED', 'output': 'Test completed'}
                else:
                    self.results[test_name] = {'status': 'FAILED', 'output': 'No main function found'}
                    
        except Exception as e:
            print(f"❌ Error running {test_name}: {e}")
            self.results[test_name] = {
                'status': 'FAILED',
                'output': '',
                'errors': str(e)
            }
    
    async def run_all_tests(self):
        """Run all test suites"""
        self.print_header()
        self.start_time = datetime.now()
        
        # Define test suites
        test_suites = [
            ("Code Quality Check", "test_code_quality.py"),
            ("Feature Tests", "test_all_features.py"),
            ("Integration Tests", "test_integration.py"),
        ]
        
        # Run each test suite
        for test_name, test_file in test_suites:
            if (self.test_dir / test_file).exists():
                await self.run_test_suite(test_name, test_file)
            else:
                print(f"⚠️  Test file not found: {test_file}")
                self.results[test_name] = {
                    'status': 'SKIPPED',
                    'output': 'Test file not found'
                }
        
        self.end_time = datetime.now()
        self.print_summary()
    
    def print_summary(self):
        """Print test summary report"""
        duration = (self.end_time - self.start_time).total_seconds()
        
        print("\n" + "="*80)
        print("TEST EXECUTION SUMMARY")
        print("="*80)
        print(f"Total Duration: {duration:.2f} seconds")
        print(f"Test Suites Run: {len(self.results)}")
        
        # Count results
        passed = sum(1 for r in self.results.values() if r['status'] == 'PASSED')
        failed = sum(1 for r in self.results.values() if r['status'] == 'FAILED')
        skipped = sum(1 for r in self.results.values() if r['status'] == 'SKIPPED')
        
        print(f"\nResults:")
        print(f"  ✅ Passed: {passed}")
        print(f"  ❌ Failed: {failed}")
        print(f"  ⏭️  Skipped: {skipped}")
        
        # Success rate
        if passed + failed > 0:
            success_rate = (passed / (passed + failed)) * 100
            print(f"\n📊 Success Rate: {success_rate:.1f}%")
        
        # Detailed results
        print("\nDetailed Results:")
        print("-" * 60)
        for test_name, result in self.results.items():
            status_icon = {
                'PASSED': '✅',
                'FAILED': '❌',
                'SKIPPED': '⏭️'
            }.get(result['status'], '❓')
            
            print(f"{status_icon} {test_name}: {result['status']}")
            if result['status'] == 'FAILED' and result.get('errors'):
                print(f"   Error: {result['errors'][:100]}...")
        
        print("="*80)
        
        # Final verdict
        if failed == 0 and passed > 0:
            print("\n🎉 All tests passed! The system is ready for use.")
            print("\nKey achievements:")
            print("✓ No hard-coded values found")
            print("✓ No mock implementations detected")
            print("✓ All features working correctly")
            print("✓ Integration workflows validated")
        else:
            print("\n⚠️  Some tests failed. Please review the output above.")
            print("\nRecommended actions:")
            if 'Code Quality Check' in self.results and self.results['Code Quality Check']['status'] == 'FAILED':
                print("- Fix code quality issues identified")
            if 'Feature Tests' in self.results and self.results['Feature Tests']['status'] == 'FAILED':
                print("- Debug failing features")
            if 'Integration Tests' in self.results and self.results['Integration Tests']['status'] == 'FAILED':
                print("- Check system integration points")
    
    def generate_report_file(self):
        """Generate a test report file"""
        report_path = self.test_dir / f"test_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
        
        with open(report_path, 'w', encoding='utf-8') as f:
            f.write("WhatsApp MCP System Test Report\n")
            f.write("="*80 + "\n")
            f.write(f"Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"Duration: {(self.end_time - self.start_time).total_seconds():.2f} seconds\n")
            f.write("="*80 + "\n\n")
            
            for test_name, result in self.results.items():
                f.write(f"\n{test_name}\n")
                f.write("-"*60 + "\n")
                f.write(f"Status: {result['status']}\n")
                if result.get('output'):
                    f.write("\nOutput:\n")
                    f.write(result['output'][:5000])  # Limit output
                    if len(result['output']) > 5000:
                        f.write("\n... (truncated)")
                f.write("\n\n")
        
        print(f"\n📄 Test report saved to: {report_path}")

async def main():
    """Main entry point"""
    runner = TestRunner()
    
    try:
        await runner.run_all_tests()
        runner.generate_report_file()
    except KeyboardInterrupt:
        print("\n\n⚠️  Test execution interrupted by user")
    except Exception as e:
        print(f"\n\n❌ Fatal error during test execution: {e}")

if __name__ == "__main__":
    # Make script executable
    script_path = Path(__file__)
    script_path.chmod(script_path.stat().st_mode | 0o111)
    
    # Run tests
    asyncio.run(main())