"""
Integration tests for complete WhatsApp MCP workflow
Simulates real customer interactions and verifies system behavior
"""

import sys
import os
import asyncio
import logging
from datetime import datetime
from pathlib import Path

# Add parent directory to path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.core import get_config, get_db_manager
from src.processors import MediaProcessor, OCRProcessor
from src.customer import (
    CustomerManager, InformationCollector,
    HealthTracker, CustomerAnalytics
)

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class IntegrationTester:
    """Test complete system workflows"""
    
    def __init__(self):
        self.config = get_config()
        self.db = get_db_manager()
        self.test_results = []
    
    async def test_new_customer_workflow(self):
        """Test complete workflow for new customer"""
        print("\n" + "="*50)
        print("TEST: New Customer Workflow")
        print("="*50)
        
        test_customer_id = f"test_{datetime.now().timestamp()}@s.whatsapp.net"
        
        try:
            # Step 1: Customer sends first message
            print("\n1. Customer sends first message...")
            customer_manager = CustomerManager()
            profile = customer_manager.get_or_create_profile(test_customer_id)
            assert profile['customer_status'] == 'new', "Should be new customer"
            print("   ✓ New customer profile created")
            
            # Step 2: Start information collection
            print("\n2. Starting information collection...")
            collector = InformationCollector()
            greeting = collector.start_collection(test_customer_id, 'zh')
            assert len(greeting) > 0, "Should return greeting"
            print(f"   Bot: {greeting}")
            
            # Step 3: Simulate customer responses
            print("\n3. Simulating customer responses...")
            test_responses = [
                ("陳大文", "Name provided"),
                ("先生", "Gender provided"),
                ("1985-06-15", "Birth date provided"),
                ("98765432", "Phone provided"),
                ("<EMAIL>", "Email provided"),
                ("中環", "District provided"),
                ("有高血壓，正在服藥", "Health info provided")
            ]
            
            for response, description in test_responses:
                print(f"   Customer: {response} ({description})")
                bot_response, complete = collector.process_response(
                    test_customer_id, response, 'zh'
                )
                if bot_response:
                    print(f"   Bot: {bot_response}")
                customer_manager.record_interaction(test_customer_id)
            
            # Step 4: Verify profile completeness
            print("\n4. Verifying profile completeness...")
            updated_profile = customer_manager.get_or_create_profile(test_customer_id)
            completeness = updated_profile['profile_completeness']
            print(f"   Profile completeness: {completeness}%")
            assert completeness > 50, "Profile should be substantially complete"
            
            # Step 5: Check customer classification
            print("\n5. Checking customer classification...")
            status = customer_manager.classify_customer(test_customer_id)
            print(f"   Customer status: {status.value}")
            assert status.value in ['potential', 'regular'], "Should be classified"
            
            # Step 6: Add health record
            print("\n6. Adding health record...")
            health_tracker = HealthTracker()
            health_data = {
                'conditions': '高血壓',
                'chronic_diseases': ['高血壓'],
                'medications': ['降壓藥'],
                'allergies': []
            }
            success = health_tracker.update_health_info(
                updated_profile['id'], health_data
            )
            assert success, "Health info should be saved"
            print("   ✓ Health information recorded")
            
            # Step 7: Generate insights
            print("\n7. Generating customer insights...")
            insights = customer_manager.get_customer_insights(test_customer_id)
            print(f"   Total interactions: {insights['total_interactions']}")
            print(f"   Profile completeness: {insights['profile_completeness']}%")
            print(f"   Health records: {insights.get('health_records', 0)}")
            
            self.test_results.append(('New Customer Workflow', 'PASSED'))
            
        except Exception as e:
            logger.error(f"Test failed: {e}")
            self.test_results.append(('New Customer Workflow', f'FAILED: {e}'))
    
    async def test_media_processing_workflow(self):
        """Test media processing workflow"""
        print("\n" + "="*50)
        print("TEST: Media Processing Workflow")
        print("="*50)
        
        try:
            # Step 1: Check media processor
            print("\n1. Initializing media processor...")
            media_processor = MediaProcessor()
            stats = media_processor.get_statistics()
            print(f"   Total media messages: {stats['total_media_messages']}")
            print(f"   Processed: {stats['processed_messages']}")
            print(f"   Storage path: {stats['storage_path']}")
            
            # Step 2: Scan for unprocessed media
            print("\n2. Scanning for unprocessed media...")
            unprocessed = await media_processor.scan_unprocessed_media()
            print(f"   Found {len(unprocessed)} unprocessed media files")
            
            # Step 3: Check OCR availability
            print("\n3. Checking OCR processor...")
            ocr_processor = OCRProcessor()
            ocr_available = ocr_processor.is_available()
            print(f"   OCR available: {ocr_available}")
            
            if ocr_available:
                # Step 4: Test medical content analysis
                print("\n4. Testing medical content analysis...")
                test_text = """
                健康檢查報告
                姓名: 陳大文
                日期: 2024-01-15
                
                血液檢查結果:
                血糖: 6.8 mmol/L ↑
                血壓: 145/92 mmHg ↑
                膽固醇: 5.5 mmol/L
                
                建議: 注意飲食，定期覆查
                """
                
                analysis = ocr_processor.analyze_medical_content(test_text)
                print(f"   Report type: {analysis['report_type']}")
                print(f"   Abnormal values found: {len(analysis['abnormal_values'])}")
                print(f"   Date found: {analysis['date_found']}")
                
                assert analysis['report_type'] != 'unknown', "Should detect report type"
            
            self.test_results.append(('Media Processing Workflow', 'PASSED'))
            
        except Exception as e:
            logger.error(f"Test failed: {e}")
            self.test_results.append(('Media Processing Workflow', f'FAILED: {e}'))
    
    async def test_analytics_workflow(self):
        """Test analytics and reporting workflow"""
        print("\n" + "="*50)
        print("TEST: Analytics Workflow")
        print("="*50)
        
        try:
            analytics = CustomerAnalytics()
            
            # Step 1: Conversation analytics
            print("\n1. Generating conversation analytics...")
            conv_analytics = analytics.get_conversation_analytics(30)
            print(f"   Unique customers: {conv_analytics['unique_customers']}")
            print(f"   Total conversations: {conv_analytics['total_conversations']}")
            print(f"   Average response time: {conv_analytics['response_time_avg']:.1f} minutes")
            
            # Step 2: Customer segmentation
            print("\n2. Analyzing customer segments...")
            segments = analytics.get_customer_segmentation()
            print(f"   Customer segments:")
            for status, count in segments['by_status'].items():
                print(f"     - {status}: {count}")
            
            # Step 3: Business opportunities
            print("\n3. Identifying business opportunities...")
            opportunities = analytics.get_business_opportunities()
            print(f"   Found {len(opportunities)} opportunities:")
            for opp in opportunities[:3]:
                print(f"     - {opp['type']}: {opp['recommendation']}")
            
            # Step 4: Executive summary
            print("\n4. Generating executive summary...")
            summary = analytics.generate_insights_summary()
            print(f"   {summary}")
            
            assert len(summary) > 0, "Should generate summary"
            
            self.test_results.append(('Analytics Workflow', 'PASSED'))
            
        except Exception as e:
            logger.error(f"Test failed: {e}")
            self.test_results.append(('Analytics Workflow', f'FAILED: {e}'))
    
    async def test_configuration_validation(self):
        """Validate system configuration"""
        print("\n" + "="*50)
        print("TEST: Configuration Validation")
        print("="*50)
        
        try:
            config = get_config()
            
            # Check critical configurations
            print("\n1. Validating critical configurations...")
            
            checks = [
                ('API URL configured', bool(config.whatsapp_bridge.api_url)),
                ('Database path valid', config.database.full_path.parent.exists()),
                ('Storage path valid', config.storage.full_path.exists() or True),
                ('OCR languages configured', len(config.ocr.languages) > 0),
                ('Customer thresholds set', config.customer.vip_spending_threshold > 0),
            ]
            
            all_passed = True
            for check_name, result in checks:
                status = "✓" if result else "✗"
                print(f"   {status} {check_name}")
                if not result:
                    all_passed = False
            
            # Check feature flags
            print("\n2. Checking feature flags...")
            features = [
                'auto_download_media',
                'ocr_processing',
                'auto_reminders'
            ]
            
            for feature in features:
                enabled = config.is_feature_enabled(feature)
                print(f"   - {feature}: {'enabled' if enabled else 'disabled'}")
            
            assert all_passed, "Some configurations are invalid"
            
            self.test_results.append(('Configuration Validation', 'PASSED'))
            
        except Exception as e:
            logger.error(f"Test failed: {e}")
            self.test_results.append(('Configuration Validation', f'FAILED: {e}'))
    
    def print_summary(self):
        """Print test summary"""
        print("\n" + "="*70)
        print("INTEGRATION TEST SUMMARY")
        print("="*70)
        
        passed = sum(1 for _, status in self.test_results if status == 'PASSED')
        total = len(self.test_results)
        
        print(f"Total Tests: {total}")
        print(f"Passed: {passed}")
        print(f"Failed: {total - passed}")
        print(f"Success Rate: {passed/total*100:.1f}%")
        
        print("\nTest Results:")
        for test_name, status in self.test_results:
            if status == 'PASSED':
                print(f"  ✓ {test_name}")
            else:
                print(f"  ✗ {test_name}: {status}")
        
        print("="*70)
        
        if passed == total:
            print("\n🎉 All integration tests passed!")
        else:
            print("\n⚠️  Some tests failed. Please check the logs above.")

async def main():
    """Run all integration tests"""
    tester = IntegrationTester()
    
    # Run all test workflows
    await tester.test_configuration_validation()
    await tester.test_new_customer_workflow()
    await tester.test_media_processing_workflow()
    await tester.test_analytics_workflow()
    
    # Print summary
    tester.print_summary()

if __name__ == "__main__":
    asyncio.run(main())