# WhatsApp MCP 測試指南

## 前置步驟確認

### 1. WhatsApp Bridge 狀態
✅ 數據庫文件已創建:
- `/whatsapp-bridge/store/messages.db`
- `/whatsapp-bridge/store/whatsapp.db`

### 2. Claude Desktop 配置
✅ 配置文件已正確設置在:
`~/Library/Application Support/Claude/claude_desktop_config.json`

## 測試步驟

### 步驟 1: 重啟 Claude Desktop
1. 完全退出 Claude Desktop (Cmd+Q)
2. 重新開啟 Claude Desktop
3. 在 Claude 介面中應該會看到 WhatsApp 圖示

### 步驟 2: 基礎功能測試

在 Claude Desktop 中輸入以下測試指令：

#### A. 搜尋聯絡人
```
使用 WhatsApp 搜尋聯絡人 "測試"
```

#### B. 查看最近訊息
```
顯示我最近的 10 條 WhatsApp 訊息
```

#### C. 查看聊天列表
```
列出我的 WhatsApp 聊天室
```

#### D. 發送測試訊息（發給自己）
```
發送 WhatsApp 訊息給自己: "MCP 測試訊息"
```

### 步驟 3: 進階功能測試

#### A. 搜尋特定訊息
```
在 WhatsApp 中搜尋包含 "測試" 的訊息
```

#### B. 查看特定聊天
```
顯示與 [聯絡人名稱] 的 WhatsApp 對話
```

#### C. 發送檔案（如果有圖片）
```
發送圖片 [檔案路徑] 到 WhatsApp [電話號碼]
```

## 故障排除

### 問題 1: Claude Desktop 沒有顯示 WhatsApp
**解決方案**:
1. 確認配置文件路徑正確
2. 確認 UV 已安裝: `which uv`
3. 重新啟動 Claude Desktop

### 問題 2: WhatsApp Bridge 連接失敗
**解決方案**:
1. 回到運行 bridge 的終端機
2. 檢查是否有錯誤訊息
3. 如需重新認證，刪除 store 資料夾並重新運行:
   ```bash
   cd /Users/<USER>/Claude/whatsapp-mcp/whatsapp-bridge
   rm -rf store
   go run main.go
   ```

### 問題 3: MCP Server 錯誤
**解決方案**:
1. 檢查 Python 環境: `python3 --version`
2. 確認 UV 可執行: `uv --version`
3. 手動測試 MCP server:
   ```bash
   cd /Users/<USER>/Claude/whatsapp-mcp/whatsapp-mcp-server
   uv run main.py
   ```

## 預期結果

成功設置後，您應該能夠：
- ✅ 在 Claude 中看到 WhatsApp 整合
- ✅ 搜尋和查看 WhatsApp 聯絡人
- ✅ 讀取歷史訊息
- ✅ 發送文字訊息
- ✅ 發送媒體檔案
- ✅ 下載接收的媒體

## 注意事項

1. WhatsApp Bridge 必須保持運行
2. 約 20 天後可能需要重新掃描 QR code
3. 首次同步可能需要幾分鐘時間
4. 所有訊息都儲存在本地，只在需要時才發送給 Claude