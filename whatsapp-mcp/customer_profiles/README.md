# WhatsApp MCP 客戶檔案管理系統

這是一個完整的客戶檔案管理系統，與 WhatsApp MCP 整合，能夠自動學習客戶行為並提供個性化服務建議。

## 系統架構

### 1. 客戶檔案管理器 (CustomerProfileManager)
- 管理客戶基本信息、分類、標籤
- 記錄購買歷史和交互歷史
- 自動分類客戶（VIP、Regular、Potential、Inactive）
- 提供客戶搜索和洞察分析

### 2. 學習引擎 (CustomerLearningEngine)
- 分析對話內容，識別情感、意圖、緊急度
- 從歷史對話中學習客戶行為模式
- 識別客戶需求和產品興趣
- 生成個性化溝通建議

### 3. 整合層 (WhatsAppProfileIntegration)
- 連接 WhatsApp MCP 和客戶檔案系統
- 實時處理新消息並更新客戶檔案
- 提供完整的客戶上下文
- 批量處理和數據導出功能

## 安裝和使用

### 前置要求
1. WhatsApp Bridge 必須正在運行
2. Python 3.8+ 環境
3. SQLite 支持

### 安裝步驟

```bash
# 1. 進入目錄
cd /Users/<USER>/Desktop/Claude/whatsapp-mcp/customer_profiles

# 2. 安裝依賴（如果需要）
pip install sqlite3 logging

# 3. 初始化系統
python profile_integration.py
```

### 使用方式

#### 1. 在您的應用中集成

```python
from profile_integration import WhatsAppProfileIntegration

# 初始化
integration = WhatsAppProfileIntegration()

# 處理新消息
await integration.process_new_message({
    'chat_id': 'customer_whatsapp_id',
    'sender_name': 'Customer Name',
    'messages': [...],
    'conversation_ended': True
})

# 獲取客戶上下文
context = integration.get_customer_context('customer_whatsapp_id')
```

#### 2. 使用 API 端點（如果集成到 FastAPI）

```python
from fastapi import FastAPI
from profile_integration import WhatsAppProfileIntegration

app = FastAPI()
integration = WhatsAppProfileIntegration()

@app.post("/customer/profile/{whatsapp_id}")
async def get_customer_profile(whatsapp_id: str):
    return integration.get_customer_context(whatsapp_id)

@app.post("/message/process")
async def process_message(message_data: dict):
    return await integration.process_new_message(message_data)
```

## 功能特點

### 客戶分類邏輯
- **VIP**: 總購買金額超過 10,000
- **Regular**: 有購買記錄的客戶
- **Potential**: 30天內互動超過5次
- **Inactive**: 其他情況

### 自動學習功能
1. **對話分析**
   - 情感分析（正面、負面、中性）
   - 意圖識別（購買、詢問、支持、反饋）
   - 緊急度評估（高、中、低）

2. **行為模式學習**
   - 活躍時間段
   - 溝通風格（正式、休閒、中性）
   - 回應速度偏好
   - 產品興趣

3. **個性化建議**
   - 問候語風格
   - 消息長度建議
   - 最佳聯繫時間
   - 產品推薦

### 數據存儲結構

#### 主要數據表
1. **customer_profiles** - 客戶基本信息
2. **customer_tags** - 客戶標籤
3. **interaction_history** - 交互歷史
4. **purchase_history** - 購買記錄
5. **customer_preferences** - 客戶偏好
6. **automation_settings** - 自動化設置

## 擴展和自定義

### 添加新的分析維度

```python
# 在 learning_engine.py 中添加新的模式
self.patterns['new_dimension'] = {
    'pattern1': ['keyword1', 'keyword2'],
    'pattern2': ['keyword3', 'keyword4']
}
```

### 自定義客戶分類規則

```python
# 修改 customer_manager.py 中的 classify_customer 方法
def classify_customer(self, whatsapp_id: str) -> str:
    # 添加您的自定義邏輯
    pass
```

### 集成外部系統

```python
# 在 profile_integration.py 中添加
async def sync_with_crm(self, customer_data):
    # 與您的 CRM 系統同步
    pass
```

## 最佳實踐

1. **隱私保護**
   - 確保客戶數據加密存儲
   - 定期清理過期數據
   - 遵守 GDPR 等隱私法規

2. **性能優化**
   - 使用索引優化查詢
   - 批量處理大量數據
   - 實施緩存機制

3. **數據質量**
   - 定期驗證數據完整性
   - 實施數據清洗流程
   - 建立數據備份機制

## 監控和維護

### 日誌記錄
系統會自動記錄所有重要操作：
- 客戶檔案創建/更新
- 分類變更
- 學習結果
- 錯誤和異常

### 定期任務
建議設置以下定期任務：
1. 每日：批量分類客戶
2. 每週：生成客戶洞察報告
3. 每月：清理無效數據

## 故障排除

### 常見問題

1. **數據庫連接錯誤**
   - 檢查數據庫文件路徑
   - 確保有寫入權限

2. **分析結果不準確**
   - 增加訓練數據量
   - 調整關鍵詞模式
   - 檢查時區設置

3. **性能問題**
   - 優化數據庫查詢
   - 實施分頁處理
   - 使用異步處理

## 聯繫支持

如有問題或需要幫助，請聯繫技術支持團隊。