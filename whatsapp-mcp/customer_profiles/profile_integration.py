import json
import os
from typing import Dict, Any, List, Optional
from datetime import datetime
import logging
import asyncio
from pathlib import Path

from customer_manager import CustomerProfileManager
from learning_engine import CustomerLearningEngine

class WhatsAppProfileIntegration:
    """整合 WhatsApp MCP 與客戶檔案系統"""
    
    def __init__(self):
        self.customer_manager = CustomerProfileManager()
        self.learning_engine = CustomerLearningEngine()
        self.learning_engine.customer_manager = self.customer_manager
        self.logger = logging.getLogger(__name__)
        
        # 設置日誌
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
    
    async def process_new_message(self, message_data: Dict[str, Any]) -> Dict[str, Any]:
        """處理新消息並更新客戶檔案"""
        whatsapp_id = message_data.get('chat_id')
        
        if not whatsapp_id:
            return {'status': 'error', 'message': 'No chat_id provided'}
        
        # 獲取或創建客戶檔案
        profile = self.customer_manager.get_profile(whatsapp_id)
        
        if not profile:
            # 創建新客戶檔案
            self.customer_manager.create_or_update_profile(whatsapp_id, {
                'display_name': message_data.get('sender_name', 'Unknown'),
                'phone_number': whatsapp_id.split('@')[0] if '@' in whatsapp_id else whatsapp_id,
                'first_contact_date': datetime.now()
            })
            profile = self.customer_manager.get_profile(whatsapp_id)
        
        # 更新最後聯繫時間
        self.customer_manager.create_or_update_profile(whatsapp_id, {
            'last_contact_date': datetime.now()
        })
        
        # 分析消息（如果是對話結束）
        if message_data.get('conversation_ended'):
            await self._analyze_completed_conversation(whatsapp_id, message_data.get('messages', []))
        
        return {
            'status': 'success',
            'profile': profile,
            'customer_type': profile.get('customer_type', 'Potential')
        }
    
    async def _analyze_completed_conversation(self, whatsapp_id: str, messages: List[Dict[str, Any]]):
        """分析完成的對話"""
        # 分析對話
        analysis = self.learning_engine.analyze_conversation(whatsapp_id, messages)
        
        # 記錄交互
        interaction_data = {
            'conversation_id': f"conv_{datetime.now().timestamp()}",
            'message_count': len(messages),
            'duration_seconds': self._calculate_duration(messages),
            'sentiment': analysis['sentiment'],
            'topics': json.dumps(analysis['topics']),
            'outcome': analysis['intent']
        }
        
        self.customer_manager.add_interaction(whatsapp_id, interaction_data)
        
        # 更新客戶分類
        new_type = self.customer_manager.classify_customer(whatsapp_id)
        
        # 從歷史學習
        learning_result = self.learning_engine.learn_from_history(whatsapp_id)
        
        # 生成個性化方法
        personalized_approach = self.learning_engine.generate_personalized_approach(
            whatsapp_id, learning_result
        )
        
        # 保存學習結果
        self._save_learning_result(whatsapp_id, {
            'analysis': analysis,
            'learning': learning_result,
            'approach': personalized_approach,
            'customer_type': new_type
        })
    
    def _calculate_duration(self, messages: List[Dict[str, Any]]) -> int:
        """計算對話持續時間（秒）"""
        if len(messages) < 2:
            return 0
        
        first_time = datetime.fromisoformat(messages[0]['timestamp'])
        last_time = datetime.fromisoformat(messages[-1]['timestamp'])
        
        return int((last_time - first_time).total_seconds())
    
    def _save_learning_result(self, whatsapp_id: str, result: Dict[str, Any]):
        """保存學習結果"""
        learning_dir = Path("learning_results")
        learning_dir.mkdir(exist_ok=True)
        
        filename = learning_dir / f"{whatsapp_id.replace('@', '_')}_learning.json"
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(result, f, ensure_ascii=False, indent=2)
    
    def get_customer_context(self, whatsapp_id: str) -> Dict[str, Any]:
        """獲取客戶完整上下文"""
        profile = self.customer_manager.get_profile(whatsapp_id)
        
        if not profile:
            return {'status': 'not_found'}
        
        # 獲取洞察
        insights = self.customer_manager.get_customer_insights(whatsapp_id)
        
        # 獲取最新學習結果
        learning_file = Path(f"learning_results/{whatsapp_id.replace('@', '_')}_learning.json")
        learning_result = {}
        if learning_file.exists():
            with open(learning_file, 'r', encoding='utf-8') as f:
                learning_result = json.load(f)
        
        context = {
            'profile': profile,
            'insights': insights,
            'learning': learning_result,
            'recommendations': self._generate_recommendations(profile, insights, learning_result)
        }
        
        return context
    
    def _generate_recommendations(self, profile: Dict[str, Any], 
                                 insights: Dict[str, Any], 
                                 learning: Dict[str, Any]) -> List[str]:
        """生成行動建議"""
        recommendations = []
        
        # 基於客戶類型
        customer_type = profile.get('customer_type', 'Potential')
        if customer_type == 'VIP':
            recommendations.append("安排專屬客服經理")
            recommendations.append("提供優先支援服務")
        elif customer_type == 'Potential':
            recommendations.append("積極跟進，轉化為付費客戶")
        
        # 基於購買分析
        purchase_analytics = insights.get('purchase_analytics', {})
        if purchase_analytics.get('total_revenue', 0) > 10000:
            recommendations.append("提供忠誠度獎勵計劃")
        
        # 基於學習結果
        approach = learning.get('approach', {})
        if approach.get('response_timing', {}).get('preference') == 'immediate':
            recommendations.append("設置快速響應機制")
        
        # 基於產品興趣
        product_interests = learning.get('learning', {}).get('product_interests', [])
        if product_interests:
            recommendations.append(f"推薦相關產品：{', '.join(product_interests)}")
        
        return recommendations
    
    def batch_classify_customers(self) -> Dict[str, int]:
        """批量分類所有客戶"""
        results = {'VIP': 0, 'Regular': 0, 'Potential': 0, 'Inactive': 0}
        
        # 獲取所有客戶
        all_customers = self.customer_manager.search_customers({})
        
        for customer in all_customers:
            whatsapp_id = customer['whatsapp_id']
            customer_type = self.customer_manager.classify_customer(whatsapp_id)
            results[customer_type] += 1
            
            self.logger.info(f"Classified {whatsapp_id} as {customer_type}")
        
        return results
    
    def export_customer_data(self, output_file: str = "customer_export.json"):
        """導出所有客戶數據"""
        all_customers = self.customer_manager.search_customers({})
        export_data = []
        
        for customer in all_customers:
            whatsapp_id = customer['whatsapp_id']
            context = self.get_customer_context(whatsapp_id)
            
            if context.get('status') != 'not_found':
                export_data.append({
                    'whatsapp_id': whatsapp_id,
                    'profile': context['profile'],
                    'insights': context['insights'],
                    'recommendations': context['recommendations']
                })
        
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(export_data, f, ensure_ascii=False, indent=2)
        
        self.logger.info(f"Exported {len(export_data)} customer profiles to {output_file}")
        return len(export_data)
    
    async def sync_with_whatsapp(self):
        """與 WhatsApp 數據同步"""
        # 這裡可以實現定期同步邏輯
        # 例如：從 WhatsApp Bridge 獲取新消息並處理
        self.logger.info("Starting sync with WhatsApp...")
        
        # 模擬同步過程
        # 實際實現時，這裡會調用 WhatsApp Bridge API
        
        self.logger.info("Sync completed")


# 使用示例
async def main():
    """示例：如何使用整合系統"""
    integration = WhatsAppProfileIntegration()
    
    # 處理新消息
    message_data = {
        'chat_id': '<EMAIL>',
        'sender_name': 'John Doe',
        'messages': [
            {
                'text': '你好，我想了解你們的產品',
                'from_me': False,
                'timestamp': datetime.now().isoformat()
            },
            {
                'text': '您好！很高興為您介紹我們的產品。',
                'from_me': True,
                'timestamp': datetime.now().isoformat()
            }
        ],
        'conversation_ended': True
    }
    
    result = await integration.process_new_message(message_data)
    print(f"Message processed: {result}")
    
    # 獲取客戶上下文
    context = integration.get_customer_context('<EMAIL>')
    print(f"Customer context: {json.dumps(context, indent=2, ensure_ascii=False)}")
    
    # 批量分類
    classification_results = integration.batch_classify_customers()
    print(f"Classification results: {classification_results}")
    
    # 導出數據
    export_count = integration.export_customer_data()
    print(f"Exported {export_count} customer profiles")


if __name__ == "__main__":
    asyncio.run(main())