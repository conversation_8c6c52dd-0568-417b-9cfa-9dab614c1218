import json
import sqlite3
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
import re
from collections import Counter
import logging

class CustomerLearningEngine:
    """從客戶對話中學習並提取洞察的引擎"""
    
    def __init__(self, db_path: str = "../whatsapp-bridge/store/messages.db"):
        self.db_path = db_path
        self.logger = logging.getLogger(__name__)
        self.customer_manager = None  # Will be injected
        
        # 關鍵詞模式定義
        self.patterns = {
            'product_interest': {
                '價格': ['價格', '多少錢', '費用', '收費', '報價', 'price', 'cost'],
                '功能': ['功能', '特點', '特色', '能做什麼', 'feature', 'function'],
                '比較': ['比較', '對比', '差異', '區別', 'vs', 'compare'],
                '試用': ['試用', '體驗', '演示', 'demo', 'trial', 'try']
            },
            'sentiment': {
                'positive': ['好', '滿意', '不錯', '喜歡', '感謝', 'good', 'great', 'excellent'],
                'negative': ['差', '不滿', '問題', '失望', '投訴', 'bad', 'poor', 'problem'],
                'neutral': ['了解', '知道', '明白', '好的', 'ok', 'understand']
            },
            'urgency': {
                'high': ['急', '馬上', '立即', '今天', '現在', 'urgent', 'asap', 'now'],
                'medium': ['這週', '幾天內', '盡快', 'this week', 'soon'],
                'low': ['考慮', '再說', '以後', 'later', 'thinking', 'maybe']
            },
            'intent': {
                'purchase': ['購買', '訂購', '下單', '付款', 'buy', 'order', 'purchase'],
                'inquiry': ['詢問', '了解', '請問', '想知道', 'ask', 'question', 'what'],
                'support': ['幫助', '協助', '問題', '故障', 'help', 'support', 'issue'],
                'feedback': ['建議', '反饋', '評價', 'feedback', 'suggest', 'review']
            }
        }
    
    def analyze_conversation(self, whatsapp_id: str, messages: List[Dict[str, Any]]) -> Dict[str, Any]:
        """分析單次對話並提取洞察"""
        analysis = {
            'sentiment': self._analyze_sentiment(messages),
            'topics': self._extract_topics(messages),
            'intent': self._identify_intent(messages),
            'urgency': self._assess_urgency(messages),
            'key_points': self._extract_key_points(messages),
            'next_action': self._suggest_next_action(messages),
            'customer_needs': self._identify_needs(messages)
        }
        
        return analysis
    
    def _analyze_sentiment(self, messages: List[Dict[str, Any]]) -> str:
        """分析對話情感傾向"""
        sentiment_scores = {'positive': 0, 'negative': 0, 'neutral': 0}
        
        for msg in messages:
            if msg.get('from_me'):
                continue
                
            text = msg.get('text', '').lower()
            for sentiment, keywords in self.patterns['sentiment'].items():
                for keyword in keywords:
                    if keyword in text:
                        sentiment_scores[sentiment] += 1
        
        # 返回最高分的情感
        return max(sentiment_scores, key=sentiment_scores.get)
    
    def _extract_topics(self, messages: List[Dict[str, Any]]) -> List[str]:
        """提取對話主題"""
        topics = []
        
        for msg in messages:
            text = msg.get('text', '').lower()
            for topic, keywords in self.patterns['product_interest'].items():
                for keyword in keywords:
                    if keyword in text and topic not in topics:
                        topics.append(topic)
        
        return topics
    
    def _identify_intent(self, messages: List[Dict[str, Any]]) -> str:
        """識別客戶意圖"""
        intent_scores = Counter()
        
        for msg in messages:
            if msg.get('from_me'):
                continue
                
            text = msg.get('text', '').lower()
            for intent, keywords in self.patterns['intent'].items():
                for keyword in keywords:
                    if keyword in text:
                        intent_scores[intent] += 1
        
        if intent_scores:
            return intent_scores.most_common(1)[0][0]
        return 'inquiry'  # 默認為詢問
    
    def _assess_urgency(self, messages: List[Dict[str, Any]]) -> str:
        """評估客戶緊急程度"""
        for msg in reversed(messages):  # 從最新消息開始檢查
            if msg.get('from_me'):
                continue
                
            text = msg.get('text', '').lower()
            for urgency, keywords in self.patterns['urgency'].items():
                for keyword in keywords:
                    if keyword in text:
                        return urgency
        
        return 'medium'  # 默認中等緊急度
    
    def _extract_key_points(self, messages: List[Dict[str, Any]]) -> List[str]:
        """提取關鍵信息點"""
        key_points = []
        
        # 提取包含數字的消息（可能是預算、數量等）
        for msg in messages:
            text = msg.get('text', '')
            numbers = re.findall(r'\d+[\d,]*', text)
            if numbers and len(text) < 100:  # 短消息中的數字更可能是關鍵信息
                key_points.append(f"提到數字: {', '.join(numbers)}")
        
        # 提取問句
        for msg in messages:
            if not msg.get('from_me') and '?' in msg.get('text', ''):
                key_points.append(f"問題: {msg['text'][:50]}...")
        
        return key_points[:5]  # 限制返回最多5個關鍵點
    
    def _suggest_next_action(self, messages: List[Dict[str, Any]]) -> str:
        """建議下一步行動"""
        analysis = {
            'intent': self._identify_intent(messages),
            'urgency': self._assess_urgency(messages),
            'sentiment': self._analyze_sentiment(messages)
        }
        
        # 基於分析結果給出建議
        if analysis['intent'] == 'purchase' and analysis['urgency'] == 'high':
            return "立即跟進，協助完成訂單"
        elif analysis['intent'] == 'support' and analysis['sentiment'] == 'negative':
            return "優先處理客戶問題，安排專人跟進"
        elif analysis['intent'] == 'inquiry' and analysis['urgency'] == 'high':
            return "快速提供詳細產品信息"
        elif analysis['sentiment'] == 'positive':
            return "把握機會，推薦相關產品或服務"
        else:
            return "定期跟進，保持良好關係"
    
    def _identify_needs(self, messages: List[Dict[str, Any]]) -> List[str]:
        """識別客戶需求"""
        needs = []
        need_patterns = {
            '預算考慮': ['預算', '價格', '便宜', '優惠', 'budget', 'discount'],
            '功能需求': ['需要', '想要', '希望', '能不能', 'need', 'want'],
            '時間要求': ['什麼時候', '多久', '期限', 'when', 'deadline'],
            '品質要求': ['品質', '質量', '保證', '保修', 'quality', 'warranty']
        }
        
        for msg in messages:
            if msg.get('from_me'):
                continue
                
            text = msg.get('text', '').lower()
            for need_type, keywords in need_patterns.items():
                for keyword in keywords:
                    if keyword in text and need_type not in needs:
                        needs.append(need_type)
        
        return needs
    
    def learn_from_history(self, whatsapp_id: str, days: int = 30) -> Dict[str, Any]:
        """從歷史對話中學習客戶模式"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # 獲取指定時間內的所有消息
                since_date = (datetime.now() - timedelta(days=days)).isoformat()
                cursor.execute('''
                    SELECT text, from_me, timestamp 
                    FROM messages 
                    WHERE chat_id = ? AND timestamp > ?
                    ORDER BY timestamp
                ''', (whatsapp_id, since_date))
                
                messages = []
                for row in cursor.fetchall():
                    messages.append({
                        'text': row[0],
                        'from_me': bool(row[1]),
                        'timestamp': row[2]
                    })
                
                if not messages:
                    return {'status': 'no_history'}
                
                # 分析模式
                patterns = self._analyze_patterns(messages)
                
                # 生成學習結果
                learning_result = {
                    'communication_patterns': patterns,
                    'preferred_topics': self._get_frequent_topics(messages),
                    'response_time_preference': self._analyze_response_times(messages),
                    'conversation_style': self._analyze_conversation_style(messages),
                    'product_interests': self._extract_product_interests(messages),
                    'behavioral_insights': self._generate_behavioral_insights(patterns)
                }
                
                return learning_result
        except Exception as e:
            self.logger.error(f"Error learning from history: {e}")
            return {'status': 'error', 'message': str(e)}
    
    def _analyze_patterns(self, messages: List[Dict[str, Any]]) -> Dict[str, Any]:
        """分析溝通模式"""
        patterns = {
            'message_frequency': self._calculate_message_frequency(messages),
            'active_hours': self._find_active_hours(messages),
            'avg_message_length': self._calculate_avg_message_length(messages),
            'response_rate': self._calculate_response_rate(messages),
            'conversation_initiator': self._identify_conversation_initiator(messages)
        }
        return patterns
    
    def _calculate_message_frequency(self, messages: List[Dict[str, Any]]) -> float:
        """計算消息頻率（每天平均消息數）"""
        if not messages:
            return 0
        
        first_msg = datetime.fromisoformat(messages[0]['timestamp'])
        last_msg = datetime.fromisoformat(messages[-1]['timestamp'])
        days_diff = (last_msg - first_msg).days + 1
        
        return len(messages) / days_diff
    
    def _find_active_hours(self, messages: List[Dict[str, Any]]) -> List[int]:
        """找出最活躍的時間段"""
        hour_counts = Counter()
        
        for msg in messages:
            if not msg.get('from_me'):
                hour = datetime.fromisoformat(msg['timestamp']).hour
                hour_counts[hour] += 1
        
        # 返回最活躍的3個小時
        return [hour for hour, _ in hour_counts.most_common(3)]
    
    def _calculate_avg_message_length(self, messages: List[Dict[str, Any]]) -> float:
        """計算平均消息長度"""
        customer_messages = [len(msg['text']) for msg in messages if not msg.get('from_me')]
        return sum(customer_messages) / len(customer_messages) if customer_messages else 0
    
    def _calculate_response_rate(self, messages: List[Dict[str, Any]]) -> float:
        """計算回覆率"""
        our_messages = sum(1 for msg in messages if msg.get('from_me'))
        their_messages = sum(1 for msg in messages if not msg.get('from_me'))
        
        if our_messages == 0:
            return 0
        return their_messages / our_messages
    
    def _identify_conversation_initiator(self, messages: List[Dict[str, Any]]) -> str:
        """識別誰通常發起對話"""
        # 簡化實現：檢查第一條消息
        if messages and not messages[0].get('from_me'):
            return 'customer'
        return 'business'
    
    def _get_frequent_topics(self, messages: List[Dict[str, Any]]) -> List[str]:
        """獲取常見話題"""
        all_topics = []
        for msg in messages:
            topics = self._extract_topics([msg])
            all_topics.extend(topics)
        
        topic_counts = Counter(all_topics)
        return [topic for topic, _ in topic_counts.most_common(5)]
    
    def _analyze_response_times(self, messages: List[Dict[str, Any]]) -> str:
        """分析響應時間偏好"""
        response_times = []
        
        for i in range(1, len(messages)):
            if messages[i].get('from_me') != messages[i-1].get('from_me'):
                time1 = datetime.fromisoformat(messages[i-1]['timestamp'])
                time2 = datetime.fromisoformat(messages[i]['timestamp'])
                diff = (time2 - time1).seconds
                if diff < 3600:  # 只考慮1小時內的響應
                    response_times.append(diff)
        
        if not response_times:
            return 'unknown'
        
        avg_response = sum(response_times) / len(response_times)
        if avg_response < 300:  # 5分鐘
            return 'immediate'
        elif avg_response < 1800:  # 30分鐘
            return 'quick'
        else:
            return 'relaxed'
    
    def _analyze_conversation_style(self, messages: List[Dict[str, Any]]) -> str:
        """分析對話風格"""
        customer_messages = [msg['text'] for msg in messages if not msg.get('from_me')]
        
        if not customer_messages:
            return 'unknown'
        
        # 分析指標
        avg_length = sum(len(msg) for msg in customer_messages) / len(customer_messages)
        has_emoji = any('😊' in msg or '👍' in msg or '😄' in msg for msg in customer_messages)
        formal_words = sum(1 for msg in customer_messages if any(word in msg for word in ['您', '請', '謝謝']))
        
        if avg_length > 50 and formal_words > len(customer_messages) * 0.3:
            return 'formal'
        elif has_emoji or avg_length < 20:
            return 'casual'
        else:
            return 'neutral'
    
    def _extract_product_interests(self, messages: List[Dict[str, Any]]) -> List[str]:
        """提取產品興趣"""
        interests = []
        product_keywords = {
            '基礎套餐': ['基礎', '入門', 'basic', 'starter'],
            '專業套餐': ['專業', '進階', 'professional', 'advanced'],
            '企業套餐': ['企業', '團隊', 'enterprise', 'team'],
            '定制方案': ['定制', '客製', 'custom', 'tailored']
        }
        
        for msg in messages:
            if msg.get('from_me'):
                continue
                
            text = msg.get('text', '').lower()
            for product, keywords in product_keywords.items():
                if any(keyword in text for keyword in keywords) and product not in interests:
                    interests.append(product)
        
        return interests
    
    def _generate_behavioral_insights(self, patterns: Dict[str, Any]) -> List[str]:
        """生成行為洞察"""
        insights = []
        
        if patterns['message_frequency'] > 5:
            insights.append("高頻互動客戶，顯示強烈興趣")
        
        if patterns['response_rate'] > 0.8:
            insights.append("高回應率，積極參與對話")
        
        if patterns['active_hours']:
            hours = patterns['active_hours']
            insights.append(f"最活躍時間：{hours[0]}:00-{hours[0]+1}:00")
        
        if patterns['avg_message_length'] > 50:
            insights.append("偏好詳細溝通，需要充分信息")
        elif patterns['avg_message_length'] < 20:
            insights.append("偏好簡潔溝通，直接明瞭")
        
        return insights
    
    def generate_personalized_approach(self, whatsapp_id: str, learning_result: Dict[str, Any]) -> Dict[str, Any]:
        """基於學習結果生成個性化方法"""
        approach = {
            'greeting_style': self._suggest_greeting_style(learning_result),
            'message_length': self._suggest_message_length(learning_result),
            'response_timing': self._suggest_response_timing(learning_result),
            'product_recommendations': self._suggest_products(learning_result),
            'communication_tips': self._generate_communication_tips(learning_result)
        }
        
        return approach
    
    def _suggest_greeting_style(self, learning_result: Dict[str, Any]) -> str:
        """建議問候風格"""
        style = learning_result.get('conversation_style', 'neutral')
        
        if style == 'formal':
            return "您好！很高興為您服務。"
        elif style == 'casual':
            return "Hi！有什麼可以幫到你嗎？😊"
        else:
            return "你好！請問有什麼可以協助您的嗎？"
    
    def _suggest_message_length(self, learning_result: Dict[str, Any]) -> str:
        """建議消息長度"""
        patterns = learning_result.get('communication_patterns', {})
        avg_length = patterns.get('avg_message_length', 30)
        
        if avg_length > 50:
            return "detailed"
        elif avg_length < 20:
            return "concise"
        else:
            return "moderate"
    
    def _suggest_response_timing(self, learning_result: Dict[str, Any]) -> str:
        """建議響應時機"""
        pref = learning_result.get('response_time_preference', 'quick')
        patterns = learning_result.get('communication_patterns', {})
        active_hours = patterns.get('active_hours', [])
        
        timing = {
            'preference': pref,
            'best_hours': active_hours,
            'delay_seconds': 30 if pref == 'immediate' else 180
        }
        
        return timing
    
    def _suggest_products(self, learning_result: Dict[str, Any]) -> List[str]:
        """建議產品"""
        return learning_result.get('product_interests', [])
    
    def _generate_communication_tips(self, learning_result: Dict[str, Any]) -> List[str]:
        """生成溝通技巧"""
        tips = []
        style = learning_result.get('conversation_style', 'neutral')
        insights = learning_result.get('behavioral_insights', [])
        
        if style == 'formal':
            tips.append("保持專業禮貌的語氣")
        elif style == 'casual':
            tips.append("可以使用輕鬆友好的語氣")
        
        if '高頻互動客戶' in str(insights):
            tips.append("定期主動關懷，保持互動")
        
        if '偏好詳細溝通' in str(insights):
            tips.append("提供完整詳細的信息")
        elif '偏好簡潔溝通' in str(insights):
            tips.append("直接切入重點，避免冗長")
        
        return tips