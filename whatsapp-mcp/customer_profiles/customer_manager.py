import json
import sqlite3
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Optional, Any
import logging

class CustomerProfileManager:
    """管理 WhatsApp 客戶檔案的核心類"""
    
    def __init__(self, db_path: str = "../whatsapp-bridge/store/customer_profiles.db"):
        self.db_path = Path(db_path)
        self.db_path.parent.mkdir(parents=True, exist_ok=True)
        self.logger = logging.getLogger(__name__)
        self._init_database()
    
    def _init_database(self):
        """初始化客戶檔案數據庫"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            
            # 客戶基本信息表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS customer_profiles (
                    whatsapp_id TEXT PRIMARY KEY,
                    name TEXT,
                    display_name TEXT,
                    phone_number TEXT,
                    profile_picture TEXT,
                    first_contact_date TIMESTAMP,
                    last_contact_date TIMESTAMP,
                    timezone TEXT,
                    language TEXT,
                    customer_type TEXT DEFAULT 'Potential',
                    industry TEXT,
                    company TEXT,
                    position TEXT,
                    purchase_power TEXT,
                    lifecycle_stage TEXT DEFAULT 'Lead',
                    total_revenue REAL DEFAULT 0,
                    engagement_score REAL DEFAULT 0,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # 標籤表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS customer_tags (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    whatsapp_id TEXT,
                    tag TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (whatsapp_id) REFERENCES customer_profiles(whatsapp_id)
                )
            ''')
            
            # 交互歷史表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS interaction_history (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    whatsapp_id TEXT,
                    conversation_id TEXT,
                    message_count INTEGER,
                    duration_seconds INTEGER,
                    sentiment TEXT,
                    topics TEXT,
                    outcome TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (whatsapp_id) REFERENCES customer_profiles(whatsapp_id)
                )
            ''')
            
            # 購買歷史表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS purchase_history (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    whatsapp_id TEXT,
                    product TEXT,
                    amount REAL,
                    status TEXT,
                    purchase_date TIMESTAMP,
                    notes TEXT,
                    FOREIGN KEY (whatsapp_id) REFERENCES customer_profiles(whatsapp_id)
                )
            ''')
            
            # 客戶偏好表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS customer_preferences (
                    whatsapp_id TEXT PRIMARY KEY,
                    communication_frequency TEXT,
                    price_sensitivity TEXT,
                    preferred_products TEXT,
                    pain_points TEXT,
                    goals TEXT,
                    notes TEXT,
                    FOREIGN KEY (whatsapp_id) REFERENCES customer_profiles(whatsapp_id)
                )
            ''')
            
            # 自動化設置表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS automation_settings (
                    whatsapp_id TEXT PRIMARY KEY,
                    auto_followup BOOLEAN DEFAULT 1,
                    followup_interval INTEGER DEFAULT 7,
                    personalization_level TEXT DEFAULT 'Medium',
                    allowed_topics TEXT,
                    escalation_triggers TEXT,
                    FOREIGN KEY (whatsapp_id) REFERENCES customer_profiles(whatsapp_id)
                )
            ''')
            
            conn.commit()
    
    def create_or_update_profile(self, whatsapp_id: str, profile_data: Dict[str, Any]) -> bool:
        """創建或更新客戶檔案"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # 檢查是否已存在
                cursor.execute("SELECT whatsapp_id FROM customer_profiles WHERE whatsapp_id = ?", (whatsapp_id,))
                exists = cursor.fetchone() is not None
                
                if exists:
                    # 更新現有記錄
                    update_fields = []
                    values = []
                    for key, value in profile_data.items():
                        if key != 'whatsapp_id':
                            update_fields.append(f"{key} = ?")
                            values.append(value)
                    values.append(datetime.now())
                    values.append(whatsapp_id)
                    
                    query = f"UPDATE customer_profiles SET {', '.join(update_fields)}, updated_at = ? WHERE whatsapp_id = ?"
                    cursor.execute(query, values)
                else:
                    # 創建新記錄
                    profile_data['whatsapp_id'] = whatsapp_id
                    profile_data['first_contact_date'] = datetime.now()
                    profile_data['last_contact_date'] = datetime.now()
                    
                    columns = list(profile_data.keys())
                    placeholders = ['?' for _ in columns]
                    query = f"INSERT INTO customer_profiles ({', '.join(columns)}) VALUES ({', '.join(placeholders)})"
                    cursor.execute(query, list(profile_data.values()))
                
                conn.commit()
                self.logger.info(f"Profile {'updated' if exists else 'created'} for {whatsapp_id}")
                return True
        except Exception as e:
            self.logger.error(f"Error creating/updating profile: {e}")
            return False
    
    def get_profile(self, whatsapp_id: str) -> Optional[Dict[str, Any]]:
        """獲取客戶檔案"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.row_factory = sqlite3.Row
                cursor = conn.cursor()
                
                # 獲取基本信息
                cursor.execute("SELECT * FROM customer_profiles WHERE whatsapp_id = ?", (whatsapp_id,))
                profile = cursor.fetchone()
                
                if not profile:
                    return None
                
                result = dict(profile)
                
                # 獲取標籤
                cursor.execute("SELECT tag FROM customer_tags WHERE whatsapp_id = ?", (whatsapp_id,))
                result['tags'] = [row['tag'] for row in cursor.fetchall()]
                
                # 獲取偏好設置
                cursor.execute("SELECT * FROM customer_preferences WHERE whatsapp_id = ?", (whatsapp_id,))
                preferences = cursor.fetchone()
                if preferences:
                    result['preferences'] = dict(preferences)
                
                # 獲取自動化設置
                cursor.execute("SELECT * FROM automation_settings WHERE whatsapp_id = ?", (whatsapp_id,))
                automation = cursor.fetchone()
                if automation:
                    result['automation_settings'] = dict(automation)
                
                return result
        except Exception as e:
            self.logger.error(f"Error getting profile: {e}")
            return None
    
    def add_interaction(self, whatsapp_id: str, interaction_data: Dict[str, Any]) -> bool:
        """記錄客戶交互"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                interaction_data['whatsapp_id'] = whatsapp_id
                columns = list(interaction_data.keys())
                placeholders = ['?' for _ in columns]
                
                query = f"INSERT INTO interaction_history ({', '.join(columns)}) VALUES ({', '.join(placeholders)})"
                cursor.execute(query, list(interaction_data.values()))
                
                # 更新最後聯繫時間
                cursor.execute(
                    "UPDATE customer_profiles SET last_contact_date = ? WHERE whatsapp_id = ?",
                    (datetime.now(), whatsapp_id)
                )
                
                conn.commit()
                return True
        except Exception as e:
            self.logger.error(f"Error adding interaction: {e}")
            return False
    
    def classify_customer(self, whatsapp_id: str) -> str:
        """基於互動數據自動分類客戶"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # 獲取購買歷史
                cursor.execute(
                    "SELECT COUNT(*) as purchase_count, SUM(amount) as total_amount FROM purchase_history WHERE whatsapp_id = ? AND status = 'Completed'",
                    (whatsapp_id,)
                )
                purchase_data = cursor.fetchone()
                
                # 獲取互動頻率
                cursor.execute(
                    "SELECT COUNT(*) as interaction_count FROM interaction_history WHERE whatsapp_id = ? AND created_at > datetime('now', '-30 days')",
                    (whatsapp_id,)
                )
                interaction_data = cursor.fetchone()
                
                # 分類邏輯
                if purchase_data[1] and purchase_data[1] > 10000:  # 總購買金額超過10000
                    customer_type = 'VIP'
                elif purchase_data[0] > 0:  # 有購買記錄
                    customer_type = 'Regular'
                elif interaction_data[0] > 5:  # 30天內互動超過5次
                    customer_type = 'Potential'
                else:
                    customer_type = 'Inactive'
                
                # 更新客戶類型
                cursor.execute(
                    "UPDATE customer_profiles SET customer_type = ?, updated_at = ? WHERE whatsapp_id = ?",
                    (customer_type, datetime.now(), whatsapp_id)
                )
                conn.commit()
                
                return customer_type
        except Exception as e:
            self.logger.error(f"Error classifying customer: {e}")
            return 'Potential'
    
    def get_customer_insights(self, whatsapp_id: str) -> Dict[str, Any]:
        """獲取客戶洞察數據"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                insights = {
                    'engagement_metrics': {},
                    'purchase_analytics': {},
                    'communication_patterns': {},
                    'recommendations': []
                }
                
                # 參與度指標
                cursor.execute('''
                    SELECT 
                        COUNT(*) as total_interactions,
                        AVG(message_count) as avg_messages_per_conversation,
                        AVG(duration_seconds) as avg_conversation_duration
                    FROM interaction_history 
                    WHERE whatsapp_id = ?
                ''', (whatsapp_id,))
                
                engagement = cursor.fetchone()
                insights['engagement_metrics'] = {
                    'total_interactions': engagement[0],
                    'avg_messages_per_conversation': engagement[1] or 0,
                    'avg_conversation_duration_minutes': (engagement[2] or 0) / 60
                }
                
                # 購買分析
                cursor.execute('''
                    SELECT 
                        COUNT(*) as total_purchases,
                        SUM(amount) as total_revenue,
                        AVG(amount) as avg_order_value,
                        MAX(purchase_date) as last_purchase_date
                    FROM purchase_history 
                    WHERE whatsapp_id = ? AND status = 'Completed'
                ''', (whatsapp_id,))
                
                purchase = cursor.fetchone()
                insights['purchase_analytics'] = {
                    'total_purchases': purchase[0],
                    'total_revenue': purchase[1] or 0,
                    'avg_order_value': purchase[2] or 0,
                    'last_purchase_date': purchase[3]
                }
                
                # 生成建議
                if insights['engagement_metrics']['total_interactions'] > 10:
                    insights['recommendations'].append('高參與度客戶，考慮提供VIP服務')
                
                if insights['purchase_analytics']['total_revenue'] > 5000:
                    insights['recommendations'].append('高價值客戶，建議定期關懷')
                
                return insights
        except Exception as e:
            self.logger.error(f"Error getting insights: {e}")
            return {}
    
    def search_customers(self, criteria: Dict[str, Any]) -> List[Dict[str, Any]]:
        """根據條件搜索客戶"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.row_factory = sqlite3.Row
                cursor = conn.cursor()
                
                query = "SELECT * FROM customer_profiles WHERE 1=1"
                params = []
                
                if 'customer_type' in criteria:
                    query += " AND customer_type = ?"
                    params.append(criteria['customer_type'])
                
                if 'min_revenue' in criteria:
                    query += " AND total_revenue >= ?"
                    params.append(criteria['min_revenue'])
                
                if 'industry' in criteria:
                    query += " AND industry = ?"
                    params.append(criteria['industry'])
                
                cursor.execute(query, params)
                return [dict(row) for row in cursor.fetchall()]
        except Exception as e:
            self.logger.error(f"Error searching customers: {e}")
            return []