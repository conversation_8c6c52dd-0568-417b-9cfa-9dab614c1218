[project]
name = "whatsapp-mcp"
version = "0.2.0"
description = "WhatsApp Model Context Protocol Server"
readme = "README.md"
requires-python = ">=3.11"
dependencies = [
    "httpx>=0.28.1",
    "mcp[cli]>=1.6.0",
    "requests>=2.32.3",
    "easyocr>=1.7.0",
    "Pillow>=10.0.0",
    "torch>=2.0.0",
    "torchvision>=0.15.0",
]

[project.scripts]
whatsapp-mcp = "main:main"

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.hatch.build]
include = [
    "src/**/*.py",
    "config/*.json",
    "main.py"
]

[tool.hatch.build.targets.wheel]
packages = ["src"]