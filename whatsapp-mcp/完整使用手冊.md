# WhatsApp MCP 完整使用手冊

## 目錄
1. [系統簡介](#系統簡介)
2. [安裝與首次設置](#安裝與首次設置)
3. [連接 WhatsApp](#連接-whatsapp)
4. [使用觀察模式](#使用觀察模式)
5. [使用 Claude 發送消息](#使用-claude-發送消息)
6. [客戶檔案管理](#客戶檔案管理)
7. [數據分析與報告](#數據分析與報告)
8. [多帳號管理](#多帳號管理)
9. [常見問題解決](#常見問題解決)
10. [最佳實踐建議](#最佳實踐建議)

---

## 系統簡介

WhatsApp MCP 是一個強大的 WhatsApp 業務管理系統，可以：
- 📱 通過 Claude 管理 WhatsApp 消息
- 🔍 觀察和分析客戶對話（不發送消息）
- 📊 自動生成業務洞察報告
- 👥 智能管理客戶檔案
- 🤖 AI 輔助回覆建議

---

## 安裝與首次設置

### 前置需求
- macOS 系統
- 已安裝 Go 語言環境
- 已安裝 Python 3.8+
- <PERSON> Desktop 應用程式

### 安裝步驟

1. **檢查安裝環境**
   ```bash
   # 檢查 Go
   go version
   
   # 檢查 Python
   python3 --version
   ```

2. **安裝必要的 Python 套件**
   ```bash
   pip3 install requests pandas matplotlib seaborn sqlite3
   ```

---

## 連接 WhatsApp

### 首次連接（新號碼）

1. **打開終端（Terminal）**
   - 按 `Command + 空白鍵`，輸入 "Terminal"
   - 或從 應用程式 → 工具程式 → 終端機

2. **進入 WhatsApp Bridge 目錄**
   ```bash
   cd /Users/<USER>/Desktop/Claude/whatsapp-mcp/whatsapp-bridge
   ```

3. **啟動 WhatsApp 連接**
   ```bash
   go run main.go
   ```

4. **掃描 QR 碼**
   - 終端會顯示一個二維碼
   - 打開手機 WhatsApp
   - 點擊 設定 → 連結裝置 → 連結裝置
   - 掃描電腦螢幕上的 QR 碼

5. **確認連接成功**
   看到以下訊息表示成功：
   ```
   ✓ Connected to WhatsApp!
   REST server is running on :8080
   ```

### 更換號碼

如果要更換 WhatsApp 號碼：

```bash
# 1. 停止當前程序 (Ctrl + C)

# 2. 清除舊登錄資料
cd /Users/<USER>/Desktop/Claude/whatsapp-mcp/whatsapp-bridge
rm -rf store/whatsapp.db

# 3. 重新啟動
go run main.go

# 4. 用新號碼掃描新的 QR 碼
```

---

## 使用觀察模式

觀察模式只下載和分析對話，**不會發送任何消息**。

### 啟動觀察模式

1. **保持 WhatsApp Bridge 運行**
   確保第一個終端視窗的 WhatsApp 連接保持開啟

2. **開新終端視窗**
   - 按 `Command + N` 開新視窗
   - 或按 `Command + T` 開新分頁

3. **執行觀察程序**
   ```bash
   cd /Users/<USER>/Desktop/Claude/whatsapp-mcp/observer-mode
   ./start_observer.sh
   ```

4. **選擇運行模式**
   ```
   選擇運行模式：
   1. 實時觀察模式 - 持續監控新消息
   2. 分析模式 - 分析已下載的對話
   3. 完整模式 - 觀察並定期生成分析報告
   
   請選擇 (1/2/3): 1
   ```

### 觀察模式功能

- **實時監控**：每 5 秒檢查新消息
- **關鍵詞警報**：檢測重要業務關鍵詞
- **自動保存**：所有對話保存到本地數據庫
- **不發送消息**：純觀察，不影響業務

### 查看觀察結果

1. **實時日誌**
   ```
   [客戶名稱] 發現 3 條新消息
   🔔 關鍵詞警報: '價格' 在 張先生 的消息中
   ```

2. **停止觀察並生成報告**
   - 按 `Ctrl + C` 停止
   - 自動生成 `observation_report.json`

---

## 使用 Claude 發送消息

### 在 Claude Desktop 中使用

1. **確保 WhatsApp Bridge 正在運行**

2. **在 Claude 中使用 WhatsApp 功能**
   - Claude 會自動載入 WhatsApp MCP
   - 可以請 Claude 幫您：
     - 查看對話列表
     - 讀取特定對話
     - 發送消息
     - 搜索對話內容

### 常用指令示例

```
"幫我查看最近的 WhatsApp 消息"
"給張先生發送消息：您好，關於您詢問的產品..."
"搜索所有提到'價格'的對話"
"顯示今天的所有客戶對話"
```

---

## 客戶檔案管理

系統會自動為每個客戶建立詳細檔案。

### 客戶檔案內容

1. **基本信息**
   - 姓名、電話、WhatsApp ID
   - 首次聯繫時間
   - 最後聯繫時間

2. **客戶分類**
   - VIP：總購買超過 10,000
   - Regular：有購買記錄
   - Potential：30天內互動超過5次
   - Inactive：不活躍客戶

3. **行為分析**
   - 活躍時間段
   - 溝通風格（正式/休閒）
   - 產品興趣
   - 回應速度

### 查看客戶檔案

```bash
# 使用 Python 腳本查看
cd /Users/<USER>/Desktop/Claude/whatsapp-mcp/customer_profiles
python3
>>> from profile_integration import WhatsAppProfileIntegration
>>> integration = WhatsAppProfileIntegration()
>>> context = integration.get_customer_context('客戶WhatsApp_ID')
```

---

## 數據分析與報告

### 生成分析報告

1. **執行分析工具**
   ```bash
   cd /Users/<USER>/Desktop/Claude/whatsapp-mcp/observer-mode
   python3 conversation_analyzer.py
   ```

2. **報告內容**
   - 📊 總體概覽（對話數、用戶數、消息數）
   - ⏰ 時間分析（24小時分布、週期模式）
   - 👥 用戶分析（最活躍用戶、參與度）
   - 💬 內容分析（高頻詞、情感分布）
   - 💡 業務洞察（機會、風險、建議）

3. **查看報告**
   - JSON 報告：`reports/analysis_report_*.json`
   - 圖表：`reports/*.png`

### 報告解讀

1. **24小時消息分布圖**
   - 顯示客戶最活躍的時間
   - 用於優化客服排班

2. **Top 10 活躍用戶**
   - 識別重要客戶
   - 優先關注高價值用戶

3. **情感分析**
   - 了解整體客戶滿意度
   - 及時發現負面情緒

---

## 多帳號管理

### 設置多個 WhatsApp 帳號

1. **運行多帳號設置腳本**
   ```bash
   cd /Users/<USER>/Desktop/Claude/whatsapp-mcp
   ./multi-account-setup.sh
   ```

2. **輸入帳號數量**（1-5個）

3. **分別啟動每個帳號**
   ```bash
   # 帳號 1（端口 8080）
   cd whatsapp-account-1/whatsapp-bridge
   go run main.go
   
   # 帳號 2（端口 8081）- 新終端
   cd whatsapp-account-2/whatsapp-bridge
   go run main.go
   ```

4. **每個帳號分別掃碼**

### 管理多帳號

- **查看所有帳號狀態**
  ```bash
  ps aux | grep whatsapp
  ```

- **停止所有帳號**
  ```bash
  ./stop-all-accounts.sh
  ```

---

## 常見問題解決

### 問題 1：看不到 QR 碼

**解決方法**：
- 放大終端視窗
- 調整終端字體大小
- 確保終端支持 Unicode 字符

### 問題 2：掃碼後無法連接

**解決方法**：
```bash
# 清除所有緩存重試
rm -rf store/*
go run main.go
```

### 問題 3：觀察模式無法啟動

**解決方法**：
- 確認 WhatsApp Bridge 在運行（端口 8080）
- 檢查 Python 依賴是否安裝完整
- 查看錯誤日誌：`logs/observer.log`

### 問題 4：Claude 無法使用 WhatsApp

**解決方法**：
1. 檢查 Claude Desktop 配置
2. 重啟 Claude Desktop
3. 確保 WhatsApp Bridge 正在運行

### 問題 5：數據庫錯誤

**解決方法**：
```bash
# 修復權限問題
chmod 755 store/
chmod 644 store/*.db
```

---

## 最佳實踐建議

### 1. 日常使用流程

**早上**
1. 啟動 WhatsApp Bridge
2. 開啟觀察模式
3. 查看昨日分析報告

**工作中**
- 使用 Claude 回覆重要消息
- 觀察模式自動記錄所有對話
- 關注關鍵詞警報

**下班前**
- 生成當日分析報告
- 標記需要跟進的客戶
- 備份重要數據

### 2. 數據管理

- **每週備份**
  ```bash
  cp -r store/ backup/store_$(date +%Y%m%d)/
  ```

- **每月清理**
  - 刪除超過 3 個月的舊數據
  - 歸檔重要對話記錄

### 3. 安全建議

- 定期更換 WhatsApp 連接
- 限制數據庫文件訪問權限
- 不要分享包含客戶數據的文件
- 遵守隱私保護法規

### 4. 效能優化

- 觀察模式檢查間隔可調整（5-30秒）
- 定期清理大型對話群組
- 使用 SSD 存儲數據庫文件

### 5. 團隊協作

- 共享分析報告（不含敏感數據）
- 制定關鍵詞監控標準
- 定期檢討客戶分類標準
- 建立標準回覆模板

---

## 快速參考

### 常用命令

```bash
# 啟動 WhatsApp
cd /Users/<USER>/Desktop/Claude/whatsapp-mcp/whatsapp-bridge
go run main.go

# 啟動觀察模式
cd /Users/<USER>/Desktop/Claude/whatsapp-mcp/observer-mode
./start_observer.sh

# 生成分析報告
python3 conversation_analyzer.py

# 查看客戶檔案
python3 profile_integration.py

# 備份數據
cp -r store/ backup/
```

### 重要路徑

- WhatsApp Bridge: `/whatsapp-mcp/whatsapp-bridge/`
- 觀察模式: `/whatsapp-mcp/observer-mode/`
- 客戶檔案: `/whatsapp-mcp/customer_profiles/`
- 數據庫: `/whatsapp-bridge/store/`
- 報告: `/observer-mode/reports/`

### 聯繫支持

如有技術問題，請保留：
- 錯誤日誌
- 系統版本信息
- 操作步驟說明

---

## 結語

WhatsApp MCP 是您的智能業務助手，幫助您：
- 📈 提升客戶服務效率
- 🎯 精準了解客戶需求
- 💡 發現業務機會
- 🤝 建立更好的客戶關係

祝您使用愉快！