"""
WhatsApp API client wrapper
"""

import requests
import json
import logging
from typing import Dict, Any, Optional, Tuple, List
from pathlib import Path

from ..common.config import config

logger = logging.getLogger(__name__)


class WhatsAppAPIError(Exception):
    """Custom exception for WhatsApp API errors"""
    pass


class WhatsAppClient:
    """Client for interacting with WhatsApp bridge API"""
    
    def __init__(self, api_base_url: Optional[str] = None):
        self.api_base_url = api_base_url or config.whatsapp.api_base_url
        self.session = requests.Session()
        self.session.headers.update({
            'Content-Type': 'application/json'
        })
    
    def _make_request(self, method: str, endpoint: str, data: Optional[Dict] = None) -> Dict[str, Any]:
        """Make HTTP request to WhatsApp bridge API"""
        url = f"{self.api_base_url.rstrip('/')}/{endpoint.lstrip('/')}"
        
        try:
            if method == 'GET':
                response = self.session.get(url, params=data)
            elif method == 'POST':
                response = self.session.post(url, json=data)
            else:
                raise ValueError(f"Unsupported HTTP method: {method}")
            
            response.raise_for_status()
            
            # Handle different response types
            if response.headers.get('content-type', '').startswith('application/json'):
                return response.json()
            else:
                return {'response': response.text}
                
        except requests.exceptions.RequestException as e:
            logger.error(f"API request failed: {e}")
            raise WhatsAppAPIError(f"Failed to {method} {endpoint}: {str(e)}")
    
    def send_message(self, recipient: str, message: str) -> Tuple[bool, str]:
        """
        Send a text message to a WhatsApp contact or group
        
        Args:
            recipient: Phone number or JID
            message: Text message to send
            
        Returns:
            Tuple of (success, status_message)
        """
        # Format recipient JID if needed
        if not recipient.endswith(('@s.whatsapp.net', '@g.us')):
            # It's a phone number, format it
            recipient = f"{recipient}@s.whatsapp.net"
        
        data = {
            'recipient': recipient,
            'message': message
        }
        
        try:
            result = self._make_request('POST', 'send-message', data)
            return True, result.get('message', 'Message sent successfully')
        except WhatsAppAPIError as e:
            return False, str(e)
    
    def send_file(self, recipient: str, file_path: str, caption: Optional[str] = None) -> Tuple[bool, str]:
        """
        Send a file (image, video, document) via WhatsApp
        
        Args:
            recipient: Phone number or JID
            file_path: Path to the file to send
            caption: Optional caption for the file
            
        Returns:
            Tuple of (success, status_message)
        """
        # Format recipient JID if needed
        if not recipient.endswith(('@s.whatsapp.net', '@g.us')):
            recipient = f"{recipient}@s.whatsapp.net"
        
        # Verify file exists
        file_path_obj = Path(file_path)
        if not file_path_obj.exists():
            return False, f"File not found: {file_path}"
        
        data = {
            'recipient': recipient,
            'filePath': str(file_path_obj.absolute())
        }
        
        if caption:
            data['caption'] = caption
        
        try:
            result = self._make_request('POST', 'send-file', data)
            return True, result.get('message', 'File sent successfully')
        except WhatsAppAPIError as e:
            return False, str(e)
    
    def send_audio(self, recipient: str, audio_path: str) -> Tuple[bool, str]:
        """
        Send an audio message via WhatsApp
        
        Args:
            recipient: Phone number or JID
            audio_path: Path to the audio file
            
        Returns:
            Tuple of (success, status_message)
        """
        # Format recipient JID if needed
        if not recipient.endswith(('@s.whatsapp.net', '@g.us')):
            recipient = f"{recipient}@s.whatsapp.net"
        
        # Verify file exists
        audio_path_obj = Path(audio_path)
        if not audio_path_obj.exists():
            return False, f"Audio file not found: {audio_path}"
        
        data = {
            'recipient': recipient,
            'audioPath': str(audio_path_obj.absolute())
        }
        
        try:
            result = self._make_request('POST', 'send-audio', data)
            return True, result.get('message', 'Audio sent successfully')
        except WhatsAppAPIError as e:
            return False, str(e)
    
    def download_media(self, message_id: str, chat_jid: str) -> Optional[str]:
        """
        Download media from a WhatsApp message
        
        Args:
            message_id: ID of the message containing media
            chat_jid: JID of the chat containing the message
            
        Returns:
            Path to downloaded file or None if failed
        """
        data = {
            'messageId': message_id,
            'chatJid': chat_jid
        }
        
        try:
            result = self._make_request('POST', 'download-media', data)
            
            if result.get('success'):
                return result.get('filePath')
            else:
                logger.error(f"Failed to download media: {result.get('message')}")
                return None
                
        except WhatsAppAPIError as e:
            logger.error(f"Media download error: {e}")
            return None
    
    def get_qr_code(self) -> Optional[str]:
        """Get QR code for WhatsApp Web login"""
        try:
            result = self._make_request('GET', 'qr')
            return result.get('qr')
        except WhatsAppAPIError:
            return None
    
    def get_status(self) -> Dict[str, Any]:
        """Get WhatsApp connection status"""
        try:
            return self._make_request('GET', 'status')
        except WhatsAppAPIError:
            return {'connected': False, 'error': 'Failed to get status'}
    
    def logout(self) -> bool:
        """Logout from WhatsApp Web"""
        try:
            result = self._make_request('POST', 'logout')
            return result.get('success', False)
        except WhatsAppAPIError:
            return False


# Singleton client instance
whatsapp_client = WhatsAppClient()