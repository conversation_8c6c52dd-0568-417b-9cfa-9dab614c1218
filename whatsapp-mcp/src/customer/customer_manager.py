"""
Unified customer profile management system
Consolidates customer data operations and profile management
"""

import logging
from typing import Optional, Dict, Any, List, Tuple
from datetime import datetime, timedelta
from enum import Enum

from ..core import get_config, get_db_manager

logger = logging.getLogger(__name__)

class CustomerStatus(Enum):
    """Customer status classifications"""
    VIP = "vip"
    REGULAR = "regular"
    POTENTIAL = "potential"
    INACTIVE = "inactive"
    NEW = "new"

class CustomerManager:
    """Centralized customer profile management"""
    
    def __init__(self):
        """Initialize customer manager"""
        self.config = get_config()
        self.db = get_db_manager()
        
        # Configuration
        self.vip_threshold = self.config.customer.vip_spending_threshold
        self.inactive_days = self.config.customer.inactive_days_threshold
        
        logger.info("Customer manager initialized")
    
    def get_or_create_profile(self, whatsapp_id: str) -> Dict[str, Any]:
        """
        Get existing profile or create new one
        
        Args:
            whatsapp_id: WhatsApp JID
            
        Returns:
            Customer profile dict
        """
        # Try to get existing profile
        profile = self.db.get_customer_profile(whatsapp_id)
        
        if profile:
            return profile
        
        # Create new profile
        new_profile = {
            'whatsapp_id': whatsapp_id,
            'display_name': whatsapp_id.split('@')[0],  # Default name
            'customer_status': CustomerStatus.NEW.value,
            'total_spending': 0,
            'message_count': 0,
            'last_active': datetime.now().isoformat(),
            'profile_completeness': 0.0
        }
        
        profile_id = self.db.save_customer_profile(new_profile)
        new_profile['id'] = profile_id
        
        logger.info(f"Created new profile for {whatsapp_id}")
        return new_profile
    
    def update_profile(self, whatsapp_id: str, updates: Dict[str, Any]) -> bool:
        """
        Update customer profile
        
        Args:
            whatsapp_id: WhatsApp JID
            updates: Fields to update
            
        Returns:
            Success status
        """
        try:
            # Ensure whatsapp_id is in updates
            updates['whatsapp_id'] = whatsapp_id
            updates['last_active'] = datetime.now().isoformat()
            
            # Recalculate completeness if profile fields changed
            if any(field in updates for field in ['name_zh', 'name_en', 'gender', 
                                                   'birth_date', 'phone', 'email']):
                profile = self.get_or_create_profile(whatsapp_id)
                profile.update(updates)
                completeness = self._calculate_completeness(profile)
                updates['profile_completeness'] = completeness
            
            self.db.save_customer_profile(updates)
            logger.info(f"Updated profile for {whatsapp_id}")
            return True
            
        except Exception as e:
            logger.error(f"Error updating profile: {e}")
            return False
    
    def _calculate_completeness(self, profile: Dict[str, Any]) -> float:
        """Calculate profile completeness percentage"""
        required_fields = [
            'name_zh', 'name_en', 'gender', 'birth_date',
            'phone', 'email', 'district'
        ]
        
        completed = sum(1 for field in required_fields if profile.get(field))
        return (completed / len(required_fields)) * 100
    
    def classify_customer(self, whatsapp_id: str) -> CustomerStatus:
        """
        Classify customer based on activity and spending
        
        Args:
            whatsapp_id: WhatsApp JID
            
        Returns:
            Customer status
        """
        profile = self.get_or_create_profile(whatsapp_id)
        
        # Check last active date
        last_active = datetime.fromisoformat(profile['last_active'])
        days_inactive = (datetime.now() - last_active).days
        
        if days_inactive > self.inactive_days:
            return CustomerStatus.INACTIVE
        
        # Check spending
        total_spending = profile.get('total_spending', 0)
        
        if total_spending >= self.vip_threshold:
            return CustomerStatus.VIP
        elif total_spending > 0:
            return CustomerStatus.REGULAR
        elif profile.get('message_count', 0) > 5:
            return CustomerStatus.POTENTIAL
        else:
            return CustomerStatus.NEW
    
    def update_classification(self, whatsapp_id: str) -> CustomerStatus:
        """Update customer classification"""
        status = self.classify_customer(whatsapp_id)
        self.update_profile(whatsapp_id, {'customer_status': status.value})
        return status
    
    def record_interaction(self, whatsapp_id: str, 
                         interaction_type: str = 'message',
                         metadata: Optional[Dict[str, Any]] = None):
        """
        Record customer interaction
        
        Args:
            whatsapp_id: WhatsApp JID
            interaction_type: Type of interaction
            metadata: Additional interaction data
        """
        try:
            # Update profile counters
            profile = self.get_or_create_profile(whatsapp_id)
            message_count = profile.get('message_count', 0) + 1
            
            updates = {
                'message_count': message_count,
                'last_interaction_type': interaction_type
            }
            
            # Record purchase if applicable
            if interaction_type == 'purchase' and metadata:
                amount = metadata.get('amount', 0)
                current_spending = profile.get('total_spending', 0)
                updates['total_spending'] = current_spending + amount
            
            self.update_profile(whatsapp_id, updates)
            
            # Update classification after interaction
            self.update_classification(whatsapp_id)
            
        except Exception as e:
            logger.error(f"Error recording interaction: {e}")
    
    def get_missing_fields(self, whatsapp_id: str) -> List[str]:
        """Get list of missing profile fields"""
        profile = self.get_or_create_profile(whatsapp_id)
        
        # Essential fields
        essential_fields = {
            'name_zh': '中文姓名',
            'name_en': 'English name',
            'gender': '性別',
            'birth_date': '出生日期',
            'phone': '電話號碼',
            'email': '電子郵件',
            'district': '居住地區'
        }
        
        missing = []
        for field, label in essential_fields.items():
            if not profile.get(field):
                missing.append(label)
        
        return missing
    
    def search_customers(self, query: str, limit: int = 20) -> List[Dict[str, Any]]:
        """Search customers by name or ID"""
        try:
            query_sql = """
                SELECT * FROM customer_profiles
                WHERE whatsapp_id LIKE ? 
                OR name_zh LIKE ?
                OR name_en LIKE ?
                OR display_name LIKE ?
                ORDER BY last_active DESC
                LIMIT ?
            """
            
            search_term = f'%{query}%'
            params = (search_term, search_term, search_term, search_term, limit)
            
            results = []
            for row in self.db.execute(query_sql, params):
                profile = dict(row)
                # Add computed fields
                profile['status_display'] = CustomerStatus(
                    profile.get('customer_status', 'new')
                ).name
                results.append(profile)
            
            return results
            
        except Exception as e:
            logger.error(f"Error searching customers: {e}")
            return []
    
    def get_customer_insights(self, whatsapp_id: str) -> Dict[str, Any]:
        """Get customer insights and analytics"""
        profile = self.get_or_create_profile(whatsapp_id)
        
        # Basic insights
        insights = {
            'profile_completeness': profile.get('profile_completeness', 0),
            'customer_status': profile.get('customer_status', 'new'),
            'total_interactions': profile.get('message_count', 0),
            'total_spending': profile.get('total_spending', 0),
            'days_since_last_active': 0
        }
        
        # Calculate days since last active
        if profile.get('last_active'):
            last_active = datetime.fromisoformat(profile['last_active'])
            insights['days_since_last_active'] = (datetime.now() - last_active).days
        
        # Get health information if available
        health_query = """
            SELECT COUNT(*) as health_records
            FROM customer_health_info
            WHERE customer_id IN (
                SELECT id FROM customer_profiles WHERE whatsapp_id = ?
            )
        """
        
        health_count = self.db.execute_one(health_query, (whatsapp_id,))
        if health_count:
            insights['health_records'] = health_count['health_records']
        
        # Get examination history
        exam_query = """
            SELECT COUNT(*) as exam_count,
                   MAX(examination_date) as last_exam
            FROM examination_records
            WHERE customer_id IN (
                SELECT id FROM customer_profiles WHERE whatsapp_id = ?
            )
        """
        
        exam_data = self.db.execute_one(exam_query, (whatsapp_id,))
        if exam_data and exam_data['exam_count']:
            insights['examination_count'] = exam_data['exam_count']
            insights['last_examination'] = exam_data['last_exam']
        
        return insights
    
    def get_statistics(self) -> Dict[str, Any]:
        """Get overall customer statistics"""
        try:
            stats = {}
            
            # Total customers
            total = self.db.execute_one(
                "SELECT COUNT(*) as count FROM customer_profiles"
            )
            stats['total_customers'] = total['count']
            
            # By status
            status_query = """
                SELECT customer_status, COUNT(*) as count
                FROM customer_profiles
                GROUP BY customer_status
            """
            
            status_counts = {}
            for row in self.db.execute(status_query):
                status = row['customer_status'] or 'unknown'
                status_counts[status] = row['count']
            
            stats['by_status'] = status_counts
            
            # Active in last 30 days
            active_query = """
                SELECT COUNT(*) as count
                FROM customer_profiles
                WHERE datetime(last_active) > datetime('now', '-30 days')
            """
            
            active = self.db.execute_one(active_query)
            stats['active_last_30_days'] = active['count']
            
            # Average completeness
            avg_completeness = self.db.execute_one(
                "SELECT AVG(profile_completeness) as avg FROM customer_profiles"
            )
            stats['average_completeness'] = avg_completeness['avg'] or 0
            
            # VIP customers
            vip_count = self.db.execute_one(
                "SELECT COUNT(*) as count FROM customer_profiles WHERE customer_status = 'vip'"
            )
            stats['vip_customers'] = vip_count['count']
            
            return stats
            
        except Exception as e:
            logger.error(f"Error getting statistics: {e}")
            return {}

# Convenience functions
def get_customer_manager() -> CustomerManager:
    """Get customer manager instance"""
    return CustomerManager()

# Example usage
if __name__ == "__main__":
    manager = get_customer_manager()
    
    # Test customer operations
    test_whatsapp_id = "<EMAIL>"
    
    # Get or create profile
    profile = manager.get_or_create_profile(test_whatsapp_id)
    print(f"Profile: {profile}")
    
    # Update profile
    manager.update_profile(test_whatsapp_id, {
        'name_zh': '陳大文',
        'name_en': 'David Chan',
        'gender': 'M',
        'district': '中環'
    })
    
    # Get missing fields
    missing = manager.get_missing_fields(test_whatsapp_id)
    print(f"Missing fields: {missing}")
    
    # Get insights
    insights = manager.get_customer_insights(test_whatsapp_id)
    print(f"Customer insights: {insights}")
    
    # Get statistics
    stats = manager.get_statistics()
    print(f"Overall statistics: {stats}")