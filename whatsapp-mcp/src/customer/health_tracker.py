"""
Health information tracking and management
Specialized handling of customer health data
"""

import logging
import json
from typing import Optional, Dict, Any, List
from datetime import datetime, timedelta

from ..core import get_config, get_db_manager

logger = logging.getLogger(__name__)

class HealthTracker:
    """Manage customer health information and medical records"""
    
    def __init__(self):
        """Initialize health tracker"""
        self.config = get_config()
        self.db = get_db_manager()
        
        # Configuration
        self.reminder_days_before = self.config.customer.health_reminder_days
        self.exam_validity_days = self.config.customer.exam_validity_days
        
        logger.info("Health tracker initialized")
    
    def get_health_profile(self, customer_id: int) -> Dict[str, Any]:
        """
        Get complete health profile for customer
        
        Args:
            customer_id: Customer database ID
            
        Returns:
            Health profile dict
        """
        profile = {
            'health_conditions': [],
            'medications': [],
            'allergies': [],
            'examination_history': [],
            'upcoming_reminders': []
        }
        
        try:
            # Get health conditions
            health_query = """
                SELECT * FROM customer_health_info
                WHERE customer_id = ?
                ORDER BY recorded_at DESC
            """
            
            health_records = self.db.execute(health_query, (customer_id,))
            for record in health_records:
                profile['health_conditions'].append({
                    'id': record['id'],
                    'conditions': record['health_conditions'],
                    'chronic_diseases': record['chronic_diseases'],
                    'current_medications': record['current_medications'],
                    'known_allergies': record['known_allergies'],
                    'recorded_at': record['recorded_at']
                })
                
                # Extract medications and allergies
                if record['current_medications']:
                    meds = json.loads(record['current_medications'])
                    profile['medications'].extend(meds)
                
                if record['known_allergies']:
                    allergies = json.loads(record['known_allergies'])
                    profile['allergies'].extend(allergies)
            
            # Get examination history
            exam_query = """
                SELECT * FROM examination_records
                WHERE customer_id = ?
                ORDER BY examination_date DESC
                LIMIT 10
            """
            
            exams = self.db.execute(exam_query, (customer_id,))
            for exam in exams:
                profile['examination_history'].append({
                    'id': exam['id'],
                    'exam_type': exam['examination_type'],
                    'date': exam['examination_date'],
                    'results_summary': exam['results_summary'],
                    'abnormal_findings': exam['abnormal_findings'],
                    'doctor_recommendations': exam['doctor_recommendations']
                })
            
            # Get upcoming reminders
            reminder_query = """
                SELECT * FROM auto_reminders
                WHERE customer_id = ?
                AND scheduled_date >= date('now')
                AND status = 'pending'
                ORDER BY scheduled_date
            """
            
            reminders = self.db.execute(reminder_query, (customer_id,))
            for reminder in reminders:
                profile['upcoming_reminders'].append({
                    'id': reminder['id'],
                    'type': reminder['reminder_type'],
                    'message': reminder['reminder_message'],
                    'scheduled_date': reminder['scheduled_date']
                })
            
        except Exception as e:
            logger.error(f"Error getting health profile: {e}")
        
        return profile
    
    def update_health_info(self, customer_id: int, 
                          health_data: Dict[str, Any]) -> bool:
        """
        Update customer health information
        
        Args:
            customer_id: Customer database ID
            health_data: Health information to update
            
        Returns:
            Success status
        """
        try:
            # Prepare data
            data = {
                'customer_id': customer_id,
                'health_conditions': health_data.get('conditions', ''),
                'chronic_diseases': json.dumps(
                    health_data.get('chronic_diseases', []), 
                    ensure_ascii=False
                ),
                'current_medications': json.dumps(
                    health_data.get('medications', []), 
                    ensure_ascii=False
                ),
                'known_allergies': json.dumps(
                    health_data.get('allergies', []), 
                    ensure_ascii=False
                ),
                'emergency_contact_name': health_data.get('emergency_contact_name'),
                'emergency_contact_phone': health_data.get('emergency_contact_phone'),
                'recorded_at': datetime.now().isoformat()
            }
            
            self.db.insert('customer_health_info', data)
            logger.info(f"Updated health info for customer {customer_id}")
            return True
            
        except Exception as e:
            logger.error(f"Error updating health info: {e}")
            return False
    
    def add_examination_record(self, customer_id: int, 
                              exam_data: Dict[str, Any]) -> Optional[int]:
        """
        Add new examination record
        
        Args:
            customer_id: Customer database ID
            exam_data: Examination data
            
        Returns:
            Record ID if successful
        """
        try:
            data = {
                'customer_id': customer_id,
                'examination_type': exam_data['exam_type'],
                'examination_date': exam_data['date'],
                'results_summary': exam_data.get('summary', ''),
                'abnormal_findings': json.dumps(
                    exam_data.get('abnormal_findings', []), 
                    ensure_ascii=False
                ),
                'doctor_recommendations': exam_data.get('recommendations', ''),
                'follow_up_required': exam_data.get('follow_up_required', False),
                'follow_up_date': exam_data.get('follow_up_date'),
                'report_file_path': exam_data.get('report_path'),
                'created_at': datetime.now().isoformat()
            }
            
            record_id = self.db.insert('examination_records', data)
            
            # Create reminder if follow-up required
            if data['follow_up_required'] and data['follow_up_date']:
                self.create_follow_up_reminder(
                    customer_id, 
                    data['follow_up_date'],
                    exam_data['exam_type']
                )
            
            logger.info(f"Added examination record {record_id} for customer {customer_id}")
            return record_id
            
        except Exception as e:
            logger.error(f"Error adding examination record: {e}")
            return None
    
    def create_follow_up_reminder(self, customer_id: int, 
                                 follow_up_date: str, 
                                 exam_type: str):
        """Create automatic follow-up reminder"""
        try:
            # Calculate reminder date
            follow_up = datetime.fromisoformat(follow_up_date)
            reminder_date = follow_up - timedelta(days=self.reminder_days_before)
            
            # Create reminder
            reminder_data = {
                'customer_id': customer_id,
                'reminder_type': 'follow_up',
                'reminder_message': f'您需要進行{exam_type}複檢，預約日期：{follow_up_date}',
                'scheduled_date': reminder_date.date().isoformat(),
                'status': 'pending',
                'created_at': datetime.now().isoformat()
            }
            
            self.db.insert('auto_reminders', reminder_data)
            logger.info(f"Created follow-up reminder for customer {customer_id}")
            
        except Exception as e:
            logger.error(f"Error creating reminder: {e}")
    
    def check_examination_due(self, customer_id: int, 
                             exam_type: str) -> Dict[str, Any]:
        """
        Check if examination is due
        
        Args:
            customer_id: Customer database ID
            exam_type: Type of examination
            
        Returns:
            Due status and details
        """
        try:
            # Get last examination of this type
            query = """
                SELECT examination_date 
                FROM examination_records
                WHERE customer_id = ?
                AND examination_type = ?
                ORDER BY examination_date DESC
                LIMIT 1
            """
            
            last_exam = self.db.execute_one(query, (customer_id, exam_type))
            
            if not last_exam:
                return {
                    'is_due': True,
                    'reason': 'no_previous_record',
                    'message': f'建議您進行{exam_type}檢查'
                }
            
            # Calculate days since last exam
            last_date = datetime.fromisoformat(last_exam['examination_date'])
            days_elapsed = (datetime.now() - last_date).days
            
            if days_elapsed > self.exam_validity_days:
                return {
                    'is_due': True,
                    'reason': 'expired',
                    'days_overdue': days_elapsed - self.exam_validity_days,
                    'last_exam_date': last_exam['examination_date'],
                    'message': f'您上次{exam_type}檢查已過期{days_elapsed - self.exam_validity_days}天'
                }
            else:
                return {
                    'is_due': False,
                    'days_remaining': self.exam_validity_days - days_elapsed,
                    'last_exam_date': last_exam['examination_date'],
                    'message': f'您的{exam_type}檢查仍有效，還有{self.exam_validity_days - days_elapsed}天'
                }
                
        except Exception as e:
            logger.error(f"Error checking examination due: {e}")
            return {'is_due': True, 'reason': 'error', 'message': '無法確認檢查狀態'}
    
    def get_health_summary(self, customer_id: int) -> str:
        """Generate health summary for customer"""
        profile = self.get_health_profile(customer_id)
        
        summary_parts = []
        
        # Chronic conditions
        if profile['health_conditions']:
            latest = profile['health_conditions'][0]
            if latest.get('chronic_diseases'):
                diseases = json.loads(latest['chronic_diseases'])
                if diseases:
                    summary_parts.append(f"慢性病: {', '.join(diseases)}")
        
        # Current medications
        if profile['medications']:
            unique_meds = list(set(profile['medications']))
            summary_parts.append(f"用藥: {', '.join(unique_meds[:3])}")
            if len(unique_meds) > 3:
                summary_parts.append(f"等{len(unique_meds)}種")
        
        # Allergies
        if profile['allergies']:
            unique_allergies = list(set(profile['allergies']))
            summary_parts.append(f"過敏: {', '.join(unique_allergies)}")
        
        # Recent examinations
        if profile['examination_history']:
            recent_exam = profile['examination_history'][0]
            summary_parts.append(
                f"最近檢查: {recent_exam['exam_type']} ({recent_exam['date']})"
            )
        
        # Upcoming reminders
        if profile['upcoming_reminders']:
            next_reminder = profile['upcoming_reminders'][0]
            summary_parts.append(
                f"下次提醒: {next_reminder['scheduled_date']}"
            )
        
        if summary_parts:
            return ' | '.join(summary_parts)
        else:
            return '暫無健康記錄'
    
    def search_by_condition(self, condition: str, 
                           limit: int = 20) -> List[Dict[str, Any]]:
        """Search customers by health condition"""
        try:
            query = """
                SELECT DISTINCT 
                    cp.id, cp.whatsapp_id, cp.name_zh, cp.name_en,
                    chi.health_conditions, chi.chronic_diseases,
                    chi.current_medications, chi.known_allergies
                FROM customer_profiles cp
                JOIN customer_health_info chi ON cp.id = chi.customer_id
                WHERE chi.health_conditions LIKE ?
                OR chi.chronic_diseases LIKE ?
                OR chi.current_medications LIKE ?
                ORDER BY chi.recorded_at DESC
                LIMIT ?
            """
            
            search_term = f'%{condition}%'
            results = []
            
            for row in self.db.execute(query, 
                                      (search_term, search_term, search_term, limit)):
                results.append({
                    'customer_id': row['id'],
                    'whatsapp_id': row['whatsapp_id'],
                    'name': row['name_zh'] or row['name_en'],
                    'conditions': row['health_conditions'],
                    'chronic_diseases': json.loads(row['chronic_diseases'] or '[]'),
                    'medications': json.loads(row['current_medications'] or '[]'),
                    'allergies': json.loads(row['known_allergies'] or '[]')
                })
            
            return results
            
        except Exception as e:
            logger.error(f"Error searching by condition: {e}")
            return []
    
    def get_statistics(self) -> Dict[str, Any]:
        """Get health tracking statistics"""
        try:
            stats = {}
            
            # Customers with health records
            health_count = self.db.execute_one(
                "SELECT COUNT(DISTINCT customer_id) as count FROM customer_health_info"
            )
            stats['customers_with_health_records'] = health_count['count']
            
            # Total examination records
            exam_count = self.db.execute_one(
                "SELECT COUNT(*) as count FROM examination_records"
            )
            stats['total_examination_records'] = exam_count['count']
            
            # Pending reminders
            reminder_count = self.db.execute_one(
                "SELECT COUNT(*) as count FROM auto_reminders WHERE status = 'pending'"
            )
            stats['pending_reminders'] = reminder_count['count']
            
            # Common conditions
            condition_query = """
                SELECT chronic_diseases, COUNT(*) as count
                FROM customer_health_info
                WHERE chronic_diseases IS NOT NULL
                GROUP BY chronic_diseases
                ORDER BY count DESC
                LIMIT 5
            """
            
            common_conditions = []
            for row in self.db.execute(condition_query):
                if row['chronic_diseases']:
                    diseases = json.loads(row['chronic_diseases'])
                    for disease in diseases:
                        common_conditions.append(disease)
            
            # Count occurrences
            from collections import Counter
            condition_counts = Counter(common_conditions)
            stats['common_conditions'] = dict(condition_counts.most_common(5))
            
            return stats
            
        except Exception as e:
            logger.error(f"Error getting health statistics: {e}")
            return {}

# Example usage
if __name__ == "__main__":
    tracker = HealthTracker()
    
    # Test health operations
    test_customer_id = 1
    
    # Update health info
    health_data = {
        'conditions': '高血壓，糖尿病',
        'chronic_diseases': ['高血壓', '糖尿病'],
        'medications': ['美托洛爾', '二甲雙胍'],
        'allergies': ['花生', '海鮮'],
        'emergency_contact_name': '陳太太',
        'emergency_contact_phone': '98765432'
    }
    
    success = tracker.update_health_info(test_customer_id, health_data)
    print(f"Health info updated: {success}")
    
    # Add examination record
    exam_data = {
        'exam_type': '年度體檢',
        'date': datetime.now().date().isoformat(),
        'summary': '整體健康狀況良好',
        'abnormal_findings': ['血壓偏高', '血糖略高'],
        'recommendations': '注意飲食，定期監測血壓血糖',
        'follow_up_required': True,
        'follow_up_date': (datetime.now() + timedelta(days=90)).date().isoformat()
    }
    
    exam_id = tracker.add_examination_record(test_customer_id, exam_data)
    print(f"Examination record added: {exam_id}")
    
    # Get health profile
    profile = tracker.get_health_profile(test_customer_id)
    print(f"Health profile: {profile}")
    
    # Get health summary
    summary = tracker.get_health_summary(test_customer_id)
    print(f"Health summary: {summary}")
    
    # Get statistics
    stats = tracker.get_statistics()
    print(f"Health statistics: {stats}")