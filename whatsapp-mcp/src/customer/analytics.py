"""
Customer analytics and business intelligence
Provides insights from customer interactions and data
"""

import logging
from typing import Dict, Any, List, Tuple
from datetime import datetime, timedelta
from collections import Counter, defaultdict

from ..core import get_config, get_db_manager

logger = logging.getLogger(__name__)

class CustomerAnalytics:
    """Generate customer insights and business analytics"""
    
    def __init__(self):
        """Initialize analytics engine"""
        self.config = get_config()
        self.db = get_db_manager()
        
        logger.info("Customer analytics initialized")
    
    def get_conversation_analytics(self, days: int = 30) -> Dict[str, Any]:
        """
        Analyze conversation patterns
        
        Args:
            days: Number of days to analyze
            
        Returns:
            Analytics data
        """
        cutoff_date = (datetime.now() - timedelta(days=days)).isoformat()
        
        analytics = {
            'total_conversations': 0,
            'unique_customers': 0,
            'messages_per_day': {},
            'peak_hours': {},
            'response_time_avg': 0,
            'conversation_topics': {},
            'customer_sentiment': {
                'positive': 0,
                'neutral': 0,
                'negative': 0
            }
        }
        
        try:
            # Total messages and unique customers
            message_stats = self.db.execute_one("""
                SELECT 
                    COUNT(*) as total_messages,
                    COUNT(DISTINCT chat_jid) as unique_customers
                FROM messages
                WHERE timestamp >= ?
            """, (cutoff_date,))
            
            analytics['total_conversations'] = message_stats['total_messages']
            analytics['unique_customers'] = message_stats['unique_customers']
            
            # Messages per day
            daily_query = """
                SELECT 
                    DATE(timestamp) as msg_date,
                    COUNT(*) as count
                FROM messages
                WHERE timestamp >= ?
                GROUP BY DATE(timestamp)
                ORDER BY msg_date
            """
            
            for row in self.db.execute(daily_query, (cutoff_date,)):
                analytics['messages_per_day'][row['msg_date']] = row['count']
            
            # Peak hours analysis
            hour_query = """
                SELECT 
                    strftime('%H', timestamp) as hour,
                    COUNT(*) as count
                FROM messages
                WHERE timestamp >= ?
                GROUP BY strftime('%H', timestamp)
                ORDER BY count DESC
            """
            
            for row in self.db.execute(hour_query, (cutoff_date,)):
                analytics['peak_hours'][f"{row['hour']}:00"] = row['count']
            
            # Conversation topics (based on keywords)
            analytics['conversation_topics'] = self._analyze_topics(cutoff_date)
            
            # Response time analysis
            analytics['response_time_avg'] = self._calculate_avg_response_time(cutoff_date)
            
        except Exception as e:
            logger.error(f"Error in conversation analytics: {e}")
        
        return analytics
    
    def _analyze_topics(self, cutoff_date: str) -> Dict[str, int]:
        """Analyze conversation topics from messages"""
        # Topic keywords
        topic_keywords = {
            'pricing': ['價錢', '幾錢', '收費', 'price', 'cost', 'fee', '多少錢'],
            'appointment': ['預約', '幾時', '時間', 'appointment', 'book', 'schedule', '安排'],
            'examination': ['檢查', '體檢', '驗', 'check', 'exam', 'test', '身體檢查'],
            'report': ['報告', '結果', 'report', 'result', '化驗'],
            'health_concern': ['痛', '不適', '病', 'pain', 'sick', '不舒服', '唔舒服'],
            'follow_up': ['覆診', '跟進', 'follow up', '複查', '再檢查']
        }
        
        topic_counts = defaultdict(int)
        
        try:
            # Get recent messages
            messages = self.db.execute("""
                SELECT content FROM messages
                WHERE timestamp >= ?
                AND content IS NOT NULL
                LIMIT 10000
            """, (cutoff_date,))
            
            for msg in messages:
                content = msg['content'].lower()
                for topic, keywords in topic_keywords.items():
                    if any(keyword in content for keyword in keywords):
                        topic_counts[topic] += 1
            
        except Exception as e:
            logger.error(f"Error analyzing topics: {e}")
        
        return dict(topic_counts)
    
    def _calculate_avg_response_time(self, cutoff_date: str) -> float:
        """Calculate average response time in minutes"""
        try:
            # This is a simplified calculation
            # In real implementation, would track actual response pairs
            query = """
                SELECT AVG(
                    CAST((julianday(m2.timestamp) - julianday(m1.timestamp)) * 24 * 60 AS REAL)
                ) as avg_minutes
                FROM messages m1
                JOIN messages m2 ON m1.chat_jid = m2.chat_jid
                WHERE m1.timestamp >= ?
                AND m2.timestamp > m1.timestamp
                AND m2.timestamp <= datetime(m1.timestamp, '+1 hour')
                AND m1.is_from_me = 0
                AND m2.is_from_me = 1
            """
            
            result = self.db.execute_one(query, (cutoff_date,))
            return result['avg_minutes'] or 0
            
        except Exception as e:
            logger.error(f"Error calculating response time: {e}")
            return 0
    
    def get_customer_segmentation(self) -> Dict[str, Any]:
        """Segment customers based on behavior and value"""
        segments = {
            'by_status': {},
            'by_spending': {
                'high_value': [],
                'medium_value': [],
                'low_value': []
            },
            'by_engagement': {
                'highly_engaged': [],
                'moderately_engaged': [],
                'low_engaged': []
            },
            'by_completeness': {
                'complete': [],
                'partial': [],
                'minimal': []
            }
        }
        
        try:
            # Get all customers with stats
            customer_query = """
                SELECT 
                    cp.*,
                    COUNT(DISTINCT m.id) as message_count,
                    MAX(m.timestamp) as last_message
                FROM customer_profiles cp
                LEFT JOIN messages m ON cp.whatsapp_id = m.chat_jid
                GROUP BY cp.id
            """
            
            customers = []
            for row in self.db.execute(customer_query):
                customers.append(dict(row))
            
            # Segment by status
            status_counts = Counter(c['customer_status'] for c in customers)
            segments['by_status'] = dict(status_counts)
            
            # Segment by spending
            for customer in customers:
                spending = customer.get('total_spending', 0)
                if spending >= self.config.customer.vip_spending_threshold:
                    segments['by_spending']['high_value'].append({
                        'id': customer['whatsapp_id'],
                        'name': customer.get('name_zh') or customer.get('name_en'),
                        'spending': spending
                    })
                elif spending > 0:
                    segments['by_spending']['medium_value'].append({
                        'id': customer['whatsapp_id'],
                        'name': customer.get('name_zh') or customer.get('name_en'),
                        'spending': spending
                    })
                else:
                    segments['by_spending']['low_value'].append({
                        'id': customer['whatsapp_id'],
                        'name': customer.get('name_zh') or customer.get('name_en'),
                        'spending': spending
                    })
            
            # Segment by engagement
            for customer in customers:
                msg_count = customer.get('message_count', 0)
                if msg_count > 50:
                    segments['by_engagement']['highly_engaged'].append({
                        'id': customer['whatsapp_id'],
                        'messages': msg_count
                    })
                elif msg_count > 10:
                    segments['by_engagement']['moderately_engaged'].append({
                        'id': customer['whatsapp_id'],
                        'messages': msg_count
                    })
                else:
                    segments['by_engagement']['low_engaged'].append({
                        'id': customer['whatsapp_id'],
                        'messages': msg_count
                    })
            
            # Segment by profile completeness
            for customer in customers:
                completeness = customer.get('profile_completeness', 0)
                if completeness >= 80:
                    segments['by_completeness']['complete'].append({
                        'id': customer['whatsapp_id'],
                        'completeness': completeness
                    })
                elif completeness >= 40:
                    segments['by_completeness']['partial'].append({
                        'id': customer['whatsapp_id'],
                        'completeness': completeness
                    })
                else:
                    segments['by_completeness']['minimal'].append({
                        'id': customer['whatsapp_id'],
                        'completeness': completeness
                    })
            
            # Add counts for lists
            for category in ['by_spending', 'by_engagement', 'by_completeness']:
                for subcategory in segments[category]:
                    count = len(segments[category][subcategory])
                    segments[category][f'{subcategory}_count'] = count
                    # Keep only count for large lists
                    if count > 10:
                        segments[category][subcategory] = segments[category][subcategory][:5]
            
        except Exception as e:
            logger.error(f"Error in customer segmentation: {e}")
        
        return segments
    
    def get_business_opportunities(self) -> List[Dict[str, Any]]:
        """Identify business opportunities from customer data"""
        opportunities = []
        
        try:
            # Inactive high-value customers
            inactive_vips = self.db.execute("""
                SELECT 
                    whatsapp_id, name_zh, name_en, total_spending,
                    last_active
                FROM customer_profiles
                WHERE customer_status = 'vip'
                AND datetime(last_active) < datetime('now', '-30 days')
            """)
            
            for customer in inactive_vips:
                opportunities.append({
                    'type': 'win_back_vip',
                    'priority': 'high',
                    'customer_id': customer['whatsapp_id'],
                    'customer_name': customer['name_zh'] or customer['name_en'],
                    'value': customer['total_spending'],
                    'recommendation': '發送個人化優惠以挽回VIP客戶'
                })
            
            # Customers due for examination
            exam_due = self.db.execute("""
                SELECT DISTINCT
                    cp.whatsapp_id, cp.name_zh, cp.name_en,
                    er.examination_type,
                    MAX(er.examination_date) as last_exam
                FROM customer_profiles cp
                JOIN examination_records er ON cp.id = er.customer_id
                GROUP BY cp.id, er.examination_type
                HAVING datetime(last_exam) < datetime('now', '-365 days')
            """)
            
            for customer in exam_due:
                opportunities.append({
                    'type': 'examination_reminder',
                    'priority': 'medium',
                    'customer_id': customer['whatsapp_id'],
                    'customer_name': customer['name_zh'] or customer['name_en'],
                    'exam_type': customer['examination_type'],
                    'last_exam': customer['last_exam'],
                    'recommendation': f'提醒客戶進行年度{customer["examination_type"]}'
                })
            
            # Incomplete profiles with high engagement
            incomplete_engaged = self.db.execute("""
                SELECT 
                    cp.whatsapp_id, cp.name_zh, cp.name_en,
                    cp.profile_completeness,
                    COUNT(m.id) as message_count
                FROM customer_profiles cp
                JOIN messages m ON cp.whatsapp_id = m.chat_jid
                WHERE cp.profile_completeness < 50
                GROUP BY cp.id
                HAVING COUNT(m.id) > 20
            """)
            
            for customer in incomplete_engaged:
                opportunities.append({
                    'type': 'profile_completion',
                    'priority': 'low',
                    'customer_id': customer['whatsapp_id'],
                    'customer_name': customer['name_zh'] or customer['name_en'],
                    'completeness': customer['profile_completeness'],
                    'engagement': customer['message_count'],
                    'recommendation': '鼓勵完善個人資料以提供更好服務'
                })
            
        except Exception as e:
            logger.error(f"Error identifying opportunities: {e}")
        
        return opportunities
    
    def get_revenue_analytics(self, days: int = 30) -> Dict[str, Any]:
        """Analyze revenue and spending patterns"""
        cutoff_date = (datetime.now() - timedelta(days=days)).isoformat()
        
        analytics = {
            'total_revenue': 0,
            'average_transaction': 0,
            'revenue_by_day': {},
            'top_customers': [],
            'revenue_growth': 0
        }
        
        try:
            # Note: This assumes transaction data exists
            # In real implementation, would need proper transaction tracking
            
            # Total revenue from customer spending
            revenue_query = """
                SELECT 
                    SUM(total_spending) as total,
                    AVG(total_spending) as average,
                    COUNT(*) as customer_count
                FROM customer_profiles
                WHERE total_spending > 0
                AND last_active >= ?
            """
            
            revenue_stats = self.db.execute_one(revenue_query, (cutoff_date,))
            analytics['total_revenue'] = revenue_stats['total'] or 0
            analytics['average_transaction'] = revenue_stats['average'] or 0
            
            # Top customers by spending
            top_customers = self.db.execute("""
                SELECT 
                    whatsapp_id, name_zh, name_en, total_spending
                FROM customer_profiles
                WHERE total_spending > 0
                ORDER BY total_spending DESC
                LIMIT 10
            """)
            
            for customer in top_customers:
                analytics['top_customers'].append({
                    'id': customer['whatsapp_id'],
                    'name': customer['name_zh'] or customer['name_en'],
                    'spending': customer['total_spending']
                })
            
        except Exception as e:
            logger.error(f"Error in revenue analytics: {e}")
        
        return analytics
    
    def generate_insights_summary(self) -> str:
        """Generate executive summary of insights"""
        try:
            # Get key metrics
            conv_analytics = self.get_conversation_analytics(30)
            segmentation = self.get_customer_segmentation()
            opportunities = self.get_business_opportunities()
            
            # Build summary
            summary_parts = []
            
            # Conversation insights
            total_customers = conv_analytics['unique_customers']
            total_messages = conv_analytics['total_conversations']
            avg_response = conv_analytics['response_time_avg']
            
            summary_parts.append(
                f"過去30天：{total_customers}位客戶，{total_messages}條訊息"
            )
            
            if avg_response > 0:
                summary_parts.append(f"平均回覆時間：{avg_response:.1f}分鐘")
            
            # Top topics
            topics = conv_analytics['conversation_topics']
            if topics:
                top_topic = max(topics, key=topics.get)
                summary_parts.append(f"最熱門話題：{top_topic}")
            
            # Customer segments
            vip_count = segmentation['by_status'].get('vip', 0)
            if vip_count > 0:
                summary_parts.append(f"VIP客戶：{vip_count}位")
            
            # Opportunities
            high_priority = [o for o in opportunities if o['priority'] == 'high']
            if high_priority:
                summary_parts.append(f"高優先商機：{len(high_priority)}個")
            
            return ' | '.join(summary_parts)
            
        except Exception as e:
            logger.error(f"Error generating summary: {e}")
            return "無法生成摘要"

# Example usage
if __name__ == "__main__":
    analytics = CustomerAnalytics()
    
    # Get conversation analytics
    conv_analytics = analytics.get_conversation_analytics(30)
    print("Conversation Analytics:")
    print(f"  Total conversations: {conv_analytics['total_conversations']}")
    print(f"  Unique customers: {conv_analytics['unique_customers']}")
    print(f"  Topics: {conv_analytics['conversation_topics']}")
    
    # Get customer segmentation
    segments = analytics.get_customer_segmentation()
    print("\nCustomer Segmentation:")
    print(f"  By status: {segments['by_status']}")
    print(f"  High value customers: {segments['by_spending']['high_value_count']}")
    
    # Get business opportunities
    opportunities = analytics.get_business_opportunities()
    print(f"\nBusiness Opportunities: {len(opportunities)} found")
    for opp in opportunities[:3]:
        print(f"  - {opp['type']}: {opp['recommendation']}")
    
    # Get insights summary
    summary = analytics.generate_insights_summary()
    print(f"\nExecutive Summary: {summary}")