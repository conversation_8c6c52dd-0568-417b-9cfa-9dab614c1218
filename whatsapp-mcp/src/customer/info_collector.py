"""
Information collection state machine
Progressive collection of customer information through conversation
"""

import logging
import re
from typing import Optional, Dict, Any, List, Tuple
from datetime import datetime
from enum import Enum

from ..core import get_config, get_db_manager
from .customer_manager import CustomerManager

logger = logging.getLogger(__name__)

class CollectionState(Enum):
    """Information collection states"""
    IDLE = "idle"
    COLLECTING_NAME = "collecting_name"
    COLLECTING_GENDER = "collecting_gender"
    COLLECTING_BIRTH_DATE = "collecting_birth_date"
    COLLECTING_PHONE = "collecting_phone"
    COLLECTING_EMAIL = "collecting_email"
    COLLECTING_DISTRICT = "collecting_district"
    COLLECTING_HEALTH = "collecting_health"
    COMPLETE = "complete"

class InformationCollector:
    """Progressive information collection through conversation"""
    
    def __init__(self):
        """Initialize information collector"""
        self.config = get_config()
        self.db = get_db_manager()
        self.customer_manager = CustomerManager()
        
        # Load conversation templates
        self.templates = self._load_templates()
        
        # State tracking
        self.collection_states = {}  # whatsapp_id -> state
        
        logger.info("Information collector initialized")
    
    def _load_templates(self) -> Dict[str, Dict[str, str]]:
        """Load conversation templates from database"""
        templates = {}
        
        try:
            db_templates = self.db.get_conversation_templates()
            
            for template in db_templates:
                lang = template['language']
                category = template['category']
                
                if lang not in templates:
                    templates[lang] = {}
                
                templates[lang][category] = template['template_text']
            
            logger.info(f"Loaded {len(db_templates)} conversation templates")
            
        except Exception as e:
            logger.error(f"Error loading templates: {e}")
            # Fallback templates
            templates = {
                'zh': {
                    'greeting': '您好！我係健康檢查AI助手。',
                    'ask_name': '請問點稱呼您呢？',
                    'ask_gender': '請問您係先生定女士？',
                    'ask_birth_date': '方便提供您嘅出生日期嗎？（格式：YYYY-MM-DD）',
                    'ask_phone': '請提供您嘅聯絡電話。',
                    'ask_email': '請提供您嘅電郵地址。',
                    'ask_district': '請問您住邊區？',
                    'ask_health': '有冇任何健康問題或者長期病患需要我哋留意？',
                    'thank_you': '多謝您提供資料！'
                },
                'en': {
                    'greeting': 'Hello! I am the HealthCheck AI assistant.',
                    'ask_name': 'How may I address you?',
                    'ask_gender': 'Are you Mr. or Ms.?',
                    'ask_birth_date': 'May I have your birth date? (Format: YYYY-MM-DD)',
                    'ask_phone': 'Please provide your contact number.',
                    'ask_email': 'Please provide your email address.',
                    'ask_district': 'Which district do you live in?',
                    'ask_health': 'Do you have any health conditions we should be aware of?',
                    'thank_you': 'Thank you for providing the information!'
                }
            }
        
        return templates
    
    def get_collection_state(self, whatsapp_id: str) -> CollectionState:
        """Get current collection state for customer"""
        if whatsapp_id not in self.collection_states:
            # Check what information we already have
            profile = self.customer_manager.get_or_create_profile(whatsapp_id)
            missing_fields = self.customer_manager.get_missing_fields(whatsapp_id)
            
            if not missing_fields:
                self.collection_states[whatsapp_id] = CollectionState.COMPLETE
            else:
                self.collection_states[whatsapp_id] = CollectionState.IDLE
        
        return self.collection_states[whatsapp_id]
    
    def start_collection(self, whatsapp_id: str, language: str = 'zh') -> str:
        """
        Start information collection process
        
        Args:
            whatsapp_id: Customer WhatsApp ID
            language: Language preference
            
        Returns:
            Response message
        """
        # Get missing fields
        missing_fields = self.customer_manager.get_missing_fields(whatsapp_id)
        
        if not missing_fields:
            self.collection_states[whatsapp_id] = CollectionState.COMPLETE
            return self.templates[language].get('thank_you', 'Thank you!')
        
        # Start with first missing field
        if '中文姓名' in missing_fields or 'English name' in missing_fields:
            self.collection_states[whatsapp_id] = CollectionState.COLLECTING_NAME
            return self.templates[language].get('ask_name', 'What is your name?')
        elif '性別' in missing_fields:
            self.collection_states[whatsapp_id] = CollectionState.COLLECTING_GENDER
            return self.templates[language].get('ask_gender', 'Are you Mr. or Ms.?')
        elif '出生日期' in missing_fields:
            self.collection_states[whatsapp_id] = CollectionState.COLLECTING_BIRTH_DATE
            return self.templates[language].get('ask_birth_date', 'What is your birth date?')
        elif '電話號碼' in missing_fields:
            self.collection_states[whatsapp_id] = CollectionState.COLLECTING_PHONE
            return self.templates[language].get('ask_phone', 'What is your phone number?')
        elif '電子郵件' in missing_fields:
            self.collection_states[whatsapp_id] = CollectionState.COLLECTING_EMAIL
            return self.templates[language].get('ask_email', 'What is your email?')
        elif '居住地區' in missing_fields:
            self.collection_states[whatsapp_id] = CollectionState.COLLECTING_DISTRICT
            return self.templates[language].get('ask_district', 'Which district do you live in?')
        
        # All basic info collected
        self.collection_states[whatsapp_id] = CollectionState.COMPLETE
        return self.templates[language].get('thank_you', 'Thank you!')
    
    def process_response(self, whatsapp_id: str, message: str, 
                        language: str = 'zh') -> Tuple[str, bool]:
        """
        Process user response based on current state
        
        Args:
            whatsapp_id: Customer WhatsApp ID
            message: User's message
            language: Language preference
            
        Returns:
            Tuple of (response message, collection complete)
        """
        state = self.get_collection_state(whatsapp_id)
        
        if state == CollectionState.IDLE:
            # Not in collection mode
            return "", False
        
        if state == CollectionState.COMPLETE:
            return self.templates[language].get('thank_you', 'Thank you!'), True
        
        # Process based on state
        if state == CollectionState.COLLECTING_NAME:
            return self._process_name(whatsapp_id, message, language)
        elif state == CollectionState.COLLECTING_GENDER:
            return self._process_gender(whatsapp_id, message, language)
        elif state == CollectionState.COLLECTING_BIRTH_DATE:
            return self._process_birth_date(whatsapp_id, message, language)
        elif state == CollectionState.COLLECTING_PHONE:
            return self._process_phone(whatsapp_id, message, language)
        elif state == CollectionState.COLLECTING_EMAIL:
            return self._process_email(whatsapp_id, message, language)
        elif state == CollectionState.COLLECTING_DISTRICT:
            return self._process_district(whatsapp_id, message, language)
        elif state == CollectionState.COLLECTING_HEALTH:
            return self._process_health(whatsapp_id, message, language)
        
        return "", False
    
    def _process_name(self, whatsapp_id: str, message: str, 
                     language: str) -> Tuple[str, bool]:
        """Process name input"""
        # Detect if Chinese or English name
        has_chinese = bool(re.search(r'[\u4e00-\u9fff]', message))
        
        updates = {}
        if has_chinese:
            updates['name_zh'] = message.strip()
        else:
            updates['name_en'] = message.strip()
        
        updates['display_name'] = message.strip()
        
        self.customer_manager.update_profile(whatsapp_id, updates)
        
        # Move to next state
        return self.start_collection(whatsapp_id, language), False
    
    def _process_gender(self, whatsapp_id: str, message: str, 
                       language: str) -> Tuple[str, bool]:
        """Process gender input"""
        message_lower = message.lower()
        
        gender = None
        if any(word in message_lower for word in ['先生', 'mr', 'male', '男']):
            gender = 'M'
        elif any(word in message_lower for word in ['女士', 'ms', 'mrs', 'miss', 'female', '女']):
            gender = 'F'
        
        if gender:
            self.customer_manager.update_profile(whatsapp_id, {'gender': gender})
            return self.start_collection(whatsapp_id, language), False
        else:
            # Ask again
            return self.templates[language].get('ask_gender', 'Are you Mr. or Ms.?'), False
    
    def _process_birth_date(self, whatsapp_id: str, message: str, 
                           language: str) -> Tuple[str, bool]:
        """Process birth date input"""
        # Try various date formats
        date_patterns = [
            r'(\d{4})[-/](\d{1,2})[-/](\d{1,2})',  # YYYY-MM-DD
            r'(\d{1,2})[-/](\d{1,2})[-/](\d{4})',  # DD-MM-YYYY
            r'(\d{4})年(\d{1,2})月(\d{1,2})日',      # Chinese format
        ]
        
        birth_date = None
        for pattern in date_patterns:
            match = re.search(pattern, message)
            if match:
                try:
                    if '年' in pattern:
                        # Chinese format
                        year, month, day = match.groups()
                    elif pattern.startswith(r'(\d{4})'):
                        # YYYY first
                        year, month, day = match.groups()
                    else:
                        # DD first
                        day, month, year = match.groups()
                    
                    birth_date = f"{year}-{month:0>2}-{day:0>2}"
                    # Validate date
                    datetime.strptime(birth_date, '%Y-%m-%d')
                    break
                except:
                    continue
        
        if birth_date:
            self.customer_manager.update_profile(whatsapp_id, {'birth_date': birth_date})
            return self.start_collection(whatsapp_id, language), False
        else:
            # Ask again with format hint
            return self.templates[language].get('ask_birth_date', 
                                              'Please provide birth date (YYYY-MM-DD)'), False
    
    def _process_phone(self, whatsapp_id: str, message: str, 
                      language: str) -> Tuple[str, bool]:
        """Process phone number input"""
        # Extract digits
        phone_digits = re.sub(r'\D', '', message)
        
        # Validate Hong Kong phone number
        if len(phone_digits) == 8 and phone_digits[0] in '23456789':
            phone = phone_digits
        elif len(phone_digits) == 11 and phone_digits.startswith('852'):
            phone = phone_digits[3:]
        else:
            # Ask again
            return self.templates[language].get('ask_phone', 
                                              'Please provide valid phone number'), False
        
        self.customer_manager.update_profile(whatsapp_id, {'phone': phone})
        return self.start_collection(whatsapp_id, language), False
    
    def _process_email(self, whatsapp_id: str, message: str, 
                      language: str) -> Tuple[str, bool]:
        """Process email input"""
        # Basic email validation
        email_pattern = r'^[\w\.-]+@[\w\.-]+\.\w+$'
        
        email_match = re.search(email_pattern, message.strip())
        if email_match:
            email = email_match.group(0)
            self.customer_manager.update_profile(whatsapp_id, {'email': email})
            return self.start_collection(whatsapp_id, language), False
        else:
            # Ask again
            return self.templates[language].get('ask_email', 
                                              'Please provide valid email address'), False
    
    def _process_district(self, whatsapp_id: str, message: str, 
                         language: str) -> Tuple[str, bool]:
        """Process district input"""
        # Hong Kong districts
        hk_districts = [
            '中西區', '灣仔', '東區', '南區', '油尖旺', '深水埗', 
            '九龍城', '黃大仙', '觀塘', '葵青', '荃灣', '屯門', 
            '元朗', '北區', '大埔', '沙田', '西貢', '離島',
            'Central', 'Wan Chai', 'Eastern', 'Southern', 'Yau Tsim Mong',
            'Sham Shui Po', 'Kowloon City', 'Wong Tai Sin', 'Kwun Tong'
        ]
        
        # Find matching district
        message_lower = message.lower()
        district = None
        
        for d in hk_districts:
            if d.lower() in message_lower:
                district = d
                break
        
        if district:
            self.customer_manager.update_profile(whatsapp_id, {'district': district})
            
            # Check if we should collect health info
            if self.config.customer.collect_health_info:
                self.collection_states[whatsapp_id] = CollectionState.COLLECTING_HEALTH
                return self.templates[language].get('ask_health', 
                                                  'Any health conditions?'), False
            else:
                return self.start_collection(whatsapp_id, language), False
        else:
            # Ask again
            return self.templates[language].get('ask_district', 
                                              'Which district do you live in?'), False
    
    def _process_health(self, whatsapp_id: str, message: str, 
                       language: str) -> Tuple[str, bool]:
        """Process health information input"""
        # Save health info to separate table
        profile = self.customer_manager.get_or_create_profile(whatsapp_id)
        
        health_data = {
            'customer_id': profile['id'],
            'health_conditions': message.strip(),
            'recorded_at': datetime.now().isoformat()
        }
        
        try:
            self.db.insert('customer_health_info', health_data)
        except Exception as e:
            logger.error(f"Error saving health info: {e}")
        
        # Collection complete
        self.collection_states[whatsapp_id] = CollectionState.COMPLETE
        return self.templates[language].get('thank_you', 'Thank you!'), True
    
    def get_progress(self, whatsapp_id: str) -> Dict[str, Any]:
        """Get collection progress for customer"""
        profile = self.customer_manager.get_or_create_profile(whatsapp_id)
        missing_fields = self.customer_manager.get_missing_fields(whatsapp_id)
        state = self.get_collection_state(whatsapp_id)
        
        return {
            'current_state': state.value,
            'profile_completeness': profile.get('profile_completeness', 0),
            'missing_fields': missing_fields,
            'fields_collected': {
                'name': bool(profile.get('name_zh') or profile.get('name_en')),
                'gender': bool(profile.get('gender')),
                'birth_date': bool(profile.get('birth_date')),
                'phone': bool(profile.get('phone')),
                'email': bool(profile.get('email')),
                'district': bool(profile.get('district'))
            }
        }
    
    def reset_collection(self, whatsapp_id: str):
        """Reset collection state for customer"""
        if whatsapp_id in self.collection_states:
            del self.collection_states[whatsapp_id]
        logger.info(f"Reset collection state for {whatsapp_id}")

# Example usage
if __name__ == "__main__":
    collector = InformationCollector()
    
    # Test collection flow
    test_id = "<EMAIL>"
    
    # Start collection
    response = collector.start_collection(test_id)
    print(f"Bot: {response}")
    
    # Simulate user responses
    test_responses = [
        "陳大文",
        "先生",
        "1985-06-15",
        "98765432",
        "<EMAIL>",
        "中環",
        "冇特別健康問題"
    ]
    
    for user_input in test_responses:
        print(f"User: {user_input}")
        response, complete = collector.process_response(test_id, user_input)
        print(f"Bot: {response}")
        if complete:
            print("Collection complete!")
            break
    
    # Check progress
    progress = collector.get_progress(test_id)
    print(f"Progress: {progress}")