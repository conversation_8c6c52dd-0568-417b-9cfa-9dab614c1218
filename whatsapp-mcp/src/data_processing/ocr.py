"""
Unified OCR processor with mock and real implementations
"""

import os
import json
import logging
import random
from typing import Optional, List, Dict, Any, <PERSON><PERSON>
from datetime import datetime
from abc import ABC, abstractmethod

from ..common.config import config
from ..common.database import db_manager

logger = logging.getLogger(__name__)


class BaseOCRProcessor(ABC):
    """Abstract base class for OCR processors"""
    
    @abstractmethod
    def extract_text_from_image(self, image_path: str) -> Tuple[str, float]:
        """Extract text from image and return (text, confidence)"""
        pass
    
    def analyze_medical_content(self, text: str) -> Dict[str, Any]:
        """Analyze medical-related content"""
        analysis = {
            'report_type': 'unknown',
            'key_findings': [],
            'abnormal_values': [],
            'date_found': None,
            'patient_info': {}
        }
        
        # Check report type
        if any(keyword in text for keyword in ['血液檢查', '血液報告', 'Blood Test', 'CBC']):
            analysis['report_type'] = 'blood_test'
        elif any(keyword in text for keyword in ['X光', 'X-Ray', '胸部', 'Chest']):
            analysis['report_type'] = 'xray'
        elif any(keyword in text for keyword in ['心電圖', 'ECG', 'EKG']):
            analysis['report_type'] = 'ecg'
        elif any(keyword in text for keyword in ['體檢報告', '健康檢查', 'Health Check']):
            analysis['report_type'] = 'general_checkup'
        
        # Find dates
        import re
        date_patterns = [
            r'\d{4}[-/]\d{1,2}[-/]\d{1,2}',
            r'\d{1,2}[-/]\d{1,2}[-/]\d{4}',
        ]
        
        for pattern in date_patterns:
            matches = re.findall(pattern, text)
            if matches:
                analysis['date_found'] = matches[0]
                break
        
        # Find abnormal values
        abnormal_keywords = ['異常', '偏高', '偏低', 'Abnormal', 'High', 'Low', '↑', '↓', 'H', 'L']
        lines = text.split('\n')
        
        for line in lines:
            if any(keyword in line for keyword in abnormal_keywords):
                analysis['abnormal_values'].append(line.strip())
        
        # Extract patient info
        name_patterns = [
            r'姓名[:：]\s*([^\s]+)',
            r'Name[:：]\s*([^\s]+)',
            r'患者[:：]\s*([^\s]+)'
        ]
        
        for pattern in name_patterns:
            match = re.search(pattern, text)
            if match:
                analysis['patient_info']['name'] = match.group(1)
                break
        
        return analysis
    
    async def process_pending_images(self) -> List[Dict[str, Any]]:
        """Process pending OCR images"""
        # Query for pending images
        pending_images = db_manager.execute_query("""
            SELECT id, message_id, chat_jid, file_path, metadata
            FROM media_content
            WHERE media_type = 'image'
            AND extraction_method = 'DOWNLOAD_ONLY'
            AND extracted_text IS NULL
            LIMIT 10
        """)
        
        results = []
        
        for row in pending_images:
            record_id = row['id']
            message_id = row['message_id']
            file_path = row['file_path']
            metadata_json = row['metadata']
            
            logger.info(f"Processing image {message_id}...")
            
            # Extract text
            text, confidence = self.extract_text_from_image(file_path)
            
            if text:
                # Analyze content
                analysis = self.analyze_medical_content(text)
                
                # Update database
                metadata = json.loads(metadata_json or '{}')
                metadata['ocr_analysis'] = analysis
                
                db_manager.execute_update("""
                    UPDATE media_content
                    SET extracted_text = ?,
                        extraction_method = ?,
                        extraction_confidence = ?,
                        metadata = ?,
                        processed_at = ?
                    WHERE id = ?
                """, (
                    text,
                    'OCR' if not config.ocr.use_mock else 'MOCK_OCR',
                    confidence,
                    json.dumps(metadata),
                    datetime.now().isoformat(),
                    record_id
                ))
                
                results.append({
                    'message_id': message_id,
                    'success': True,
                    'text_length': len(text),
                    'confidence': confidence,
                    'report_type': analysis['report_type']
                })
                
                logger.info(f"Successfully extracted {len(text)} characters from {message_id}")
            else:
                results.append({
                    'message_id': message_id,
                    'success': False,
                    'error': 'No text extracted'
                })
        
        return results


class MockOCRProcessor(BaseOCRProcessor):
    """Mock OCR processor for testing"""
    
    def __init__(self):
        self.sample_texts = [
            """
森仁醫健中心
健康檢查報告
日期: 2025-07-11
姓名: 張小明
年齡: 35
性別: 男

血液檢查結果:
白血球 WBC: 7.2 x10^9/L (正常)
紅血球 RBC: 4.8 x10^12/L (正常)
血紅蛋白 Hb: 145 g/L (正常)
血小板 PLT: 220 x10^9/L (正常)
血糖 GLU: 6.8 mmol/L ↑ (偏高)

建議: 注意飲食，定期檢查血糖
            """,
            """
HK REN Healthcare Center
Medical Report
Date: 2025-07-10
Name: LEE Siu Ming
Age: 42

Liver Function Test:
ALT: 45 U/L (Normal)
AST: 38 U/L (Normal)
ALP: 110 U/L ↑ (High)
Total Bilirubin: 18 umol/L (Normal)

Recommendation: Follow up in 3 months
            """,
            """
X光檢查報告
檢查部位: 胸部正位
檢查日期: 2025-07-09

發現:
1. 心臟大小正常
2. 肺野清晰
3. 無明顯異常發現

結論: 胸部X光檢查正常
醫生簽名: Dr. Wong
            """
        ]
    
    def extract_text_from_image(self, image_path: str) -> Tuple[str, float]:
        """Mock text extraction"""
        text = random.choice(self.sample_texts)
        confidence = random.uniform(0.85, 0.98)
        logger.info(f"Mock OCR: Extracted text from {image_path}")
        return text, confidence


class RealOCRProcessor(BaseOCRProcessor):
    """Real OCR processor using EasyOCR"""
    
    def __init__(self):
        self.reader = None
        self._initialize_ocr()
    
    def _initialize_ocr(self):
        """Initialize OCR engine"""
        try:
            import easyocr
            logger.info("Initializing EasyOCR with Chinese and English support...")
            self.reader = easyocr.Reader(config.ocr.languages, gpu=config.ocr.gpu_enabled)
            logger.info("OCR engine initialized successfully")
        except ImportError as e:
            logger.error(f"Failed to import OCR libraries: {e}")
            logger.error("Please install: pip install easyocr pillow")
            raise
        except Exception as e:
            logger.error(f"Failed to initialize OCR: {e}")
            raise
    
    def extract_text_from_image(self, image_path: str) -> Tuple[str, float]:
        """Extract text from image using EasyOCR"""
        if not self.reader:
            logger.error("OCR reader not initialized")
            return "", 0.0
        
        if not os.path.exists(image_path):
            logger.error(f"Image file not found: {image_path}")
            return "", 0.0
        
        try:
            logger.info(f"Processing image: {image_path}")
            results = self.reader.readtext(image_path)
            
            text_parts = []
            confidence_scores = []
            
            for (bbox, text, confidence) in results:
                text_parts.append(text)
                confidence_scores.append(confidence)
            
            full_text = "\n".join(text_parts)
            avg_confidence = sum(confidence_scores) / len(confidence_scores) if confidence_scores else 0.0
            
            logger.info(f"Extracted {len(text_parts)} text segments with average confidence: {avg_confidence:.2f}")
            
            return full_text, avg_confidence
            
        except Exception as e:
            logger.error(f"Error extracting text from image: {e}")
            return "", 0.0


class OCRProcessor:
    """Factory class for OCR processors"""
    
    _instance: Optional[BaseOCRProcessor] = None
    
    @classmethod
    def get_instance(cls) -> BaseOCRProcessor:
        """Get OCR processor instance based on configuration"""
        if cls._instance is None:
            if not config.ocr.enabled:
                logger.info("OCR is disabled in configuration")
                cls._instance = MockOCRProcessor()
            elif config.ocr.use_mock:
                logger.info("Using mock OCR processor")
                cls._instance = MockOCRProcessor()
            else:
                logger.info("Using real OCR processor")
                try:
                    cls._instance = RealOCRProcessor()
                except Exception as e:
                    logger.error(f"Failed to initialize real OCR, falling back to mock: {e}")
                    cls._instance = MockOCRProcessor()
        
        return cls._instance


def search_extracted_content(query: str) -> List[Dict[str, Any]]:
    """Search in extracted OCR content"""
    results = db_manager.execute_query("""
        SELECT 
            mc.message_id,
            mc.chat_jid,
            mc.extracted_text,
            mc.metadata,
            mc.processed_at,
            c.name as chat_name
        FROM media_content mc
        JOIN messages m ON mc.message_id = m.id
        JOIN chats c ON m.chat_jid = c.jid
        WHERE mc.extracted_text LIKE ?
        ORDER BY mc.processed_at DESC
        LIMIT 20
    """, (f'%{query}%',))
    
    formatted_results = []
    for row in results:
        formatted_results.append({
            'message_id': row['message_id'],
            'chat_jid': row['chat_jid'],
            'chat_name': row['chat_name'],
            'text_preview': row['extracted_text'][:200] + '...' if len(row['extracted_text']) > 200 else row['extracted_text'],
            'metadata': json.loads(row['metadata'] or '{}'),
            'processed_at': row['processed_at']
        })
    
    return formatted_results