"""
Media processor for WhatsApp messages
Handles automatic download and organization of media files
"""

import asyncio
import logging
import hashlib
import shutil
from datetime import datetime
from pathlib import Path
from typing import Optional, List, Dict, Any

from ..core import get_config, get_db_manager, get_api_client

logger = logging.getLogger(__name__)

class MediaProcessor:
    """Process media messages from WhatsApp"""
    
    def __init__(self):
        """Initialize media processor"""
        self.config = get_config()
        self.db = get_db_manager()
        self.api = get_api_client()
        
        # Get configuration values
        self.storage_path = self.config.storage.full_path
        self.batch_size = self.config.media_processing.batch_size
        self.scan_interval = self.config.media_processing.scan_interval
        self.download_delay = self.config.media_processing.download_delay
        self.error_retry_delay = self.config.media_processing.error_retry_delay
        self.use_date_folders = self.config.storage.use_date_folders
        self.max_filename_length = self.config.storage.max_filename_length
        
        # Processing state
        self.processing_queue = asyncio.Queue()
        self.processed_messages = set()
        self._load_processed_messages()
        
        # Ensure storage directory exists
        self.storage_path.mkdir(parents=True, exist_ok=True)
        logger.info(f"Media processor initialized. Storage: {self.storage_path}")
    
    def _load_processed_messages(self):
        """Load already processed message IDs"""
        try:
            # Query processed messages from database
            results = self.db.execute(
                "SELECT DISTINCT message_id FROM media_content"
            )
            self.processed_messages = {row['message_id'] for row in results}
            logger.info(f"Loaded {len(self.processed_messages)} processed messages")
        except Exception as e:
            logger.error(f"Error loading processed messages: {e}")
            self.processed_messages = set()
    
    async def scan_unprocessed_media(self) -> List[Dict[str, Any]]:
        """Scan for unprocessed media messages"""
        try:
            # Use database manager to get unprocessed media
            unprocessed = self.db.get_unprocessed_media(limit=self.batch_size)
            
            # Filter out already processing
            unprocessed = [
                msg for msg in unprocessed 
                if msg['id'] not in self.processed_messages
            ]
            
            logger.info(f"Found {len(unprocessed)} unprocessed media messages")
            return unprocessed
            
        except Exception as e:
            logger.error(f"Error scanning unprocessed media: {e}")
            return []
    
    async def download_media(self, message_id: str, chat_jid: str) -> Optional[Path]:
        """Download media file using API client"""
        try:
            # Use API client to download
            remote_path = self.api.download_media(message_id, chat_jid)
            
            if remote_path and Path(remote_path).exists():
                # Organize the file
                local_path = await self._organize_media_file(
                    remote_path, chat_jid, message_id
                )
                logger.info(f"Downloaded media {message_id} to {local_path}")
                return local_path
            else:
                logger.error(f"Failed to download media {message_id}")
                return None
                
        except Exception as e:
            logger.error(f"Error downloading media {message_id}: {e}")
            return None
    
    async def _organize_media_file(self, source_path: str, 
                                 chat_jid: str, message_id: str) -> Path:
        """Organize media file into structured directory"""
        try:
            source = Path(source_path)
            
            # Create directory structure
            if self.use_date_folders:
                date_str = datetime.now().strftime("%Y-%m")
                chat_dir = self._sanitize_filename(chat_jid)
                target_dir = self.storage_path / date_str / chat_dir
            else:
                target_dir = self.storage_path / self._sanitize_filename(chat_jid)
            
            target_dir.mkdir(parents=True, exist_ok=True)
            
            # Generate unique filename
            file_ext = source.suffix
            file_hash = hashlib.md5(
                f"{message_id}{chat_jid}".encode()
            ).hexdigest()[:8]
            
            target_filename = f"{message_id}_{file_hash}{file_ext}"
            target_path = target_dir / target_filename
            
            # Copy file
            shutil.copy2(source, target_path)
            
            return target_path
            
        except Exception as e:
            logger.error(f"Error organizing media file: {e}")
            # Return original path as fallback
            return Path(source_path)
    
    def _sanitize_filename(self, filename: str) -> str:
        """Sanitize filename for safe storage"""
        # Replace unsafe characters
        safe_chars = "".join(
            c if c.isalnum() or c in ".-_" else "_" 
            for c in filename
        )
        # Limit length
        return safe_chars[:self.max_filename_length]
    
    async def process_media_message(self, message: Dict[str, Any]) -> bool:
        """Process a single media message"""
        message_id = message['id']
        chat_jid = message['chat_jid']
        
        # Skip if already processed
        if message_id in self.processed_messages:
            logger.debug(f"Message {message_id} already processed")
            return True
        
        logger.info(f"Processing media message {message_id}")
        
        # Download media
        media_path = await self.download_media(message_id, chat_jid)
        if not media_path:
            logger.error(f"Failed to download media for {message_id}")
            return False
        
        # Save to database
        try:
            self.db.save_media_content(
                message_id=message_id,
                chat_jid=chat_jid,
                media_type=message['media_type'],
                filename=message['filename'],
                file_path=str(media_path),
                metadata={
                    'sender': message['sender'],
                    'timestamp': message['timestamp'],
                    'chat_name': message['chat_name']
                }
            )
            
            self.processed_messages.add(message_id)
            logger.info(f"Successfully processed media message {message_id}")
            return True
            
        except Exception as e:
            logger.error(f"Error saving media record: {e}")
            return False
    
    async def process_batch(self, messages: List[Dict[str, Any]]) -> Dict[str, int]:
        """Process a batch of media messages"""
        results = {
            'success': 0,
            'failed': 0,
            'skipped': 0
        }
        
        for message in messages:
            if message['id'] in self.processed_messages:
                results['skipped'] += 1
                continue
            
            success = await self.process_media_message(message)
            if success:
                results['success'] += 1
            else:
                results['failed'] += 1
            
            # Add delay between downloads
            if self.download_delay > 0:
                await asyncio.sleep(self.download_delay)
        
        return results
    
    async def start_auto_processing(self):
        """Start automatic processing loop"""
        if not self.config.is_feature_enabled('auto_download_media'):
            logger.info("Auto download media feature is disabled")
            return
        
        logger.info("Starting automatic media processing...")
        
        while True:
            try:
                # Scan for unprocessed media
                unprocessed = await self.scan_unprocessed_media()
                
                if unprocessed:
                    logger.info(f"Processing {len(unprocessed)} media messages...")
                    results = await self.process_batch(unprocessed)
                    logger.info(f"Batch processing complete: {results}")
                
                # Wait before next scan
                await asyncio.sleep(self.scan_interval)
                
            except Exception as e:
                logger.error(f"Error in auto processing loop: {e}")
                # Wait longer after error
                await asyncio.sleep(self.error_retry_delay)
    
    async def process_specific_message(self, message_id: str, 
                                     chat_jid: str) -> bool:
        """Process a specific media message"""
        # Get message details from database
        query = """
            SELECT m.*, c.name as chat_name
            FROM messages m
            JOIN chats c ON m.chat_jid = c.jid
            WHERE m.id = ? AND m.chat_jid = ?
        """
        
        row = self.db.execute_one(query, (message_id, chat_jid))
        if not row:
            logger.error(f"Message {message_id} not found")
            return False
        
        if not row['media_type']:
            logger.error(f"Message {message_id} is not a media message")
            return False
        
        message = {
            'id': row['id'],
            'chat_jid': row['chat_jid'],
            'sender': row['sender'],
            'media_type': row['media_type'],
            'filename': row['filename'],
            'timestamp': row['timestamp'],
            'chat_name': row['chat_name']
        }
        
        return await self.process_media_message(message)
    
    def get_statistics(self) -> Dict[str, Any]:
        """Get processing statistics"""
        try:
            # Total messages
            total_media = self.db.execute_one(
                "SELECT COUNT(*) as count FROM messages WHERE media_type IS NOT NULL"
            )['count']
            
            # Processed messages
            processed = len(self.processed_messages)
            
            # By media type
            type_stats = {}
            results = self.db.execute("""
                SELECT media_type, COUNT(*) as count 
                FROM media_content 
                GROUP BY media_type
            """)
            for row in results:
                type_stats[row['media_type']] = row['count']
            
            return {
                'total_media_messages': total_media,
                'processed_messages': processed,
                'pending_messages': total_media - processed,
                'by_type': type_stats,
                'storage_path': str(self.storage_path)
            }
            
        except Exception as e:
            logger.error(f"Error getting statistics: {e}")
            return {}

# Convenience functions
async def process_all_media():
    """Process all unprocessed media"""
    processor = MediaProcessor()
    await processor.start_auto_processing()

async def process_single_media(message_id: str, chat_jid: str):
    """Process a single media message"""
    processor = MediaProcessor()
    return await processor.process_specific_message(message_id, chat_jid)

# Example usage
if __name__ == "__main__":
    # Test media processor
    async def test():
        processor = MediaProcessor()
        
        # Get statistics
        stats = processor.get_statistics()
        print("Media Processing Statistics:")
        print(f"  Total media: {stats.get('total_media_messages', 0)}")
        print(f"  Processed: {stats.get('processed_messages', 0)}")
        print(f"  Pending: {stats.get('pending_messages', 0)}")
        print(f"  Storage: {stats.get('storage_path', 'N/A')}")
        
        # Process a few messages
        unprocessed = await processor.scan_unprocessed_media()
        if unprocessed:
            print(f"\nProcessing first 3 messages...")
            results = await processor.process_batch(unprocessed[:3])
            print(f"Results: {results}")
    
    asyncio.run(test())