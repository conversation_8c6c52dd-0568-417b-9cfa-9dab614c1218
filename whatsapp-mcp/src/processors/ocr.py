"""
OCR processor for extracting text from images
Real implementation using EasyOCR
"""

import logging
import re
import json
from typing import Optional, List, Dict, Any, Tuple
from datetime import datetime
from pathlib import Path

from ..core import get_config, get_db_manager

logger = logging.getLogger(__name__)

# Lazy imports for OCR libraries
_ocr_available = False
_reader = None

def _init_ocr():
    """Initialize OCR engine (lazy loading)"""
    global _ocr_available, _reader
    
    if _reader is not None:
        return _reader
    
    config = get_config()
    
    # Check if OCR is enabled
    if not config.is_feature_enabled('ocr_processing'):
        logger.info("OCR processing is disabled in configuration")
        return None
    
    try:
        import easyocr
        
        languages = config.ocr.languages
        use_gpu = config.ocr.use_gpu
        
        logger.info(f"Initializing EasyOCR with languages: {languages}, GPU: {use_gpu}")
        _reader = easyocr.Reader(languages, gpu=use_gpu)
        _ocr_available = True
        logger.info("OCR engine initialized successfully")
        
        return _reader
        
    except ImportError:
        logger.error("EasyOCR not installed. Install with: pip install easyocr")
        _ocr_available = False
        return None
    except Exception as e:
        logger.error(f"Failed to initialize OCR: {e}")
        _ocr_available = False
        return None

class OCRProcessor:
    """Process images to extract text content"""
    
    def __init__(self):
        """Initialize OCR processor"""
        self.config = get_config()
        self.db = get_db_manager()
        
        # Configuration
        self.batch_size = self.config.ocr.batch_size
        self.confidence_threshold = self.config.ocr.confidence_threshold
        
        # Initialize OCR engine (lazy)
        self.reader = None
        
        logger.info("OCR processor initialized")
    
    def is_available(self) -> bool:
        """Check if OCR is available"""
        if self.reader is None:
            self.reader = _init_ocr()
        return self.reader is not None
    
    def extract_text_from_image(self, image_path: str) -> Tuple[str, float]:
        """
        Extract text from an image file
        
        Args:
            image_path: Path to image file
            
        Returns:
            Tuple of (extracted text, average confidence)
        """
        # Check if OCR is available
        if not self.is_available():
            logger.warning("OCR not available, cannot extract text")
            return "", 0.0
        
        # Validate file exists
        if not Path(image_path).exists():
            logger.error(f"Image file not found: {image_path}")
            return "", 0.0
        
        try:
            # Read text from image
            logger.info(f"Processing image: {image_path}")
            results = self.reader.readtext(image_path)
            
            if not results:
                logger.info("No text found in image")
                return "", 0.0
            
            # Extract text and confidence scores
            text_parts = []
            confidence_scores = []
            
            for (bbox, text, confidence) in results:
                # Filter by confidence threshold
                if confidence >= self.confidence_threshold:
                    text_parts.append(text)
                    confidence_scores.append(confidence)
            
            # Combine text
            full_text = "\n".join(text_parts)
            
            # Calculate average confidence
            avg_confidence = (
                sum(confidence_scores) / len(confidence_scores) 
                if confidence_scores else 0.0
            )
            
            logger.info(
                f"Extracted {len(text_parts)} text segments "
                f"with average confidence: {avg_confidence:.2f}"
            )
            
            return full_text, avg_confidence
            
        except Exception as e:
            logger.error(f"Error extracting text from image: {e}")
            return "", 0.0
    
    def analyze_medical_content(self, text: str) -> Dict[str, Any]:
        """
        Analyze medical content in extracted text
        
        Args:
            text: Extracted text
            
        Returns:
            Analysis results
        """
        analysis = {
            'report_type': 'unknown',
            'key_findings': [],
            'abnormal_values': [],
            'date_found': None,
            'patient_info': {},
            'medical_terms': []
        }
        
        if not text:
            return analysis
        
        # Detect report type
        report_keywords = {
            'blood_test': ['血液檢查', '血液報告', 'Blood Test', 'CBC', 
                          '血常規', 'Blood Report'],
            'xray': ['X光', 'X-Ray', '胸部', 'Chest', '放射'],
            'ecg': ['心電圖', 'ECG', 'EKG', '心臟'],
            'ultrasound': ['超聲波', 'Ultrasound', 'B超'],
            'ct_scan': ['CT', '電腦掃描', 'Computed Tomography'],
            'mri': ['MRI', '磁力共振', 'Magnetic Resonance'],
            'general_checkup': ['體檢報告', '健康檢查', 'Health Check', 
                               'Medical Examination', '身體檢查']
        }
        
        for report_type, keywords in report_keywords.items():
            if any(keyword.lower() in text.lower() for keyword in keywords):
                analysis['report_type'] = report_type
                break
        
        # Extract dates
        date_patterns = [
            r'\d{4}[-/]\d{1,2}[-/]\d{1,2}',  # YYYY-MM-DD or YYYY/MM/DD
            r'\d{1,2}[-/]\d{1,2}[-/]\d{4}',  # DD-MM-YYYY or DD/MM/YYYY
            r'\d{4}年\d{1,2}月\d{1,2}日',     # Chinese date format
        ]
        
        for pattern in date_patterns:
            matches = re.findall(pattern, text)
            if matches:
                analysis['date_found'] = matches[0]
                break
        
        # Find abnormal indicators
        abnormal_indicators = [
            r'[↑↓]',  # Arrow indicators
            r'\b[HL]\b',  # H/L markers
            r'異常|偏高|偏低|Abnormal|High|Low',
            r'陽性|Positive',
            r'需要?複查|Follow.?up|Recheck'
        ]
        
        lines = text.split('\n')
        for line in lines:
            if any(re.search(pattern, line) for pattern in abnormal_indicators):
                analysis['abnormal_values'].append(line.strip())
        
        # Extract patient information
        name_patterns = [
            (r'姓名[:：]\s*([^\s,，]+)', 'name_zh'),
            (r'Name[:：]\s*([^\s,，]+)', 'name_en'),
            (r'患者[:：]\s*([^\s,，]+)', 'name_zh'),
            (r'Patient[:：]\s*([^\s,，]+)', 'name_en')
        ]
        
        for pattern, field in name_patterns:
            match = re.search(pattern, text, re.IGNORECASE)
            if match:
                analysis['patient_info'][field] = match.group(1).strip()
        
        # Extract medical terms
        medical_terms = [
            'WBC', 'RBC', 'Hb', 'PLT', 'ALT', 'AST', 'Cholesterol',
            '白血球', '紅血球', '血紅蛋白', '血小板', '膽固醇',
            'Glucose', '血糖', 'Creatinine', '肌酐'
        ]
        
        for term in medical_terms:
            if term in text:
                # Try to find associated value
                value_pattern = rf'{term}[:\s]*([0-9.]+[^\s]*)'
                match = re.search(value_pattern, text)
                if match:
                    analysis['medical_terms'].append({
                        'term': term,
                        'value': match.group(1)
                    })
        
        return analysis
    
    async def process_pending_images(self) -> List[Dict[str, Any]]:
        """Process images pending OCR extraction"""
        if not self.is_available():
            logger.warning("OCR not available")
            return []
        
        try:
            # Get pending images from database
            query = """
                SELECT id, message_id, chat_jid, file_path, metadata
                FROM media_content
                WHERE media_type = 'image'
                AND extraction_method = 'DOWNLOAD_ONLY'
                AND extracted_text IS NULL
                LIMIT ?
            """
            
            pending = self.db.execute(query, (self.batch_size,))
            results = []
            
            for row in pending:
                record_id = row['id']
                message_id = row['message_id']
                file_path = row['file_path']
                
                logger.info(f"Processing image {message_id}...")
                
                # Extract text
                text, confidence = self.extract_text_from_image(file_path)
                
                if text:
                    # Analyze content
                    analysis = self.analyze_medical_content(text)
                    
                    # Update metadata
                    metadata = self.db.json_decode(row['metadata']) or {}
                    metadata['ocr_analysis'] = analysis
                    
                    # Update database
                    self.db.update(
                        'media_content',
                        {
                            'extracted_text': text,
                            'extraction_method': 'OCR',
                            'extraction_confidence': confidence,
                            'metadata': self.db.json_encode(metadata),
                            'processed_at': datetime.now().isoformat()
                        },
                        'id = ?',
                        (record_id,)
                    )
                    
                    results.append({
                        'message_id': message_id,
                        'success': True,
                        'text_length': len(text),
                        'confidence': confidence,
                        'report_type': analysis['report_type'],
                        'abnormal_count': len(analysis['abnormal_values'])
                    })
                    
                    logger.info(
                        f"Successfully extracted {len(text)} characters "
                        f"from {message_id} (type: {analysis['report_type']})"
                    )
                else:
                    # Mark as processed even if no text found
                    self.db.update(
                        'media_content',
                        {
                            'extraction_method': 'OCR_NO_TEXT',
                            'processed_at': datetime.now().isoformat()
                        },
                        'id = ?',
                        (record_id,)
                    )
                    
                    results.append({
                        'message_id': message_id,
                        'success': False,
                        'error': 'No text found'
                    })
            
            return results
            
        except Exception as e:
            logger.error(f"Error processing pending images: {e}")
            return []
    
    def search_content(self, query: str, limit: int = 20) -> List[Dict[str, Any]]:
        """Search extracted text content"""
        try:
            query_sql = """
                SELECT 
                    mc.message_id,
                    mc.chat_jid,
                    mc.extracted_text,
                    mc.metadata,
                    mc.processed_at,
                    mc.extraction_confidence,
                    c.name as chat_name
                FROM media_content mc
                JOIN messages m ON mc.message_id = m.id
                JOIN chats c ON m.chat_jid = c.jid
                WHERE mc.extracted_text LIKE ?
                AND mc.extraction_method = 'OCR'
                ORDER BY mc.processed_at DESC
                LIMIT ?
            """
            
            results = []
            for row in self.db.execute(query_sql, (f'%{query}%', limit)):
                metadata = self.db.json_decode(row['metadata']) or {}
                
                results.append({
                    'message_id': row['message_id'],
                    'chat_jid': row['chat_jid'],
                    'chat_name': row['chat_name'],
                    'confidence': row['extraction_confidence'],
                    'processed_at': row['processed_at'],
                    'text_preview': row['extracted_text'][:200] + '...' 
                                   if len(row['extracted_text']) > 200 
                                   else row['extracted_text'],
                    'analysis': metadata.get('ocr_analysis', {})
                })
            
            return results
            
        except Exception as e:
            logger.error(f"Error searching content: {e}")
            return []
    
    def get_statistics(self) -> Dict[str, Any]:
        """Get OCR processing statistics"""
        try:
            stats = {}
            
            # Total images
            total_images = self.db.execute_one(
                "SELECT COUNT(*) as count FROM media_content WHERE media_type = 'image'"
            )['count']
            
            # Processed with OCR
            ocr_processed = self.db.execute_one(
                "SELECT COUNT(*) as count FROM media_content WHERE extraction_method = 'OCR'"
            )['count']
            
            # No text found
            no_text = self.db.execute_one(
                "SELECT COUNT(*) as count FROM media_content WHERE extraction_method = 'OCR_NO_TEXT'"
            )['count']
            
            # Pending
            pending = self.db.execute_one("""
                SELECT COUNT(*) as count FROM media_content 
                WHERE media_type = 'image' AND extraction_method = 'DOWNLOAD_ONLY'
            """)['count']
            
            # Report types
            report_types = {}
            results = self.db.execute("""
                SELECT 
                    json_extract(metadata, '$.ocr_analysis.report_type') as report_type,
                    COUNT(*) as count
                FROM media_content
                WHERE extraction_method = 'OCR'
                AND json_extract(metadata, '$.ocr_analysis.report_type') IS NOT NULL
                GROUP BY report_type
            """)
            
            for row in results:
                report_types[row['report_type']] = row['count']
            
            stats = {
                'total_images': total_images,
                'ocr_processed': ocr_processed,
                'no_text_found': no_text,
                'pending_ocr': pending,
                'report_types': report_types,
                'ocr_available': self.is_available()
            }
            
            return stats
            
        except Exception as e:
            logger.error(f"Error getting statistics: {e}")
            return {}

# Example usage
if __name__ == "__main__":
    import asyncio
    
    async def test():
        processor = OCRProcessor()
        
        # Check availability
        if processor.is_available():
            print("OCR is available")
            
            # Get statistics
            stats = processor.get_statistics()
            print("\nOCR Statistics:")
            print(f"  Total images: {stats.get('total_images', 0)}")
            print(f"  OCR processed: {stats.get('ocr_processed', 0)}")
            print(f"  No text found: {stats.get('no_text_found', 0)}")
            print(f"  Pending: {stats.get('pending_ocr', 0)}")
            
            # Process some images
            print("\nProcessing pending images...")
            results = await processor.process_pending_images()
            for result in results:
                if result['success']:
                    print(f"  ✓ {result['message_id']}: {result['text_length']} chars, "
                          f"confidence: {result['confidence']:.2f}")
                else:
                    print(f"  ✗ {result['message_id']}: {result.get('error', 'Failed')}")
        else:
            print("OCR is not available. Please install easyocr.")
    
    asyncio.run(test())