"""
Unified configuration management for WhatsApp MCP system
"""

import os
import json
from pathlib import Path
from typing import Dict, Any, Optional
from dataclasses import dataclass


@dataclass
class WhatsAppConfig:
    """WhatsApp bridge configuration"""
    api_base_url: str = "http://localhost:8080/api"
    db_path: str = ""
    bridge_port: int = 8080
    
    def __post_init__(self):
        if not self.db_path:
            self.db_path = str(Path(__file__).parent.parent.parent / "go-bridge" / "store" / "messages.db")


@dataclass
class MCPConfig:
    """MCP server configuration"""
    server_name: str = "whatsapp"
    transport: str = "stdio"


@dataclass
class OCRConfig:
    """OCR configuration"""
    enabled: bool = True
    use_mock: bool = False
    languages: list = None
    gpu_enabled: bool = False
    
    def __post_init__(self):
        if self.languages is None:
            self.languages = ['ch_tra', 'en']  # Traditional Chinese and English


@dataclass
class DatabaseConfig:
    """Database configuration"""
    connection_timeout: int = 30
    enable_foreign_keys: bool = True
    journal_mode: str = "WAL"  # Write-Ahead Logging for better concurrency


class ConfigManager:
    """Central configuration manager"""
    
    _instance: Optional['ConfigManager'] = None
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super(ConfigManager, cls).__new__(cls)
            cls._instance._initialized = False
        return cls._instance
    
    def __init__(self):
        if self._initialized:
            return
            
        self._initialized = True
        self.base_path = Path(__file__).parent.parent.parent
        self.config_file = self.base_path / "config" / "config.json"
        
        # Initialize default configurations
        self.whatsapp = WhatsAppConfig()
        self.mcp = MCPConfig()
        self.ocr = OCRConfig()
        self.database = DatabaseConfig()
        
        # Load configurations
        self._load_config_file()
        self._load_env_vars()
    
    def _load_config_file(self):
        """Load configuration from JSON file if exists"""
        if self.config_file.exists():
            try:
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    config_data = json.load(f)
                    
                # Update WhatsApp config
                if 'whatsapp' in config_data:
                    for key, value in config_data['whatsapp'].items():
                        if hasattr(self.whatsapp, key):
                            setattr(self.whatsapp, key, value)
                
                # Update MCP config
                if 'mcp' in config_data:
                    for key, value in config_data['mcp'].items():
                        if hasattr(self.mcp, key):
                            setattr(self.mcp, key, value)
                
                # Update OCR config
                if 'ocr' in config_data:
                    for key, value in config_data['ocr'].items():
                        if hasattr(self.ocr, key):
                            setattr(self.ocr, key, value)
                            
                # Update Database config
                if 'database' in config_data:
                    for key, value in config_data['database'].items():
                        if hasattr(self.database, key):
                            setattr(self.database, key, value)
                            
            except Exception as e:
                print(f"Warning: Failed to load config file: {e}")
    
    def _load_env_vars(self):
        """Load configuration from environment variables"""
        # WhatsApp configuration
        if api_url := os.getenv('WHATSAPP_API_URL'):
            self.whatsapp.api_base_url = api_url
        
        if db_path := os.getenv('WHATSAPP_DB_PATH'):
            self.whatsapp.db_path = db_path
            
        if bridge_port := os.getenv('WHATSAPP_BRIDGE_PORT'):
            self.whatsapp.bridge_port = int(bridge_port)
        
        # OCR configuration
        if ocr_enabled := os.getenv('OCR_ENABLED'):
            self.ocr.enabled = ocr_enabled.lower() == 'true'
            
        if ocr_mock := os.getenv('OCR_USE_MOCK'):
            self.ocr.use_mock = ocr_mock.lower() == 'true'
    
    def get_media_storage_path(self, account_id: Optional[str] = None) -> Path:
        """Get media storage path for specific account"""
        if account_id:
            return self.base_path / "accounts" / account_id / "media"
        return self.base_path / "media"
    
    def get_customer_data_path(self, account_id: Optional[str] = None) -> Path:
        """Get customer data path for specific account"""
        if account_id:
            return self.base_path / "accounts" / account_id / "customer_data"
        return self.base_path / "customer_data"
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert configuration to dictionary"""
        return {
            'whatsapp': {
                'api_base_url': self.whatsapp.api_base_url,
                'db_path': self.whatsapp.db_path,
                'bridge_port': self.whatsapp.bridge_port
            },
            'mcp': {
                'server_name': self.mcp.server_name,
                'transport': self.mcp.transport
            },
            'ocr': {
                'enabled': self.ocr.enabled,
                'use_mock': self.ocr.use_mock,
                'languages': self.ocr.languages,
                'gpu_enabled': self.ocr.gpu_enabled
            },
            'database': {
                'connection_timeout': self.database.connection_timeout,
                'enable_foreign_keys': self.database.enable_foreign_keys,
                'journal_mode': self.database.journal_mode
            }
        }
    
    def save_config(self):
        """Save current configuration to file"""
        self.config_file.parent.mkdir(parents=True, exist_ok=True)
        with open(self.config_file, 'w', encoding='utf-8') as f:
            json.dump(self.to_dict(), f, indent=2, ensure_ascii=False)


# Singleton instance
config = ConfigManager()