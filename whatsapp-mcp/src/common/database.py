"""
Unified database management for WhatsApp MCP system
"""

import sqlite3
import logging
from contextlib import contextmanager
from typing import Optional, List, Dict, Any, Tuple
from pathlib import Path
import threading

from .config import config

logger = logging.getLogger(__name__)


class DatabaseManager:
    """Centralized database management with connection pooling"""
    
    _instance: Optional['DatabaseManager'] = None
    _lock = threading.Lock()
    
    def __new__(cls):
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super(DatabaseManager, cls).__new__(cls)
                    cls._instance._initialized = False
        return cls._instance
    
    def __init__(self):
        if self._initialized:
            return
            
        self._initialized = True
        self.db_path = config.whatsapp.db_path
        self._local = threading.local()
        
    def _get_connection(self) -> sqlite3.Connection:
        """Get thread-local database connection"""
        if not hasattr(self._local, 'connection') or self._local.connection is None:
            self._local.connection = self._create_connection()
        return self._local.connection
    
    def _create_connection(self) -> sqlite3.Connection:
        """Create a new database connection with proper settings"""
        conn = sqlite3.connect(
            self.db_path,
            timeout=config.database.connection_timeout,
            check_same_thread=False
        )
        
        # Enable foreign keys
        if config.database.enable_foreign_keys:
            conn.execute("PRAGMA foreign_keys = ON")
        
        # Set journal mode
        conn.execute(f"PRAGMA journal_mode = {config.database.journal_mode}")
        
        # Row factory for dict-like access
        conn.row_factory = sqlite3.Row
        
        return conn
    
    @contextmanager
    def get_connection(self):
        """Context manager for database connections"""
        conn = self._get_connection()
        try:
            yield conn
        except Exception as e:
            conn.rollback()
            logger.error(f"Database error: {e}")
            raise
        finally:
            # Don't close the connection, keep it in thread-local storage
            pass
    
    def execute_query(self, query: str, params: Optional[Tuple] = None) -> List[Dict[str, Any]]:
        """Execute a SELECT query and return results as list of dicts"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            
            if params:
                cursor.execute(query, params)
            else:
                cursor.execute(query)
            
            columns = [desc[0] for desc in cursor.description]
            return [dict(zip(columns, row)) for row in cursor.fetchall()]
    
    def execute_update(self, query: str, params: Optional[Tuple] = None) -> int:
        """Execute an INSERT/UPDATE/DELETE query and return affected rows"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            
            if params:
                cursor.execute(query, params)
            else:
                cursor.execute(query)
            
            conn.commit()
            return cursor.rowcount
    
    def execute_many(self, query: str, params_list: List[Tuple]) -> int:
        """Execute multiple INSERT/UPDATE queries efficiently"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            cursor.executemany(query, params_list)
            conn.commit()
            return cursor.rowcount
    
    def table_exists(self, table_name: str) -> bool:
        """Check if a table exists in the database"""
        query = """
            SELECT name FROM sqlite_master 
            WHERE type='table' AND name=?
        """
        result = self.execute_query(query, (table_name,))
        return len(result) > 0
    
    def get_table_info(self, table_name: str) -> List[Dict[str, Any]]:
        """Get information about table columns"""
        query = f"PRAGMA table_info({table_name})"
        return self.execute_query(query)
    
    def close_all_connections(self):
        """Close all thread-local connections"""
        if hasattr(self._local, 'connection') and self._local.connection:
            self._local.connection.close()
            self._local.connection = None


# Singleton instance
db_manager = DatabaseManager()


# Helper functions for common operations
def get_message(message_id: str, chat_jid: str) -> Optional[Dict[str, Any]]:
    """Get a single message by ID and chat JID"""
    query = """
        SELECT * FROM messages 
        WHERE id = ? AND chat_jid = ?
    """
    results = db_manager.execute_query(query, (message_id, chat_jid))
    return results[0] if results else None


def get_chat(chat_jid: str) -> Optional[Dict[str, Any]]:
    """Get chat information by JID"""
    query = "SELECT * FROM chats WHERE jid = ?"
    results = db_manager.execute_query(query, (chat_jid,))
    return results[0] if results else None


def get_messages_by_chat(chat_jid: str, limit: int = 20, offset: int = 0) -> List[Dict[str, Any]]:
    """Get messages for a specific chat"""
    query = """
        SELECT * FROM messages 
        WHERE chat_jid = ?
        ORDER BY timestamp DESC
        LIMIT ? OFFSET ?
    """
    return db_manager.execute_query(query, (chat_jid, limit, offset))


def search_messages(query_text: str, limit: int = 20) -> List[Dict[str, Any]]:
    """Search messages by content"""
    query = """
        SELECT m.*, c.name as chat_name
        FROM messages m
        JOIN chats c ON m.chat_jid = c.jid
        WHERE m.content LIKE ?
        ORDER BY m.timestamp DESC
        LIMIT ?
    """
    return db_manager.execute_query(query, (f'%{query_text}%', limit))


def update_chat_name(chat_jid: str, name: str) -> bool:
    """Update chat name"""
    query = "UPDATE chats SET name = ? WHERE jid = ?"
    affected = db_manager.execute_update(query, (name, chat_jid))
    return affected > 0