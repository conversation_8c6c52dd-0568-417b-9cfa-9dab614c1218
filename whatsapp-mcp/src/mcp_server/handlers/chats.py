"""
Chat-related MCP tool handlers
"""

from typing import List, Dict, Any, Optional
from ...common.database import db_manager


def list_chats_handler(
    query: Optional[str] = None,
    limit: int = 20,
    page: int = 0,
    include_last_message: bool = True,
    sort_by: str = "last_active"
) -> List[Dict[str, Any]]:
    """Get WhatsApp chats matching specified criteria.
    
    Args:
        query: Optional search term to filter chats by name or JID
        limit: Maximum number of chats to return (default 20)
        page: Page number for pagination (default 0)
        include_last_message: Whether to include the last message in each chat (default True)
        sort_by: Field to sort results by, either "last_active" or "name" (default "last_active")
    """
    # Build query
    conditions = []
    params = []
    
    if query:
        conditions.append("(c.name LIKE ? OR c.jid LIKE ?)")
        params.extend([f'%{query}%', f'%{query}%'])
    
    where_clause = " AND ".join(conditions) if conditions else "1=1"
    
    # Determine sort order
    order_by = "c.last_message_time DESC" if sort_by == "last_active" else "c.name ASC"
    
    offset = page * limit
    
    # Main query
    if include_last_message:
        sql_query = f"""
            SELECT 
                c.jid,
                c.name,
                c.last_message_time,
                m.content as last_message_content,
                m.sender as last_message_sender,
                m.is_from_me as last_message_is_from_me,
                m.timestamp as last_message_timestamp,
                CASE
                    WHEN c.jid LIKE '%@g.us' THEN 'group'
                    ELSE 'direct'
                END as chat_type
            FROM chats c
            LEFT JOIN messages m ON m.chat_jid = c.jid 
                AND m.timestamp = (
                    SELECT MAX(timestamp) 
                    FROM messages 
                    WHERE chat_jid = c.jid
                )
            WHERE {where_clause}
            ORDER BY {order_by}
            LIMIT ? OFFSET ?
        """
    else:
        sql_query = f"""
            SELECT 
                c.jid,
                c.name,
                c.last_message_time,
                CASE
                    WHEN c.jid LIKE '%@g.us' THEN 'group'
                    ELSE 'direct'
                END as chat_type
            FROM chats c
            WHERE {where_clause}
            ORDER BY {order_by}
            LIMIT ? OFFSET ?
        """
    
    params.extend([limit, offset])
    results = db_manager.execute_query(sql_query, params)
    
    # Format results
    chats = []
    for row in results:
        chat = {
            'jid': row['jid'],
            'name': row['name'] or row['jid'],
            'chat_type': row['chat_type'],
            'last_message_time': row['last_message_time']
        }
        
        if include_last_message and row.get('last_message_content'):
            chat['last_message'] = {
                'content': row['last_message_content'],
                'sender': row['last_message_sender'],
                'is_from_me': bool(row['last_message_is_from_me']),
                'timestamp': row['last_message_timestamp']
            }
        
        chats.append(chat)
    
    return chats


def get_chat_handler(chat_jid: str, include_last_message: bool = True) -> Dict[str, Any]:
    """Get WhatsApp chat metadata by JID.
    
    Args:
        chat_jid: The JID of the chat to retrieve
        include_last_message: Whether to include the last message (default True)
    """
    # Get chat info
    chat_query = """
        SELECT 
            jid,
            name,
            last_message_time,
            CASE
                WHEN jid LIKE '%@g.us' THEN 'group'
                ELSE 'direct'
            END as chat_type
        FROM chats
        WHERE jid = ?
    """
    
    results = db_manager.execute_query(chat_query, (chat_jid,))
    
    if not results:
        return {
            'error': 'Chat not found',
            'chat_jid': chat_jid
        }
    
    chat = results[0]
    
    # Build response
    response = {
        'jid': chat['jid'],
        'name': chat['name'] or chat['jid'],
        'chat_type': chat['chat_type'],
        'last_message_time': chat['last_message_time']
    }
    
    # Add last message if requested
    if include_last_message:
        message_query = """
            SELECT 
                content,
                sender,
                is_from_me,
                timestamp,
                media_type
            FROM messages
            WHERE chat_jid = ?
            ORDER BY timestamp DESC
            LIMIT 1
        """
        
        message_results = db_manager.execute_query(message_query, (chat_jid,))
        
        if message_results:
            msg = message_results[0]
            response['last_message'] = {
                'content': msg['content'],
                'sender': msg['sender'],
                'is_from_me': bool(msg['is_from_me']),
                'timestamp': msg['timestamp'],
                'media_type': msg['media_type']
            }
    
    # Add participant count for groups
    if chat['chat_type'] == 'group':
        participant_query = """
            SELECT COUNT(DISTINCT sender) as participant_count
            FROM messages
            WHERE chat_jid = ?
        """
        
        participant_results = db_manager.execute_query(participant_query, (chat_jid,))
        if participant_results:
            response['participant_count'] = participant_results[0]['participant_count']
    
    return response


def get_direct_chat_handler(sender_phone_number: str) -> Dict[str, Any]:
    """Get WhatsApp chat metadata by sender phone number.
    
    Args:
        sender_phone_number: The phone number to search for
    """
    # Format JID from phone number
    if not sender_phone_number.endswith('@s.whatsapp.net'):
        jid = f"{sender_phone_number}@s.whatsapp.net"
    else:
        jid = sender_phone_number
    
    # Use the get_chat_handler with the formatted JID
    return get_chat_handler(jid, include_last_message=True)