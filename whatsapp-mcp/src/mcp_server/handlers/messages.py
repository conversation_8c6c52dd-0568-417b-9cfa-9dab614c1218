"""
Message-related MCP tool handlers
"""

from typing import List, Dict, Any, Optional
from datetime import datetime
from ...common.database import db_manager
from ...whatsapp_bridge.client import whatsapp_client


def list_messages_handler(
    after: Optional[str] = None,
    before: Optional[str] = None,
    sender_phone_number: Optional[str] = None,
    chat_jid: Optional[str] = None,
    query: Optional[str] = None,
    limit: int = 20,
    page: int = 0,
    include_context: bool = True,
    context_before: int = 1,
    context_after: int = 1
) -> List[Dict[str, Any]]:
    """Get WhatsApp messages matching specified criteria with optional context.
    
    Args:
        after: Optional ISO-8601 formatted string to only return messages after this date
        before: Optional ISO-8601 formatted string to only return messages before this date
        sender_phone_number: Optional phone number to filter messages by sender
        chat_jid: Optional chat JID to filter messages by chat
        query: Optional search term to filter messages by content
        limit: Maximum number of messages to return (default 20)
        page: Page number for pagination (default 0)
        include_context: Whether to include messages before and after matches (default True)
        context_before: Number of messages to include before each match (default 1)
        context_after: Number of messages to include after each match (default 1)
    """
    # Build query conditions
    conditions = []
    params = []
    
    if after:
        conditions.append("m.timestamp > ?")
        params.append(after)
    
    if before:
        conditions.append("m.timestamp < ?")
        params.append(before)
    
    if sender_phone_number:
        sender_jid = f"{sender_phone_number}@s.whatsapp.net"
        conditions.append("m.sender = ?")
        params.append(sender_jid)
    
    if chat_jid:
        conditions.append("m.chat_jid = ?")
        params.append(chat_jid)
    
    if query:
        conditions.append("m.content LIKE ?")
        params.append(f'%{query}%')
    
    where_clause = " AND ".join(conditions) if conditions else "1=1"
    
    # Main query
    offset = page * limit
    sql_query = f"""
        SELECT 
            m.id,
            m.chat_jid,
            m.sender,
            m.content,
            m.timestamp,
            m.is_from_me,
            m.media_type,
            m.filename,
            c.name as chat_name
        FROM messages m
        JOIN chats c ON m.chat_jid = c.jid
        WHERE {where_clause}
        ORDER BY m.timestamp DESC
        LIMIT ? OFFSET ?
    """
    
    params.extend([limit, offset])
    messages = db_manager.execute_query(sql_query, params)
    
    # Format results
    formatted_messages = []
    for msg in messages:
        formatted_msg = {
            'id': msg['id'],
            'chat_jid': msg['chat_jid'],
            'chat_name': msg['chat_name'] or msg['chat_jid'],
            'sender': msg['sender'],
            'content': msg['content'],
            'timestamp': msg['timestamp'],
            'is_from_me': bool(msg['is_from_me']),
            'media_type': msg['media_type'],
            'filename': msg['filename']
        }
        
        # Add context if requested
        if include_context and query:
            context = _get_message_context_data(
                msg['id'], 
                msg['chat_jid'],
                context_before,
                context_after
            )
            formatted_msg['context'] = context
        
        formatted_messages.append(formatted_msg)
    
    return formatted_messages


def get_message_context_handler(
    message_id: str,
    before: int = 5,
    after: int = 5
) -> Dict[str, Any]:
    """Get context around a specific WhatsApp message.
    
    Args:
        message_id: The ID of the message to get context for
        before: Number of messages to include before the target message (default 5)
        after: Number of messages to include after the target message (default 5)
    """
    # First find the message to get its chat_jid and timestamp
    target_query = """
        SELECT id, chat_jid, timestamp, content, sender, is_from_me
        FROM messages
        WHERE id = ?
        LIMIT 1
    """
    
    target_results = db_manager.execute_query(target_query, (message_id,))
    
    if not target_results:
        return {
            'error': 'Message not found',
            'message_id': message_id
        }
    
    target_msg = target_results[0]
    
    # Get messages before
    before_query = """
        SELECT id, content, sender, timestamp, is_from_me
        FROM messages
        WHERE chat_jid = ? AND timestamp < ?
        ORDER BY timestamp DESC
        LIMIT ?
    """
    
    before_messages = db_manager.execute_query(
        before_query, 
        (target_msg['chat_jid'], target_msg['timestamp'], before)
    )
    
    # Get messages after
    after_query = """
        SELECT id, content, sender, timestamp, is_from_me
        FROM messages
        WHERE chat_jid = ? AND timestamp > ?
        ORDER BY timestamp ASC
        LIMIT ?
    """
    
    after_messages = db_manager.execute_query(
        after_query,
        (target_msg['chat_jid'], target_msg['timestamp'], after)
    )
    
    # Format the context
    return {
        'target_message': {
            'id': target_msg['id'],
            'content': target_msg['content'],
            'sender': target_msg['sender'],
            'timestamp': target_msg['timestamp'],
            'is_from_me': bool(target_msg['is_from_me'])
        },
        'before': [
            {
                'id': msg['id'],
                'content': msg['content'],
                'sender': msg['sender'],
                'timestamp': msg['timestamp'],
                'is_from_me': bool(msg['is_from_me'])
            }
            for msg in reversed(before_messages)
        ],
        'after': [
            {
                'id': msg['id'],
                'content': msg['content'],
                'sender': msg['sender'],
                'timestamp': msg['timestamp'],
                'is_from_me': bool(msg['is_from_me'])
            }
            for msg in after_messages
        ]
    }


def send_message_handler(recipient: str, message: str) -> Dict[str, Any]:
    """Send a WhatsApp message to a person or group. For group chats use the JID.

    Args:
        recipient: The recipient - either a phone number with country code but no + or other symbols,
                 or a JID (e.g., "<EMAIL>" or a group JID like "<EMAIL>")
        message: The message text to send
    
    Returns:
        A dictionary containing success status and a status message
    """
    # Validate input
    if not recipient:
        return {
            "success": False,
            "message": "Recipient must be provided"
        }
    
    if not message:
        return {
            "success": False,
            "message": "Message must be provided"
        }
    
    # Send message via WhatsApp client
    success, status_message = whatsapp_client.send_message(recipient, message)
    
    return {
        "success": success,
        "message": status_message
    }


def send_file_handler(recipient: str, media_path: str) -> Dict[str, Any]:
    """Send a file such as a picture, raw audio, video or document via WhatsApp to the specified recipient. For group messages use the JID.
    
    Args:
        recipient: The recipient - either a phone number with country code but no + or other symbols,
                 or a JID (e.g., "<EMAIL>" or a group JID like "<EMAIL>")
        media_path: The absolute path to the media file to send (image, video, document)
    
    Returns:
        A dictionary containing success status and a status message
    """
    # Validate input
    if not recipient:
        return {
            "success": False,
            "message": "Recipient must be provided"
        }
    
    if not media_path:
        return {
            "success": False,
            "message": "Media path must be provided"
        }
    
    # Send file via WhatsApp client
    success, status_message = whatsapp_client.send_file(recipient, media_path)
    
    return {
        "success": success,
        "message": status_message
    }


def send_audio_message_handler(recipient: str, media_path: str) -> Dict[str, Any]:
    """Send any audio file as a WhatsApp audio message to the specified recipient. For group messages use the JID. If it errors due to ffmpeg not being installed, use send_file instead.
    
    Args:
        recipient: The recipient - either a phone number with country code but no + or other symbols,
                 or a JID (e.g., "<EMAIL>" or a group JID like "<EMAIL>")
        media_path: The absolute path to the audio file to send (will be converted to Opus .ogg if it's not a .ogg file)
    
    Returns:
        A dictionary containing success status and a status message
    """
    # Validate input
    if not recipient:
        return {
            "success": False,
            "message": "Recipient must be provided"
        }
    
    if not media_path:
        return {
            "success": False,
            "message": "Media path must be provided"
        }
    
    # Send audio via WhatsApp client
    success, status_message = whatsapp_client.send_audio(recipient, media_path)
    
    return {
        "success": success,
        "message": status_message
    }


def download_media_handler(message_id: str, chat_jid: str) -> Dict[str, Any]:
    """Download media from a WhatsApp message and get the local file path.
    
    Args:
        message_id: The ID of the message containing the media
        chat_jid: The JID of the chat containing the message
    
    Returns:
        A dictionary containing success status, a status message, and the file path if successful
    """
    # Download media via WhatsApp client
    file_path = whatsapp_client.download_media(message_id, chat_jid)
    
    if file_path:
        return {
            "success": True,
            "message": "Media downloaded successfully",
            "file_path": file_path
        }
    else:
        return {
            "success": False,
            "message": "Failed to download media"
        }


def _get_message_context_data(message_id: str, chat_jid: str, before: int, after: int) -> Dict[str, Any]:
    """Helper function to get message context for a single message"""
    # Similar to get_message_context_handler but simplified for internal use
    context_query = """
        SELECT id, content, sender, timestamp, is_from_me
        FROM messages
        WHERE chat_jid = ?
        AND id != ?
        AND (
            timestamp < (SELECT timestamp FROM messages WHERE id = ? AND chat_jid = ?)
            OR timestamp > (SELECT timestamp FROM messages WHERE id = ? AND chat_jid = ?)
        )
        ORDER BY timestamp
    """
    
    messages = db_manager.execute_query(
        context_query,
        (chat_jid, message_id, message_id, chat_jid, message_id, chat_jid)
    )
    
    return {
        'total_context_messages': len(messages),
        'has_more_before': len([m for m in messages if m['timestamp'] < datetime.now()]) > before,
        'has_more_after': len([m for m in messages if m['timestamp'] > datetime.now()]) > after
    }