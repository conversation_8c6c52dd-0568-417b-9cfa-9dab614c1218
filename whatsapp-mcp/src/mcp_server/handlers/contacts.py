"""
Contact-related MCP tool handlers
"""

from typing import List, Dict, Any
from ...common.database import db_manager


def search_contacts_handler(query: str) -> List[Dict[str, Any]]:
    """Search WhatsApp contacts by name or phone number.
    
    Args:
        query: Search term to match against contact names or phone numbers
    """
    sql_query = """
        SELECT DISTINCT
            c.jid,
            c.name,
            CASE
                WHEN c.jid LIKE '%@s.whatsapp.net' THEN substr(c.jid, 1, length(c.jid) - 15)
                ELSE c.jid
            END as phone_number
        FROM chats c
        WHERE c.jid LIKE '%@s.whatsapp.net'
        AND (
            c.name LIKE ? OR
            c.jid LIKE ?
        )
        ORDER BY c.last_message_time DESC
        LIMIT 20
    """
    
    search_pattern = f'%{query}%'
    results = db_manager.execute_query(sql_query, (search_pattern, search_pattern))
    
    # Format results
    contacts = []
    for row in results:
        contacts.append({
            'jid': row['jid'],
            'name': row['name'] or row['phone_number'],
            'phone_number': row['phone_number']
        })
    
    return contacts


def get_contact_chats_handler(jid: str, limit: int = 20, page: int = 0) -> List[Dict[str, Any]]:
    """Get all WhatsApp chats involving the contact.
    
    Args:
        jid: The contact's JID to search for
        limit: Maximum number of chats to return (default 20)
        page: Page number for pagination (default 0)
    """
    offset = page * limit
    
    # Query for direct chats and group chats where the contact is a participant
    sql_query = """
        SELECT DISTINCT
            c.jid,
            c.name,
            c.last_message_time,
            CASE
                WHEN c.jid LIKE '%@g.us' THEN 'group'
                ELSE 'direct'
            END as chat_type
        FROM chats c
        WHERE c.jid = ?
        OR c.jid IN (
            SELECT DISTINCT chat_jid
            FROM messages
            WHERE sender = ?
        )
        ORDER BY c.last_message_time DESC
        LIMIT ? OFFSET ?
    """
    
    results = db_manager.execute_query(sql_query, (jid, jid, limit, offset))
    
    # Format results
    chats = []
    for row in results:
        chats.append({
            'jid': row['jid'],
            'name': row['name'] or row['jid'],
            'chat_type': row['chat_type'],
            'last_message_time': row['last_message_time']
        })
    
    return chats


def get_last_interaction_handler(jid: str) -> str:
    """Get most recent WhatsApp message involving the contact.
    
    Args:
        jid: The JID of the contact to search for
    """
    sql_query = """
        SELECT 
            m.content,
            m.timestamp,
            m.sender,
            m.is_from_me,
            c.name as chat_name
        FROM messages m
        JOIN chats c ON m.chat_jid = c.jid
        WHERE m.sender = ? OR (m.chat_jid = ? AND m.chat_jid LIKE '%@s.whatsapp.net')
        ORDER BY m.timestamp DESC
        LIMIT 1
    """
    
    results = db_manager.execute_query(sql_query, (jid, jid))
    
    if not results:
        return f"No interactions found with {jid}"
    
    msg = results[0]
    sender = "You" if msg['is_from_me'] else (msg['sender'] or "Unknown")
    chat_context = f" in {msg['chat_name']}" if msg['chat_name'] else ""
    
    return f"[{msg['timestamp']}] {sender}{chat_context}: {msg['content']}"