"""
WhatsApp MCP Server Main Entry Point
"""

from mcp.server.fastmcp import FastMCP
from .handlers import (
    search_contacts_handler,
    list_messages_handler,
    list_chats_handler,
    get_chat_handler,
    get_direct_chat_handler,
    get_contact_chats_handler,
    get_last_interaction_handler,
    get_message_context_handler,
    send_message_handler,
    send_file_handler,
    send_audio_message_handler,
    download_media_handler
)
from ..common.config import config

# Initialize FastMCP server
mcp = FastMCP(config.mcp.server_name)

# Register all handlers
mcp.tool()(search_contacts_handler)
mcp.tool()(list_messages_handler)
mcp.tool()(list_chats_handler)
mcp.tool()(get_chat_handler)
mcp.tool()(get_direct_chat_handler)
mcp.tool()(get_contact_chats_handler)
mcp.tool()(get_last_interaction_handler)
mcp.tool()(get_message_context_handler)
mcp.tool()(send_message_handler)
mcp.tool()(send_file_handler)
mcp.tool()(send_audio_message_handler)
mcp.tool()(download_media_handler)


def main():
    """Main entry point for the MCP server"""
    mcp.run(transport=config.mcp.transport)


if __name__ == "__main__":
    main()