"""
Configuration management for WhatsApp MCP system
Handles loading from YAML files and environment variables
"""

import os
import yaml
import logging
from pathlib import Path
from typing import Any, Dict, Optional
from dataclasses import dataclass, field

logger = logging.getLogger(__name__)

@dataclass
class DatabaseConfig:
    """Database configuration"""
    messages_db: str = "whatsapp-bridge/store/messages.db"
    timeout: int = 30
    check_same_thread: bool = False
    
    @property
    def full_path(self) -> Path:
        """Get full database path relative to project root"""
        return Config.get_project_root() / self.messages_db

@dataclass
class WhatsAppBridgeConfig:
    """WhatsApp Bridge API configuration"""
    api_url: str = "http://localhost:8080/api"
    timeout: int = 30
    retry_attempts: int = 3
    retry_delay: float = 1.0

@dataclass
class StorageConfig:
    """Storage configuration"""
    media_root: str = "customer_data/media"
    use_date_folders: bool = True
    max_filename_length: int = 100
    
    @property
    def full_path(self) -> Path:
        """Get full storage path relative to project root"""
        return Config.get_project_root() / self.media_root

@dataclass
class MediaProcessingConfig:
    """Media processing configuration"""
    batch_size: int = 10
    scan_interval: int = 30
    download_delay: float = 1.0
    error_retry_delay: int = 60

@dataclass
class OCRConfig:
    """OCR configuration"""
    enabled: bool = True
    languages: list = field(default_factory=lambda: ["ch_tra", "en"])
    use_gpu: bool = False
    batch_size: int = 5
    confidence_threshold: float = 0.7

@dataclass
class CustomerConfig:
    """Customer management configuration"""
    required_fields: list = field(default_factory=list)
    field_weights: dict = field(default_factory=dict)
    collection: dict = field(default_factory=dict)

class Config:
    """Main configuration class"""
    
    _instance: Optional['Config'] = None
    _config_data: Dict[str, Any] = {}
    
    def __init__(self, config_file: Optional[str] = None):
        """Initialize configuration"""
        self.reload(config_file)
    
    @classmethod
    def get_instance(cls) -> 'Config':
        """Get singleton instance"""
        if cls._instance is None:
            cls._instance = cls()
        return cls._instance
    
    @staticmethod
    def get_project_root() -> Path:
        """Get project root directory"""
        # Navigate up from src/core/config.py to project root
        return Path(__file__).parent.parent.parent
    
    def reload(self, config_file: Optional[str] = None):
        """Reload configuration from file and environment"""
        # Determine config file
        if config_file is None:
            env = os.getenv('APP_ENV', 'development')
            config_dir = self.get_project_root() / 'config'
            
            # Try environment-specific config first
            env_config = config_dir / f'{env}.yaml'
            if env_config.exists():
                config_file = str(env_config)
            else:
                config_file = str(config_dir / 'default.yaml')
        
        # Load YAML config
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                self._config_data = yaml.safe_load(f) or {}
            logger.info(f"Loaded configuration from {config_file}")
        except Exception as e:
            logger.warning(f"Failed to load config file {config_file}: {e}")
            self._config_data = {}
        
        # Override with environment variables
        self._apply_env_overrides()
        
        # Create typed config objects
        self.database = DatabaseConfig(**self._config_data.get('database', {}))
        self.whatsapp_bridge = WhatsAppBridgeConfig(**self._config_data.get('whatsapp_bridge', {}))
        self.storage = StorageConfig(**self._config_data.get('storage', {}))
        
        # Processing configs
        processing = self._config_data.get('processing', {})
        self.media_processing = MediaProcessingConfig(**processing.get('media', {}))
        self.ocr = OCRConfig(**processing.get('ocr', {}))
        
        # Customer config
        self.customer = CustomerConfig(**self._config_data.get('customer', {}))
        
        # App settings
        app = self._config_data.get('app', {})
        self.app_name = app.get('name', 'WhatsApp MCP Server')
        self.environment = app.get('environment', 'development')
        self.log_level = app.get('log_level', 'INFO')
        
        # Feature flags
        self.features = self._config_data.get('features', {})
        
        # Development settings
        self.development = self._config_data.get('development', {})
    
    def _apply_env_overrides(self):
        """Apply environment variable overrides"""
        # Database
        if db_path := os.getenv('DB_PATH'):
            self._config_data.setdefault('database', {})['messages_db'] = db_path
        
        # WhatsApp API
        if api_url := os.getenv('WHATSAPP_API_URL'):
            self._config_data.setdefault('whatsapp_bridge', {})['api_url'] = api_url
        
        # Storage
        if media_root := os.getenv('MEDIA_ROOT'):
            self._config_data.setdefault('storage', {})['media_root'] = media_root
        
        # Environment
        if app_env := os.getenv('APP_ENV'):
            self._config_data.setdefault('app', {})['environment'] = app_env
        
        # Log level
        if log_level := os.getenv('LOG_LEVEL'):
            self._config_data.setdefault('app', {})['log_level'] = log_level
    
    def get(self, key: str, default: Any = None) -> Any:
        """Get configuration value by dot notation key"""
        keys = key.split('.')
        value = self._config_data
        
        for k in keys:
            if isinstance(value, dict):
                value = value.get(k)
            else:
                return default
            
            if value is None:
                return default
        
        return value
    
    def is_development(self) -> bool:
        """Check if running in development mode"""
        return self.environment == 'development'
    
    def is_production(self) -> bool:
        """Check if running in production mode"""
        return self.environment == 'production'
    
    def is_feature_enabled(self, feature: str) -> bool:
        """Check if a feature is enabled"""
        return self.features.get(feature, False)

# Convenience function
def get_config() -> Config:
    """Get configuration instance"""
    return Config.get_instance()

# Example usage
if __name__ == "__main__":
    # Test configuration loading
    config = get_config()
    
    print(f"App Name: {config.app_name}")
    print(f"Environment: {config.environment}")
    print(f"Database Path: {config.database.full_path}")
    print(f"Media Storage: {config.storage.full_path}")
    print(f"API URL: {config.whatsapp_bridge.api_url}")
    print(f"OCR Enabled: {config.ocr.enabled}")
    print(f"OCR Languages: {config.ocr.languages}")
    
    # Test feature flags
    print(f"\nFeature - Auto Download: {config.is_feature_enabled('auto_download_media')}")
    print(f"Feature - OCR: {config.is_feature_enabled('ocr_processing')}")