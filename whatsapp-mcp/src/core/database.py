"""
Database management layer for WhatsApp MCP system
Provides unified database access and connection management
"""

import sqlite3
import logging
import json
from contextlib import contextmanager
from typing import Any, Dict, List, Optional, Tuple, Union
from datetime import datetime
from pathlib import Path

from .config import get_config

logger = logging.getLogger(__name__)

class DatabaseError(Exception):
    """Database operation error"""
    pass

class DatabaseManager:
    """Centralized database management"""
    
    def __init__(self, db_path: Optional[Path] = None):
        """Initialize database manager"""
        config = get_config()
        self.db_path = db_path or config.database.full_path
        self.timeout = config.database.timeout
        self.check_same_thread = config.database.check_same_thread
        
        # Ensure database directory exists
        self.db_path.parent.mkdir(parents=True, exist_ok=True)
        
        # Initialize database if needed
        self._init_database()
    
    def _init_database(self):
        """Initialize database schema if needed"""
        try:
            with self.get_connection() as conn:
                # Check if tables exist
                cursor = conn.cursor()
                cursor.execute("""
                    SELECT name FROM sqlite_master 
                    WHERE type='table' AND name='messages'
                """)
                if not cursor.fetchone():
                    logger.warning("Database tables not found. Please run database setup.")
        except Exception as e:
            logger.error(f"Database initialization check failed: {e}")
    
    @contextmanager
    def get_connection(self):
        """Get database connection context manager"""
        conn = None
        try:
            conn = sqlite3.connect(
                str(self.db_path),
                timeout=self.timeout,
                check_same_thread=self.check_same_thread
            )
            # Enable row factory for dict-like access
            conn.row_factory = sqlite3.Row
            yield conn
        except sqlite3.Error as e:
            logger.error(f"Database connection error: {e}")
            raise DatabaseError(f"Failed to connect to database: {e}")
        finally:
            if conn:
                conn.close()
    
    def execute(self, query: str, params: Tuple = ()) -> List[sqlite3.Row]:
        """Execute a query and return results"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            try:
                cursor.execute(query, params)
                return cursor.fetchall()
            except sqlite3.Error as e:
                logger.error(f"Query execution error: {e}")
                raise DatabaseError(f"Query failed: {e}")
    
    def execute_one(self, query: str, params: Tuple = ()) -> Optional[sqlite3.Row]:
        """Execute a query and return single result"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            try:
                cursor.execute(query, params)
                return cursor.fetchone()
            except sqlite3.Error as e:
                logger.error(f"Query execution error: {e}")
                raise DatabaseError(f"Query failed: {e}")
    
    def execute_many(self, query: str, params_list: List[Tuple]) -> int:
        """Execute multiple queries and return affected rows"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            try:
                cursor.executemany(query, params_list)
                conn.commit()
                return cursor.rowcount
            except sqlite3.Error as e:
                conn.rollback()
                logger.error(f"Batch execution error: {e}")
                raise DatabaseError(f"Batch execution failed: {e}")
    
    def insert(self, table: str, data: Dict[str, Any]) -> int:
        """Insert a record and return the ID"""
        columns = ', '.join(data.keys())
        placeholders = ', '.join(['?' for _ in data])
        query = f"INSERT INTO {table} ({columns}) VALUES ({placeholders})"
        
        with self.get_connection() as conn:
            cursor = conn.cursor()
            try:
                cursor.execute(query, tuple(data.values()))
                conn.commit()
                return cursor.lastrowid
            except sqlite3.Error as e:
                conn.rollback()
                logger.error(f"Insert error: {e}")
                raise DatabaseError(f"Insert failed: {e}")
    
    def update(self, table: str, data: Dict[str, Any], where: str, where_params: Tuple = ()) -> int:
        """Update records and return affected rows"""
        set_clause = ', '.join([f"{k} = ?" for k in data.keys()])
        query = f"UPDATE {table} SET {set_clause} WHERE {where}"
        params = tuple(data.values()) + where_params
        
        with self.get_connection() as conn:
            cursor = conn.cursor()
            try:
                cursor.execute(query, params)
                conn.commit()
                return cursor.rowcount
            except sqlite3.Error as e:
                conn.rollback()
                logger.error(f"Update error: {e}")
                raise DatabaseError(f"Update failed: {e}")
    
    def transaction(self):
        """Return a transaction context manager"""
        return self.get_connection()
    
    # Specialized methods for common operations
    
    def get_unprocessed_media(self, limit: int = 100) -> List[Dict[str, Any]]:
        """Get unprocessed media messages"""
        query = """
            SELECT m.id, m.chat_jid, m.sender, m.media_type, m.filename, 
                   m.timestamp, c.name as chat_name
            FROM messages m
            JOIN chats c ON m.chat_jid = c.jid
            WHERE m.media_type IS NOT NULL 
            AND m.media_type != ''
            AND m.id NOT IN (SELECT message_id FROM media_content)
            ORDER BY m.timestamp DESC
            LIMIT ?
        """
        
        results = []
        for row in self.execute(query, (limit,)):
            results.append({
                'id': row['id'],
                'chat_jid': row['chat_jid'],
                'sender': row['sender'],
                'media_type': row['media_type'],
                'filename': row['filename'],
                'timestamp': row['timestamp'],
                'chat_name': row['chat_name']
            })
        
        return results
    
    def save_media_content(self, message_id: str, chat_jid: str, media_type: str,
                          filename: str, file_path: str, extracted_text: Optional[str] = None,
                          extraction_method: Optional[str] = None, 
                          confidence: Optional[float] = None,
                          metadata: Optional[Dict] = None) -> int:
        """Save media content record"""
        data = {
            'message_id': message_id,
            'chat_jid': chat_jid,
            'media_type': media_type,
            'original_filename': filename,
            'file_path': file_path,
            'extracted_text': extracted_text,
            'extraction_method': extraction_method or 'DOWNLOAD_ONLY',
            'extraction_confidence': confidence,
            'metadata': json.dumps(metadata or {}, ensure_ascii=False),
            'processed_at': datetime.now().isoformat()
        }
        
        return self.insert('media_content', data)
    
    def get_customer_profile(self, whatsapp_id: str) -> Optional[Dict[str, Any]]:
        """Get customer profile by WhatsApp ID"""
        query = "SELECT * FROM customer_profiles WHERE whatsapp_id = ?"
        row = self.execute_one(query, (whatsapp_id,))
        
        if row:
            return dict(row)
        return None
    
    def save_customer_profile(self, profile_data: Dict[str, Any]) -> int:
        """Save or update customer profile"""
        whatsapp_id = profile_data.get('whatsapp_id')
        if not whatsapp_id:
            raise ValueError("whatsapp_id is required")
        
        # Check if exists
        existing = self.get_customer_profile(whatsapp_id)
        
        if existing:
            # Update
            profile_id = existing['id']
            profile_data.pop('id', None)
            profile_data['updated_at'] = datetime.now().isoformat()
            
            self.update('customer_profiles', profile_data, 
                       'id = ?', (profile_id,))
            return profile_id
        else:
            # Insert
            profile_data['created_at'] = datetime.now().isoformat()
            profile_data['updated_at'] = datetime.now().isoformat()
            return self.insert('customer_profiles', profile_data)
    
    def get_conversation_templates(self, language: Optional[str] = None) -> List[Dict[str, Any]]:
        """Get conversation templates"""
        if language:
            query = "SELECT * FROM conversation_templates WHERE language = ?"
            params = (language,)
        else:
            query = "SELECT * FROM conversation_templates"
            params = ()
        
        results = []
        for row in self.execute(query, params):
            results.append(dict(row))
        
        return results
    
    def json_encode(self, data: Any) -> str:
        """Encode data as JSON for storage"""
        return json.dumps(data, ensure_ascii=False)
    
    def json_decode(self, data: Optional[str]) -> Any:
        """Decode JSON data from storage"""
        if data:
            try:
                return json.loads(data)
            except json.JSONDecodeError:
                logger.warning(f"Failed to decode JSON: {data}")
        return None

# Singleton instance
_db_manager: Optional[DatabaseManager] = None

def get_db_manager() -> DatabaseManager:
    """Get database manager instance"""
    global _db_manager
    if _db_manager is None:
        _db_manager = DatabaseManager()
    return _db_manager

# Example usage
if __name__ == "__main__":
    # Test database manager
    db = get_db_manager()
    
    # Test queries
    try:
        # Get unprocessed media
        media = db.get_unprocessed_media(limit=5)
        print(f"Found {len(media)} unprocessed media messages")
        
        # Get customer profile
        profile = db.get_customer_profile("<EMAIL>")
        print(f"Profile: {profile}")
        
    except DatabaseError as e:
        print(f"Database error: {e}")