"""
WhatsApp Bridge API client
Handles all communication with the WhatsApp bridge
"""

import logging
import json
import time
from typing import Op<PERSON>, Tu<PERSON>, Dict, Any
from pathlib import Path

import requests
from requests.adapters import HTTPAdapter
from requests.packages.urllib3.util.retry import Retry

from .config import get_config

logger = logging.getLogger(__name__)

class WhatsAppAPIError(Exception):
    """WhatsApp API error"""
    pass

class WhatsAppAPIClient:
    """Client for WhatsApp Bridge API"""
    
    def __init__(self, api_url: Optional[str] = None):
        """Initialize API client"""
        config = get_config()
        self.api_url = api_url or config.whatsapp_bridge.api_url
        self.timeout = config.whatsapp_bridge.timeout
        self.retry_attempts = config.whatsapp_bridge.retry_attempts
        self.retry_delay = config.whatsapp_bridge.retry_delay
        
        # Create session with retry strategy
        self.session = self._create_session()
    
    def _create_session(self) -> requests.Session:
        """Create HTTP session with retry configuration"""
        session = requests.Session()
        
        # Configure retry strategy
        retry_strategy = Retry(
            total=self.retry_attempts,
            backoff_factor=self.retry_delay,
            status_forcelist=[429, 500, 502, 503, 504],
            method_whitelist=["HEAD", "GET", "POST", "PUT", "DELETE", "OPTIONS", "TRACE"]
        )
        
        adapter = HTTPAdapter(max_retries=retry_strategy)
        session.mount("http://", adapter)
        session.mount("https://", adapter)
        
        return session
    
    def _make_request(self, method: str, endpoint: str, 
                     json_data: Optional[Dict] = None,
                     params: Optional[Dict] = None) -> Dict[str, Any]:
        """Make HTTP request to API"""
        url = f"{self.api_url}/{endpoint}"
        
        try:
            response = self.session.request(
                method=method,
                url=url,
                json=json_data,
                params=params,
                timeout=self.timeout
            )
            
            response.raise_for_status()
            
            # Parse JSON response
            try:
                return response.json()
            except json.JSONDecodeError:
                logger.warning(f"Non-JSON response from {url}: {response.text}")
                return {"success": True, "message": response.text}
                
        except requests.exceptions.Timeout:
            raise WhatsAppAPIError(f"Request timeout after {self.timeout}s")
        except requests.exceptions.ConnectionError:
            raise WhatsAppAPIError("Failed to connect to WhatsApp Bridge")
        except requests.exceptions.HTTPError as e:
            raise WhatsAppAPIError(f"HTTP error: {e}")
        except Exception as e:
            raise WhatsAppAPIError(f"Unexpected error: {e}")
    
    def send_message(self, recipient: str, message: str) -> Tuple[bool, str]:
        """
        Send a text message
        
        Args:
            recipient: Phone number or JID
            message: Message text
            
        Returns:
            Tuple of (success, message)
        """
        try:
            response = self._make_request(
                "POST",
                "send",
                json_data={
                    "recipient": recipient,
                    "message": message
                }
            )
            
            success = response.get("success", False)
            message = response.get("message", "Unknown response")
            
            if success:
                logger.info(f"Message sent to {recipient}")
            else:
                logger.warning(f"Failed to send message to {recipient}: {message}")
            
            return success, message
            
        except WhatsAppAPIError as e:
            logger.error(f"API error sending message: {e}")
            return False, str(e)
    
    def send_file(self, recipient: str, media_path: str, 
                  caption: Optional[str] = None) -> Tuple[bool, str]:
        """
        Send a file/media message
        
        Args:
            recipient: Phone number or JID
            media_path: Path to media file
            caption: Optional caption
            
        Returns:
            Tuple of (success, message)
        """
        # Validate file exists
        if not Path(media_path).exists():
            return False, f"File not found: {media_path}"
        
        try:
            json_data = {
                "recipient": recipient,
                "media_path": str(media_path)
            }
            
            if caption:
                json_data["message"] = caption
            
            response = self._make_request("POST", "send", json_data=json_data)
            
            success = response.get("success", False)
            message = response.get("message", "Unknown response")
            
            if success:
                logger.info(f"Media sent to {recipient}: {media_path}")
            else:
                logger.warning(f"Failed to send media to {recipient}: {message}")
            
            return success, message
            
        except WhatsAppAPIError as e:
            logger.error(f"API error sending file: {e}")
            return False, str(e)
    
    def download_media(self, message_id: str, chat_jid: str) -> Optional[str]:
        """
        Download media from a message
        
        Args:
            message_id: Message ID
            chat_jid: Chat JID
            
        Returns:
            Local file path if successful, None otherwise
        """
        try:
            response = self._make_request(
                "POST",
                "download",
                json_data={
                    "message_id": message_id,
                    "chat_jid": chat_jid
                }
            )
            
            if response.get("success", False):
                file_path = response.get("path")
                if file_path and Path(file_path).exists():
                    logger.info(f"Downloaded media from message {message_id}")
                    return file_path
                else:
                    logger.warning(f"Downloaded file not found: {file_path}")
            else:
                logger.warning(f"Failed to download media: {response.get('message')}")
            
            return None
            
        except WhatsAppAPIError as e:
            logger.error(f"API error downloading media: {e}")
            return None
    
    def get_connection_status(self) -> Dict[str, Any]:
        """Check WhatsApp connection status"""
        try:
            response = self._make_request("GET", "status")
            return response
        except WhatsAppAPIError:
            return {
                "connected": False,
                "message": "Failed to check status"
            }
    
    def is_connected(self) -> bool:
        """Check if connected to WhatsApp"""
        status = self.get_connection_status()
        return status.get("connected", False)
    
    def test_connection(self) -> bool:
        """Test API connection"""
        try:
            # Try a simple status check
            self.get_connection_status()
            logger.info("WhatsApp API connection test successful")
            return True
        except Exception as e:
            logger.error(f"WhatsApp API connection test failed: {e}")
            return False

# Singleton instance
_api_client: Optional[WhatsAppAPIClient] = None

def get_api_client() -> WhatsAppAPIClient:
    """Get API client instance"""
    global _api_client
    if _api_client is None:
        _api_client = WhatsAppAPIClient()
    return _api_client

# Example usage
if __name__ == "__main__":
    # Test API client
    client = get_api_client()
    
    # Test connection
    if client.test_connection():
        print("API connection successful")
        
        # Check status
        status = client.get_connection_status()
        print(f"Connection status: {status}")
    else:
        print("API connection failed")