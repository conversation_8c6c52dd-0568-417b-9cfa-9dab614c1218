#!/usr/bin/env python3
"""
對話分析工具 - 分析下載的 WhatsApp 對話
"""

import sqlite3
import json
from datetime import datetime, timedelta
from collections import Counter, defaultdict
from typing import Dict, List, Any, Tuple
import pandas as pd
from pathlib import Path
import matplotlib.pyplot as plt
import matplotlib.font_manager as fm
import seaborn as sns

class ConversationAnalyzer:
    """對話分析器"""
    
    def __init__(self, db_path: str = "../whatsapp-bridge/store/observations.db"):
        self.db_path = Path(db_path)
        
        # 設置中文字體（避免顯示問題）
        plt.rcParams['font.sans-serif'] = ['Arial Unicode MS', 'SimHei']
        plt.rcParams['axes.unicode_minus'] = False
    
    def analyze_all(self) -> Dict[str, Any]:
        """執行完整分析"""
        print("開始分析對話數據...")
        
        analysis = {
            'overview': self.get_overview(),
            'time_analysis': self.analyze_time_patterns(),
            'user_analysis': self.analyze_users(),
            'content_analysis': self.analyze_content(),
            'business_insights': self.generate_business_insights()
        }
        
        return analysis
    
    def get_overview(self) -> Dict[str, Any]:
        """獲取總體概覽"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            
            # 基本統計
            cursor.execute('''
                SELECT 
                    COUNT(DISTINCT chat_id) as total_chats,
                    COUNT(DISTINCT sender) as total_users,
                    COUNT(*) as total_messages,
                    MIN(timestamp) as first_message,
                    MAX(timestamp) as last_message
                FROM observations
            ''')
            
            row = cursor.fetchone()
            
            # 群組 vs 個人對話
            cursor.execute('''
                SELECT 
                    SUM(CASE WHEN is_group THEN 1 ELSE 0 END) as group_messages,
                    SUM(CASE WHEN NOT is_group THEN 1 ELSE 0 END) as private_messages
                FROM observations
            ''')
            
            msg_types = cursor.fetchone()
            
            return {
                'total_chats': row[0],
                'total_users': row[1],
                'total_messages': row[2],
                'date_range': {
                    'start': row[3],
                    'end': row[4]
                },
                'message_types': {
                    'group': msg_types[0],
                    'private': msg_types[1]
                }
            }
    
    def analyze_time_patterns(self) -> Dict[str, Any]:
        """分析時間模式"""
        with sqlite3.connect(self.db_path) as conn:
            # 按小時分析
            df = pd.read_sql_query('''
                SELECT 
                    timestamp,
                    is_from_me,
                    chat_id
                FROM observations
            ''', conn)
            
            df['timestamp'] = pd.to_datetime(df['timestamp'])
            df['hour'] = df['timestamp'].dt.hour
            df['weekday'] = df['timestamp'].dt.day_name()
            df['date'] = df['timestamp'].dt.date
            
            # 每小時消息分布
            hourly_dist = df.groupby('hour').size().to_dict()
            
            # 每週日消息分布
            weekday_dist = df.groupby('weekday').size().to_dict()
            
            # 每日消息趨勢
            daily_trend = df.groupby('date').size().to_dict()
            daily_trend = {str(k): v for k, v in daily_trend.items()}
            
            # 響應時間分析
            response_times = self._analyze_response_times(df)
            
            return {
                'hourly_distribution': hourly_dist,
                'weekday_distribution': weekday_dist,
                'daily_trend': daily_trend,
                'peak_hours': sorted(hourly_dist.items(), key=lambda x: x[1], reverse=True)[:3],
                'response_times': response_times
            }
    
    def _analyze_response_times(self, df: pd.DataFrame) -> Dict[str, Any]:
        """分析響應時間"""
        response_times = []
        
        for chat_id in df['chat_id'].unique():
            chat_df = df[df['chat_id'] == chat_id].sort_values('timestamp')
            
            for i in range(1, len(chat_df)):
                if chat_df.iloc[i]['is_from_me'] != chat_df.iloc[i-1]['is_from_me']:
                    time_diff = (chat_df.iloc[i]['timestamp'] - chat_df.iloc[i-1]['timestamp']).total_seconds()
                    if time_diff < 3600:  # 只考慮1小時內的響應
                        response_times.append(time_diff)
        
        if response_times:
            return {
                'average_seconds': sum(response_times) / len(response_times),
                'median_seconds': sorted(response_times)[len(response_times)//2],
                'fastest_seconds': min(response_times),
                'slowest_seconds': max(response_times)
            }
        return {}
    
    def analyze_users(self) -> Dict[str, Any]:
        """分析用戶行為"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            
            # 最活躍用戶
            cursor.execute('''
                SELECT 
                    sender_name,
                    COUNT(*) as message_count,
                    COUNT(DISTINCT DATE(timestamp)) as active_days
                FROM observations
                WHERE NOT is_from_me
                GROUP BY sender_name
                ORDER BY message_count DESC
                LIMIT 20
            ''')
            
            top_users = [
                {
                    'name': row[0],
                    'message_count': row[1],
                    'active_days': row[2],
                    'avg_messages_per_day': row[1] / row[2] if row[2] > 0 else row[1]
                }
                for row in cursor.fetchall()
            ]
            
            # 用戶參與度分析
            cursor.execute('''
                SELECT 
                    chat_name,
                    COUNT(DISTINCT sender) as participants,
                    COUNT(*) as total_messages,
                    MAX(timestamp) as last_activity
                FROM observations
                GROUP BY chat_id, chat_name
                ORDER BY total_messages DESC
            ''')
            
            chat_engagement = [
                {
                    'chat_name': row[0],
                    'participants': row[1],
                    'total_messages': row[2],
                    'last_activity': row[3]
                }
                for row in cursor.fetchall()
            ]
            
            return {
                'top_users': top_users,
                'chat_engagement': chat_engagement[:10]
            }
    
    def analyze_content(self) -> Dict[str, Any]:
        """分析對話內容"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            
            # 獲取所有非空消息
            cursor.execute('''
                SELECT message_text 
                FROM observations 
                WHERE message_text IS NOT NULL AND message_text != ''
            ''')
            
            all_messages = [row[0] for row in cursor.fetchall()]
            
            # 詞頻分析
            word_freq = self._analyze_word_frequency(all_messages)
            
            # 問題分析
            questions = [msg for msg in all_messages if '?' in msg or '？' in msg]
            
            # 情感分析（簡單版）
            sentiment = self._simple_sentiment_analysis(all_messages)
            
            # 關鍵詞檢測結果
            cursor.execute('''
                SELECT 
                    keyword,
                    COUNT(*) as count
                FROM keyword_alerts
                GROUP BY keyword
                ORDER BY count DESC
            ''')
            
            keyword_stats = [
                {'keyword': row[0], 'count': row[1]}
                for row in cursor.fetchall()
            ]
            
            return {
                'total_messages_analyzed': len(all_messages),
                'top_words': word_freq[:20],
                'questions_count': len(questions),
                'sentiment_distribution': sentiment,
                'keyword_detections': keyword_stats
            }
    
    def _analyze_word_frequency(self, messages: List[str]) -> List[Tuple[str, int]]:
        """分析詞頻"""
        # 停用詞
        stop_words = {
            '的', '了', '是', '我', '你', '在', '有', '和', '就', '不', '也',
            'the', 'is', 'at', 'which', 'on', 'a', 'an', 'as', 'are', 'was',
            '嗎', '吧', '啊', '呢', '麼', '著', '過', '這', '那', '個'
        }
        
        word_counter = Counter()
        
        for msg in messages:
            # 簡單分詞（實際應用中可以使用 jieba 等）
            words = msg.lower().split()
            for word in words:
                if len(word) > 1 and word not in stop_words:
                    word_counter[word] += 1
        
        return word_counter.most_common()
    
    def _simple_sentiment_analysis(self, messages: List[str]) -> Dict[str, int]:
        """簡單情感分析"""
        positive_words = {'好', '謝謝', '感謝', '不錯', '滿意', 'good', 'great', 'thanks', 'excellent', '棒'}
        negative_words = {'不', '問題', '差', '糟', '壞', 'bad', 'problem', 'issue', 'wrong', '投訴'}
        
        sentiment_count = {'positive': 0, 'negative': 0, 'neutral': 0}
        
        for msg in messages:
            msg_lower = msg.lower()
            pos_score = sum(1 for word in positive_words if word in msg_lower)
            neg_score = sum(1 for word in negative_words if word in msg_lower)
            
            if pos_score > neg_score:
                sentiment_count['positive'] += 1
            elif neg_score > pos_score:
                sentiment_count['negative'] += 1
            else:
                sentiment_count['neutral'] += 1
        
        return sentiment_count
    
    def generate_business_insights(self) -> List[Dict[str, Any]]:
        """生成業務洞察"""
        insights = []
        
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            
            # 1. 客戶活躍度洞察
            cursor.execute('''
                SELECT COUNT(DISTINCT chat_id) 
                FROM observations 
                WHERE timestamp > datetime('now', '-7 days')
            ''')
            active_chats_week = cursor.fetchone()[0]
            
            cursor.execute("SELECT COUNT(DISTINCT chat_id) FROM observations")
            total_chats = cursor.fetchone()[0]
            
            if total_chats > 0:
                active_rate = (active_chats_week / total_chats) * 100
                insights.append({
                    'type': 'activity',
                    'title': '客戶活躍度',
                    'content': f'過去7天有 {active_chats_week} 個活躍對話，活躍率 {active_rate:.1f}%',
                    'priority': 'medium' if active_rate > 30 else 'high'
                })
            
            # 2. 響應需求洞察
            cursor.execute('''
                SELECT COUNT(*) 
                FROM observations 
                WHERE message_text LIKE '%?%' OR message_text LIKE '%？%'
                AND NOT is_from_me
            ''')
            questions_count = cursor.fetchone()[0]
            
            if questions_count > 0:
                insights.append({
                    'type': 'questions',
                    'title': '待回答問題',
                    'content': f'客戶共提出了 {questions_count} 個問題，需要確保及時回應',
                    'priority': 'high'
                })
            
            # 3. 業務機會洞察
            cursor.execute('''
                SELECT COUNT(*) 
                FROM keyword_alerts 
                WHERE keyword IN ('價格', '報價', '購買', '訂購')
            ''')
            business_keywords = cursor.fetchone()[0]
            
            if business_keywords > 0:
                insights.append({
                    'type': 'opportunity',
                    'title': '潛在商機',
                    'content': f'檢測到 {business_keywords} 次購買意向相關關鍵詞',
                    'priority': 'high'
                })
            
            # 4. 客戶情緒洞察
            cursor.execute('''
                SELECT COUNT(*) 
                FROM keyword_alerts 
                WHERE keyword IN ('問題', '故障', '投訴', '不滿意')
            ''')
            negative_keywords = cursor.fetchone()[0]
            
            if negative_keywords > 0:
                insights.append({
                    'type': 'risk',
                    'title': '客戶情緒預警',
                    'content': f'檢測到 {negative_keywords} 次負面關鍵詞，需要關注客戶滿意度',
                    'priority': 'urgent'
                })
        
        return insights
    
    def export_report(self, output_dir: str = "reports"):
        """導出完整分析報告"""
        Path(output_dir).mkdir(exist_ok=True)
        
        # 執行分析
        analysis = self.analyze_all()
        
        # 保存 JSON 報告
        report_file = Path(output_dir) / f"analysis_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(analysis, f, ensure_ascii=False, indent=2)
        
        # 生成圖表
        self._generate_charts(analysis, output_dir)
        
        print(f"分析報告已生成: {report_file}")
        return str(report_file)
    
    def _generate_charts(self, analysis: Dict[str, Any], output_dir: str):
        """生成分析圖表"""
        # 1. 每小時消息分布圖
        plt.figure(figsize=(12, 6))
        hours = list(range(24))
        hourly_data = analysis['time_analysis']['hourly_distribution']
        counts = [hourly_data.get(h, 0) for h in hours]
        
        plt.bar(hours, counts, color='skyblue')
        plt.xlabel('小時')
        plt.ylabel('消息數量')
        plt.title('24小時消息分布')
        plt.xticks(hours)
        plt.grid(axis='y', alpha=0.3)
        plt.savefig(Path(output_dir) / 'hourly_distribution.png', dpi=150, bbox_inches='tight')
        plt.close()
        
        # 2. 用戶活躍度圖
        if analysis['user_analysis']['top_users']:
            plt.figure(figsize=(10, 8))
            top_users = analysis['user_analysis']['top_users'][:10]
            names = [u['name'] for u in top_users]
            counts = [u['message_count'] for u in top_users]
            
            plt.barh(names, counts, color='lightgreen')
            plt.xlabel('消息數量')
            plt.title('最活躍用戶 Top 10')
            plt.grid(axis='x', alpha=0.3)
            plt.savefig(Path(output_dir) / 'top_users.png', dpi=150, bbox_inches='tight')
            plt.close()
        
        # 3. 情感分布餅圖
        if analysis['content_analysis']['sentiment_distribution']:
            plt.figure(figsize=(8, 8))
            sentiment = analysis['content_analysis']['sentiment_distribution']
            
            labels = list(sentiment.keys())
            sizes = list(sentiment.values())
            colors = ['lightgreen', 'lightcoral', 'lightskyblue']
            
            plt.pie(sizes, labels=labels, colors=colors, autopct='%1.1f%%', startangle=90)
            plt.title('消息情感分布')
            plt.savefig(Path(output_dir) / 'sentiment_distribution.png', dpi=150, bbox_inches='tight')
            plt.close()


# 使用示例
if __name__ == "__main__":
    analyzer = ConversationAnalyzer()
    
    print("對話分析工具")
    print("=" * 50)
    
    # 執行分析
    analysis_result = analyzer.analyze_all()
    
    # 打印概覽
    overview = analysis_result['overview']
    print(f"\n📊 總體概覽:")
    print(f"- 總對話數: {overview['total_chats']}")
    print(f"- 總用戶數: {overview['total_users']}")
    print(f"- 總消息數: {overview['total_messages']}")
    
    # 打印業務洞察
    insights = analysis_result['business_insights']
    if insights:
        print(f"\n💡 業務洞察:")
        for insight in insights:
            print(f"- [{insight['priority'].upper()}] {insight['title']}: {insight['content']}")
    
    # 導出報告
    print("\n正在生成詳細報告...")
    report_path = analyzer.export_report()
    print(f"✅ 分析完成！")
    print(f"報告位置: {report_path}")