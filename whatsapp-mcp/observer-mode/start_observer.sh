#!/bin/bash

# WhatsApp 觀察模式啟動腳本

echo "WhatsApp 業務觀察模式"
echo "===================="
echo "此模式只會下載和分析對話，不會發送任何消息"
echo ""

# 檢查 WhatsApp Bridge 是否在運行
if ! curl -s http://localhost:8080/health > /dev/null; then
    echo "❌ 錯誤：WhatsApp Bridge 未運行"
    echo "請先啟動 WhatsApp Bridge："
    echo "cd ../whatsapp-bridge && go run main.go"
    exit 1
fi

echo "✅ WhatsApp Bridge 已連接"
echo ""

# 檢查 Python 環境
if ! command -v python3 &> /dev/null; then
    echo "❌ 錯誤：未找到 Python 3"
    exit 1
fi

# 安裝必要的 Python 包（如果需要）
echo "檢查 Python 依賴..."
pip3 install -q requests pandas matplotlib seaborn 2>/dev/null

# 創建報告目錄
mkdir -p reports
mkdir -p logs

echo ""
echo "選擇運行模式："
echo "1. 實時觀察模式 - 持續監控新消息"
echo "2. 分析模式 - 分析已下載的對話"
echo "3. 完整模式 - 觀察並定期生成分析報告"
echo ""
read -p "請選擇 (1/2/3): " mode

case $mode in
    1)
        echo ""
        echo "啟動實時觀察模式..."
        echo "按 Ctrl+C 停止觀察並生成報告"
        echo ""
        python3 whatsapp_observer.py
        ;;
    2)
        echo ""
        echo "啟動分析模式..."
        python3 conversation_analyzer.py
        ;;
    3)
        echo ""
        echo "啟動完整觀察分析模式..."
        echo "將在後台運行觀察，每小時生成一次分析報告"
        echo ""
        
        # 在後台運行觀察器
        nohup python3 whatsapp_observer.py > logs/observer.log 2>&1 &
        OBSERVER_PID=$!
        echo "觀察器 PID: $OBSERVER_PID"
        
        # 定期運行分析
        while true; do
            sleep 3600  # 每小時
            echo "生成分析報告..."
            python3 conversation_analyzer.py
        done &
        ANALYZER_PID=$!
        echo "分析器 PID: $ANALYZER_PID"
        
        echo ""
        echo "完整模式已啟動！"
        echo "查看日誌: tail -f logs/observer.log"
        echo "停止服務: kill $OBSERVER_PID $ANALYZER_PID"
        ;;
    *)
        echo "無效選擇"
        exit 1
        ;;
esac