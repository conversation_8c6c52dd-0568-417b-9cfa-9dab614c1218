#!/usr/bin/env python3
"""
WhatsApp 觀察模式 - 只監聽和記錄對話，不發送任何消息
"""

import json
import sqlite3
import requests
from datetime import datetime
from pathlib import Path
import time
import logging
from typing import Dict, List, Any
import os

class WhatsAppObserver:
    """WhatsApp 觀察者 - 只下載和分析對話"""
    
    def __init__(self, api_url: str = "http://localhost:8080/api", db_path: str = "../whatsapp-bridge/store/observations.db"):
        self.api_url = api_url
        self.db_path = Path(db_path)
        self.db_path.parent.mkdir(parents=True, exist_ok=True)
        
        # 設置日誌
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('observer.log'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)
        
        # 初始化觀察數據庫
        self._init_database()
        
        # 已處理的消息ID集合
        self.processed_messages = set()
        self._load_processed_messages()
        
        self.logger.info("WhatsApp 觀察模式已啟動 - 只監聽不發送")
    
    def _init_database(self):
        """初始化觀察數據庫"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            
            # 對話記錄表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS observations (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    chat_id TEXT NOT NULL,
                    chat_name TEXT,
                    message_id TEXT UNIQUE,
                    sender TEXT,
                    sender_name TEXT,
                    message_text TEXT,
                    message_type TEXT,
                    timestamp TIMESTAMP,
                    is_from_me BOOLEAN,
                    is_group BOOLEAN,
                    observed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # 對話統計表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS chat_statistics (
                    chat_id TEXT PRIMARY KEY,
                    chat_name TEXT,
                    total_messages INTEGER DEFAULT 0,
                    first_message_time TIMESTAMP,
                    last_message_time TIMESTAMP,
                    participants TEXT,
                    message_frequency REAL,
                    is_active BOOLEAN DEFAULT 1,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # 關鍵詞監控表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS keyword_alerts (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    keyword TEXT NOT NULL,
                    chat_id TEXT,
                    message_id TEXT,
                    message_text TEXT,
                    sender TEXT,
                    detected_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # 業務洞察表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS business_insights (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    insight_type TEXT,
                    chat_id TEXT,
                    content TEXT,
                    confidence REAL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            conn.commit()
    
    def _load_processed_messages(self):
        """加載已處理的消息ID"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute("SELECT message_id FROM observations")
                self.processed_messages = {row[0] for row in cursor.fetchall()}
                self.logger.info(f"已加載 {len(self.processed_messages)} 條已處理消息")
        except Exception as e:
            self.logger.error(f"加載已處理消息失敗: {e}")
    
    def start_observing(self, interval: int = 5, keywords: List[str] = None):
        """開始觀察模式
        
        Args:
            interval: 檢查新消息的間隔（秒）
            keywords: 要監控的關鍵詞列表
        """
        self.keywords = keywords or []
        self.logger.info(f"開始觀察，檢查間隔: {interval}秒")
        
        if self.keywords:
            self.logger.info(f"監控關鍵詞: {', '.join(self.keywords)}")
        
        try:
            while True:
                self._check_new_messages()
                time.sleep(interval)
        except KeyboardInterrupt:
            self.logger.info("觀察模式已停止")
        except Exception as e:
            self.logger.error(f"觀察模式出錯: {e}")
    
    def _check_new_messages(self):
        """檢查新消息"""
        try:
            # 獲取所有對話
            response = requests.get(f"{self.api_url}/chats")
            if response.status_code != 200:
                self.logger.error(f"獲取對話列表失敗: {response.status_code}")
                return
            
            chats = response.json()
            
            for chat in chats:
                chat_id = chat.get('id')
                if not chat_id:
                    continue
                
                # 獲取該對話的消息
                self._process_chat_messages(chat_id, chat.get('name', 'Unknown'))
        
        except Exception as e:
            self.logger.error(f"檢查新消息失敗: {e}")
    
    def _process_chat_messages(self, chat_id: str, chat_name: str):
        """處理單個對話的消息"""
        try:
            response = requests.get(f"{self.api_url}/messages", params={'chatId': chat_id})
            if response.status_code != 200:
                return
            
            messages = response.json()
            new_messages = 0
            
            for msg in messages:
                message_id = msg.get('id')
                if not message_id or message_id in self.processed_messages:
                    continue
                
                # 記錄新消息
                self._save_observation(chat_id, chat_name, msg)
                self.processed_messages.add(message_id)
                new_messages += 1
                
                # 檢查關鍵詞
                if self.keywords:
                    self._check_keywords(chat_id, msg)
            
            if new_messages > 0:
                self.logger.info(f"[{chat_name}] 發現 {new_messages} 條新消息")
                self._update_chat_statistics(chat_id, chat_name)
        
        except Exception as e:
            self.logger.error(f"處理對話消息失敗 [{chat_id}]: {e}")
    
    def _save_observation(self, chat_id: str, chat_name: str, message: Dict[str, Any]):
        """保存觀察記錄"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                cursor.execute('''
                    INSERT OR IGNORE INTO observations 
                    (chat_id, chat_name, message_id, sender, sender_name, 
                     message_text, message_type, timestamp, is_from_me, is_group)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    chat_id,
                    chat_name,
                    message.get('id'),
                    message.get('sender'),
                    message.get('senderName', 'Unknown'),
                    message.get('text', ''),
                    message.get('type', 'text'),
                    message.get('timestamp'),
                    message.get('fromMe', False),
                    '@g.us' in chat_id
                ))
                
                conn.commit()
        
        except Exception as e:
            self.logger.error(f"保存觀察記錄失敗: {e}")
    
    def _check_keywords(self, chat_id: str, message: Dict[str, Any]):
        """檢查關鍵詞"""
        text = message.get('text', '').lower()
        if not text:
            return
        
        for keyword in self.keywords:
            if keyword.lower() in text:
                self._save_keyword_alert(keyword, chat_id, message)
    
    def _save_keyword_alert(self, keyword: str, chat_id: str, message: Dict[str, Any]):
        """保存關鍵詞警報"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                cursor.execute('''
                    INSERT INTO keyword_alerts 
                    (keyword, chat_id, message_id, message_text, sender)
                    VALUES (?, ?, ?, ?, ?)
                ''', (
                    keyword,
                    chat_id,
                    message.get('id'),
                    message.get('text'),
                    message.get('senderName', 'Unknown')
                ))
                
                conn.commit()
                self.logger.warning(f"🔔 關鍵詞警報: '{keyword}' 在 {message.get('senderName')} 的消息中")
        
        except Exception as e:
            self.logger.error(f"保存關鍵詞警報失敗: {e}")
    
    def _update_chat_statistics(self, chat_id: str, chat_name: str):
        """更新對話統計"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # 獲取統計數據
                cursor.execute('''
                    SELECT 
                        COUNT(*) as total_messages,
                        MIN(timestamp) as first_message,
                        MAX(timestamp) as last_message,
                        COUNT(DISTINCT sender) as participant_count
                    FROM observations
                    WHERE chat_id = ?
                ''', (chat_id,))
                
                stats = cursor.fetchone()
                
                # 計算消息頻率（每天平均消息數）
                if stats[1] and stats[2]:
                    first_time = datetime.fromisoformat(stats[1])
                    last_time = datetime.fromisoformat(stats[2])
                    days = (last_time - first_time).days + 1
                    frequency = stats[0] / days if days > 0 else stats[0]
                else:
                    frequency = 0
                
                # 更新或插入統計
                cursor.execute('''
                    INSERT OR REPLACE INTO chat_statistics
                    (chat_id, chat_name, total_messages, first_message_time, 
                     last_message_time, message_frequency, updated_at)
                    VALUES (?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
                ''', (
                    chat_id,
                    chat_name,
                    stats[0],
                    stats[1],
                    stats[2],
                    frequency
                ))
                
                conn.commit()
        
        except Exception as e:
            self.logger.error(f"更新對話統計失敗: {e}")
    
    def generate_report(self, output_file: str = "observation_report.json"):
        """生成觀察報告"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.row_factory = sqlite3.Row
                cursor = conn.cursor()
                
                report = {
                    'generated_at': datetime.now().isoformat(),
                    'summary': {},
                    'active_chats': [],
                    'keyword_alerts': [],
                    'insights': []
                }
                
                # 總體統計
                cursor.execute("SELECT COUNT(DISTINCT chat_id) as total_chats, COUNT(*) as total_messages FROM observations")
                summary = cursor.fetchone()
                report['summary'] = dict(summary)
                
                # 活躍對話
                cursor.execute('''
                    SELECT * FROM chat_statistics 
                    WHERE message_frequency > 5
                    ORDER BY total_messages DESC
                    LIMIT 10
                ''')
                report['active_chats'] = [dict(row) for row in cursor.fetchall()]
                
                # 最近的關鍵詞警報
                cursor.execute('''
                    SELECT * FROM keyword_alerts 
                    ORDER BY detected_at DESC 
                    LIMIT 20
                ''')
                report['keyword_alerts'] = [dict(row) for row in cursor.fetchall()]
                
                # 生成業務洞察
                self._generate_insights(cursor, report)
                
                # 保存報告
                with open(output_file, 'w', encoding='utf-8') as f:
                    json.dump(report, f, ensure_ascii=False, indent=2)
                
                self.logger.info(f"觀察報告已生成: {output_file}")
                return report
        
        except Exception as e:
            self.logger.error(f"生成報告失敗: {e}")
            return None
    
    def _generate_insights(self, cursor, report):
        """生成業務洞察"""
        insights = []
        
        # 最活躍的時間段
        cursor.execute('''
            SELECT 
                strftime('%H', timestamp) as hour,
                COUNT(*) as message_count
            FROM observations
            GROUP BY hour
            ORDER BY message_count DESC
            LIMIT 3
        ''')
        
        peak_hours = cursor.fetchall()
        if peak_hours:
            insights.append({
                'type': 'peak_hours',
                'content': f"最活躍時間段: {', '.join([f'{row[0]}:00' for row in peak_hours])}",
                'data': [dict(row) for row in peak_hours]
            })
        
        # 頻繁發言者
        cursor.execute('''
            SELECT 
                sender_name,
                COUNT(*) as message_count
            FROM observations
            WHERE NOT is_from_me
            GROUP BY sender_name
            ORDER BY message_count DESC
            LIMIT 5
        ''')
        
        top_senders = cursor.fetchall()
        if top_senders:
            insights.append({
                'type': 'top_senders',
                'content': f"最活躍用戶: {', '.join([row[0] for row in top_senders[:3]])}",
                'data': [dict(row) for row in top_senders]
            })
        
        report['insights'] = insights


# 使用示例
if __name__ == "__main__":
    # 創建觀察者
    observer = WhatsAppObserver()
    
    # 設置要監控的關鍵詞
    keywords = [
        # 業務相關
        "價格", "報價", "多少錢", "費用", "成本",
        "產品", "服務", "方案", "套餐",
        "訂購", "購買", "下單", "付款",
        
        # 客戶情緒
        "問題", "故障", "投訴", "不滿意",
        "謝謝", "很好", "滿意", "推薦",
        
        # 競爭對手
        "其他公司", "別家", "比較", "對比",
        
        # 緊急情況
        "緊急", "馬上", "立即", "急需"
    ]
    
    print("WhatsApp 觀察模式已啟動")
    print("=" * 50)
    print("模式: 只觀察和下載對話，不發送任何消息")
    print(f"監控關鍵詞: {len(keywords)} 個")
    print("按 Ctrl+C 停止觀察")
    print("=" * 50)
    
    try:
        # 開始觀察
        observer.start_observing(interval=5, keywords=keywords)
    except KeyboardInterrupt:
        print("\n正在生成觀察報告...")
        observer.generate_report()
        print("觀察模式已結束")