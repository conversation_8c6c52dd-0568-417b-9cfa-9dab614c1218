# WhatsApp 觀察模式 (Observer Mode)

純觀察模式 - 只下載和分析對話，**不發送任何消息**。適合業務分析和客戶行為研究。

## 功能特點

### 🔍 觀察功能
- **靜默監聽** - 不會發送任何消息，不會影響正常業務
- **實時下載** - 自動下載所有對話內容
- **關鍵詞監控** - 監測重要業務關鍵詞
- **數據持久化** - 所有對話保存到本地數據庫

### 📊 分析功能
- **時間分析** - 客戶活躍時間、響應時間統計
- **用戶分析** - 最活躍用戶、參與度分析
- **內容分析** - 詞頻統計、情感分析、問題統計
- **業務洞察** - 自動生成業務機會和風險提示

## 快速開始

### 1. 確保 WhatsApp Bridge 正在運行
```bash
# 在另一個終端
cd ../whatsapp-bridge
go run main.go
```

### 2. 啟動觀察模式
```bash
cd observer-mode
./start_observer.sh
```

選擇運行模式：
- **模式 1**: 實時觀察 - 持續監控新消息
- **模式 2**: 分析模式 - 分析已下載的對話
- **模式 3**: 完整模式 - 觀察並定期生成報告

## 監控的關鍵詞

默認監控以下類型的關鍵詞：

### 業務相關
- 價格、報價、多少錢、費用、成本
- 產品、服務、方案、套餐
- 訂購、購買、下單、付款

### 客戶情緒
- 問題、故障、投訴、不滿意
- 謝謝、很好、滿意、推薦

### 競爭情報
- 其他公司、別家、比較、對比

### 緊急情況
- 緊急、馬上、立即、急需

## 數據存儲

所有數據存儲在 SQLite 數據庫中：

```
observer-mode/
├── ../whatsapp-bridge/store/
│   └── observations.db      # 觀察數據庫
├── reports/                 # 分析報告
│   ├── analysis_report_*.json
│   ├── hourly_distribution.png
│   ├── top_users.png
│   └── sentiment_distribution.png
└── logs/                    # 運行日誌
    └── observer.log
```

## 數據庫結構

### observations 表
- 所有觀察到的消息記錄
- 包含發送者、時間、內容等

### chat_statistics 表
- 對話統計信息
- 消息頻率、活躍度等

### keyword_alerts 表
- 關鍵詞觸發記錄
- 用於業務機會識別

### business_insights 表
- 自動生成的業務洞察
- 包含類型、內容、置信度

## 分析報告

系統會生成以下類型的報告：

### 1. 總體概覽
- 總對話數、用戶數、消息數
- 時間範圍
- 群組 vs 私聊比例

### 2. 時間分析
- 24小時消息分布圖
- 每週活躍日分析
- 平均響應時間

### 3. 用戶分析
- Top 10 最活躍用戶
- 用戶參與度排名
- 平均每日消息數

### 4. 內容分析
- 高頻詞彙統計
- 問題數量統計
- 情感分布（正面/負面/中性）

### 5. 業務洞察
- 客戶活躍度趨勢
- 潛在商機提醒
- 客戶情緒預警
- 待回答問題統計

## 使用案例

### 案例 1：了解客戶行為模式
```python
# 查看客戶最活躍的時間段
# 報告會顯示：最活躍時間段：14:00, 15:00, 16:00
# 建議：在這些時段安排更多客服人員
```

### 案例 2：發現業務機會
```python
# 系統檢測到"價格"、"購買"等關鍵詞
# 自動標記為潛在商機
# 可以主動跟進這些客戶
```

### 案例 3：客戶滿意度監控
```python
# 通過情感分析了解客戶情緒
# 及時發現不滿情緒
# 預防客戶流失
```

## 注意事項

1. **隱私保護**
   - 確保遵守當地隱私法規
   - 僅用於業務分析目的
   - 定期清理敏感數據

2. **性能考慮**
   - 大量消息可能佔用較多存儲空間
   - 建議定期歸檔舊數據
   - 可調整檢查間隔減少資源消耗

3. **數據安全**
   - 數據庫文件應妥善保管
   - 建議定期備份
   - 限制訪問權限

## 高級配置

### 自定義關鍵詞
編輯 `whatsapp_observer.py` 中的 keywords 列表：
```python
keywords = [
    "您的自定義關鍵詞",
    "特定產品名稱",
    "競爭對手名稱"
]
```

### 調整檢查間隔
```python
observer.start_observing(interval=10)  # 10秒檢查一次
```

### 導出數據
```bash
# 導出為 CSV
sqlite3 observations.db ".mode csv" ".output messages.csv" "SELECT * FROM observations;" ".quit"
```

## 故障排除

### 無法連接到 WhatsApp Bridge
- 確認 Bridge 在端口 8080 運行
- 檢查防火牆設置

### 沒有新消息
- 確認 WhatsApp 已登錄
- 檢查是否有新對話

### 分析報告為空
- 確認數據庫中有數據
- 檢查時間範圍設置

## 最佳實踐

1. **定期分析** - 每天或每週生成分析報告
2. **關鍵詞優化** - 根據業務調整監控關鍵詞
3. **數據清理** - 定期清理超過 3 個月的舊數據
4. **備份策略** - 每週備份數據庫文件
5. **團隊共享** - 將報告分享給相關團隊成員