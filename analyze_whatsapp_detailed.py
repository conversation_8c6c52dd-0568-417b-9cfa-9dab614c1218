import csv
from collections import Counter
import re

# Read the CSV file
messages = []
with open('/tmp/whatsapp_messages.csv', 'r', encoding='utf-8') as f:
    reader = csv.reader(f)
    for row in reader:
        if len(row) >= 2:
            messages.append(row[1])

# Extract common phrases and patterns
common_phrases = Counter()
question_patterns = []
confirmation_patterns = []
time_patterns = []

for msg in messages:
    # Extract common phrases (3-10 characters)
    chinese_phrases = re.findall(r'[\u4e00-\u9fff]{3,10}', msg)
    common_phrases.update(chinese_phrases)
    
    # Question patterns
    if '？' in msg or '嗎' in msg or '呢' in msg:
        question_patterns.append(msg[:200])
    
    # Confirmation patterns
    if '確認' in msg or '收到' in msg or '已經' in msg or 'ok' in msg.lower():
        confirmation_patterns.append(msg[:200])
    
    # Time patterns
    if re.search(r'\d+[時点]|\d+:\d+|[上下]午|星期|明天|今日', msg):
        time_patterns.append(msg[:200])

print("=== 最常用的中文短語 TOP 30 ===")
for phrase, count in common_phrases.most_common(30):
    if count > 5:  # 只顯示出現5次以上的
        print(f"{phrase}: {count}次")

print("\n=== 常見問句模式 (前5個) ===")
unique_questions = list(set(question_patterns))[:5]
for i, msg in enumerate(unique_questions, 1):
    print(f"\n{i}. {msg}")

print("\n=== 確認類消息模式 (前5個) ===")
unique_confirmations = list(set(confirmation_patterns))[:5]
for i, msg in enumerate(unique_confirmations, 1):
    print(f"\n{i}. {msg}")

print("\n=== 時間相關模式 (前5個) ===")
unique_times = list(set(time_patterns))[:5]
for i, msg in enumerate(unique_times, 1):
    print(f"\n{i}. {msg}")

# Analyze message starts and ends
print("\n=== 常見開頭語 ===")
message_starts = Counter()
for msg in messages:
    if len(msg) > 10:
        start = msg[:10].strip()
        if start:
            message_starts[start] += 1

for start, count in message_starts.most_common(10):
    if count > 3:
        print(f"{start}: {count}次")

# Analyze greeting variations
print("\n=== 打招呼變化 ===")
greetings = []
for msg in messages:
    if any(word in msg[:50] for word in ['你好', '您好', '歡迎', '早晨', '午安', '晚安', 'Hi', 'Hello']):
        greetings.append(msg[:100])

unique_greetings = list(set(greetings))[:10]
for i, greeting in enumerate(unique_greetings, 1):
    print(f"\n{i}. {greeting}")