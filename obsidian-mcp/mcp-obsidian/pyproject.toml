[project]
name = "mcp-obsidian"
version = "0.2.1"
description = "MCP server to work with Obsidian via the remote REST plugin"
readme = "README.md"
requires-python = ">=3.11"
dependencies = [
 "mcp>=1.1.0",
 "python-dotenv>=1.0.1",
 "requests>=2.32.3",
]
[[project.authors]]
name = "<PERSON>"
email = "<EMAIL>"

[build-system]
requires = [ "hatchling",]
build-backend = "hatchling.build"

[dependency-groups]
dev = [
    "pyright>=1.1.389",
]

[project.scripts]
mcp-obsidian = "mcp_obsidian:main"
