# Obsidian MCP 設置指南

## 問題診斷

您的 Obsidian MCP 遇到兩個主要問題：

1. **錯誤的 vault 路徑**
   - 配置中的路徑：`/Users/<USER>/Documents/KiriAi`
   - 實際 vault 路徑：`/Users/<USER>/Documents/KiriAi/KiriAi`

2. **缺少 API Key**
   - 當前使用的 `obsidian-mcp` 需要 Obsidian REST API 插件和 API Key

## 解決方案

### 方案 1：修復現有配置（簡單快速）

1. **安裝 Obsidian REST API 插件**
   - 打開 Obsidian
   - 進入設置 → 社區插件
   - 搜索 "Local REST API"
   - 安裝並啟用插件
   - 在插件設置中獲取 API Key

2. **更新 Claude Desktop 配置**
   編輯 `~/Library/Application Support/Claude/claude_desktop_config.json`：

```json
{
  "mcpServers": {
    "whatsapp": {
      "command": "/opt/homebrew/bin/uv",
      "args": [
        "--directory",
        "/Users/<USER>/Claude/whatsapp-mcp/whatsapp-mcp-server",
        "run",
        "main.py"
      ]
    },
    "obsidian": {
      "command": "npx",
      "args": ["-y", "obsidian-mcp", "/Users/<USER>/Documents/KiriAi/KiriAi"],
      "env": {
        "OBSIDIAN_API_KEY": "你的API密鑰"
      }
    }
  }
}
```

### 方案 2：使用 MarkusPfundstein 的實現（功能更豐富）

1. **安裝 Obsidian REST API 插件**（同上）

2. **更新配置使用新的實現**：

```json
{
  "mcpServers": {
    "whatsapp": {
      "command": "/opt/homebrew/bin/uv",
      "args": [
        "--directory",
        "/Users/<USER>/Claude/whatsapp-mcp/whatsapp-mcp-server",
        "run",
        "main.py"
      ]
    },
    "obsidian": {
      "command": "/opt/homebrew/bin/uv",
      "args": [
        "--directory",
        "/Users/<USER>/Claude/obsidian-mcp/mcp-obsidian",
        "run",
        "mcp-obsidian"
      ],
      "env": {
        "OBSIDIAN_API_KEY": "你的API密鑰",
        "OBSIDIAN_HOST": "127.0.0.1",
        "OBSIDIAN_PORT": "27124"
      }
    }
  }
}
```

## 測試步驟

1. **重啟 Claude Desktop**
   - 完全退出 Claude Desktop (Cmd+Q)
   - 重新開啟

2. **測試指令**
   - "列出我的 Obsidian vault 中的所有文件"
   - "搜索 Obsidian 中包含 'AI' 的筆記"
   - "讀取 [筆記名稱] 的內容"

## 功能對比

### npx obsidian-mcp
- 簡單的文件訪問
- 需要 API Key

### MarkusPfundstein/mcp-obsidian
- 列出文件和目錄
- 獲取文件內容
- 搜索功能
- 修改內容（patch_content）
- 追加內容（append_content）
- 刪除文件

## 故障排除

查看日誌：
```bash
tail -f ~/Library/Logs/Claude/mcp-server-obsidian.log
```

常見問題：
- 確保 Obsidian 正在運行
- 確保 REST API 插件已啟用
- 檢查 API Key 是否正確
- 確認 vault 路徑正確