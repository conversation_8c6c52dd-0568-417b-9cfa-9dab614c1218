# Obsidian MCP 工作使用指南

## 什麼是 Obsidian MCP？

Obsidian MCP 讓 Claude Desktop 能夠直接訪問和操作您的 Obsidian 筆記庫，實現 AI 輔助的知識管理。

## 日常工作場景

### 1. 知識檢索與總結
```
範例指令：
- "搜尋我所有關於專案管理的筆記並總結要點"
- "找出上週的所有會議記錄並整理成報告"
- "查找所有提到 Python 的技術筆記"
```

### 2. 筆記整理與優化
```
範例指令：
- "幫我把今天的會議筆記整理成結構化格式"
- "為我的技術文檔添加目錄和標籤"
- "檢查並改進我的學習筆記格式"
```

### 3. 知識連結建立
```
範例指令：
- "分析我的筆記並建議相關主題的連結"
- "找出可以合併的重複內容"
- "建立主題索引頁面"
```

### 4. 內容生成與擴展
```
範例指令：
- "基於我的研究筆記撰寫一份報告"
- "將我的想法筆記擴展成完整文章"
- "根據會議記錄生成行動項目清單"
```

## 實際工作流程

### 早晨規劃
1. "顯示我昨天的所有筆記"
2. "基於未完成任務創建今日待辦清單"
3. "查找本週的重要截止日期"

### 會議準備
1. "搜尋與 [客戶名] 相關的所有筆記"
2. "整理上次會議的行動項目"
3. "創建新的會議筆記模板"

### 項目管理
1. "列出 [專案名] 的所有相關文檔"
2. "更新專案進度筆記"
3. "生成專案週報"

### 學習研究
1. "整理所有關於 [主題] 的學習筆記"
2. "創建知識卡片"
3. "建立學習路線圖"

## 進階技巧

### 1. 批量操作
```
"將所有標記為 'draft' 的筆記移到草稿文件夾"
"為本月的所有筆記添加月份標籤"
```

### 2. 自動化工作流
```
"每天總結新增的筆記並創建日誌"
"檢查並修復所有斷開的連結"
```

### 3. 知識庫維護
```
"找出最久未更新的筆記"
"識別孤立的筆記（沒有連結）"
"優化標籤系統"
```

## 最佳實踐

### 1. 筆記命名規範
- 使用一致的命名模式
- 包含日期、類別等元數據
- 範例：`2024-06-29-會議-產品規劃`

### 2. 標籤系統
- 建立標籤層級（#主題/子主題）
- 定期審查和整理標籤
- 使用 Claude 協助標籤建議

### 3. 定期維護
- 每週請 Claude 檢查知識庫健康度
- 定期合併重複內容
- 更新過時信息

## 常用提示詞模板

### 搜尋類
- "在 Obsidian 中搜尋 [關鍵詞]"
- "列出所有包含 [標籤] 的筆記"
- "找出 [日期範圍] 內創建的筆記"

### 分析類
- "分析我的筆記結構並提出改進建議"
- "識別最常用的標籤和主題"
- "生成知識圖譜概覽"

### 創建類
- "基於 [主題] 創建新筆記"
- "生成 [類型] 的筆記模板"
- "將 [內容] 整理成 Obsidian 筆記"

## 注意事項

1. **隱私保護**
   - 敏感信息不要包含在筆記中
   - 定期檢查權限設置

2. **備份策略**
   - 定期備份 Obsidian vault
   - 使用版本控制（如 Git）

3. **性能優化**
   - 避免過大的單個筆記
   - 定期清理附件和圖片

## 故障排除

如果 Obsidian MCP 無法正常工作：

1. 確認 Obsidian 正在運行
2. 檢查 REST API 插件是否啟用
3. 重啟 Claude Desktop
4. 查看錯誤日誌：
   ```bash
   tail -f ~/Library/Logs/Claude/mcp-server-obsidian.log
   ```

## 提升效率的秘訣

1. **建立個人工作流模板**
2. **使用 Claude 定期審查和優化筆記結構**
3. **結合 Obsidian 插件增強功能**
4. **培養每日回顧習慣**

通過 Obsidian MCP，您可以將 Claude 的 AI 能力與您的個人知識管理系統完美結合，大幅提升工作效率。