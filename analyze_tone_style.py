import csv
import re
from collections import Counter, defaultdict

# Read the CSV file
messages = []
with open('/tmp/whatsapp_messages.csv', 'r', encoding='utf-8') as f:
    reader = csv.reader(f)
    for row in reader:
        if len(row) >= 2:
            messages.append(row[1])

print("=== 語氣和用詞習慣分析 ===\n")

# Analyze language patterns
polite_words = Counter()
casual_words = Counter()
professional_words = Counter()
ending_particles = Counter()

for msg in messages:
    # Polite expressions
    polite = re.findall(r'請問|感謝|謝謝|歡迎|您好|您|麻煩|不好意思|抱歉', msg)
    polite_words.update(polite)
    
    # Casual/friendly expressions
    casual = re.findall(r'呀|啦|呢|啊|哦|囉|嘅|咁|嗎|喇|啫|㗎|唔|冇', msg)
    casual_words.update(casual)
    
    # Professional terms
    professional = re.findall(r'安排|預約|確認|提供|了解|建議|方便|通知|提醒', msg)
    professional_words.update(professional)
    
    # Sentence ending particles (Cantonese style)
    endings = re.findall(r'[呀啦呢啊哦囉喇啫㗎嘛](?=[。！？\s]|$)', msg)
    ending_particles.update(endings)

print("1. 禮貌用語頻率：")
for word, count in polite_words.most_common(10):
    print(f"   {word}: {count}次")

print("\n2. 親切/口語化表達：")
for word, count in casual_words.most_common(15):
    print(f"   {word}: {count}次")

print("\n3. 專業用語：")
for word, count in professional_words.most_common(10):
    print(f"   {word}: {count}次")

print("\n4. 句尾語氣詞：")
for particle, count in ending_particles.most_common(10):
    print(f"   {particle}: {count}次")

# Analyze response patterns
print("\n\n=== 回覆句式模板 ===\n")

# Extract short responses
short_responses = [msg for msg in messages if 10 < len(msg) < 100]
response_patterns = defaultdict(list)

for resp in short_responses:
    if '可以' in resp:
        response_patterns['確認可以'].append(resp)
    elif '收到' in resp:
        response_patterns['確認收到'].append(resp)
    elif '好的' in resp or 'ok' in resp.lower():
        response_patterns['同意確認'].append(resp)
    elif '？' in resp:
        response_patterns['詢問確認'].append(resp)
    elif '謝' in resp:
        response_patterns['感謝回覆'].append(resp)

for pattern_type, examples in response_patterns.items():
    print(f"{pattern_type}類：")
    unique_examples = list(set(examples))[:3]
    for example in unique_examples:
        print(f"  - {example}")
    print()

# Analyze greeting variations by time
print("\n=== 時間相關問候語 ===\n")
time_greetings = {
    '早晨/早上': [],
    '午安/下午': [],
    '晚上': [],
    '一般問候': []
}

for msg in messages:
    if '早晨' in msg or '早上好' in msg:
        time_greetings['早晨/早上'].append(msg[:50])
    elif '午安' in msg or '下午好' in msg:
        time_greetings['午安/下午'].append(msg[:50])
    elif '晚上好' in msg or '晚安' in msg:
        time_greetings['晚上'].append(msg[:50])
    elif any(greeting in msg[:30] for greeting in ['你好', '您好', 'Hi', 'Hello']):
        time_greetings['一般問候'].append(msg[:50])

for time_period, greetings in time_greetings.items():
    if greetings:
        print(f"{time_period}：")
        unique_greetings = list(set(greetings))[:2]
        for greeting in unique_greetings:
            print(f"  - {greeting}")
        print()

# Extract action prompts
print("\n=== 行動呼籲用語 ===\n")
action_prompts = []
for msg in messages:
    # Look for call-to-action phrases
    if re.search(r'回覆|聯絡|致電|WhatsApp|預約|告訴|提供|即刻', msg):
        # Extract the sentence containing the action
        sentences = re.split(r'[。！？\n]', msg)
        for sent in sentences:
            if re.search(r'回覆|聯絡|致電|WhatsApp|預約|告訴|提供|即刻', sent):
                action_prompts.append(sent.strip())

unique_prompts = list(set(action_prompts))[:10]
for i, prompt in enumerate(unique_prompts, 1):
    if prompt:
        print(f"{i}. {prompt}")

# Analyze number formatting
print("\n\n=== 數字和價格格式習慣 ===\n")
number_formats = []
for msg in messages:
    # Extract price formats
    prices = re.findall(r'[$￥]\s*[\d,]+|\d+元|HK\$[\d,]+', msg)
    number_formats.extend(prices)

price_counter = Counter(number_formats)
print("常見價格格式：")
for format_example, count in price_counter.most_common(10):
    if count > 2:
        print(f"  {format_example} (出現{count}次)")